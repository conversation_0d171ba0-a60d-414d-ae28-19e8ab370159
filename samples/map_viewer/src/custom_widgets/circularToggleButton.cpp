

#include "circularToggleButton.h"
#include <QDebug>

CircularToggleButton::CircularToggleButton(QWidget *parent)
    : QPushButton(parent)
{

    // 设置圆形外观
           setFixedSize(100, 100);
           setStyleSheet(R"(
               QPushButton {
                   border-radius: 50px;
                   background-color: #cccccc;
                   color: black;
                   font-size: 20px;
                   font-weight: bold;
               }
               QPushButton:pressed {
                   background-color: #aaaaaa;
               }
           )");

           // 初始化文本选项
           textOptions = QStringList({CIRCULAR_2D, CIRCULAR_3D, CIRCULAR_NORTHUP});
           currentIndex = 0;
           setText(textOptions[currentIndex]);

           // 连接点击信号
           connect(this, &QPushButton::clicked, this, &CircularToggleButton::onClicked);

}


CircularToggleButton::~CircularToggleButton()
{

}

void CircularToggleButton::setCurrentStatus(int index)
{
    currentIndex = index;
    setText(textOptions[currentIndex]);

    // 发出文本变化信号
    emit textChanged(textOptions[currentIndex]);

    // 处理点击逻辑
    handleButtonClick();
}


void CircularToggleButton::onClicked()
{
    // 循环切换文本
    currentIndex = (currentIndex + 1) % textOptions.size();
    setText(textOptions[currentIndex]);

    // 发出文本变化信号
    emit textChanged(textOptions[currentIndex]);

    // 处理点击逻辑
    handleButtonClick();
}


void CircularToggleButton::handleButtonClick()
{
    // 根据当前文本执行不同操作
    const QString &currentText = textOptions[currentIndex];
    qDebug() << "Button clicked. Current text:" << currentText;

    // 添加你的处理逻辑
    if (currentText == CIRCULAR_3D) {
        // 处理3D模式
    } else if (currentText == CIRCULAR_2D) {
        // 处理2D模式
    } else if (currentText == CIRCULAR_NORTHUP) {
        // 处理车头正北模式
    }

}

void CircularToggleButton::setButtonSize(int size, int fontSize)
{
    setFixedSize(size, size);
    setStyleSheet(QString(R"(
        QPushButton {
            border-radius: %1px;
            background-color: #cccccc;
            color: black;
            font-size: %2px;
            font-weight: bold;
        }
        QPushButton:pressed {
            background-color: #aaaaaa;
        }
    )").arg(size / 2).arg(fontSize));
}
