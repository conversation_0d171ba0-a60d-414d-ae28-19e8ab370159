

#ifndef CIRCULARTOGGLEBUTTON_H
#define CIRCULARTOGGLEBUTTON_H



#include <QPushButton>
#include <QStringList>

const QString CIRCULAR_2D = "2D";
const QString CIRCULAR_3D = "3D";
const QString CIRCULAR_NORTHUP = "正北";

class CircularToggleButton : public QPushButton
{
    Q_OBJECT
public:
    explicit CircularToggleButton(QWidget *parent = nullptr);

    ~CircularToggleButton();

    void setButtonSize(int size, int fontSize);
    void setCurrentStatus(int index);


signals:
    void textChanged(const QString newText);

private slots:
    void onClicked();



private:
    void handleButtonClick();

private:
    QStringList textOptions;
    int currentIndex;




};


#endif // CIRCULARTOGGLEBUTTON_H
