﻿
#include <QStylePainter>
#include <QPixmap>
#include "utils/util_funtion_tools.h"
#include "uigpnctrl.h"

UIGpnCtrl::UIGpnCtrl(QWidget *parent)
: QWidget(parent)
{
	_backgroundImageFlag = false;
	_imageFlag = false;
    m_QiconFlag = false;
	_imageID = 0;
	_resPath = util::GetResourceFolder();
}

UIGpnCtrl::~UIGpnCtrl()
{
}

void UIGpnCtrl::paintEvent(QPaintEvent *event)
{
	QStylePainter paint(this);

	if (_backgroundImageFlag) {
		paint.drawItemPixmap(this->rect(), Qt::AlignHCenter | Qt::AlignVCenter, QPixmap(_resPath + _backgroundImageName, "PNG"));
	}

	if (_imageFlag) {
		char imageName[64] = { 0 };
		sprintf(imageName, "gpd_%d.png", _imageID);
		paint.drawItemPixmap(this->rect(), Qt::AlignHCenter | Qt::AlignVCenter, QPixmap(_resPath + imageName, "PNG"));
	}

    if (m_QiconFlag) {
        paint.drawItemPixmap(this->rect(), Qt::AlignHCenter | Qt::AlignVCenter, m_Qicon.pixmap(QSize(64, 76)));
    }
}

void UIGpnCtrl::setResPath(const QString& resPath)
{
	_resPath = resPath;
	update();
}

void UIGpnCtrl::setBackgroundImageName(const QString& imageName)
{
	_backgroundImageName = imageName;
	_backgroundImageFlag = true;
	update();
}

void UIGpnCtrl::setImageID(const int imageID)
{
	_imageID = imageID;
	_imageFlag = true;
	update();
}


void UIGpnCtrl::setImage(const QIcon mark_image)
{
    m_Qicon = mark_image;
    m_QiconFlag = true;
    update();
}

void UIGpnCtrl::hideImage()
{
    m_QiconFlag = false;
    update();
}
