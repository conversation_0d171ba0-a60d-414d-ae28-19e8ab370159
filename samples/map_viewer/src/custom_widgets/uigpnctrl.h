﻿
#ifndef UIGPNCTRL_H
#define UIGPNCTRL_H

#include <QtWidgets>

#if defined(QDESIGNER_EXPORT_WIDGETS)
#include <QtDesigner/QDesignerExportWidget>

class QDESIGNER_WIDGET_EXPORT UIGpnCtrl : public QWidget
#else
class UIGpnCtrl : public QWidget
#endif
{
	Q_OBJECT
		
	//Q_PROPERTY(QString resPath READ resPath WRITE setResPath)
	Q_PROPERTY(QString backgroundImageName READ backgroundImageName WRITE setBackgroundImageName)
	Q_PROPERTY(int imageID READ imageID WRITE setImageID)

public:
	UIGpnCtrl(QWidget *parent = 0);
	~UIGpnCtrl();

	void setResPath(const QString& resPath);
	const QString& resPath() { return _resPath; }

	void setBackgroundImageName(const QString& imageName);
	const QString& backgroundImageName() { return _backgroundImageName; }

	void setImageID(const int imageID);
    int imageID() { return _imageID; }

    void setImage(const QIcon mark_image);
    void hideImage();

protected:
	void paintEvent(QPaintEvent *event);


private:
	QString	_resPath;

	bool	_backgroundImageFlag;
	QString	_backgroundImageName;

	bool	_imageFlag;
	int		_imageID;

    bool	m_QiconFlag;
    QIcon   m_Qicon;
};

#endif // UIGPNCTRL_H
