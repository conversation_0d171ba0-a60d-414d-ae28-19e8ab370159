
#ifndef MAP_ENGINE_MANAGE_H
#define MAP_ENGINE_MANAGE_H


#include <QObject>
#include "routing_control.h"
#include "data_provider.h"
#include "path/include/path_module.h"
#include "search/include/search_service.h"
#include "map_render_module.h"
#include "config_data/cfg_manager.h"
#include "guidance/include/guidance_module.h"
#include "module.h"
#include "location/include/location_module.h"
#include "location/include/location_def.h"
#include "location_control.h"
#include "guidance_control.h"

class RoutePathListener;
class GetSearchByTextResponseHandler;
class MyGuidanceListener;
class LoctionMatchListner;
class MySoundListener;

typedef std::shared_ptr<aurora::search::SearchByTextResponse> SearchByTextResponsePtr ;

enum MapMode {
    MM_MAP_VIEW = 0,
    MM_SIMUL_NAVI = 1,
    MM_NAVI,
    MM_ROUTE_PATH,
    MM_INVALID
};

class MapEngineManage : public QObject
{
    Q_OBJECT

public:
    MapEngineManage();
    ~MapEngineManage();

    static MapEngineManage* GetInstance();
    static void Close();

    const QStringList& getRecentFileList() { return m_recentFileList; }

    aurora::parser::DataProvider &GetDataProvider() {return m_data_provider; }
    std::shared_ptr<aurora::path::PathInterface> getRouterPathProvider() {return m_path_interface_ptr; }
    std::shared_ptr<aurora::IMapRenderService> onGetRenderEngineService() {return m_map_render_service_ptr;}

    bool InitMapEngineConfig(const QString *AppPath, const QString *DataPath);
    bool isDataEngineReady() {return m_is_data_engine_ready;}
    bool isRouteCaclEngineReady() {return m_is_route_cacl_engine_ready;}
    bool isRenderEngineReady() {return m_is_render_engine_ready;}

    void onRequestPoiSearch(std::shared_ptr<aurora::search::SearchByTextRequest> request);
    void setPoiResultList(std::shared_ptr<aurora::search::SearchByTextResponse> &response);
    void clearPoiResult();
    bool initMapEngineRenderMap(const QString * DataPath);
    std::shared_ptr<aurora::guide::IGuidance> getGuideInterface() {return m_guide_interface_ptr;}
    void setSelectRoutePathID(uint64_t select_path_id);
    MapMode getCurMapMode();
    void setCurMapMode(MapMode map_mode);

    void setNavigationMode(aurora::guide::NavigationMode type);
    void updateMapMatchingPos(const aurora::loc::MatchResult &map_result);
    void stopNavigation();
    void stopSimulation();

    bool getFavoritePos(int index , map_engine::RGeoPoint &pos);


private:

    bool CheckDataPath(const QString *DataPath);

    bool initDefaultDataPath();
    bool initMapEngineRoutePathCacl(const QString * ConfigPath, const QString *DataPath);
    bool initMapEngineDataProvider(const QString * DataPath);
    bool initMapEngineSearch(const QString * DataPath);
    bool initMapEngineLoction(const QString * DataPath);
    bool initMapEngineGuidance(const QString * DataPath);

    void clearMapEngine();



signals:
    void getRoutePathResult(const aurora::path::PathQueryPtr& query, const aurora::path::PathResultPtr& result);
    void getPoiResultList();
    void getTBTDetailsInfo();
    void sendLoctionMatchResult(aurora::loc::MatchResult result);
    void updatePoiSearchStatusInfo(QString info);
    void updatePlayTTS(aurora::guide::SoundInfoPtr sound_tts);
    void navigationArrive();

    void updateExpandView(aurora::guide::JunctionViewInfoPtr info);
    void updateLane(aurora::guide::NavigationLaneInfoPtr info);
    void updateNavigationInfo(aurora::guide::NavigationInfoPtr info);

private slots:
    void onGetPoiResultList();
    void onUpdatePoiSearchStatusInfo(QString info);
    void onGetLoctionMatchResult(aurora::loc::MatchResult result);
    void onNavigationArrive();


public:
    static const QString PREFERENCES_FILE_NAME;

    std::shared_ptr<RoutingControl> m_routingCenter_ptr;
    std::shared_ptr<LocationControl> m_loction_control_ptr;
    std::shared_ptr<GuidanceControl> m_guidance_control_ptr;

    SearchByTextResponsePtr m_poi_search_response_ptr;
    bool is_engine_valid;

    //for engine config
    double m_def_longitude;
    double m_def_latitude;
    double m_def_scale;

    QString m_render_config_path;
    QString m_render_data_path;
    QString m_render_fonts_path;
    QString m_route_data_path;
    QString m_route_cacl_config_path;
    QString m_res_data_path;
    QString m_base_data_path;


private:
    static MapEngineManage* _instance;
    QStringList m_recentFileList;

    //for render data
    bool m_is_render_engine_ready;

    //for data provider
    aurora::parser::DataProvider m_data_provider;
    bool m_is_data_engine_ready;

    //for route cacl path
    std::shared_ptr<aurora::path::PathInterface> m_path_interface_ptr;
    std::shared_ptr<aurora::path::PathModule> m_path_module_ptr;
    std::shared_ptr<RoutePathListener> m_path_listener_ptr;
    bool m_is_route_cacl_engine_ready;

    //for guide
    QString m_guidance_conf_path;
    aurora::guide::GuidanceModule m_engine_guidance_module;
    std::shared_ptr<aurora::guide::IGuidance> m_guide_interface_ptr;
    std::shared_ptr<MyGuidanceListener> m_guidance_listener;
    std::shared_ptr<MySoundListener> m_sound_listener;

    //for search
    QString m_search_data_path;
    std::shared_ptr<aurora::search::SearchService> m_search_service_ptr;
    std::shared_ptr<GetSearchByTextResponseHandler> m_search_listener_ptr;
    
    //for render map
    std::shared_ptr<aurora::IMapRenderService> m_map_render_service_ptr;
    std::shared_ptr<aurora::parser::CfgManager> m_map_render_thema_ptr;

    //for loction
    aurora::loc::LocationModule m_loction_nodule;
    std::shared_ptr<aurora::loc::ILocation> m_loction_interface_ptr;
    std::shared_ptr<LoctionMatchListner> m_loc_match_listener;

    MapMode m_cur_map_mode;

    vector<QString> m_region_list;
    vector<map_engine::RGeoPoint> m_favorite_pos;

;



};


#endif // MAP_ENGINE_MANAGE_H
