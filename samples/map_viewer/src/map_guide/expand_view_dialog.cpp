﻿
#include "expand_view_dialog.h"
#include <QImage>
#include <QImageReader>
#include <QByteArray>
#include <QFile>
//#include "http_viewdata_center.h"
#include "map_engine_manage.h"

using namespace aurora::guide;


ExpandViewDialog::ExpandViewDialog(QWidget *parent)
: QDialog(parent)
{
	ui.setupUi(this);
	//this->setAttribute(Qt::WA_DeleteOnClose);
	this->setWindowFlags(Qt::FramelessWindowHint);
    //this->move(270, 412);
	ui.uIPicture->setBkColor(Qt::black);
	ui.uIPicture->setColor(Qt::white);

	//this->setAttribute(Qt::WA_ShowModal, true);
	//this->resize(ADDMENU_SIZE_WIDTH, ADDMENU_SIZE_HEIGHT);
	// Gpb 信息
//	connect(DataCenter::instance(), SIGNAL(rge_gp_msg(long, long)), this, SLOT(do_rge_gp_msg(long, long)), Qt::QueuedConnection);
//	connect(DataCenter::instance(), SIGNAL(showimage()), this, SLOT(do_showImage()), Qt::QueuedConnection);
//	connect(DataCenter::instance(), SIGNAL(hideimage()), this, SLOT(do_hideImage()), Qt::QueuedConnection);

    qRegisterMetaType<aurora::guide::JunctionViewInfoPtr>("aurora::guide::JunctionViewInfoPtr");
    connect(MapEngineManage::GetInstance(), &MapEngineManage::updateExpandView, this, &ExpandViewDialog::onUpdateExpandView, Qt::QueuedConnection);
}

ExpandViewDialog::~ExpandViewDialog()
{
	emit(winclose());
	// 	Nmc_Action_Map_GPPic_Hide();
	// 	FsmObject::instance()->set_virtualfigure_state(false);
}

void ExpandViewDialog::closepopmenu()
{
	this->close();
}

void ExpandViewDialog::onUpdateExpandView(aurora::guide::JunctionViewInfoPtr jv_ptr)
{

    const char* format = "PNG";
    if (jv_ptr) {

        qDebug() << "onUpdateExpandView jv_ptr->id= " << jv_ptr->id.c_str();

        if (m_id == jv_ptr->id)
            return;

        m_id = jv_ptr->id;

        QRect rect;
        uint32 pic_size = 0;
        uchar* data = nullptr;
        if (jv_ptr->back_view_data.size() > 0) {
//            QString image_name = "/home/<USER>/workspace/map_engine/samples/map_viewer/back_view_data.png";
//            saveImage(jv_ptr->back_view_data, image_name);
            pic_size = jv_ptr->back_view_data.size();
            data = reinterpret_cast<uchar*>(jv_ptr->back_view_data.data());
            rect = ui.uigpbctrl->setBaseImage(data, pic_size, format);
        }

        if (jv_ptr->front_view_data.size() > 0) {
//            QString image_name = "/home/<USER>/workspace/map_engine/samples/map_viewer/front_view_data.png";
//            saveImage(jv_ptr->front_view_data, image_name);
            pic_size = jv_ptr->front_view_data.size();
            data = reinterpret_cast<uchar*>(jv_ptr->front_view_data.data());
            ui.uigpbctrl->addImage(data, pic_size, format);
        }

        ui.uigpbctrl->update();
        this->resize(rect.width(), rect.height());
        this->show();
    
    } else {
        do_hideImage();
        m_id = "";
    }

}

bool ExpandViewDialog::saveImage(const std::vector<int8_t>& imageData, const QString& filePath)
{
    // 1. 将 int8_t 数据转换为 uchar
    QByteArray byteArray (reinterpret_cast<const char*>(imageData.data()), imageData.size());

    // 2. 使用 QImageReader 解析 PNG 数据
    QBuffer buffer(&byteArray);
    buffer.open(QIODevice::ReadOnly); // 必须显式打开
    QImageReader reader(&buffer, "PNG");
    QImage image = reader.read();

    if (image.isNull ()) {
    // 解析失败，输出错误信息
       qDebug() << "Failed to read image:" << reader.errorString();
       return false;
    }

    // 3. 保存图像为 PNG 文件
    return image.save(filePath, "PNG");
}

void ExpandViewDialog::DoMsgNewGp()
{
//	uint32				data_size = 0;
//	PST_RGE_EVENTPARA	gp_info = NULL;
//	USHORT				gp_mark = 0;
//	INT iRet = RgeEngGetCurGpInfo(&gp_info, &data_size);
//	if (iRet != SUCCESS) {
//		assert(false);
//		return;
//	}

//	if (gp_info->usGpType == RGE_GPTYPE_B)
//	{
//		PST_RGE_GPN		rge_gpn = NULL;
//		rge_gpn = RgeEngGetGpn(gp_info);
//		gp_mark = rge_gpn->mark_id;
//		// 更新gpn型引导点信息
//		UpdateProgressbar(gp_info->ulDistToGp, false);
//		UpdateDistToGp(gp_info->ulDistToGp);
//		UpdatePicture(rge_gpn);
//	}
//	else
//	{ // NEW GPN时可以关闭GPB的图片窗口
//		if (!isHidden())
//		{
//			closepopmenu();
//		}
//	}
}

void ExpandViewDialog::DoMsgUpdateGp()
{
//	uint32				data_size = 0;
//	PST_RGE_EVENTPARA	gp_info = NULL;
//	INT iRet = RgeEngGetCurGpInfo(&gp_info, &data_size);
//	if (iRet != SUCCESS) {
//		assert(false);
//		return;
//	}
//	if (gp_info->usGpType == RGE_GPTYPE_B) {
//		UpdateProgressbar(gp_info->ulDistToGp, false);
//		UpdateDistToGp(gp_info->ulDistToGp);
//		//printf("dist=%d\n", gp_info->ulDistToGp);
//	}
}

void ExpandViewDialog::do_rge_gp_msg(long event_type, long param)
{
//	if (event_type == RGEMSG_NEW_GP) {		// 新的引导点
//		DoMsgNewGp();
//	}
//	else if (event_type == RGEMSG_UPDATE_GP) {		// 更新引导点
//		DoMsgUpdateGp();
//	}
}

void ExpandViewDialog::UpdateProgressbar(ULONG dist_to_gp, bool is_freeway)
{
	if (is_freeway) {
		ui.uiprogressbar->setRange(0, 500);
		if (dist_to_gp > 500) {
			dist_to_gp = 500;
		}
	}
	else {
		ui.uiprogressbar->setRange(0, 300);
		if (dist_to_gp > 300) {
			dist_to_gp = 300;
		}
	}
	ui.uiprogressbar->setValue(dist_to_gp);
}

///显示自车到引导点距离
void ExpandViewDialog::UpdateDistToGp(ULONG dist_to_gp)
{
	//显示距离
	if (dist_to_gp < 1000)///小于1Km
	{
		ui.uIPicture->setText(QString("%1m").arg(dist_to_gp));
	}
	else if (dist_to_gp < 100000)///大于1Km且小于100Km,精确到小数点后1位
	{
		float fLastDist = float(dist_to_gp) / 1000;
		ui.uIPicture->setText(QString("%1km").arg(fLastDist, 0, 'f', 1));
	}
	else///大于100Km
	{
		ui.uIPicture->setText(QString("%1km").arg(dist_to_gp / 1000));
	}
}

///实景图 模式图
//void ExpandViewDialog::UpdatePicture(PST_RGE_GPN rge_gpn)
//{
//#if USE_NAVI_ENGINE
//	ENGPARSERHANDLE parser = ((CNaviEngine*)CNaviFactory::GetInstance())->GetParseHandle();
//#else
//	ENGPARSERHANDLE parser = DataCenter::instance()->m_parser;
//#endif
//	const char* format = "PNG";
//	uint32 col = (int32)(rge_gpn->gp_pos.CoordX_ / ROUTE_UNIT_LON / 100000);
//	uint32 row = (int32)(rge_gpn->gp_pos.CoordY_ / ROUTE_UNIT_LAT / 100000);
//	uint32 image_len = 0;
//	BYTE*  image_data = NULL;
//
//	for (int index = 0; index < rge_gpn->ulImageCnt; index++)
//	{
//		char* image_id = rge_gpb->ImageIDs[index];
//		
//		if (0 == index) {		// 背景图
//			if (HttpViewDataCenter::GetInstance()->isOnline()){
//
//			}
//			else
//			{
//				image_data = EngGetImageResByImageNumber(parser, col, row,
//				image_id, rge_gpb->ulImageType, RGE_IMG_BACKGROUND, image_len);
//			}			
//			if (image_data != NULL && image_len > 0) { // 可能存在找不到的图片（目前假定为资源文件与导航数据不一致时）
//				ui.uigpbctrl->setBaseImage(image_data, image_len, format);
//			}
//		}
//		else {
//			if (index != 1) {	// 箭头图
//				break;
//			}
//			image_len = 0;
//			if (HttpViewDataCenter::GetInstance()->isOnline()){
//			}
//			else{
//				image_data = EngGetImageResByImageNumber(parser, col, row,
//					image_id, rge_gpb->ulImageType, RGE_IMG_ARROW, image_len);
//			}
//
//			if (image_data != NULL && image_len > 0) {	// 可能存在找不到的图片（目前假定为资源文件与导航数据不一致时）
//				ui.uigpbctrl->addImage(image_data, image_len, format);
//			}
//		}
//		if (image_data != NULL) {
//			delete[] image_data;
//		}
//	}
//	ui.uigpbctrl->update();
//}

void ExpandViewDialog::do_showImage()
{
//	GuideImageFormat pic_format;
//	BYTE* pic_buf1;
//	BYTE* pic_buf2;
//	uint32 pic_size1;
//	uint32 pic_size2;
//	DataCenter::instance()->GetImageData(pic_format, &pic_buf1, &pic_buf2, pic_size1, pic_size2);
//	const char* format = "PNG";
//	QRect rect;
//	if (pic_buf1 != NULL && pic_size1 > 0)
//	{
//		rect = ui.uigpbctrl->setBaseImage(pic_buf1, pic_size1, format);
//	}
//	if (pic_buf2 != NULL && pic_size2 > 0)
//	{
//		ui.uigpbctrl->addImage(pic_buf2, pic_size2, format);
//	}
//	ui.uigpbctrl->update();
//	this->resize(rect.width(), rect.height());
//	this->show();
}

void ExpandViewDialog::do_hideImage()
{
	//closepopmenu();
	this->hide();
}
