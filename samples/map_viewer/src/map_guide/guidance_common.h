

#ifndef GUIDANCE_COMMON_H
#define GUIDANCE_COMMON_H

#include <QIcon>
#include "guidance/include/guidance_def.h"



namespace  guidanceCommon{

using namespace aurora::guide;

static QIcon getTbtInfoIconByType(const int &type)
{
    switch (static_cast<ManeuverType>(type)) {
    case ManeuverType::kTypeStart: //开始位置
    case ManeuverType::kTypeStartLeft:
    case ManeuverType::kTypeStartRight:
        return QIcon(":/MainWindow/resources/s.png");
    case ManeuverType::kTypeDestination: //目的地
    case ManeuverType::kTypeDestinationLeft:     // 目的地在路线左侧
    case ManeuverType::kTypeDestinationRight:    // 目的地在路线右侧
        return QIcon(":/MainWindow/resources/e.png");
    case ManeuverType::kTypeContinue: //直行
        return QIcon(":/MainWindow/resources/gpd_1.png");
    case ManeuverType::kTypeSlightRight: //右前方
        return QIcon(":/MainWindow/resources/gpd_7.png");
    case ManeuverType::kTypeRight: //右转
        return QIcon(":/MainWindow/resources/gpd_3.png");
    case ManeuverType::kTypeSharpRight: //右后
        return QIcon(":/MainWindow/resources/gpd_9.png");
    case ManeuverType::kTypeLeftUTurn: //左掉头
        return QIcon(":/MainWindow/resources/gpd_10.png");
    case ManeuverType::kTypeRightUTurn: //右掉头
        return QIcon(":/MainWindow/resources/gpd_11.png");
    case ManeuverType::kTypeSharpLeft: //左后
        return QIcon(":/MainWindow/resources/gpd_8.png");
    case ManeuverType::kTypeLeft: //左转
        return QIcon(":/MainWindow/resources/gpd_2.png");
    case ManeuverType::kTypeSlightLeft: //左前
        return QIcon(":/MainWindow/resources/gpd_6.png");
    case ManeuverType::kTypeStayStraight: //居中
        return QIcon(":/MainWindow/resources/gpd_13.png");
    case ManeuverType::kTypeStayRight: //靠右
        return QIcon(":/MainWindow/resources/gpd_5.png");
    case ManeuverType::kTypeStayLeft: //靠左
        return QIcon(":/MainWindow/resources/gpd_4.png");
    default:
        return QIcon(":/MainWindow/resources/lane_bg.png"); // 默认图标
    }

}

}



#endif // GUIDANCE_COMMON_H
