﻿
#include "turn_mark_info_dialog.h"
#include "map_widget_manage.h"
#include "map_engine_manage.h"
#include "guidance_common.h"

#define DISP_LANE_SIZE		308
#define UNDISP_LANE_SIZE	273
#define BASIC_HEIGHT			430
#define LANE_HEIGHT_DIFF		60
#define SAPA_HEIGHT_DIFF		80

using namespace aurora::guide;

QString Time_toString(uint32 seconds)
{
	uint32 minutes, hours;
	minutes = seconds / 60;
	seconds = seconds % 60;
	hours = minutes / 60;
	minutes = minutes % 60;
	QString timeStr = QString("%1:%2:%3").arg(hours, 2, 10, QLatin1Char('0')).arg(minutes, 2, 10, QLatin1Char('0')).arg(seconds, 2, 10, QLatin1Char('0'));
	return timeStr;
}
uint32 TurnMarkInfoDialog::CalcHeight()
{
	return BASIC_HEIGHT +
		(ui.widget_laneinfo->isVisible() ? LANE_HEIGHT_DIFF : 0) +
		(ui.widget_sapainfo->isVisible() ? SAPA_HEIGHT_DIFF : 0);
}



// 不显示车道信息
void TurnMarkInfoDialog::HideLaneInfoCtrl()
{
	ui.widget_laneinfo->setVisible(false); // 不显示车道信息
	//this->setFixedHeight(UNDISP_LANE_SIZE);
    this->setFixedHeight(CalcHeight());
}


TurnMarkInfoDialog::TurnMarkInfoDialog(QWidget *parent)
	: QDockWidget(parent)
{
	ui.setupUi(this);

    //setAttribute(Qt::WA_DeleteOnClose);
    setFloating(true);


//    ui.widget_turnmark->setVisible(false);
//    ui.widget_nextroad->setVisible(false);
//    ui.widget_distTogp->setVisible(false);
    ui.widget_airconditioner->setVisible(false);
    ui.widget_jamsmartvoice->setVisible(false);
//    ui.widget_laneinfo->setVisible(false);
    ui.widget_sapainfo->setVisible(false);
//    ui.widget_destinfo->setVisible(false);

//	ui.widget_turnmark->setBackgroundImageName("gpn_background.png");
//    ui.widget_nextroad->setImgName("gpn_dist.png");
//    ui.widget_distTogp->setImgName("gpn_dist.png");
    m_imgPath = util::GetResourceFolder();

	QFont font(QString::fromLocal8Bit("宋体"), 16, QFont::Bold, false);// 设置字体的类型，大小，加粗
	ui.widget_destinfo->setFont(font);
	ui.widget_destinfo->setLabelOneIconRect(QRect(0, 9, 34, 34));
	ui.widget_destinfo->setLabelOneTextRect(QRect(34, 0, 91, 50));
	ui.widget_destinfo->setLabelTwoIconRect(QRect(125, 9, 34, 34));
	ui.widget_destinfo->setLabelTwoTextRect(QRect(159, 0, 91, 50));
    ui.widget_destinfo->setLabelOneIconName("to_dest_dist.png");
    ui.widget_destinfo->setLabelTwoIconName("to_dest_time.png");

    //ui.widget_airconditioner->SetAcInfo(-1);
    //ui.widget_jamsmartvoice->SetSmartVoiceInfo(true);

    // Gpn 信息
//	connect(DataCenter::instance(), SIGNAL(rge_gp_msg(long, long)), this, SLOT(do_rge_gp_msg(long, long)), Qt::QueuedConnection);
//	connect(DataCenter::instance(), SIGNAL(update_lane_msg()), this, SLOT(do_update_lane()), Qt::QueuedConnection);
    connect(MapEngineManage::GetInstance()->m_guidance_control_ptr.get(), SIGNAL(update_guide_msg(QString)), this, SLOT(do_update_guide(QString)), Qt::QueuedConnection);
//	connect(DataCenter::instance(), SIGNAL(update_hwboards()), this, SLOT(do_update_hwboards()), Qt::QueuedConnection);
//	connect(DataCenter::instance(), SIGNAL(update_air_condition(int)), this, SLOT(do_update_aircondition(int)), Qt::QueuedConnection);
//	connect(DataCenter::instance(), SIGNAL(update_smart_voice()), this, SLOT(do_update_smart_voice()), Qt::QueuedConnection);

    qRegisterMetaType<aurora::guide::NavigationLaneInfoPtr>("aurora::guide::NavigationLaneInfoPtr");
    connect(MapEngineManage::GetInstance(), &MapEngineManage::updateLane, this, &TurnMarkInfoDialog::onUpdateLane, Qt::QueuedConnection);
    qRegisterMetaType<aurora::guide::NavigationInfoPtr>("aurora::guide::NavigationInfoPtr");
    connect(MapEngineManage::GetInstance(), &MapEngineManage::updateNavigationInfo, this, &TurnMarkInfoDialog::onUpdateNavigationInfo, Qt::QueuedConnection);

    QRect rec = ui.widget_laneinfo->geometry();
}

TurnMarkInfoDialog::~TurnMarkInfoDialog()
{

}

//// 导航面板信息
//struct NavigationInfo {
//    int64_t         path_id;            // 路线id
//    NavigationMode  mode;               // 导航模式
//    TimeAndDist     path_remain;        // 路线剩余距离&时间
//    std::vector<TimeAndDist> via_remain; // 途径地剩余距离&时间
//    // int32_t         remain_light_num;    // 红绿灯数数量
//    // int32_t         segment_index;
//    // int32_t         edge_idx;
//    // float           edge_offset;
//    std::string        road_name;

//    // int32_t         drive_time;     // 已经行驶的时间
//    // int32_t         drive_dist;     // 已经行驶的距离
//    // int32_t         city_code;      // 行政区划编码
//    // int32_t         maneuver_id;    // 转向id

//    std::vector<ManeuverPoint> next_guide_points;  // 临接机动点
//    // NavigationExitDirectionInfo *exit_direction_info; // 方向路牌信息
//};

// 到引导点剩余距离
void TurnMarkInfoDialog::UpdateDistToGp(uint32 dist_to_gp)
{
    qDebug() << "UpdateDistToGp dist_to_gp = " << dist_to_gp;

    //dist_to_gp = dist_to_gp / 100;
    //dist_to_gp = DataCenter::instance()->RealDistToRgeDist(dist_to_gp);
    if (dist_to_gp < 1000)///小于1Km
    {
        ui.widget_distTogp->setText(QString("%1m").arg(dist_to_gp));
    }
    else if (dist_to_gp < 100000)///大于1Km且小于100Km,精确到小数点后1位
    {
        float fLastDist = float(dist_to_gp) / 1000;
        ui.widget_distTogp->setText(QString("%1km").arg(fLastDist, 0, 'f', 1));
    }
    else///大于100Km
    {
        ui.widget_distTogp->setText(QString("%1km").arg(dist_to_gp / 1000));
    }
}

void TurnMarkInfoDialog::onUpdateNavigationInfo(aurora::guide::NavigationInfoPtr info)
{
    if (!info) {
        return;
    }

    if (info->next_guide_points.size() > 0){
        QIcon mark_image = guidanceCommon::getTbtInfoIconByType(static_cast<int>(info->next_guide_points.at(0).type));
        ui.widget_turnmark->setImage(mark_image);
        //ui.widget_turnmark->setVisible(true);

        qDebug() << "onUpdateNavigationInfo dist_to_gp = " << info->next_guide_points.at(0).distance;

        UpdateDistToGp(info->next_guide_points.at(0).distance);

    }

    QTextCodec *tc = QTextCodec::codecForName("UTF-8");
    if (info->road_name.size() == 0)
    {
        ui.widget_nextroad->setText(QString::fromLocal8Bit("无名道路"));
    } else {
        ui.widget_nextroad->setText(tc->toUnicode(QByteArray(info->road_name.c_str())));
    }

    UpdateDestInfo(info->path_remain.dis, info->path_remain.sec);


}

 void TurnMarkInfoDialog::onUpdateLane(aurora::guide::NavigationLaneInfoPtr info)
 {

     if (!info) {
         ui.widget_laneinfo->removeAll();
         ui.widget_laneinfo->setVisible(false);
         m_id = "";
         setFixedHeight(CalcHeight());

     } else {

         qDebug() << "onUpdateLane info->id= " << info->id.c_str();

         if (m_id == info->id) {
             return;
         }

         m_id = info->id;
         ui.widget_laneinfo->removeAll();
         ui.widget_laneinfo->setVisible(true);
         setFixedHeight(CalcHeight());
         QPixmap ArrowImg;

         for (size_t i = 0; i < info->back_lanes.size(); i++) {
             if (i >= info->front_lanes.size())
                 break;

             qDebug() << "onUpdateLane index = " << i << "back_lanes_type = " << (int)info->back_lanes.at(i) << "front_lanes_type = " << (int)info->front_lanes.at(i);

             int id = LaneTurnToArrow(info->back_lanes.at(i), info->front_lanes.at(i));
             if (id >= 0) {

                 char cArrowImgName[128];
                 sprintf(cArrowImgName, "%s%d.png", m_imgPath, id);
                 ArrowImg.load(cArrowImgName, "PNG");
                 ui.widget_laneinfo->insertLaneData(ArrowImg);

             }

         }

     }

 }


int TurnMarkInfoDialog::LaneTurnToArrow(LaneAction back_type, LaneAction front_type )
 {
    int id = -1;

    if (back_type == LaneAction::kLaneActionAhead) { // 直行
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 2;
        } else if (front_type == LaneAction::kLaneActionAhead) {
            id = 1;
        }
    } else if (back_type == LaneAction::kLaneActionLeft) { // 左转
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 4;
        } else if (front_type == LaneAction::kLaneActionLeft) {
            id = 3;
        }
    } else if (back_type == LaneAction::kLaneActionAheadLeft) { // 直行+左转
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 31;
        } else if (front_type == LaneAction::kLaneActionLeft) {
            id = 29;
        } else if (front_type == LaneAction::kLaneActionAhead) {
            id = 30;
        }
    } else if (back_type == LaneAction::kLaneActionRight) { // 右转
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 6;
        } else if (front_type == LaneAction::kLaneActionRight) {
            id = 5;
        }
    } else if (back_type == LaneAction::kLaneActionAheadRight) { // 直行+右转
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 37;
        } else if (front_type == LaneAction::kLaneActionRight) {
            id = 36;
        } else if (front_type == LaneAction::kLaneActionAhead) {
            id = 35;
        }
    } else if (back_type == LaneAction::kLaneActionLUTurn) { // 左掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 8;
        } else if (front_type == LaneAction::kLaneActionLUTurn) {
            id = 7;
        }
    } else if (back_type == LaneAction::kLaneActionLeftRight) { // 左转+右转
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 34;
        } else if (front_type == LaneAction::kLaneActionLeft) {
            id = 32;
        } else if (front_type == LaneAction::kLaneActionRight) {
            id = 33;
        }
    } else if (back_type == LaneAction::kLaneActionAheadLeftRight) { // 直行+左转+右转
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 65;
        } else if (front_type == LaneAction::kLaneActionLeft) {
            id = 62;
        } else if (front_type == LaneAction::kLaneActionRight) {
            id = 64;
        } else if (front_type == LaneAction::kLaneActionAhead) {
            id = 63;
        }
    } else if (back_type == LaneAction::kLaneActionRUTurn) { // 右掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 10;
        } else if (front_type == LaneAction::kLaneActionRUTurn) {
            id = 9;
        }
    } else if (back_type == LaneAction::kLaneActionAheadLUTurn) { // 直行+左掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 16;
        } else if (front_type == LaneAction::kLaneActionLUTurn) {
            id = 14;
        } else if (front_type == LaneAction::kLaneActionAhead) {
            id = 15;
        }
    } else if (back_type == LaneAction::kLaneActionAheadRUTurn) { // 直行+右掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 25;
        } else if (front_type == LaneAction::kLaneActionRUTurn) {
            id = 23;
        } else if (front_type == LaneAction::kLaneActionAhead) {
            id = 24;
        }
    } else if (back_type == LaneAction::kLaneActionLeftLUTurn) { // 左转+左掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 13;
        } else if (front_type == LaneAction::kLaneActionLUTurn) {
            id = 11;
        } else if (front_type == LaneAction::kLaneActionLeft) {
            id = 12;
        }
    } else if (back_type == LaneAction::kLaneActionRightRUTurn) { // 右转+右掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 28;
        } else if (front_type == LaneAction::kLaneActionRUTurn) {
            id = 26;
        } else if (front_type == LaneAction::kLaneActionRight) {
            id = 27;
        }
    } else if (back_type == LaneAction::kLaneActionAheadLeftLUTurn) { // 直行+左转+左掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 41;
        } else if (front_type == LaneAction::kLaneActionLeft) {
            id = 39;
        } else if (front_type == LaneAction::kLaneActionLUTurn) {
            id = 38;
        } else if (front_type == LaneAction::kLaneActionAhead) {
            id = 40;
        }
    } else if (back_type == LaneAction::kLaneActionRightLUTurn) { // 右转+左掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 28;
        } else if (front_type == LaneAction::kLaneActionRight) {
            id = 27;
        } else if (front_type == LaneAction::kLaneActionRUTurn) {
            id = 26;
        }
    } else if (back_type == LaneAction::kLaneActionLeftRightLUTurn) { // 左转+右转+左掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 45;
        } else if (front_type == LaneAction::kLaneActionLeft) {
            id = 43;
        } else if (front_type == LaneAction::kLaneActionLUTurn) {
            id = 42;
        } else if (front_type == LaneAction::kLaneActionRight) {
            id = 44;
        }
    } else if (back_type == LaneAction::kLaneActionAheadRightLUTurn) { // 直行+右转+左掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 49;
        } else if (front_type == LaneAction::kLaneActionAhead) {
            id = 47;
        } else if (front_type == LaneAction::kLaneActionLUTurn) {
            id = 46;
        } else if (front_type == LaneAction::kLaneActionRight) {
            id = 48;
        }
    } else if (back_type == LaneAction::kLaneActionLeftRUTurn) { // 左转+右掉头
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 22;
        } else if (front_type == LaneAction::kLaneActionLeft) {
            id = 21;
        } else if (front_type == LaneAction::kLaneActionRUTurn) {
            id = 23;
        }
    } else if (back_type == LaneAction::kLaneActionBus) { // 公交车道
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 77;
        } else if (front_type == LaneAction::kLaneActionBus) {
            id = 76;
        }
    } else if (back_type == LaneAction::kLaneActionVariable) { // 可变车道
        if (front_type == LaneAction::kLaneActionNULL) {
            id = 83;
        } else if (front_type == LaneAction::kLaneActionVariable) {
            id = 82;
        }
    }


    return id;
 }

// 模拟引导时，到达目的地
void TurnMarkInfoDialog::DoMsgSimArriveDest()
{
//	if (((CNaviEngine*)CNaviFactory::GetInstance())->IsSimuRunning())
//	{
//		if (SUCCESS != ((CNaviEngine*)CNaviFactory::GetInstance())->StopSimu())
//		{
//			assert(false);
//		}

//		ui.widget_airconditioner->SetAcInfo(-1);
//		ui.widget_jamsmartvoice->SetSmartVoiceInfo(true);
//		DataCenter::instance()->m_routingCenter->removeAllDest();
//		AppGetMainWindow()->updateNavigationToolbarUi(ToStop);
//		close();
//	}
//	else {
//		assert(false);
//	}
}


void TurnMarkInfoDialog::do_rge_gp_msg(long event_type, long param)
{
//	if (event_type == RGEMSG_SIM_ARRIVE_DEST)
//	{// 模拟引导时，到达目的地
//		DoMsgSimArriveDest();
//	}
//	else if (event_type == RGEMSG_DRIVE_ARRIVE_DEST)
//	{ // 到达目的地
//		if (((CNaviEngine*)CNaviFactory::GetInstance())->IsDriveRunning())
//		{
//			if (SUCCESS != ((CNaviEngine*)CNaviFactory::GetInstance())->StopDrive())
//			{
//				assert(false);
//			}

//			DataCenter::instance()->m_routingCenter->removeAllDest();
//			AppGetMainWindow()->updateNavigationToolbarUi(ToStop);
//			close();
//		}
//		else {
//			assert(false);
//		}
//	}
}

USHORT TurnMarkInfoDialog::GetViaPntMark(USHORT gp_mark)
{   // 获取当前通过的VIA个数
//	ULONG ulVia = RgeEngGetPassMidPntCnt();
//	USHORT via_pnt_mark;
//	if (gp_mark == RGE_GPMARK_VIA) {
//		switch (ulVia) {
//		case 0:
//			via_pnt_mark = RGE_GPMARK_VIA;
//			break;
//		case 1:
//			via_pnt_mark = RGE_GPMARK_VIA_2;
//			break;
//		case 2:
//			via_pnt_mark = RGE_GPMARK_VIA_3;
//			break;
//		case 3:
//			via_pnt_mark = RGE_GPMARK_VIA_4;
//			break;
//		case 4:
//			via_pnt_mark = RGE_GPMARK_VIA_5;
//			break;
//		}
//		return via_pnt_mark;
//	}
//	else {
//		return gp_mark;
//	}
}



//更新自车到目的地 时间 距离/途径地的时间 距离
void TurnMarkInfoDialog::UpdateDestInfo(ULONG ulDist, ULONG ultime)
{
    //ulDist = ulDist / 100;
    //ultime = ultime / 1000;
    //ulDist = DataCenter::instance()->RealDistToRgeDist(ulDist);
	// 距离为零时不更新
	if (ulDist == 0) return;

	//显示距离
	if (ulDist < 50)///小于50m
	{
		ui.widget_destinfo->setLabelOneText(QString("<50m"));
	}
	else if (ulDist < 1000)///小于1Km
	{
		ui.widget_destinfo->setLabelOneText(QString("%1m").arg(ulDist));
	}
	else if (ulDist < 100000)///大于1Km且小于100Km,精确到小数点后1位
	{
		float fLastDist = float(ulDist) / 1000;
		ui.widget_destinfo->setLabelOneText(QString("%1km").arg(fLastDist, 0, 'f', 1));
	}
	else///大于100Km
	{
		ui.widget_destinfo->setLabelOneText(QString("%1km").arg(ulDist / 1000));
	}

	//不满一分钟显示1分钟
	ULONG ihour, iMinute;
	if (ultime < 60) 
	{
		ihour = 0;
		iMinute = 1;
	}
	else 
	{
		ihour = ultime / 3600;
		iMinute = (ultime - ihour * 3600) / 60;
	}

	ui.widget_destinfo->setLabelTwoText(QString("%1h:%2m").arg(ihour).arg(iMinute));
}

// 更新前方路名
void TurnMarkInfoDialog::UpdateNextName(int status, const char* next_road_name)
{
	QTextCodec *tc = QTextCodec::codecForName("UTF-8");
	if (status == 0) 
	{
		ui.widget_nextroad->setText("off route");
	}
	else 
	{
		if (*next_road_name == '\0') 
		{
			ui.widget_nextroad->setText(QString::fromLocal8Bit("无名道路"));
		}
		else
		{
			ui.widget_nextroad->setText(tc->toUnicode(QByteArray(next_road_name)));
		}
	}
}

// 更新车道信息
void TurnMarkInfoDialog::UpdateLaneInfo()
{
//	char	cMaskImgName[RGE_STR_LEN];
//	char	cBackGroundImgName[RGE_STR_LEN];
//	char	cArrowImgName[RGE_STR_LEN];
	QPixmap ArrowImg, MaskImg, BackGroundImg;

//	GuideLaneInfo lane_info;
//	DataCenter::instance()->GetLaneInfo(lane_info);
//	if (lane_info.lane_cnt <= 0)
//	{
//		ui.widget_laneinfo->removeAll();
//		HideLaneInfoCtrl();
//		return;
//	}

//	ui.widget_laneinfo->setVisible(true);
//	setFixedHeight(CalcHeight());
//	ui.widget_laneinfo->removeAll();
//	//assert(lane_info->ulDispFlg == 1);

//	// 显示各个车道
//	for (BYTE ulIdx = 0; ulIdx < lane_info.lane_cnt; ulIdx++)
//	{
//		sprintf(cArrowImgName, "%s%d.png", m_imgPath, lane_info.lane_list[ulIdx].lane_arrow_type);
//		ArrowImg.load(cArrowImgName, "PNG");

//		ui.widget_laneinfo->insertLaneData(ArrowImg);

		//0:无定义 1 : 可通行、2 : 不能通行、3 : 推荐(肯定为通行)
// 		if (lane_info.lane_list[ulIdx].lane_rcmd_type == 1) {
// 			sprintf(cMaskImgName, "%slane_bg.png", m_imgPath);
// 		}
// 		else if (lane_info.lane_list[ulIdx].lane_rcmd_type == 2) 
// 		{
// 			sprintf(cMaskImgName, "%slane_m.png", m_imgPath);
// 		}
// 		else if (lane_info.lane_list[ulIdx].lane_rcmd_type == 3) 
// 		{
// 			sprintf(cMaskImgName, "%slane_g.png", m_imgPath);
// 		}
// 		else
// 		{
// 			cMaskImgName[0] = '\0';
// 		}
		// 黑色背景图名称
		/*sprintf(cBackGroundImgName, "%slane_m.png", m_imgPath);*/
		// 背景图
		/*BackGroundImg.load(cBackGroundImgName, "PNG");*/
		// 前景图
		/*MaskImg.load(cMaskImgName, "PNG");*/

		// 方向箭头图
// 		if (lane_info.lane_list[ulIdx].lane_type) 
// 		{
			//公交车道
// 			sprintf(cArrowImgName, "%slane_bus.png", m_imgPath);
// 			ArrowImg.load(cArrowImgName, "PNG");
// 		}
// 		else 
// 		{
// 			int arrowNo = LaneTurnToArrow(lane_info.lane_list[ulIdx].lane_arrow_type);
// 			sprintf(cArrowImgName, "%slane_%d.png", m_imgPath, arrowNo);
// 			ArrowImg.load(cArrowImgName, "PNG");
// 		}
// 		ui.widget_laneinfo->insertLaneData(ArrowImg, MaskImg, BackGroundImg);
//	}
}

enum LaneInfoArrowType
{
	RGE_LANEINFO_ARROWTYPE_NONE = 0,      // 无定义
	RGE_LANEINFO_ARROWTYPE_STRAIHT = 1,      // 直行
	RGE_LANEINFO_ARROWTYPE_LEFT = 2,      // 左转
	RGE_LANEINFO_ARROWTYPE_RIGHT = 3,      // 右转
	RGE_LANEINFO_ARROWTYPE_LUTURN = 4,      // 左掉头
	RGE_LANEINFO_ARROWTYPE_RUTURN = 5,      // 右掉头

	RGE_LANEINFO_ARROWTYPE_STRAIHT_LEFT = 6,  // 直行左转
	RGE_LANEINFO_ARROWTYPE_STRAIHT_RIGHT = 7,  // 直行右转
	RGE_LANEINFO_ARROWTYPE_LEFTLUTURN = 8,    // 左转左掉头
	RGE_LANEINFO_ARROWTYPE_RIGHTLUTURN = 9,    // 右转左掉头

	RGE_LANEINFO_ARROWTYPE_LEFTRUTURN = 10,        // 左转右掉头
	RGE_LANEINFO_ARROWTYPE_RIGHTRUTURN = 11,      // 右转右掉头
	RGE_LANEINFO_ARROWTYPE_LEFTRIGHT = 12,        // 左转右转
	RGE_LANEINFO_ARROWTYPE_STRAIHTLUTURN = 13,      // 直行左掉头

	RGE_LANEINFO_ARROWTYPE_STRAIHTRUTURN = 14,      // 直行右掉头
	RGE_LANEINFO_ARROWTYPE_STRAIHT_LEFT_LUTURN = 15,  // 直行左转左掉头
	RGE_LANEINFO_ARROWTYPE_STRAIHT_RIGHT_LUTURN = 16,  // 直行右转左掉头

	RGE_LANEINFO_ARROWTYPE_STRAIT_LEFT_RUTURN = 17,    // 直行左转右掉头
	RGE_LANEINFO_ARROWTYPE_STRAIT_RIGHT_RUTURN = 18,  // 直行右转右掉头
	RGE_LANEINFO_ARROWTYPE_STRAIT_LEFT_RIGHT = 19,    // 直行左转右转

	RGE_LANEINFO_ARROWTYPE_LEFT_LUTURN_RIGHT = 20,      // 左转左掉头右转
	RGE_LANEINFO_ARROWTYPE_RIGHT_RUTURN_LEFT = 21,      // 右转右掉头左转
	RGE_LANEINFO_ARROWTYPE_STRAIHT_LEFT_LUTURN_RIGHT = 22,  // 直行左转左掉头右转
	RGE_LANEINFO_ARROWTYPE_STRAIHT_RIGHT_RUTURN_LEFT = 23  // 直行右转右掉头左转
};

//unsigned char TurnMarkDialog::LaneTurnToArrow(uint turn_type)
//{
//	if (turn_type & LANE_DESC_STRAIGHT && turn_type & LANE_DESC_LEFT
//		&& turn_type & LANE_DESC_UTURN &&  turn_type & LANE_DESC_RIGHT) {
//		// 直行左转左掉头右转
//		return RGE_LANEINFO_ARROWTYPE_STRAIHT_LEFT_LUTURN_RIGHT;
//	}
//	else if (turn_type & LANE_DESC_LEFT && turn_type & LANE_DESC_UTURN && turn_type & LANE_DESC_RIGHT)
//	{
//		// 左转左掉头右转
//		return RGE_LANEINFO_ARROWTYPE_LEFT_LUTURN_RIGHT;
//	}
//	else if (turn_type & LANE_DESC_STRAIGHT && turn_type &  LANE_DESC_LEFT && turn_type & LANE_DESC_RIGHT) 
//	{
//		// 直行左转右转
//		return RGE_LANEINFO_ARROWTYPE_STRAIT_LEFT_RIGHT;
//	}
//	else if (turn_type & LANE_DESC_STRAIGHT && turn_type &  LANE_DESC_RIGHT && turn_type & LANE_DESC_UTURN) 
//	{
//		// 直行右转左掉头
//		return RGE_LANEINFO_ARROWTYPE_STRAIHT_RIGHT_LUTURN;
//	}
//	else if (turn_type & LANE_DESC_STRAIGHT && turn_type &  LANE_DESC_LEFT && turn_type & LANE_DESC_UTURN) 
//	{
//		// 直行左转左掉头
//		return RGE_LANEINFO_ARROWTYPE_STRAIHT_LEFT_LUTURN;
//	}
//	else if (turn_type & LANE_DESC_STRAIGHT && turn_type & LANE_DESC_RIGHT)
//	{
//		// 直行右转
//		return RGE_LANEINFO_ARROWTYPE_STRAIHT_RIGHT;
//	}
//	else if (turn_type & LANE_DESC_STRAIGHT && turn_type &  LANE_DESC_UTURN)
//	{
//		// 直行左掉头
//		return RGE_LANEINFO_ARROWTYPE_RIGHTLUTURN;
//	}
//	else if (turn_type & LANE_DESC_LEFT && turn_type &  LANE_DESC_RIGHT)
//	{
//		// 左转右转
//		return RGE_LANEINFO_ARROWTYPE_LEFTRIGHT;
//	}
//	else if (turn_type & LANE_DESC_RIGHT && turn_type &  LANE_DESC_UTURN)
//	{
//		// 右转左掉头
//		return RGE_LANEINFO_ARROWTYPE_RIGHTLUTURN;
//	}
//	else if (turn_type & LANE_DESC_LEFT && turn_type &  LANE_DESC_UTURN)
//	{
//		// 左转左掉头
//		return RGE_LANEINFO_ARROWTYPE_LEFTLUTURN;
//	}
//	//else if (turn_type & LANE_DESC_STRAIGHT && turn_type & LANE_DESC_RIGHT)
//	//{
//	//	// 直行右转
//	//	return RGE_LANEINFO_ARROWTYPE_STRAIHT_RIGHT;
//	//}
//	else if (turn_type & LANE_DESC_STRAIGHT && turn_type &  LANE_DESC_LEFT)
//	{
//		// 直行左转
//		return RGE_LANEINFO_ARROWTYPE_STRAIHT_LEFT;
//	}
//	else if (turn_type & LANE_DESC_UTURN)
//	{
//		// 左掉头
//		return RGE_LANEINFO_ARROWTYPE_LUTURN;
//	}
//	else if (turn_type & LANE_DESC_RIGHT)
//	{
//		// 右转
//		return RGE_LANEINFO_ARROWTYPE_RIGHT;
//	}
//	else if (turn_type & LANE_DESC_LEFT)
//	{
//		// 左转
//		return RGE_LANEINFO_ARROWTYPE_LEFT;
//	}
//	else if (turn_type & LANE_DESC_STRAIGHT)
//	{
//		// 直行
//		return RGE_LANEINFO_ARROWTYPE_STRAIHT;
//	}
//	else 
//	{
//		return RGE_LANEINFO_ARROWTYPE_NONE;
//	}
//}

void TurnMarkInfoDialog::do_update_lane()
{
	UpdateLaneInfo();
}

void TurnMarkInfoDialog::do_update_guide(QString text)
{
    ui.GuidancetextBrowser->setText(text);

//	GuideNaviInfo guide_info;
//	DataCenter::instance()->GetGuideInfo(guide_info);
//	// 更新gpn型引导点信息
//	if(guide_info.gp_mark == RGE_GPMARK_NON)
//	{
//		ui.widget_turnmark->setVisible(true);
//		ui.widget_nextroad->setVisible(true);
//	}
//	else
//	{
//		int mark_no = GetViaPntMark(guide_info.gp_mark);
//		ui.widget_turnmark->setImageID(mark_no);
//		ui.widget_turnmark->setVisible(true);
//	}
//	UpdateDistToGp(guide_info.dist_to_gp);

//	// 目的地信息
//	UpdateDestInfo(guide_info.dist_to_dest, guide_info.time_to_dest);
	
//	// 道路名
//	UpdateNextName(guide_info.on_route, guide_info.next_road_name);

//	// 更新TMC bar
//	UpdateTmcBar(guide_info.dist_to_dest);
}
void TurnMarkInfoDialog::do_update_hwboards()
{
//	HwGp* boards = NULL;
//	int size = 0;
//	DataCenter::instance()->GetHighWayBoard(&boards, size);
//	ui.widget_sapainfo->SetSapaInfo(boards, size);
//	ui.widget_sapainfo->move(0, BASIC_HEIGHT + (ui.widget_laneinfo->isVisible()? LANE_HEIGHT_DIFF: 0));
//	setFixedHeight(CalcHeight());
}
void TurnMarkInfoDialog::HideSapa()
{
//	ui.widget_sapainfo->setVisible(false);
//	setFixedHeight(CalcHeight());
}
void TurnMarkInfoDialog::show()
{
	ui.widget_turnmark->setImageID(0);
//	QWidget::show();
//	HideSapa();
}

void TurnMarkInfoDialog::do_update_aircondition(int in_status)
{
	ui.widget_airconditioner->SetAcInfo(in_status);
}

void TurnMarkInfoDialog::do_update_smart_voice()
{
	ui.widget_jamsmartvoice->SetSmartVoiceInfo(false);
}

void TurnMarkInfoDialog::UpdateTmcBar(uint32 dist)
{
//	SNaviResult* result_set = ((CNaviEngine*)CNaviFactory::GetInstance())->GetRouteResultSetByRouteID(-1);
//	if (NULL == result_set)
//		return;

//	uint32 total_dist = result_set[0].route_head.dist;
//	uint32 start_pos = total_dist - dist / 100;
//	int8* tmc_status = DataCenter::instance()->GetTMCStatus();
//	if (tmc_status)
//	{
//		uint32 len_rate = ((float32)(start_pos * TMC_BAR_LEN) / total_dist + 0.5);
//		if (len_rate > 0)
//		{
//			len_rate = COM_MIN(len_rate, TMC_BAR_LEN);
//			ui.widget_nextroad->setTmcFlag(true);
//			memset(tmc_status, -1, len_rate);
//		}
//	}
}
