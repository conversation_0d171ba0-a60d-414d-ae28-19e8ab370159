
#include "location_layer.h"
#include <QPen>
#include <QPainter>
#include <iostream>
#include <Qt>
#include <QTimer>
#include "route_data/route_data_def.h"
#include "route_data/route_tile_reader.h"
#include "util_funtion_tools.h"
#include "render_engine_layer.h"



LocationLayer::LocationLayer(MapWidget *parent)
 : m_map_widget_ptr(parent)
 , m_car_flag(":/MainWindow/resources/icn_car.png")

{
    m_is_move = false;
    m_is_show_car_flag = false;
    m_is_update_center_pos = false;
    m_car_dir = 0.0;
    m_last_match_path_index = 0;

    m_refreshTimer_ptr = std::make_shared<QTimer>(this);
    connect(m_refreshTimer_ptr.get(), &QTimer::timeout, this, &LocationLayer::onRefreshCenterPosition);

}

LocationLayer::~LocationLayer()
{

}

QPen LocationLayer::getPenByLevel(int level)
{
    QPen pen = QPen(Qt::black, 2);

    switch (level)
    {
    case 0: //Light color
    {
        QColor color("#808080");
        pen.setColor(color);
        pen.setWidth(8);
    }
        break;
    case 1: //Dark color
    {
        QColor color("#2385FF");
        pen.setColor(color);
        pen.setWidth(8);
    }
        break;
    case 2: //draw original point
    {
        QColor color("#7FFF00");
        pen.setColor(color);
        pen.setWidth(3);
    }
        break;
    case 3: //draw match point
    {
        QColor color("#FF0000");
        pen.setColor(color);
        pen.setWidth(3);
    }
        break;
    default:
        break;
    }

    return pen;
}

void LocationLayer::paint(QPainter& painter, MapCamera& camera)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    //draw before driving path
    if (m_path_latlon_set.size() > 0) {
        painter.setPen(getPenByLevel(1));


        std::vector<std::vector<QPointF>> result;
        GetDisplayPolyline(camera, m_path_latlon_set, result);

        for (size_t i = 0; i < result.size(); i++) {
            QPainterPath path;
            for (size_t j = 0; j < result.at(i).size(); j++)
            {
                auto &screenPos = result.at(i).at(j);
                if (j == 0) {
                   path.moveTo(screenPos);
                } else {
                   path.lineTo(screenPos);
                }
            }
            painter.drawPath(path);

        }
    }


    //draw after driving path
    if (m_gray_offset_latlon_set.size() > 0) {
        painter.setPen(getPenByLevel(0));
        QPainterPath path;
        for (size_t j = 0; j < m_gray_offset_latlon_set.size(); j++)
        {
            QPointF screenPos;
            camera.worldToScreen(&m_gray_offset_latlon_set.at(j), screenPos);
            //std::cout << "LocationLayer index: " << j << ",lng: " << screenPos.x() << ". lat: " << screenPos.y() << std::endl;
            if (j == 0) {
               path.moveTo(screenPos);
            } else {
               path.lineTo(screenPos);
            }
        }
        painter.drawPath(path);
    }


    //draw original location pos
    if (m_original_latlon_set.size() > 0) {
       painter.setPen(getPenByLevel(2));
       for (auto item : m_original_latlon_set) {

           QPointF screenPos;
           camera.worldToScreen(&item, screenPos);
           painter.drawPoint(screenPos);

       }
    }

    //draw match location pos
    if (m_match_latlon_set.size() > 0) {
       painter.setPen(getPenByLevel(3));
       for (auto item : m_match_latlon_set) {

           QPointF screenPos;
           camera.worldToScreen(&item, screenPos);
           painter.drawPoint(screenPos);

       }
    }



    //draw car
    if (m_is_show_car_flag) {
        drawCarIcon( painter, camera);
    }
}

void LocationLayer::onMouseDown(QMouseEvent* event, MapCamera& camera)
{
    m_is_update_center_pos = false;

}

void LocationLayer::onMouseUp(QMouseEvent* event, MapCamera& camera)
{
    if (m_is_move)
    {
        if (m_refreshTimer_ptr->isActive()) {
            m_refreshTimer_ptr->stop();
        }
        m_refreshTimer_ptr->start(3000);

        m_is_move = false;
        m_is_update_center_pos = false;
    }

    if (!m_refreshTimer_ptr->isActive())
        m_is_update_center_pos = true;

}

void LocationLayer::onStoprefreshMapCenter()
{
    if (m_refreshTimer_ptr->isActive()) {
        m_refreshTimer_ptr->stop();
    }
    m_refreshTimer_ptr->start(3000);

    m_is_update_center_pos = false;
}

void LocationLayer::onMouseMove(QMouseEvent* event, MapCamera& camera)
{
    m_is_move = true;
}

void LocationLayer::onRefreshCenterPosition()
{
   m_is_update_center_pos = true;
}

// 二分查找找到投影点前后点的索引
std::pair<size_t, size_t> LocationLayer::findProjectionSegment(const std::vector<map_engine::RGeoPoint>& curve, const map_engine::RGeoPoint& projection)
{
    if (curve.size() < 2) return {0, 0};

    QPointF prj_screenPos;
    m_camera->worldToScreen(&projection, prj_screenPos);

    size_t left = 0;
    size_t right = curve.size() - 2;  // 最后一个线段是curve[size-2]到curve[size-1]
    size_t mid;

    while (left < right) {
        mid = left + (right - left) / 2;

        QPointF mid_screenPos;
        m_camera->worldToScreen(&curve[mid], mid_screenPos);
        QPointF mid_r_screenPos;
        m_camera->worldToScreen(&curve[mid + 1], mid_r_screenPos);
        QPointF mid_rr_screenPos;
        m_camera->worldToScreen(&curve[mid + 2], mid_rr_screenPos);
        // 计算当前线段和下一个线段的距离
        double dist_current = util::distanceToSegment(prj_screenPos, mid_screenPos, mid_r_screenPos);
        double dist_next = util::distanceToSegment(prj_screenPos, mid_r_screenPos, mid_rr_screenPos);

        if (dist_current < dist_next) {
            right = mid;
        } else {
            left = mid + 1;
        }
    }

    return {left, left + 1};
}

void LocationLayer::onGetLoctionMatchResult(aurora::loc::MatchResult result)
{   
   std::lock_guard<std::mutex> lock(m_mutex);

    map_engine::RGeoPoint original_pos(result.origin_pos.lnglat.lng(), result.origin_pos.lnglat.lat());
    m_original_latlon_set.push_back(original_pos);

    m_car_point.CoordX_ = result.road_match_info.proj_pos.lng();
    m_car_point.CoordY_ = result.road_match_info.proj_pos.lat();
    m_match_latlon_set.push_back(m_car_point);

    m_car_dir = result.car_pos_info.heading;

    if (m_is_update_center_pos) {

       m_map_widget_ptr->getRenderEngineLayer()->setMapRotation(m_car_dir);
       m_map_widget_ptr->setWorldCenter(&m_car_point, false);
    }

//    if (m_path_info_ptr && !result.reroute) {

//        int32_t cur_index = result.road_match_info.path_link_idx;
//        if (m_last_match_path_index == cur_index) {
//            double offset = result.road_match_info.offset;
            
//            if (offset > 0.0) {
//                double total_dis = 0.0;

//                uint64_t tile_id = m_path_info_ptr->links.at(cur_index)->tile_id;
//                uint32_t id = m_path_info_ptr->links.at(cur_index)->id;
//                aurora::parser::RouteTilePackagePtr tile_ptr = MapEngineManage::GetInstance()->GetDataProvider().GetRouteTileByID(tile_id);
//                aurora::parser::RouteTileReader reader;
//                reader.SetTarget(tile_ptr);
//                aurora::parser::AugmentEdgeSet& edges = reader.GetAugmentEdges();

//                std::vector<aurora::parser::LngLat> edge_LngLatSet = edges.at(id).GetGeoPoints();
//                std::vector<aurora::PointLL> geo_pos_set = {edge_LngLatSet.begin(), edge_LngLatSet.end()};
//                if (result.road_match_info.dir == 1) {
//                    std::reverse(geo_pos_set.begin(),geo_pos_set.end());
//                }

//                int geo_count = geo_pos_set.size();
//                for (int i = 0; i < (geo_count -1); i++) {
//                    auto &item = geo_pos_set.at(i);

//                    auto &next_pos = geo_pos_set.at(i + 1);
//                    double dis = item.Distance(next_pos);
//                    total_dis += dis;

//                    if (total_dis > offset) {
//                        double remaining_dis = total_dis - offset;
//                        double percent = 1.0 - (remaining_dis/dis);
//                        aurora::GeoPoint offset_pos = item.PointAlongSegment(next_pos, percent);
//                        map_engine::RGeoPoint interpolation_pos(offset_pos.lng(), offset_pos.lat());
//                        m_gray_offset_latlon_set.push_back(interpolation_pos);
//                        break;

//                    } else {
//                        if (m_gray_path_latlon_set.size() > 0) {
//                            map_engine::RGeoPoint pos(item.x(), item.y());
//                            m_gray_offset_latlon_set.push_back(pos);
//                        }
//                    }
//                }
//            }
//        } else {
//            if (m_last_match_path_index == 0 && m_gray_path_latlon_set.size() == 0) {
//                m_gray_path_latlon_set = m_gray_offset_latlon_set;
//                m_gray_offset_latlon_set.clear();

//            } else {
//                for (int32_t i = m_last_match_path_index; i < result.road_match_info.path_link_idx; i++) {
//                    uint64_t tile_id = m_path_info_ptr->links.at(i)->tile_id;
//                    uint32_t id = m_path_info_ptr->links.at(i)->id;
//                    aurora::parser::RouteTilePackagePtr tile_ptr = MapEngineManage::GetInstance()->GetDataProvider().GetRouteTileByID(tile_id);
//                    aurora::parser::RouteTileReader reader;
//                    reader.SetTarget(tile_ptr);
//                    aurora::parser::AugmentEdgeSet& edges = reader.GetAugmentEdges();
//                    auto &link = edges.at(id);

//                    for (auto &item : link.GetGeoPoints()) {
//                        map_engine::RGeoPoint pos(item.x(), item.y());
//                        m_gray_path_latlon_set.push_back(pos);
//                        std::cout << "LocationLayer item: "  << ",lng: " << pos.CoordX_ << ". lat: " << pos.CoordY_ << std::endl;
//                    }
//                }
//           }

//            m_gray_offset_latlon_set.clear();
//            m_last_match_path_index = result.road_match_info.path_link_idx;
//        }
//    }

    emit needRedraw();
}



void LocationLayer::drawCarIcon(QPainter& painter, MapCamera& camera)
{
    QPointF screenPos;

//	if (m_pt_cnt <= 0 || Point_isInvalid(&m_ccpPt[m_pt_cnt - 1]))
//		return;

    camera.worldToScreen(&m_car_point, screenPos);
    QRect rect(screenPos.x() - m_car_flag.width() / 2, screenPos.y() - m_car_flag.height() / 2, m_car_flag.width(), m_car_flag.height());
    QRect rotatedRect(-rect.width() / 2, -rect.height() / 2, rect.width(), rect.height());
    int32 cx = rect.x() + rect.width() / 2;
    int32 cy = rect.y() + rect.height() / 2;
    painter.save();
    painter.translate(cx, cy);

    double draw_angle = m_car_dir;
    auto rotation_angle = m_map_widget_ptr->getLastRotationValue();
    if (rotation_angle != 0.0) {
        draw_angle = m_car_dir + rotation_angle;

    } else if (m_map_widget_ptr->getRenderEngineLayer()->getMapRenderMode() != MRS_2D) {
        draw_angle = 0.0;
    }

    painter.rotate(draw_angle);
    painter.drawImage(rotatedRect, m_car_flag);
    //painter.drawRect(rotatedRect);
    painter.restore();
}

void LocationLayer::onStartDrive(std::shared_ptr<aurora::path::PathInfo> path_info)
{
    if (!path_info)
        return;

    m_path_info_ptr = path_info;

    m_is_show_car_flag = true;
    m_is_update_center_pos = true;
    onClearData();
    for (const auto &point : path_info->points) {
        m_path_latlon_set.push_back(aurora::Point2d(point.lng(), point.lat()));
    }
    emit needRedraw();
}

void LocationLayer::onClearDriveView()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    onClearData();
    m_is_show_car_flag = false;
    m_is_update_center_pos = false;
    emit needRedraw();
}

void LocationLayer::onClearData()
{
    m_last_match_path_index = 0;
    m_path_latlon_set.clear();
    m_original_latlon_set.clear();
    m_match_latlon_set.clear();
    m_gray_path_latlon_set.clear();
    m_gray_offset_latlon_set.clear();
}

bool LocationLayer::GetDisplayPolyline(MapCamera& camera, const std::vector<aurora::Point2d>& polyline,
                                       std::vector<std::vector<QPointF>>& result)
{
  if (polyline.size() < 2) {
    return false;
  }
  auto mbr = GetScreenMbr(camera);
  std::vector<bool> save_flag(polyline.size(), false);
  bool prev_in_screen = false;
  if (mbr.Contains(polyline[0])) {
    save_flag[0] = true;
    prev_in_screen = true;
  }
  bool cur_in_screen = mbr.Contains(polyline[1]);
  if (!save_flag[0]) {
    if (cur_in_screen) {
      save_flag[0] = true;
    } else {
      if (mbr.Intersects(polyline[0], polyline[1])) {
        save_flag[0] = true;
      }
    }
  }

  int32_t pre_idx = 0;
  int32_t cur_idx = 1;
  for (size_t i = 2; i < polyline.size(); ++i) {
    int32_t nex_idx = i;
    bool next_in_screen = mbr.Contains(polyline[nex_idx]);
    if (mbr.Intersects(polyline[pre_idx], polyline[cur_idx])) {
      save_flag[pre_idx] = true;
      save_flag[cur_idx] = true;
    } else if (cur_in_screen || prev_in_screen || next_in_screen) {
      save_flag[cur_idx] = true;
    } else {
    }

    prev_in_screen = cur_in_screen;
    cur_in_screen = next_in_screen;
    pre_idx = cur_idx;
    cur_idx = nex_idx;
  }
  if (mbr.Intersects(polyline[pre_idx], polyline[cur_idx])) {
    save_flag[pre_idx] = true;
    save_flag[cur_idx] = true;
  } else if (cur_in_screen || prev_in_screen) {
    save_flag[pre_idx] = true;
    save_flag[cur_idx] = true;
  } else {
  }

  std::vector<QPointF> path;
  path.reserve(64);
  for (size_t i = 0; i < save_flag.size(); ++i) {
    if (save_flag[i]) {
      SavePoint(camera, polyline[i], path);
    } else {
      if (!path.empty()) {
        result.push_back(path);
        path.clear();
      }
    }
  }
  if (!path.empty()) {
    result.push_back(path);
  }
  return true;
}

void LocationLayer::SavePoint(MapCamera& camera, const aurora::Point2d& point, std::vector<QPointF>& result)
{
  RGeoPoint word_pt(point.x(), point.y());
  QPointF p;
  camera.worldToScreen(&word_pt, p);
  result.push_back(p);
}

aurora::AABB2<aurora::Point2d> LocationLayer::GetScreenMbr(MapCamera& camera)
{
  int w = 0, h = 0;
  int delta = 10;
  camera.getScreenSize(w, h);
  double x[4] = {0.0};
  double y[4] = {0.0};
  camera.screenToWorld(-delta, h + delta, x[0], y[0]);
  camera.screenToWorld(-delta, -delta, x[1], y[1]);
  camera.screenToWorld(w + delta, h + delta, x[2], y[2]);
  camera.screenToWorld(w + delta, - delta, x[3], y[3]);
  double min_x = x[0];
  double min_y = y[0];
  double max_x = x[0];
  double max_y = y[0];
  for (int i = 1; i < 4; ++i) {
    if (min_x > x[i]) {
      min_x = x[i];
    } else if (max_x < x[i]) {
      max_x = x[i];
    } else {
    }
    if (min_y > y[i]) {
      min_y = y[i];
    } else if (max_y < y[i]) {
      max_y = y[i];
    } else {
    }
  }

  return aurora::AABB2<aurora::Point2d>(min_x, min_y, max_x, max_y);
}

