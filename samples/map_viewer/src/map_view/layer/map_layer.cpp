﻿
#include "map_layer.h"
#include "utils/util_type_define.h"

MapLayer::MapLayer()
	: QObject(),
	m_visible(true),
    m_mapCacheData(nullptr),
    m_camera(nullptr),
    m_layer_type(kMapLayer_Invalid)
{
}

MapLayer::~MapLayer()
{

}

void MapLayer::paint(QPainter& painter UNUSED_VAR, MapCamera& camera UNUSED_VAR)
{
    painter;
    camera;
}

void MapLayer::SetMapDataCache(MapDataAdapter* cache)
{
	m_mapCacheData = cache;
}

void MapLayer::setVisible(bool visibility)
{
	if (visibility == m_visible)
		return;

	m_visible = visibility;
	emit needRedraw();
}

void MapLayer::onContextMenuRequested(const QPoint& pos UNUSED_VAR)
{
}

void MapLayer::onMouseDoubleClick(QMouseEvent* event UNUSED_VAR, MapCamera& camera UNUSED_VAR)
{
}

void MapLayer::onMouseDown(QMouseEvent* event UNUSED_VAR, MapCamera& camera UNUSED_VAR)
{
}

void MapLayer::onMouseMove(QMouseEvent* event UNUSED_VAR, MapCamera& camera UNUSED_VAR)
{

}

void MapLayer::onMouseUp(QMouseEvent* event UNUSED_VAR, MapCamera& camera UNUSED_VAR)
{

}

void MapLayer::initializeGL(QOpenGLWidget *container UNUSED_VAR, QOpenGLFunctions *openGLFunctions UNUSED_VAR)
{

}

void MapLayer::resizeGL(int w UNUSED_VAR, int h UNUSED_VAR)
{

}

const QString& MapLayer::getTitle()
{
    return m_title;
}

void MapLayer::startNavi()
{

}
