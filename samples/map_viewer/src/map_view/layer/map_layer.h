﻿
#ifndef MAP_LAYER_H
#define MAP_LAYER_H

#include <QObject>
#include "map_camera.h"
#include "map_widget.h"

/**
	@brief 地图图层的基类
*/
class MapDataAdapter;

class MapLayer : public QObject
{
	Q_OBJECT

public:
	//MapLayer(MapWidget *parent = NULL);
	MapLayer();
	~MapLayer();

	/** 绘图 */
	virtual void paint(QPainter& painter, MapCamera& camera);

	/** 快捷菜单 */
	virtual void onContextMenuRequested(const QPoint& pos);

	/*  鼠标动作 */
	virtual void onMouseDoubleClick(QMouseEvent* event, MapCamera& camera);
	virtual void onMouseDown(QMouseEvent* event, MapCamera& camera);
	virtual void onMouseMove(QMouseEvent* event, MapCamera& camera);
	virtual void onMouseUp(QMouseEvent* event, MapCamera& camera);
    virtual void initializeGL(QOpenGLWidget *container, QOpenGLFunctions *openGLFunctions);
    virtual void resizeGL(int w, int h);
    virtual const QString& getTitle();
    virtual void setVisible(bool visibility);

    virtual void startNavi();


	/** 标题 */
	void SetMapDataCache(MapDataAdapter* cache);
	bool isVisible() const { return m_visible; }
    void setMapCamera(MapCamera *camera) { m_camera = camera; }
    void setMapLayerType(enMapLayerType type) {m_layer_type = type;}
    enMapLayerType getMapLayerType() { return m_layer_type;}


protected:
    QString m_title;
	bool m_visible;
	MapDataAdapter* m_mapCacheData;    
    MapCamera* m_camera;
    enMapLayerType m_layer_type;

signals:
	void needRedraw();
};

#endif // MAP_LAYER_H
