
#include "render_engine_layer.h"
#include <QPen>
#include <iostream>
#include <QPainter>
#include <QMouseEvent>
#include "map_engine_manage.h"
#include "map/src/util/scale_converter.h"
#include "map_render_module.h"



const int TIME_INTERVAL = 200;
const int ANIMATIONn_TIME = 500;

RenderEngineLayer::RenderEngineLayer(MapWidget *parent)
: MapLayer()
, m_map_widget_ptr(parent)
{

    m_last_rotationAngle = 0.0;
    m_map_render_stype = MRS_2D;
    m_isDragging = false;
    m_refreshTimer = nullptr;
    m_visible = false;
    m_mapContainer = nullptr;
    m_openGLFunctions = nullptr;
    m_title = MapLayer::tr("Render_Engine_View");

    MapEngineManage *mm = MapEngineManage::GetInstance();
    m_map_render_service_ptr = mm->onGetRenderEngineService();
    m_render_engine_scale = mm->m_def_scale;

    std::string str_zoom_in_image(":/MainWindow/resources/map_fb_zoomin_press.png");
    std::string str_zoom_out_image(":/MainWindow/resources/map_fb_zoomout_press.png");
    std::string str_zoom_in_click_image(":/MainWindow/resources/map_fb_zoomin_press_click.png");
    std::string str_zoom_out_click_image(":/MainWindow/resources/map_fb_zoomout_press_click.png");
    addZoomButton(m_zoom_in_btn, str_zoom_in_image, str_zoom_in_click_image);
    addZoomButton(m_zoom_out_btn, str_zoom_out_image, str_zoom_out_click_image);
    // 连接信号和槽
    connect(&m_zoom_in_btn, &QPushButton::clicked, this, &RenderEngineLayer::onZoomInPush);
    connect(&m_zoom_out_btn, &QPushButton::clicked, this, &RenderEngineLayer::onZoomOutPush);

    connect(this, &RenderEngineLayer::updateMapWidget, m_map_widget_ptr, &MapWidget::ForceUpdateWidget, Qt::QueuedConnection);
    initScaleDisLabel();

    m_car_north_ptr = std::make_shared<CircularToggleButton>(m_map_widget_ptr);
    m_car_north_ptr->setButtonSize(80, 20);
    connect(m_car_north_ptr.get(), &CircularToggleButton::textChanged, this, &RenderEngineLayer::onCarNorthButtonTextChanged);
}

RenderEngineLayer::~RenderEngineLayer()
{
    if (m_refreshTimer) {
        delete m_refreshTimer;
        m_refreshTimer = nullptr;
    }

    cleanup();
}

void RenderEngineLayer::cleanup()
{
    if (m_mapContainer) {
        m_mapContainer->makeCurrent();
        m_mapContainer->doneCurrent();
    }
}


bool RenderEngineLayer::initMapEngineRenderMap(MapEngineManage * engine_manage)
{
    if (!engine_manage || engine_manage->m_render_data_path.isEmpty())
        return false;

    MapEngineManage *mm = MapEngineManage::GetInstance();
    m_map_render_thema_ptr = std::make_shared<aurora::parser::CfgManager>();
    m_map_render_thema_ptr->Init(mm->m_render_config_path.toStdString());

    aurora::AuroraMapConfig config;
    config.data_path_ = engine_manage->m_render_data_path.toStdString();
    auto font_path = engine_manage->m_render_fonts_path.toStdString();
    config.default_fontPath_ = font_path;
    config.default_fontName_ = "Arial Unicode.ttf";
    config.default_mark_path_ = font_path + "/../mark/mark.png";
    m_map_render_service_ptr = std::make_shared<aurora::IMapRenderService>();
    m_map_render_service_ptr->Init(config, m_map_render_thema_ptr.get());
    RGeoPoint center;
    m_map_widget_ptr->getWorldCenter(&center);
    m_map_render_service_ptr->SetMapCenter(center.CoordX_, center.CoordY_);
    m_map_render_service_ptr->SetMapScale(mm->m_def_scale);

    int w, h;
    m_camera->getScreenSize(w, h);
    m_map_render_service_ptr->SetScreenSize(w,h);

    int32_t scale_meter = aurora::Level2Scale(static_cast<int32_t>(mm->m_def_scale));
    updateScaleDisLabel(scale_meter);

    return true;
}


void RenderEngineLayer::initializeGL(QOpenGLWidget *container, QOpenGLFunctions *openGLFunctions)
{
    m_mapContainer = container;
    m_openGLFunctions = openGLFunctions;

    m_refreshTimer = new QTimer(this);
    connect(m_refreshTimer, SIGNAL(timeout()), this, SLOT(onRefresh()));
    m_refreshTimer->start(TIME_INTERVAL);

    if (initMapEngineRenderMap(MapEngineManage::GetInstance())) {
        m_map_widget_ptr->getMapCamera()->setMapRenderService(this, m_map_render_service_ptr.get());
    }
}

void RenderEngineLayer::paint(QPainter& painter, MapCamera& camera)
{
    GLint preProgram = 0;
    m_openGLFunctions->glGetIntegerv(GL_CURRENT_PROGRAM, &preProgram);

    m_openGLFunctions->glEnable(GL_MULTISAMPLE);

    glPushAttrib(GL_ALL_ATTRIB_BITS);

    if (m_map_render_service_ptr) {
        m_map_render_service_ptr->RenderMap();
    }
    m_openGLFunctions->glBindBuffer(GL_ARRAY_BUFFER, 0);
    m_openGLFunctions->glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0);

    int w, h;
    camera.getScreenSize(w, h);
    m_openGLFunctions->glEnableVertexAttribArray(0);
    m_openGLFunctions->glEnable(GL_BLEND);
    m_openGLFunctions->glUseProgram(preProgram);
    m_openGLFunctions->glViewport(0, 0, w, h);
    m_openGLFunctions->glDisableVertexAttribArray(1);
    m_openGLFunctions->glDisableVertexAttribArray(2);
    m_openGLFunctions->glDisableVertexAttribArray(3);

    glPopAttrib();

}

void RenderEngineLayer::resizeGL(int w, int h)
{
    if (m_map_render_service_ptr) {
        m_map_render_service_ptr->SetScreenSize(w,h);
    }

    updataZoomBtnPos(*m_camera);
    updateScaleDisLabelPos(*m_camera);
}

void RenderEngineLayer::onMouseDown(QMouseEvent* event, MapCamera& camera)
{
    if (event->button() == Qt::LeftButton) {
        m_lastPos = event->pos();
        m_isDragging = true;
    }
}

void RenderEngineLayer::onMouseMove(QMouseEvent* event, MapCamera& camera)
{
    if (m_isDragging) {
        QPoint currentPos = event->pos();
        QPoint delta = currentPos - m_lastPos;
        if (m_map_render_service_ptr) {
            m_map_render_service_ptr->MoveMap(delta.x(), -delta.y());
        }
        m_lastPos = currentPos;
    }

}

void RenderEngineLayer::onMouseUp(QMouseEvent* event, MapCamera& camera)
{
    if (event->button() == Qt::LeftButton) {
        m_isDragging = false;
        QPoint currentPos = event->pos();
               QPoint delta = currentPos - m_lastPos;
               if (m_map_render_service_ptr) {
                   m_map_render_service_ptr->MoveMap(delta.x(), -delta.y(), true, 200);
               }
    }
}

void RenderEngineLayer::setVisible(bool visibility)
{
    if (visibility == m_visible)
        return;

    m_visible = visibility;

    m_zoom_in_btn.setVisible(visibility);
    m_zoom_out_btn.setVisible(visibility);
    m_scale_label.setVisible(visibility);
    m_car_north_ptr->setVisible(visibility);

    emit needRedraw();
}

void RenderEngineLayer::onRefresh()
{
    emit needRedraw();
}

void RenderEngineLayer::addZoomButton(QPushButton & zoom_btn, std::string &image_path, std::string &image_click_path)
{
    zoom_btn.setParent(m_map_widget_ptr);
    zoom_btn.setFixedSize(90, 90);  // 设置按钮大小
    zoom_btn.setFlat(true);           // 无边框
    zoom_btn.setStyleSheet(
        "QPushButton {"
        "    background-color: transparent;"
        "    border: none;"
        "    image: url('" + QString::fromStdString(image_path) + "');"  // 添加正常状态图片
        "}"
        "QPushButton:pressed {"
        "    image: url('" + QString::fromStdString(image_click_path) + "');"  // 添加按下状态图片
        "}"
    );
    zoom_btn.setIconSize(QSize(90, 90));  // 图标大小与按钮一致
    zoom_btn.setVisible(false);

}


void RenderEngineLayer::updataZoomBtnPos(MapCamera& camera)
{
    int w,h;
    camera.getScreenSize(w, h);

    int zoom_x = w - 114;
    int zoom_y = h - 240;
    m_zoom_in_btn.move(zoom_x, zoom_y);

    zoom_y = h - 140;
    m_zoom_out_btn.move(zoom_x, zoom_y);

    zoom_y = 150;
    m_car_north_ptr->move(zoom_x, zoom_y);
}

void RenderEngineLayer::updateScaleDisLabelPos(MapCamera& camera)
{
    int w,h;
    camera.getScreenSize(w, h);

    int x = w - 120;
    int y = h - 30;
    m_scale_label.move(x, y);
}

void RenderEngineLayer::initScaleDisLabel()
{
    m_scale_label.setParent(m_map_widget_ptr);
    m_scale_label.setAlignment(Qt::AlignCenter);
    m_scale_label.setStyleSheet("color: black; font-size: 16px;");
    m_scale_label.setFixedWidth(100);
}

void RenderEngineLayer::updateScaleDisLabel(int &dis)
{
    std::string str_dis;
    int dis_km = dis/1000;
    if (dis_km > 0) {
        str_dis = std::to_string(dis_km) + "km";
    } else {
        str_dis = std::to_string(dis) + "m";
    }

    m_scale_label.setText(str_dis.c_str());
}

void RenderEngineLayer::onZoomInPush()
{
    if (m_render_engine_scale >= 17.0)
        return;

    m_render_engine_scale += 1.0;
    onUpdateZoom(m_render_engine_scale, ANIMATIONn_TIME);
}

void RenderEngineLayer::onZoomOutPush()
{
    if (m_render_engine_scale <= 3.0)
        return;

    m_render_engine_scale -= 1.0;
    onUpdateZoom(m_render_engine_scale, ANIMATIONn_TIME);

}

void RenderEngineLayer::setRenderScale(double scale, int animation)
{
    m_render_engine_scale = scale;

    onUpdateZoom(scale, animation);
}

void RenderEngineLayer::onUpdateZoom(double &level, int animation_time)
{
    if (m_map_render_service_ptr) {
        if (animation_time > 0)
            m_map_render_service_ptr->SetMapScale(level, animation_time);
        else
            m_map_render_service_ptr->SetMapScale(level);

        int32_t scale_meter = aurora::Level2Scale(static_cast<int32_t>(level));
        updateScaleDisLabel(scale_meter);
        emit updateMapWidget();
    }

}

void RenderEngineLayer::setRoutePathResult(const aurora::path::PathQueryPtr& query, const aurora::path::PathResultPtr& result)
{
    if (!m_map_render_service_ptr)
        return;

    m_route_path_id_set.clear();
    for (size_t i = 0; i < result->paths.size(); i++)
    {
       std::vector<aurora::Point2d> latlon_path_set;
       for (const auto &point : result->paths.at(i).points) {
           latlon_path_set.push_back(aurora::Point2d(point.lng(), point.lat()));
       }
       m_map_render_service_ptr->SetPath(i, latlon_path_set);
       m_route_path_id_set.push_back(i);
       std::cout << "RenderEngine: addRoutePath: " << i << std::endl;
    }

}

void RenderEngineLayer::deleteRoutePath(uint32_t type)
{
    if (m_map_render_service_ptr) {
        m_map_render_service_ptr->ClearPath(type);
        std::cout << "RenderEngine: clearRoutePath: " << type << std::endl;
    }

}

void RenderEngineLayer::deleteAllRoutePathFromEngine()
{
    for (auto item : m_route_path_id_set) {
       m_map_render_service_ptr->ClearPath(item);
    }
    m_route_path_id_set.clear();
}

int RenderEngineLayer::setRoutePointToEngine(const RGeoPoint* pos, const RoutePointType type)
{
    if (!pos)
        return -1;

    aurora::Point2d mark_pos(pos->CoordX_, pos->CoordY_);
    aurora::Point2d mark_anchor(32,56);

    int ret = -1;
    std::string mark_image;
    if (type == RPT_START_POINT) {
        // m_engine_start_mark_id.clear();
        mark_image = MapEngineManage::GetInstance()->m_res_data_path.toStdString() + std::string("/resources/s.png");

        if (m_engine_start_mark_id.empty()) {
            ret = m_map_render_service_ptr->SetMarkInfo(RPT_START_POINT, mark_pos, mark_anchor, mark_image);
            m_engine_start_mark_id.push_back(ret);
            std::cout << "setStratPoint ret : " << ret << std::endl;
        } else {
            m_map_render_service_ptr->UpdateMarkInfo(RPT_START_POINT, m_engine_start_mark_id[0], mark_pos);
        }
    } else if (type == RPT_END_POINT) {
        // m_engine_end_mark_id.clear();
        mark_image = MapEngineManage::GetInstance()->m_res_data_path.toStdString() + std::string("/resources/e.png");

        if (m_engine_end_mark_id.empty()) {
            ret = m_map_render_service_ptr->SetMarkInfo(RPT_END_POINT, mark_pos, mark_anchor, mark_image);
            m_engine_end_mark_id.push_back(ret);
            std::cout << "setEndPoint ret : " << ret << std::endl;
        } else {
            m_map_render_service_ptr->UpdateMarkInfo(RPT_END_POINT, m_engine_end_mark_id[0], mark_pos);
        }
    } else if (type == RPT_WAY_POINT) {
        mark_image = MapEngineManage::GetInstance()->m_res_data_path.toStdString() + std::string("/resources/w.png");
        ret = m_map_render_service_ptr->SetMarkInfo(RPT_WAY_POINT, mark_pos, mark_anchor, mark_image);
        m_engine_way_mark_id_set.push_back(ret);
        std::cout << "setWayPoint ret : " << ret << std::endl;
    }

    return ret;
    // 返回 >= 0 MarkId, < 0 失败
}

void RenderEngineLayer::deleteRoutePointFromEngine(const RoutePointType type, const int index)
{
    if (type == RPT_START_POINT) {
        if (m_engine_start_mark_id.size() > 0) {
            m_map_render_service_ptr->ClearMark(RPT_START_POINT, m_engine_start_mark_id.at(0));
            m_engine_start_mark_id.clear();
        }
    } else if (type == RPT_END_POINT) {
        if (m_engine_end_mark_id.size() > 0) {
            m_map_render_service_ptr->ClearMark(RPT_END_POINT, m_engine_end_mark_id.at(0));
            m_engine_end_mark_id.clear();
        }
    } else if (type == RPT_WAY_POINT) {

        int cout = m_engine_way_mark_id_set.size();
        if (index < cout) {
            auto &id = m_engine_way_mark_id_set.at(index);
            if (id >= 0) {
                m_map_render_service_ptr->ClearMark(RPT_WAY_POINT, id);
            }

        }
    }
}

void RenderEngineLayer::deleteAllRoutePointFromEngine()
{
    m_map_render_service_ptr->ClearMark(RPT_START_POINT);
    m_map_render_service_ptr->ClearMark(RPT_END_POINT);
    m_map_render_service_ptr->ClearMark(RPT_WAY_POINT);

    m_engine_start_mark_id.clear();
    m_engine_end_mark_id.clear();
    m_engine_way_mark_id_set.clear();
}

void RenderEngineLayer::setCarNorthButtonStatus(mapRenderStype status)
{
    if (status == MRS_2D) {
        m_car_north_ptr->setCurrentStatus(0);
    } else if (status == MRS_3D) {
        m_car_north_ptr->setCurrentStatus(1);
    } else if (status == MRS_NORTHUP) {
        m_car_north_ptr->setCurrentStatus(2);
    }
}

void RenderEngineLayer::startNavi()
{
    setCarNorthButtonStatus(m_map_render_stype); //重置地图2D/3D/north状态

}

void RenderEngineLayer::onCarNorthButtonTextChanged(const QString text)
{
    m_map_widget_ptr->setLastRotationValue(0.0);

    if (text == CIRCULAR_3D) {
        qDebug() << "Current text:" << text;
        m_map_render_stype = MRS_3D;
        m_map_render_service_ptr->SetMapPitch(-30.0);

    } else if (text == CIRCULAR_2D) {
        qDebug() << "Current text:" << text;
        m_map_render_stype = MRS_2D;
        m_map_render_service_ptr->SetMapPitch(0.0);
        m_map_render_service_ptr->SetMapRotation(0.0);

    } else if (text == CIRCULAR_NORTHUP) {
        qDebug() << "Current text:" << text;
        m_map_render_stype = MRS_NORTHUP;
        m_map_render_service_ptr->SetMapPitch(0.0);
        m_last_rotationAngle = 0.0;

    }
    emit needRedraw();
}

void RenderEngineLayer::setMapRotation(float angle)
{
    if (m_map_render_stype == MRS_2D) {
        return;
    }

    float rotationAngle = 0.0 - angle;
    float temp_angle = rotationAngle - m_last_rotationAngle;
    if (std::fabs(temp_angle) < 5.0) {
       // qDebug() << "setMapRotation < 5.0 : angle = " << angle << ", rotationAngle = " << rotationAngle << ", last_rotationAngle= "<< m_last_rotationAngle;
        return;
    }

    //qDebug() << "setMapRotation > 5.0: angle = " << angle << ", rotationAngle = " << rotationAngle;
    m_map_render_service_ptr->SetMapRotation(rotationAngle);
    m_last_rotationAngle = rotationAngle;
}

void RenderEngineLayer::setMoveMap(double delta_x, double delta_y, uint32_t animation_duration_ms)
{
    m_map_render_service_ptr->MoveMap(delta_x, delta_y, animation_duration_ms);
}

void RenderEngineLayer::setRotationAngle(float rot, uint32_t animation_duration_ms)
{
    m_map_render_service_ptr->SetMapRotation(rot, animation_duration_ms = 0);
}




