
#ifndef RENDER_ENGINE_LAYER_H
#define RENDER_ENGINE_LAYER_H

#include <qtimer.h>
#include <QPushButton>
#include <QLabel>
#include "map_layer.h"
#include "map_render_module.h"
#include "config_data/cfg_manager.h"
#include "map_render_engine_wrapper.h"
#include "routing_path.h"
#include "circularToggleButton.h"

enum mapRenderStype {
    MRS_2D = 0 ,
    MRS_3D ,
    MRS_NORTHUP ,
    MRS_INVAID
};



class RenderEngineLayer : public MapLayer
{
    Q_OBJECT
public:
    RenderEngineLayer(MapWidget *parent = NULL);
	virtual ~RenderEngineLayer();
	virtual void paint(QPainter& painter, MapCamera& camera);

    virtual void startNavi();



signals:
    void updateMapWidget();

public slots:
    void cleanup();
    void onRefresh();
    void onCarNorthButtonTextChanged(const QString text);


public:
    virtual void initializeGL(QOpenGLWidget *container, QOpenGLFunctions *openGLFunctions);
    virtual void resizeGL(int width, int height);
    virtual void onMouseDown(QMouseEvent* event, MapCamera& camera);
    virtual void onMouseMove(QMouseEvent* event, MapCamera& camera);
    virtual void onMouseUp(QMouseEvent* event, MapCamera& camera);
    virtual void setVisible(bool visibility);

    aurora::IMapRenderService * getMapEngineRenderService() {return m_map_render_service_ptr.get();}


    double getRenderScale() {return m_render_engine_scale;}
    void setRenderScale(double scale, int animation = 0);

    int setRoutePointToEngine(const RGeoPoint* pos, const RoutePointType type);
    void deleteRoutePointFromEngine(const RoutePointType type, const int index = 0);
    void deleteAllRoutePointFromEngine();

    void setRoutePathResult(const aurora::path::PathQueryPtr& query, const aurora::path::PathResultPtr& result);
    void deleteRoutePath(uint32_t type);
    void deleteAllRoutePathFromEngine();
    void setMapRotation(float angle);
    mapRenderStype getMapRenderMode() {return m_map_render_stype;}
    void setMoveMap(double delta_x, double delta_y, uint32_t animation_duration_ms = 0);
    void setRotationAngle(float rot, uint32_t animation_duration_ms = 0);
    std::shared_ptr<CircularToggleButton> getCircularToggleButton() {return m_car_north_ptr; }
    void setCarNorthButtonStatus(mapRenderStype status);


private:
    bool initRenderMapengine();
    void addZoomButton(QPushButton & zoom_btn, std::string &image_path, std::string &image_click_path);
    void updataZoomBtnPos(MapCamera& camera);
    void updateScaleDisLabelPos(MapCamera& camera);
    void onZoomInPush();
    void onZoomOutPush();
    void onUpdateZoom(double &level, int animation_time);
    bool initMapEngineRenderMap(MapEngineManage * engine_manage);
    void initScaleDisLabel();
    void updateScaleDisLabel(int &dis);

private:
    QLabel m_scale_label;
    QTimer *m_refreshTimer;
    bool m_core;
    QPoint m_lastPos;
    bool m_isDragging;
    bool m_transparent;

    QOpenGLWidget *m_mapContainer;
    QOpenGLFunctions *m_openGLFunctions;

    MapWidget * m_map_widget_ptr;

    QPushButton  m_zoom_in_btn;
    QPushButton  m_zoom_out_btn;
    double m_render_engine_scale;


    std::shared_ptr<aurora::IMapRenderService> m_map_render_service_ptr;
    std::shared_ptr<aurora::parser::CfgManager> m_map_render_thema_ptr;

    std::vector<int> m_route_path_id_set;

    std::vector<int> m_engine_start_mark_id;
    std::vector<int> m_engine_end_mark_id;
    std::vector<int> m_engine_way_mark_id_set;

    std::shared_ptr<CircularToggleButton> m_car_north_ptr;

    mapRenderStype m_map_render_stype;
    float m_last_rotationAngle;


};

#endif // RENDER_ENGINE_LAYER_H
