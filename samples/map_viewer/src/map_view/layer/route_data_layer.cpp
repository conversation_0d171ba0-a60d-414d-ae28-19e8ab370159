
#include "route_data_layer.h"
#include <QPen>
#include <iostream>
#include <QPoint>
#include <QTextCodec>
#include <QMouseEvent>
#include <algorithm>
#include <float.h>
#include "util_coordinate_converter.h"
#include "map_widget_manage.h"
#include "map_engine_manage.h"


using namespace aurora::parser;
using namespace util;

const int route_level = 3;
const int32 MATCH_SEARCH_RADIUS = 60; // search radius
const int32 MATACH_SEARCH_COUNT = 40; // search loop 160 -> 150km
const double ROUTE_LEVEL_0_SHOW_MAX_SCALE = 0.00005;
const double ROUTE_LEVEL_1_SHOW_MAX_SCALE = 0.0003;



RouteDataLayer::RouteDataLayer()
{
    m_is_select_update = false;
    m_dataCacheValid = false;

}

RouteDataLayer::~RouteDataLayer()
{

}

void RouteDataLayer::resizeGL(int w, int h)
{
}

void RouteDataLayer::paint(Q<PERSON>ain<PERSON>& painter, MapCamera& camera)
{    
    drawData(painter, camera);

}

QPen RouteDataLayer::getPenByLevel(enMapLayerType level)
{
    QPen pen = QPen(Qt::black, 1);

    switch (level)
    {
    case kMapLayer_Route_Link_level_0:
        pen.setColor(Qt::black);
        break;
    case kMapLayer_Route_Link_Node_0:
        pen.setColor(Qt::black);
        pen.setWidth(3);
        pen.setStyle(Qt::SolidLine);
        break;
    case kMapLayer_Route_Link_level_1:
        pen.setColor(Qt::darkCyan);
        break;
    case kMapLayer_Route_Link_Node_1:
        pen.setColor(Qt::darkCyan);
        pen.setWidth(3);
        pen.setStyle(Qt::SolidLine);
        break;
    case kMapLayer_Route_Link_level_2:
        pen.setColor(Qt::magenta);
        break;
    case kMapLayer_Route_Link_Node_2:
        pen.setColor(Qt::magenta);
        pen.setWidth(3);
        pen.setStyle(Qt::SolidLine);
        break;
    default:
        break;
    }

    return pen;
}

void RouteDataLayer::onMouseDown(QMouseEvent* event, MapCamera& camera)
{
    if (event->button() != Qt::LeftButton)
        return;

    m_is_select_update = true;
}

void RouteDataLayer::onMouseUp(QMouseEvent* event, MapCamera& camera)
{
    if (!m_is_select_update) {
        return;
    }

    m_show_link_points_set.clear();

    MapWidget* map_widget = qobject_cast<MapWidget*>(this->parent());
    if (map_widget && (map_widget->getSelectMapLayerType() != m_layer_type)) {        
        return;
    }

    RGeoPoint geoPoint;
    camera.screenToWorld(event->pos(), &geoPoint);
    int width = 10;
    int height = 10;
    LLRect worldRect;
    camera.getWorldRectLatLon(&geoPoint, width, height, &worldRect);
    aurora::parser::GeoMbr mbr(worldRect.CoordX1, worldRect.CoordY1,
                               worldRect.CoordX2, worldRect.CoordY2);
    SearchNearTopolLinkByRange(camera, geoPoint, mbr, m_layer_type);
    m_is_select_update = false;

}

void RouteDataLayer::onMouseMove(QMouseEvent* event, MapCamera& camera)
{
    m_is_select_update = false;
}

void RouteDataLayer::onContextMenuRequested(const QPoint& pos)
{
    //m_contextMenu.popup(pos);
}


bool RouteDataLayer::isScreenRectChange(MapCamera *camera)
{
    LLRect worldRect;
    camera->getWorldRectLatLon(&worldRect);

    // 检查视口是否变化显著，避免不必要的重新加载
    const double threshold = 0.01; // 根据实际情况调整
    bool viewChangedSignificantly =
        std::abs(worldRect.CoordX1 - m_lastWorldRect.CoordX1) > threshold ||
        std::abs(worldRect.CoordY1 - m_lastWorldRect.CoordY1) > threshold ||
        std::abs(worldRect.CoordX2 - m_lastWorldRect.CoordX2) > threshold ||
        std::abs(worldRect.CoordY2 - m_lastWorldRect.CoordY2) > threshold;

    if (!viewChangedSignificantly && m_dataCacheValid) {
        return false; // 数据缓存有效，无需重新加载
    }

    return true;
}


bool RouteDataLayer::SearchRouteDataByScreenRect(MapCamera *camera)
{
    if (!isScreenRectChange(camera))
        return false;

    LLRect worldRect;
    camera->getWorldRectLatLon(&worldRect);

    m_lastWorldRect = worldRect;
    aurora::parser::GeoMbr mbr(worldRect.CoordX1, worldRect.CoordY1, worldRect.CoordX2, worldRect.CoordY2);

    return searchRouteData(camera, mbr, m_layer_type, m_level_tile_ids_vec);
}


bool RouteDataLayer::searchRouteData(MapCamera *camera, aurora::parser::GeoMbr mbr, enMapLayerType layer_type,  RouteTileIDSet &tiles_set)
{
    m_level_tile_ids_vec.clear();

    aurora::parser::DataProvider provider = MapEngineManage::GetInstance()->GetDataProvider();

    if (kMapLayer_Route_Link_level_0 == layer_type || kMapLayer_Route_Link_Node_0 == layer_type)
    {
        provider.GetRouteTileIDsByMBR(0, mbr, tiles_set);
    } else if (kMapLayer_Route_Link_level_1 == m_layer_type || kMapLayer_Route_Link_Node_1 == m_layer_type) {
        provider.GetRouteTileIDsByMBR(1, mbr, tiles_set);
    } else if (kMapLayer_Route_Link_level_2 == m_layer_type || kMapLayer_Route_Link_Node_2 == m_layer_type) {
        provider.GetRouteTileIDsByMBR(2, mbr, tiles_set);
    }

    return true;

}

bool RouteDataLayer::SearchNearTopolLinkByRange(MapCamera &camera,
                                                RGeoPoint &geoPoint,
                                                aurora::parser::GeoMbr mbr,
                                                enMapLayerType layer_type)
{

    m_show_link_points_set.clear();

    QPointF curScreenPos;
    RGeoPoint rWorldPos;
    rWorldPos.CoordX_ = geoPoint.CoordX_;
    rWorldPos.CoordY_ = geoPoint.CoordY_;
    camera.worldToScreen(&rWorldPos, curScreenPos);

    double min_dis = DBL_MAX;
    RouteTileReader reader;
    for (size_t i = 0; i < m_level_tile_ids_vec.size(); ++i)
    {
      RouteTilePackagePtr tile_ptr = MapEngineManage::GetInstance()->GetDataProvider().GetRouteTileByID(m_level_tile_ids_vec.at(i));
      reader.SetTarget(tile_ptr);
      AugmentEdgeSet& edges = reader.GetAugmentEdges();

      for (size_t j = 0; j < edges.size(); j++)
      {
          std::vector<map_engine::GeoPoint> match_screen_pos_set;
          for (auto &item : edges[j].GetGeoPoints()) {
              QPointF screenPos;
              RGeoPoint rWorldPos;
              rWorldPos.CoordX_ = item.x();
              rWorldPos.CoordY_ = item.y();
              camera.worldToScreen(&rWorldPos, screenPos);
              match_screen_pos_set.push_back(map_engine::GeoPoint(screenPos.x(), screenPos.y()));
          }

          double dis = util::pointToPolylineDistance(map_engine::GeoPoint(curScreenPos.x(), curScreenPos.y()), match_screen_pos_set);
          //std::cout << "dis : " << dis << std::endl;
          if (dis > 10) {
              continue;
          }

          if (dis < min_dis) {
              m_show_link_points_set.clear();
              min_dis = dis;
              m_matct_link = edges[j];
              for (auto point : edges[j].GetGeoPoints()) {
                  m_show_link_points_set.push_back(map_engine::RGeoPoint(point.x(), point.y()));
              }
              //std::cout << "min_dis" << min_dis << std::endl;
          }
      }
    }

    if (min_dis != DBL_MAX) {
        g_app->showLinkAttributeWidget(&m_matct_link, getTitle());
        return true;
    }

    return false;

}

// 添加辅助函数检查点是否在视口内
bool RouteDataLayer::isPointVisible(const GeoPoint& point, const Rect &viewport)
{
    // 添加一些边距以确保部分在视口内的线段也能被绘制
    const int margin = 50;
    return point.CoordX_ >= viewport.CoordX1 - margin &&
           point.CoordX_ <= viewport.CoordX2 + margin &&
           point.CoordY_ >= viewport.CoordY1 - margin &&
           point.CoordY_ <= viewport.CoordY2 + margin;
}


const QString& RouteDataLayer::getTitle()
{
    if (m_title.size() > 0)
        return m_title;

    if (kMapLayer_Route_Link_level_0 == m_layer_type) {
        m_title = MapLayer::tr("Route_Link_level_0");
    } else if (kMapLayer_Route_Link_level_1 == m_layer_type) {
        m_title = MapLayer::tr("Route_Link_Level_1");
    } else if (kMapLayer_Route_Link_level_2 == m_layer_type) {
        m_title = MapLayer::tr("Route_Link_Level_2");
    }
    return m_title;
}

void RouteDataLayer::setVisible(bool visibility)
{
    if (visibility == m_visible)
        return;

    m_visible = visibility;

    emit needRedraw();
}

void RouteDataLayer::selectShowData()
{
}

void RouteDataLayer::drawData(QPainter& painter, MapCamera& camera)
{
    if (camera.getScale() != m_lastZoomLevel) {
        m_lastZoomLevel = camera.getScale();
    }

    if (kMapLayer_Route_Link_level_0 == m_layer_type) {
        if (camera.getScale() > ROUTE_LEVEL_0_SHOW_MAX_SCALE)
           return;
    } else if (kMapLayer_Route_Link_level_1 == m_layer_type) {
        if (camera.getScale() > ROUTE_LEVEL_1_SHOW_MAX_SCALE)
           return;
    }

       SearchRouteDataByScreenRect(&camera);

       util::RenderLinkStringSet link_name_vec;
       util::RenderLinkStringSet link_number_vec;

        painter.setPen(getPenByLevel(m_layer_type));
        RouteTileReader reader;
        QPainterPath path;
        bool pathStarted = false;
        QTextCodec *tc = QTextCodec::codecForName("UTF-8");

        for (size_t i = 0; i < m_level_tile_ids_vec.size(); ++i) {
          RouteTilePackagePtr tile_ptr = MapEngineManage::GetInstance()->GetDataProvider().GetRouteTileByID(m_level_tile_ids_vec.at(i));
          reader.SetTarget(tile_ptr);

          AugmentEdgeSet& edges = reader.GetAugmentEdges();

          for (size_t j = 0; j < edges.size(); j++)
          {
              aurora::parser::AugmentEdge* aug_link = &edges[j];
              const LngLatSet& aug_link_points = aug_link->GetGeoPoints();
              std::vector<GeoPoint> shapePoints;

              for (size_t i = 0; i < aug_link_points.size(); i++) {
                  RGeoPoint rWorldPos;
                  rWorldPos.CoordX_ = aug_link_points[i].x();
                  rWorldPos.CoordY_ = aug_link_points[i].y();
                  GeoPoint point;
                  camera.ConvertLonLanCoord(&rWorldPos, point);
                  shapePoints.push_back(point);
              }

              // 检查线段是否在可见区域内（优化：减少不必要的路径添加）
             bool isVisible = false;
             map_engine::Rect rect;
             camera.getWorldRect(&rect);

             for (size_t k = 0; k < shapePoints.size(); k++)
             {
                 if (isPointVisible(shapePoints.at(k), rect)) {
                     isVisible = true;
                     break;
                 }
             }

             if (!isVisible) continue;

             for (size_t k = 0; k < shapePoints.size(); k++)
             {
                 QPointF screenPos;
                 camera.worldToScreen(&shapePoints.at(k), screenPos);
                 if (k == 0) {
                    path.moveTo(screenPos);
                 } else {
                    path.lineTo(screenPos);
                 }
             }

             // 画道路编号或名称
             //if (m_showLinkId || m_showRoadName)
             {
                 GeoPoint showPos;
                 QPointF showScreenPos;
                 if (shapePoints.size() == 2)
                 {
                     showPos.CoordX_ = (shapePoints.at(0).CoordX_ + shapePoints.at(0).CoordX_) / 2;
                     showPos.CoordY_ = (shapePoints.at(0).CoordY_ + shapePoints.at(0).CoordY_) / 2;
                 }
                 else
                 {
                     showPos.CoordX_ = shapePoints[shapePoints.size() / 2].CoordX_;
                     showPos.CoordY_ = shapePoints[shapePoints.size() / 2].CoordY_;
                 }

                 // 计算name锚点是否在可见区域内
                  bool isPosVisible = isPointVisible(showPos, rect);
                  if (!isPosVisible)
                      continue;

                 camera.worldToScreen(&showPos, showScreenPos);

                 //if (m_showLinkId)
//                 {
//                     int link_road_no_len = std::strlen(aug_link->GetRoadNo());
//                     QString link_road_no = tc->toUnicode(QByteArray(aug_link->GetRoadNo(), link_road_no_len));
//                     link_number_vec.push_back(RenderLinkNoObj<QString>(showScreenPos, link_road_no, link_road_no_len));
//                 }

                 //if (m_showRoadName)
                 { // 显示道路名称
                     if (aug_link->GetLocalName()) {
                         int name_len = std::strlen(aug_link->GetLocalName());
                         QString link_name = tc->toUnicode(QByteArray(aug_link->GetLocalName(), name_len));
                         link_name_vec.push_back(RenderLinkNoObj<QString>(showScreenPos, link_name, name_len));
                     }

                 }
             }

             pathStarted = true;
          }

        }
        // 绘制整个路径
        if (pathStarted) {
            painter.drawPath(path);
        }

        //if (m_showLinkId)
           // drawLinkNumber(painter, camera, link_number_vec);

        //if (m_showRoadName)
            drawLinkName(painter, camera, link_name_vec);

     if (m_show_link_points_set.size() > 0)
     {
         //painter.restore();
         QPen pen = QPen(Qt::red, 3);
         painter.setPen(pen);
         painter.setBrush(Qt::NoBrush);
         QPainterPath match_path;

         for (size_t i = 0; i < m_show_link_points_set.size(); i++)
         {
             QPointF screenPos;
             camera.worldToScreen(&m_show_link_points_set.at(i), screenPos);
             if (i == 0) {
                match_path.moveTo(screenPos);
             } else {
                match_path.lineTo(screenPos);
             }
         }
         painter.drawPath(match_path);
     }

}

void RouteDataLayer::drawLinkName(QPainter& painter, MapCamera& camera, RenderLinkStringSet& vecs)
{
    if (vecs.size() > 1000)
        return;

    int font_size = 20;
    CheckIntersection(vecs, font_size);

    QPainterPath textPath;
    QFont font("Arial", 12, QFont::Normal);
    font.setStyleStrategy(QFont::PreferQuality);  // 平衡质量和性能
    painter.setFont(font);
    painter.setPen(Qt::NoPen);          // 不绘制轮廓
    painter.setBrush(QBrush(Qt::blue)); // 设置填充色

    for (size_t i = 0; i < vecs.size(); i++)
    {
        RenderLinkNoObj<QString>& obj = vecs.at(i);
        if (1 == obj.is_render)
        {
            textPath.addText(obj.screen_point, painter.font(), obj.no);
        }
    }
    painter.drawPath(textPath);
}
