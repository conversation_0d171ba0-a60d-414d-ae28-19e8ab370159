
#include "route_data_node_layer.h"
#include <QPen>
#include <iostream>
#include <QPoint>
#include <QTextCodec>
#include <QMouseEvent>
#include <algorithm>
#include <float.h>
#include "util_coordinate_converter.h"
#include "map_widget_manage.h"
#include "map_widget.h"

using namespace aurora::parser;
using namespace util;

const double NODE_SHOW_MAX_SCALE = 0.00002;

RouteDataNodeLayer::RouteDataNodeLayer()
{
    m_dataCacheValid = false;

}

RouteDataNodeLayer::~RouteDataNodeLayer()
{

}

void RouteDataNodeLayer::resizeGL(int w, int h)
{

}


void RouteDataNodeLayer::paint(QPainter& painter, MapCamera& camera)
{
    drawData(painter, camera);
}

void RouteDataNodeLayer::onMouseDown(QMouseEvent* event, MapCamera& camera)
{
    if (event->button() != Qt::LeftButton)
        return;

    m_is_select_update = true;
}

void RouteDataNodeLayer::onMouseUp(QMouseEvent* event, MapCamera& camera)
{
    if (!m_is_select_update) {
        return;
    }

    MapWidget* map_widget = qobject_cast<MapWidget*>(this->parent());
    if (map_widget && (map_widget->getSelectMapLayerType() != m_layer_type)) {
        m_show_node_point.CoordX_ = 0.0;
        m_show_node_point.CoordY_ = 0.0;
        return;
    }

    RGeoPoint geoPoint;
    camera.screenToWorld(event->pos(), &geoPoint);
    int width = 5;
    int height = 5;
    LLRect worldRect;
    camera.getWorldRectLatLon(&geoPoint, width, height, &worldRect);
    aurora::parser::GeoMbr mbr(worldRect.CoordX1, worldRect.CoordY1,
                               worldRect.CoordX2, worldRect.CoordY2);
    SearchNearNodeByRange(camera, geoPoint, mbr, m_layer_type);
    selectShowData();


    m_is_select_update = false;

}

void RouteDataNodeLayer::onMouseMove(QMouseEvent* event, MapCamera& camera)
{
    m_is_select_update = false;
}


void RouteDataNodeLayer::setVisible(bool visibility)
{
    if (visibility == m_visible)
        return;

    m_visible = visibility;

    emit needRedraw();
}

void RouteDataNodeLayer::onContextMenuRequested(const QPoint& pos)
{
    //m_contextMenu.popup(pos);
}


bool RouteDataNodeLayer::SearchNearNodeByRange(MapCamera &camera,
                                                RGeoPoint &geoPoint,
                                                aurora::parser::GeoMbr mbr,
                                                enMapLayerType layer_type)
{

    m_show_node_point.CoordX_ = 0.0;
    m_show_node_point.CoordY_ = 0.0;

    QPointF curScreenPos;
    RGeoPoint rWorldPos;
    rWorldPos.CoordX_ = geoPoint.CoordX_;
    rWorldPos.CoordY_ = geoPoint.CoordY_;
    camera.worldToScreen(&rWorldPos, curScreenPos);

    int32 min_dis = INT32_MAX;
    RouteTileReader reader;
    for (size_t i = 0; i < m_level_tile_ids_vec.size(); ++i)
    {
      RouteTilePackagePtr tile_ptr = MapEngineManage::GetInstance()->GetDataProvider().GetRouteTileByID(m_level_tile_ids_vec.at(i));
      reader.SetTarget(tile_ptr);
      RouteNodeSet& nodes = reader.GetNodes();

      for (auto &node : nodes)
      {
          QPointF screenPos;
          RGeoPoint rWorldPos;
          rWorldPos.CoordX_ = node.GetPosition().x();
          rWorldPos.CoordY_ = node.GetPosition().y();
          camera.worldToScreen(&rWorldPos, screenPos);

          int32 dis = util::distance(map_engine::GeoPoint(curScreenPos.x(), curScreenPos.y()), map_engine::GeoPoint(screenPos.x(), screenPos.y()));
          if (dis > 5) {
              continue;

          }
          if (dis < min_dis) {
              min_dis = dis;
              m_match_node = node;
           }
          }
      }

    if (min_dis != INT32_MAX) {
        m_show_node_point.CoordX_ = m_match_node.GetPosition().x();
        m_show_node_point.CoordY_ = m_match_node.GetPosition().y();
         g_app->showNodeAttributeWidget(&m_match_node, getTitle());
        return true;
    }

    return false;

}


const QString& RouteDataNodeLayer::getTitle()
{
    if (m_title.size() > 0)
        return m_title;

    if (kMapLayer_Route_Link_Node_0 == m_layer_type) {
        m_title = MapLayer::tr("Route_node_level_0");
    } else if (kMapLayer_Route_Link_Node_1 == m_layer_type) {
        m_title = MapLayer::tr("Route_node_Level_1");
    } else if (kMapLayer_Route_Link_Node_2 == m_layer_type) {
        m_title = MapLayer::tr("Route_node_Level_2");
    }
    return m_title;
}

void RouteDataNodeLayer::selectShowData()
{

}

void RouteDataNodeLayer::drawData(QPainter& painter, MapCamera& camera)
{

    if (camera.getScale() != m_lastZoomLevel) {
        m_lastZoomLevel = camera.getScale();
    }

    if (camera.getScale() > NODE_SHOW_MAX_SCALE) {
        return;
    }

    SearchRouteDataByScreenRect(&camera);

        painter.setPen(getPenByLevel(m_layer_type));
        RouteTileReader reader;
        QVector<QPointF> points;

        for (size_t i = 0; i < m_level_tile_ids_vec.size(); ++i) {
          RouteTilePackagePtr tile_ptr = MapEngineManage::GetInstance()->GetDataProvider().GetRouteTileByID(m_level_tile_ids_vec.at(i));
          reader.SetTarget(tile_ptr);

          RouteNodeSet& nodes = reader.GetNodes();

          for (auto &node : nodes)
          {
              RGeoPoint rWorldPos;
              rWorldPos.CoordX_ = node.GetPosition().x();
              rWorldPos.CoordY_ = node.GetPosition().y();
              GeoPoint shapePoint;
              camera.ConvertLonLanCoord(&rWorldPos, shapePoint);

              // 检查线段是否在可见区域内（优化：减少不必要的添加）
             map_engine::Rect rect;
             camera.getWorldRect(&rect);

             if (!isPointVisible(shapePoint, rect)) {
                 continue;
             }

             QPointF screenPos;
             camera.worldToScreen(&shapePoint, screenPos);
             points.push_back(screenPos);

          }

        }
        // 绘制
        for (auto &pos : points) {
            painter.drawPoint(pos);
        }

     if (m_show_node_point.CoordX_ != 0.0 && m_show_node_point.CoordY_ != 0.0)
     {
         QPen pen = QPen(Qt::red, 6);
         painter.setPen(pen);

         QPointF point;
         camera.worldToScreen(&m_show_node_point, point);
         painter.drawPoint(point);
     }

}

