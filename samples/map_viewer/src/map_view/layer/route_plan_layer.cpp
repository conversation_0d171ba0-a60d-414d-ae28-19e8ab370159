
#include "route_plan_layer.h"
#include <QMouseEvent>
#include <iostream>
#include <QDebug>
#include <QPen>
#include <QPainter>
#include <float.h>
#include "base/include/pointll.h"
#include "path/include/path_def.h"
#include "map_widget_manage.h"
#include "main_window.h"
#include "guidance/include/guidance_module.h"
#include "gps_encry.h"
#include "render_engine_layer.h"

using namespace aurora::path;
using namespace aurora::guide;



RoutePlanLayer::RoutePlanLayer(MapWidget *parent )
    : m_search_poi_flag(":/MainWindow/resources/sign_post.png")
    , m_map_widget_ptr(parent)
{
    m_render_engine_layer_ptr = nullptr;
    is_show_search_poi_flag = false;

    setParent(parent);
    setupContextMenu();

    m_route_data_ptr = std::make_shared<RoutingPath>(parent);

}

RoutePlanLayer::~RoutePlanLayer()
{

}

QPen RoutePlanLayer::getPenByLevel(int level)
{
    QPen pen = QPen(Qt::black, 2);

    switch (level)
    {
    case 0: //Light color
    {
        QColor color("#9DC9FF");
        pen.setColor(color);
        pen.setWidth(8);
    }
        break;
    case 1: //Dark color
    {
        QColor color("#2385FF");
        pen.setColor(color);
        pen.setWidth(8);
    }
        break;
    case 2: //select Highlight color
    {
        QColor color("#DC143C");
        pen.setColor(color);
        pen.setWidth(8);
    }
        break;
    case 3: //baidu path Orange color
    {
        QColor color(bmap_color);
        pen.setColor(color);
        pen.setWidth(8);
    }
        break;
    case 4: //gaode path green color
    {
        QColor color(amap_color);
        pen.setColor(color);
        pen.setWidth(8);
    }
        break;
    default:
        break;
    }

    return pen;
}


void RoutePlanLayer::pos2Rect(const QPointF &screen_pos, const int &w, const int &h, map_engine::Rect& rect)
{
    rect.CoordX1 = screen_pos.x() - (w/2);
    rect.CoordY1 = screen_pos.y() - h;
    rect.CoordX2 = screen_pos.x() + (w/2);
    rect.CoordY2 = screen_pos.y();
}

void RoutePlanLayer::drawPathNameFlag()
{
    std::vector<map_engine::RGeoPoint> path_flag_pos_set;

    for (size_t i = 0; i< m_route_data_ptr->m_path_set.size(); i++)
    {
        auto &cur_path_pos = m_route_data_ptr->m_path_set.at(i).path_latlon_set;
        if ( cur_path_pos.size() <= 1)
            continue;

        size_t index = cur_path_pos.size()/2;
        for (size_t j = 0; j < path_flag_pos_set.size(); j++)
        {
            auto &his_path_pos = path_flag_pos_set.at(j);
            QPointF cur_screenPos, his_screenPos;
            m_camera->worldToScreen(&his_path_pos, his_screenPos);
            m_camera->worldToScreen(&cur_path_pos.at(index), cur_screenPos);

            map_engine::Rect his_rect, cur_rect;
            pos2Rect(cur_screenPos, name_flag_w, name_flag_h, cur_rect);
            pos2Rect(his_screenPos, name_flag_w, name_flag_h, his_rect);

            while (util::isRectIntersecting(cur_rect, his_rect)) {
                if ((index + 1) >= cur_path_pos.size()) {
                    break;
                }
                index += 1;
                m_camera->worldToScreen(&cur_path_pos.at(index), cur_screenPos);
                pos2Rect(cur_screenPos, name_flag_w, name_flag_h, cur_rect);
            }

        }
        path_flag_pos_set.push_back(cur_path_pos.at(index));
    }

    for (size_t i = 0; i < path_flag_pos_set.size(); i++)
    {
        auto name_label = m_route_data_ptr->m_path_set.at(i).label_name_ptr;
        QPointF screenPos;
        m_camera->worldToScreen(&path_flag_pos_set.at(i), screenPos);
        name_label->move(screenPos.x() - (name_flag_w - 60), screenPos.y() - (name_flag_h - 20));
    }
}

void RoutePlanLayer::paint(QPainter& painter, MapCamera& camera)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    QPainterPath demo_path;
    demo_path.moveTo(0,0);
    demo_path.lineTo(1,1);
    painter.drawPath(demo_path);

    RoutePathInfo * select_route_path_ptr = nullptr;
    //draw Light color
    for (size_t i = 0; i < m_route_data_ptr->m_path_set.size(); i++)
    {
        auto &path_item = m_route_data_ptr->m_path_set.at(i);
        auto &path_pos = m_route_data_ptr->m_path_set.at(i).path_latlon_set;

        if ( path_pos.size() <= 1)
            continue;

        if (m_route_data_ptr->getSelectPathIndex() == (int)i)  {
            select_route_path_ptr = &path_item;
            continue;
        }


        if (path_item.type == RPT_PATH_ENGINE) {
            painter.setPen(getPenByLevel(0));
        } else if (path_item.type == RPT_PATH_BMAP) {
            painter.setPen(getPenByLevel(3));
        } else if (path_item.type == RPT_PATH_AMAP) {
            painter.setPen(getPenByLevel(4));
        }

        QPainterPath path;
        for (size_t j = 0; j < path_pos.size(); j++)
        {
            QPointF screenPos;
            camera.worldToScreen(&path_pos.at(j), screenPos);
            if (j == 0) {
               path.moveTo(screenPos);
            } else {
               path.lineTo(screenPos);
            }

        }
        painter.drawPath(path);
    }

    //draw select dark path
    if (select_route_path_ptr) {

        auto &point_set = select_route_path_ptr->path_latlon_set;

        if (select_route_path_ptr->type == RPT_PATH_ENGINE) {
            painter.setPen(getPenByLevel(1));
        } else if (select_route_path_ptr->type == RPT_PATH_BMAP) {
            painter.setPen(getPenByLevel(3));
        } else if (select_route_path_ptr->type == RPT_PATH_AMAP) {
            painter.setPen(getPenByLevel(4));
        }

        QPainterPath path;
        for (size_t j = 0; j < point_set.size(); j++)
        {
            QPointF screenPos;
            camera.worldToScreen(&point_set.at(j), screenPos);
            if (j == 0) {
               path.moveTo(screenPos);
            } else {
               path.lineTo(screenPos);
            }
        }
        painter.drawPath(path);
    }


    //draw select Highlight color
    if (m_select_link_geos.size() > 1) {
        QPainterPath path;
        painter.setPen(getPenByLevel(2));
        for (size_t i = 0; i < m_select_link_geos.size(); i++) {
            auto latlon = m_select_link_geos.at(i);
            QPointF screenPos;
            RGeoPoint worldPt(latlon.CoordX_, latlon.CoordY_);
            camera.worldToScreen(&worldPt, screenPos);
            if (i == 0) {
               path.moveTo(screenPos);
            } else {
               path.lineTo(screenPos);
            }
        }
        painter.drawPath(path);

    }

    drawPathNameFlag();

    drawRouteFlag(painter, camera);

    if (is_show_search_poi_flag) {
        QPointF screenPos;
        camera.worldToScreen(&m_search_point, screenPos);
        updateRouteFlag(painter, m_search_poi_flag, screenPos);

    }

}

void RoutePlanLayer::updateRouteFlag(QPainter& painter, QImage &flag, QPointF &screenPos)
{
    int w,h;
    m_camera->getScreenSize(w,h);
    if (screenPos.x() < 0 || screenPos.x() > w || screenPos.y() < 0 || screenPos.y() > h)
        return;

     painter.drawImage(screenPos.x() - (flag_w/2), screenPos.y() - flag_h, flag);
}

void RoutePlanLayer::drawRouteFlag(QPainter& painter, MapCamera& camera)
{
    auto itr = m_route_data_ptr->getRoutePoint(RPT_START_POINT);
    if (itr && itr->size() > 0) {
        itr->at(0).drawFlag(painter, camera);
    }

    itr = m_route_data_ptr->getRoutePoint(RPT_END_POINT);
    if (itr && itr->size() > 0) {
        itr->at(0).drawFlag(painter, camera);
    }

    itr = m_route_data_ptr->getRoutePoint(RPT_WAY_POINT);
    if (itr && itr->size() > 0) {
        for (auto &item : *itr) {
             item.drawFlag(painter, camera);
        }

    }

}

void RoutePlanLayer::onContextMenuRequested(const QPoint& pos)
{
    m_contextMenu.show();
    m_contextMenu.popup(pos);
    m_screen_pos = pos;
}

void RoutePlanLayer::setupContextMenu()
{
    m_contextMenu.setParent(m_map_widget_ptr);

    QAction *start_action = m_contextMenu.addAction(QIcon(":/MainWindow/resources/s.png"), tr("Start point"));
    QAction *way_action = m_contextMenu.addAction(QIcon(":/MainWindow/resources/w.png"), tr("Way point"));
    QAction *end_action = m_contextMenu.addAction(QIcon(":/MainWindow/resources/e.png"), tr("End point"));
    QAction *poi_Search_action = m_contextMenu.addAction(QIcon(":/MainWindow/resources/sign_post.png"), tr("Search point"));

    // 连接信号和槽
    connect(start_action, &QAction::triggered, this, &RoutePlanLayer::onAddStartPoint);
    connect(way_action, &QAction::triggered, this, &RoutePlanLayer::onAddWayPoint);
    connect(end_action, &QAction::triggered, this, &RoutePlanLayer::onAddEndPoint);
    connect(poi_Search_action, &QAction::triggered, this, &RoutePlanLayer::onSetPoiSearchCenter);

    m_contextMenu.hide();

}


void RoutePlanLayer::initPathFlag(QLabel &flag, QPixmap &pixmap)
{
    flag.setParent(m_map_widget_ptr);
    flag.setPixmap(pixmap);
    // 设置窗口无边框、置顶并透明背景（可选）
    flag.setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    flag.setAttribute(Qt::WA_TranslucentBackground);
    flag.hide();
}

void RoutePlanLayer::onAddStartPoint()
{
    RGeoPoint latlng_pos;
    m_camera->screenToWorld(m_screen_pos, &latlng_pos) ;
    setStartPoint(&latlng_pos);
}

void RoutePlanLayer::onSetStartPoint(RGeoPoint *point)
{
    if (!point)
        return;

    setStartPoint(point);
}

void RoutePlanLayer::onAddWayPoint()
{
    RGeoPoint latlng_pos;
    m_camera->screenToWorld(m_screen_pos, &latlng_pos);
    setWayPoint(&latlng_pos);
}

void RoutePlanLayer::onSetWayPoint(RGeoPoint *point)
{
    if (!point)
        return;

    setWayPoint(point);
}

void RoutePlanLayer::onAddEndPoint()
{
    RGeoPoint latlng_pos;
    m_camera->screenToWorld(m_screen_pos, &latlng_pos);
    setEndPoint(&latlng_pos);
}

void RoutePlanLayer::onSetEndPoint(RGeoPoint *point)
{
    if (!point)
        return;

    setEndPoint(point);
}

void RoutePlanLayer::onSetPoiSearchCenter()
{
    RGeoPoint latlon_pos;
    m_camera->screenToWorld(m_screen_pos, &latlon_pos);
    setPoiSearchCenter(&latlon_pos);
}

void RoutePlanLayer::setStartPoint(RGeoPoint *point)
{
    m_contextMenu.hide();
    RoutePointInfo * route_point = m_route_data_ptr->addRoutePoint(point, RPT_START_POINT);

    if (m_render_engine_layer_ptr) {
        int ret = m_render_engine_layer_ptr->setRoutePointToEngine(point, RPT_START_POINT);
        if (route_point) {
            route_point->mark_id = ret;
        }
    }

    if (g_app->getMainWindow()) {
        g_app->getMainWindow()->startCaclPath();
    }
}

void RoutePlanLayer::setWayPoint(RGeoPoint *point)
{
    m_contextMenu.hide();
    RoutePointInfo * route_point = m_route_data_ptr->addRoutePoint(point, RPT_WAY_POINT);

    if (m_render_engine_layer_ptr) {
        int ret = m_render_engine_layer_ptr->setRoutePointToEngine(point, RPT_WAY_POINT);
        if (route_point) {
            route_point->mark_id = ret;
        }
    }

    if (g_app->getMainWindow()) {
        g_app->getMainWindow()->startCaclPath();
    }

}

void RoutePlanLayer::setEndPoint(RGeoPoint *point)
{
    m_contextMenu.hide();
    RoutePointInfo * route_point = m_route_data_ptr->addRoutePoint(point, RPT_END_POINT);

    if (m_render_engine_layer_ptr) {
        int ret = m_render_engine_layer_ptr->setRoutePointToEngine(point, RPT_END_POINT);
        if (route_point) {
            route_point->mark_id = ret;
        }
    }

    if (g_app->getMainWindow()) {
        g_app->getMainWindow()->startCaclPath();
    }
}

void RoutePlanLayer::setPoiSearchCenter(RGeoPoint *point)
{
    if (!point)
        return;

    m_contextMenu.hide();
    m_search_point = *point;
    is_show_search_poi_flag = true;
    emit updatePoiSearchPos(m_search_point.CoordX_, m_search_point.CoordY_);
}


void RoutePlanLayer::setRoutePathResult(const aurora::path::PathQueryPtr& query, const aurora::path::PathResultPtr& result)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    std::cout << "Path result received" << "path_result->size(): " << result->paths.size() << std::endl;

    int start_index = m_route_data_ptr->m_path_set.size(); // 高德和百度会优先插入路线数组
    for (size_t i = 0; i < result->paths.size(); i++)
    {

       std::vector<RGeoPoint> latlon_path_set;
       for (const auto &point : result->paths.at(i).points) {
           latlon_path_set.push_back(RGeoPoint(point.lng(), point.lat()));
       }
       RoutePathInfo path_item;
       path_item.type = RPT_PATH_ENGINE;
       path_item.path_latlon_set = latlon_path_set;
       path_item.path_id = result->paths.at(i).path_id;
       path_item.length= result->paths.at(i).length;
       path_item.travel_time = result->paths.at(i).travel_time;
       path_item.traffic_light_num = result->paths.at(i).traffic_light_num;
       path_item.label_name_ptr = std::make_shared<QLabel>();
       QString label_info = QString("%1%2").arg(QString::fromLocal8Bit("路线")).arg(i + 1);
       m_route_data_ptr->setPathLableName(RPT_PATH_ENGINE, label_info, path_item.label_name_ptr);

       m_route_data_ptr->m_path_set.push_back(path_item);
    }

    if (m_render_engine_layer_ptr) {
        m_render_engine_layer_ptr->setRoutePathResult(query, result);
    }

    if (result->paths.size() > 0) {
        if (m_route_data_ptr->isPathSelectChange(start_index)) {
            updateSelectPathId();
        }

        auto select_path = m_route_data_ptr->getRoutePath(start_index);
        m_route_data_ptr->highlightNameFlag(select_path->label_name_ptr, true);
        MapEngineManage::GetInstance()->getGuideInterface()->SetNavPath(query, result, select_path->path_id);


    }

   emit needRedraw();
}

void RoutePlanLayer::setOnlineMapRoute(std::vector<float64>& route, ENUM_MAPTYPE type)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    RoutePathInfo path_item;
    path_item.label_name_ptr = std::make_shared<QLabel>();

    QString label_info;
    if (type == BAIDU_MAP) {
        path_item.type = RPT_PATH_BMAP;
        label_info = QString::fromLocal8Bit("百度");
        m_route_data_ptr->setPathLableName(RPT_PATH_BMAP, label_info, path_item.label_name_ptr);
    } else if (type == AMAP) {
        path_item.type = RPT_PATH_AMAP;
        label_info = QString::fromLocal8Bit("高德");
        m_route_data_ptr->setPathLableName(RPT_PATH_AMAP, label_info, path_item.label_name_ptr);
    }

    for (size_t j = 0; j < route.size(); j++)
    {
        RGeoPoint worldPt(route.at(j),route.at(j+1));
        path_item.path_latlon_set.push_back(worldPt);

        j = j+1;
    }


    m_route_data_ptr->addRoutePath(path_item);

}

void RoutePlanLayer::onMouseDown(QMouseEvent* event, MapCamera& camera)
{
    if (event->button() == Qt::RightButton) {
        onContextMenuRequested(event->pos());
    } else if (event->button() == Qt::LeftButton) {
        std::lock_guard<std::mutex> lock(m_mutex);

        m_contextMenu.hide();
        bool is_change = false;
        for (size_t i = 0; i < m_route_data_ptr->m_path_set.size(); i++)
        {
            auto label = m_route_data_ptr->m_path_set.at(i).label_name_ptr;

            QRect rect = label->geometry(); //Tab 界面点选判断
            if (rect.contains(event->pos())) {
                if (m_route_data_ptr->isPathSelectChange(i)) {
                    is_change = true;
                    break;
                }
            }
        }

        if (!is_change) // 线路点选判断
        {
            int select_index = onCheckSelectRoutePath(event->pos());
            if (select_index >= 0 && m_route_data_ptr->isPathSelectChange(select_index)) {
                is_change = true;
            }
        }

        if(is_change)
        {
            updateSelectPathId();
            m_select_link_geos.clear();
            for (size_t i = 0; i < m_route_data_ptr->m_path_set.size(); i++) {
                auto &path_item = m_route_data_ptr->m_path_set.at(i);
                if (m_route_data_ptr->getSelectPathIndex() != (int)i) {
                    m_route_data_ptr->highlightNameFlag(path_item.label_name_ptr, false);
                } else {
                    m_route_data_ptr->highlightNameFlag(path_item.label_name_ptr, true);
                    uint64_t path_id = m_route_data_ptr->getSelectPathID();
                    if ( path_id != 0 ) {
                       MapEngineManage::GetInstance()->getGuideInterface()->SwitchMainPathId(path_id);
                    } else {
                        if (path_item.type == RPT_PATH_BMAP) {
                            g_app->showActionListWidget(BAIDU_MAP);

                        } else if (path_item.type == RPT_PATH_AMAP) {
                            g_app->showActionListWidget(AMAP);
                        }
                    }
                }
            }
            emit needRedraw();
        }
    }
}

void RoutePlanLayer::updateSelectPathId()
{
    auto rc = MapEngineManage::GetInstance()->m_routingCenter_ptr;
    if (rc) {
        uint64_t id = m_route_data_ptr->getSelectPathID();
        if (id > 0)
           emit rc->updateSelectPathId(id);
    }

}

void RoutePlanLayer::setSelectTbtLink(std::vector<aurora::PointXY<double>> &m_link_geos)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_link_geos.size() == 0)
        return;

    m_select_link_geos.clear();
    for (auto &item : m_link_geos) {
        map_engine::RGeoPoint pos(item.x(), item.y());
        m_select_link_geos.push_back(pos);
    }

   int mid_index = m_select_link_geos.size() / 2;
   m_map_widget_ptr->setWorldCenter(&m_select_link_geos.at(mid_index));
   emit needRedraw();
}

void RoutePlanLayer::setOnlineSelectActionCoords(std::vector<float64>* coords, ENUM_MAPTYPE type)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (coords->size() < 1)
        return;

    m_select_link_geos.clear();
    for (size_t i = 0; i < coords->size(); i++) {

        map_engine::RGeoPoint pos;
        if (type == BAIDU_MAP)
        {
            bd_decrypt(coords->at(i + 1), coords->at(i), pos.CoordY_, pos.CoordX_);
        } else {
            pos.CoordX_ = coords->at(i);
            pos.CoordY_ = coords->at(i + 1);
        }

        m_select_link_geos.push_back(pos);
        i +=1;
    }

    int mid_index = m_select_link_geos.size() / 2;
    m_map_widget_ptr->setWorldCenter(&m_select_link_geos.at(mid_index));
    emit needRedraw();

}

void RoutePlanLayer::clearRoutePath()
{
    m_route_data_ptr->removeAllRoutePath();
    m_select_link_geos.clear();

    if (m_render_engine_layer_ptr)
        m_render_engine_layer_ptr->deleteAllRoutePathFromEngine();

    emit needRedraw();
}

void RoutePlanLayer::clearRoutePathAndPoints()
{
    m_route_data_ptr->reset();
    m_select_link_geos.clear();

    if (m_render_engine_layer_ptr) {
        m_render_engine_layer_ptr->deleteAllRoutePathFromEngine();
        m_render_engine_layer_ptr->deleteAllRoutePointFromEngine();
    }

    emit needRedraw();
}

int RoutePlanLayer::onCheckSelectRoutePath(const QPointF& screenPt)
{
    int select_path_id = -1;
    double min_dist = DBL_MAX;
    for (size_t i = 0; i < m_route_data_ptr->m_path_set.size(); i++)
    {

        if (m_route_data_ptr->m_path_set.at(i).path_latlon_set.size() <= 1)
            continue;

        auto &path_set = m_route_data_ptr->m_path_set.at(i).path_latlon_set;
        std::vector<QPointF> match_screen_pos_set;
        for (auto &item : path_set) {
            QPointF screen_pos;
            m_camera->worldToScreen(&item, screen_pos);
            match_screen_pos_set.push_back(screen_pos);
        }

        double dis = util::pointToPolylineDistance(screenPt, match_screen_pos_set);
        if (dis > 10.0) { //距离超过10个像素忽略
            continue;
        }

        if (dis < min_dist) {
            select_path_id = i;
            min_dist = dis;
        }
    }

   return select_path_id;
}


void RoutePlanLayer::removeRoutePath(RoutePathType type)
{
    m_route_data_ptr->removeRoutePath(type);

    ENUM_MAPTYPE online_map_type = MAP_INVAID;
    if (type == RPT_PATH_BMAP)
        online_map_type = BAIDU_MAP;
    else if (type == RPT_PATH_AMAP)
        online_map_type = AMAP;

    if (g_app->getActionListWidgetMapType() == online_map_type) {
        g_app->clearActionListWidget();
        m_select_link_geos.clear();
    }

    emit needRedraw();
}

RoutePointSet * RoutePlanLayer::getRoutePoint(RoutePointType type)
{
    return m_route_data_ptr->getRoutePoint(type);
}

void RoutePlanLayer::removeRoutePoint(RoutePointType type, int index )
{
    m_route_data_ptr->removeRoutePoint(type, index);
}

void RoutePlanLayer::onSetStartPointSlot(double lng, double lat)
{
    RGeoPoint pt(lng, lat);
    setStartPoint(&pt);
}

void RoutePlanLayer::onSetEndPointSlot(double lng, double lat)
{
    RGeoPoint pt(lng, lat);
    setEndPoint(&pt);
}

void RoutePlanLayer::onSetWayPointSlot(double lng, double lat)
{
    RGeoPoint pt(lng, lat);
    setWayPoint(&pt);
}

void RoutePlanLayer::onSetPoiSearchCenterSlot(double lng, double lat)
{
    RGeoPoint pt(lng, lat);
    setPoiSearchCenter(&pt);
}
