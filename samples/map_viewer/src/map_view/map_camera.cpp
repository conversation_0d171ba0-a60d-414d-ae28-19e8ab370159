﻿
#include "map_camera.h"
#include <iostream>
#include "utils/util_type_define.h"
#include "map_render_module.h"
#include "main_window.h"
#include "map_widget_manage.h"
#include "render_engine_layer.h"

const double MapCamera::kDefaultScaleValue = 1e-5;
const double ScaleRatioValue = 1.5;
static const int32 MAPDATA_CONVERSION = 100000;
static const int32 MAPDATA_CONVERSION_DM6 = 1000000;
const double MAX_SCALE_VALUE = 0.001;
const double MIN_SCALE_VALUE = 0.000005;

inline static int32 ConvertCoord(float64 lon)
{
    return (int32)(lon * MAPDATA_CONVERSION);
}

inline static float64 InvConvertCoord(int32 lon)
{
    return (float64)lon / MAPDATA_CONVERSION;
}

inline static int32 ConvertCoord2(float64 lon)
{
    return (int32)(lon * MAPDATA_CONVERSION_DM6);
}

inline static float64 InvConvertCoord2(int32 lon)
{
    return (float64)lon / MAPDATA_CONVERSION_DM6;
}

MapCamera::MapCamera()
	: m_worldCenter({ 121.43414,31.20152 }),
	m_screenWidth(100), m_screenHeight(100),
    m_scale(kDefaultScaleValue),
    m_render_layer_ptr(nullptr),
    m_render_service_ptr(nullptr)
{
	setProjection(PROJECTION_PLATE_CARREE);
	m_worldCenterConverCoord = m_yFunc(m_worldCenter.CoordY_);
}

MapCamera::~MapCamera()
{

}

void MapCamera::worldToScreen(const RGeoPoint* coord, QPointF& screenPt) const
{
    if (isMapRenderServiceVisble()) {
        MapRenderMapToScreen(coord, screenPt);
    } else {
        screenPt.setX(m_screenWidth / 2.0 + (coord->CoordX_ - m_worldCenter.CoordX_) / m_scale);
        //screenPt.setY(m_screenHeight / 2.0 - (m_yFunc(coord->lat) - m_yFunc(m_worldCenter.lat)) / m_scale);
        screenPt.setY(m_screenHeight / 2.0 - (m_yFunc(coord->CoordY_) - m_worldCenterConverCoord) / m_scale);
    }

}

void MapCamera::pinTo(const RGeoPoint* coord, const QPointF& screenPt)
{
	RGeoPoint newWorldCenter;

	newWorldCenter.CoordX_ = coord->CoordX_ + (m_screenWidth / 2.0 - screenPt.x()) * m_scale;
	newWorldCenter.CoordY_ = m_yInvFunc(m_yFunc(coord->CoordY_) + (screenPt.y() - m_screenHeight / 2.0) * m_scale);

	setWorldCenter(&newWorldCenter);
}


void MapCamera::setWorldCenter(const RGeoPoint* center)
{
    m_worldCenter = *center;
    m_worldCenterConverCoord = m_yFunc(m_worldCenter.CoordY_);
}

/* render engine view, s */
void MapCamera::getWorldCenter(RGeoPoint * center) const
{
	center->CoordX_ = m_worldCenter.CoordX_;
	center->CoordY_ = m_worldCenter.CoordY_;
}
/* render engine view, e */

void MapCamera::getWorldCenter(GeoPoint * center) const
{
    center->CoordX_ = ConvertCoord(m_worldCenter.CoordX_);
    center->CoordY_ = ConvertCoord(m_worldCenter.CoordY_);
}

void MapCamera::getWorldCenter2(GeoPoint * center) const
{
    center->CoordX_ = ConvertCoord2(m_worldCenter.CoordX_);
    center->CoordY_ = ConvertCoord2(m_worldCenter.CoordY_);
}

void MapCamera::ConvertLonLanCoord(const RGeoPoint* coordLL, GeoPoint &coord)
{
    coord.CoordX_ = ConvertCoord(coordLL->CoordX_);
    coord.CoordY_ = ConvertCoord(coordLL->CoordY_);
}

void MapCamera::setWorldCenter(const GeoPoint* center)
{
    m_worldCenter.CoordX_ = InvConvertCoord(center->CoordX_);
    m_worldCenter.CoordY_ = InvConvertCoord(center->CoordY_);
    m_worldCenterConverCoord = m_yFunc(m_worldCenter.CoordY_);
}

void MapCamera::setWorldCenter2(const GeoPoint* center)
{
    m_worldCenter.CoordX_ = InvConvertCoord2(center->CoordX_);
    m_worldCenter.CoordY_ = InvConvertCoord2(center->CoordY_);
    m_worldCenterConverCoord = m_yFunc(m_worldCenter.CoordY_);
}

void MapCamera::resetScreenSize(int w, int h)
{
	if (w > 0 && h > 0)
	{
		m_screenWidth = w;
		m_screenHeight = h;
	}
}

void MapCamera::getScreenSize(int& w, int& h)
{
	w = m_screenWidth;
	h = m_screenHeight;
}

void MapCamera::worldToScreen(const GeoPoint* worldPt, QPointF& screenPt) const
{
    RGeoPoint worldFpPt;
    worldFpPt.CoordX_ = InvConvertCoord(worldPt->CoordX_);
    worldFpPt.CoordY_ = InvConvertCoord(worldPt->CoordY_);
    worldToScreen(&worldFpPt, screenPt);
}

void MapCamera::worldToScreen2(const GeoPoint* worldPt, QPointF& screenPt) const
{
    RGeoPoint worldFpPt;
    worldFpPt.CoordX_ = InvConvertCoord2(worldPt->CoordX_);
    worldFpPt.CoordY_ = InvConvertCoord2(worldPt->CoordY_);
    worldToScreen(&worldFpPt, screenPt);
}

void MapCamera::getWorldRectLatLon(LLRect* worldRect) const
{
    worldRect->CoordX1 = (float64)((m_worldCenter.CoordX_ - (m_screenWidth * m_scale) / 2));
    worldRect->CoordX2 = (float64)((m_worldCenter.CoordX_ + (m_screenWidth * m_scale) / 2));
    worldRect->CoordY1 = (float64)((m_worldCenter.CoordY_ - (m_screenHeight * m_scale) / 2));
    worldRect->CoordY2 = (float64)((m_worldCenter.CoordY_ + (m_screenHeight * m_scale) / 2));
}

void MapCamera::getWorldRectLatLon(const RGeoPoint* center, int width, int height, LLRect *worldRect) const
{
    worldRect->CoordX1 = (float64)((center->CoordX_ - (width * m_scale) / 2));
    worldRect->CoordX2 = (float64)((center->CoordX_ + (width * m_scale) / 2));
    worldRect->CoordY1 = (float64)((center->CoordY_ - (height * m_scale) / 2));
    worldRect->CoordY2 = (float64)((center->CoordY_ + (height * m_scale) / 2));
}

void MapCamera::getWorldRect(Rect* worldRect) const
{
    worldRect->CoordX1 = (int32)((m_worldCenter.CoordX_ - (m_screenWidth * m_scale) / 2) * MAPDATA_CONVERSION);
    worldRect->CoordX2 = (int32)((m_worldCenter.CoordX_ + (m_screenWidth * m_scale) / 2) * MAPDATA_CONVERSION) + 1;
    worldRect->CoordY1 = (int32)((m_worldCenter.CoordY_ - (m_screenHeight * m_scale) / 2) * MAPDATA_CONVERSION);
    worldRect->CoordY2 = (int32)((m_worldCenter.CoordY_ + (m_screenHeight * m_scale) / 2) * MAPDATA_CONVERSION) + 1;
}

void MapCamera::getWorldRect2(Rect* worldRect) const
{
    worldRect->CoordX1 = (int32)((m_worldCenter.CoordX_ - (m_screenWidth * m_scale) / 2) * MAPDATA_CONVERSION_DM6);
    worldRect->CoordX2 = (int32)((m_worldCenter.CoordX_ + (m_screenWidth * m_scale) / 2) * MAPDATA_CONVERSION_DM6) + 1;
    worldRect->CoordY1 = (int32)((m_worldCenter.CoordY_ - (m_screenHeight * m_scale) / 2) * MAPDATA_CONVERSION_DM6);
    worldRect->CoordY2 = (int32)((m_worldCenter.CoordY_ + (m_screenHeight * m_scale) / 2) * MAPDATA_CONVERSION_DM6) + 1;
}

void MapCamera::fitWorldRect(const Rect* worldRect)
{
    m_worldCenter.CoordX_ = (worldRect->CoordX1 + (Rect_getWidth(*worldRect) / 2)) / MAPDATA_CONVERSION;
    m_worldCenter.CoordY_ = (worldRect->CoordY1 + (Rect_getHeight(*worldRect) / 2)) / MAPDATA_CONVERSION;
    m_scale = std::max((double)Rect_getHeight(*worldRect) / MAPDATA_CONVERSION / m_screenHeight, (double)Rect_getWidth(*worldRect) / MAPDATA_CONVERSION / m_screenWidth);
}

void MapCamera::zoomIn()
{
    m_scale *= ScaleRatioValue;
}

void MapCamera::zoomIn(QWheelEvent *e)
{
    if (isMapRenderServiceVisble()) {
        float current_scale = m_render_layer_ptr->getRenderScale();
        double y_angle = (e->angleDelta().y() / 8.0);  // 转换为度数
        double new_scale = current_scale + y_angle * 0.02;
        std::cout << "zoomIn current_scale: " << current_scale << ",y_angle : " << y_angle << ",new_scale : " << new_scale << std::endl;
        m_render_layer_ptr->setRenderScale(new_scale);

    } else {
        /* render engine view, s */
        //RGeoPoint worldPt;

        //screenToWorld(screenPt, &worldPt);
        zoomIn();
        //pinTo(&worldPt, screenPt);
        /* render engine view, e */
    }

}

void MapCamera::zoomOut()
{
    m_scale /= ScaleRatioValue;
}

void MapCamera::zoomOut(QWheelEvent *e)
{
    if (isMapRenderServiceVisble()) {
        float current_scale = m_render_layer_ptr->getRenderScale();
        double y_angle = e->angleDelta().y() / 8.0;  // 转换为度数
        double new_scale = current_scale + y_angle * 0.02;
        std::cout << "zoomOut current_scale: " << current_scale << ",y_angle : " << y_angle << ",new_scale : " << new_scale << std::endl;
        m_render_layer_ptr->setRenderScale(new_scale);

    } else {
        /* render engine view, s */
        //RGeoPoint worldPt;

        //screenToWorld(screenPt, &worldPt);
        zoomOut();
        //pinTo(&worldPt, screenPt);
        /* render engine view, e */
    }

}

void MapCamera::screenToWorld(int x, int y, double &out_x, double &out_y) const
{
    QPointF screenPt(x,y);
    RGeoPoint worldFpPt;
    screenToWorld(screenPt, &worldFpPt);
    out_x = worldFpPt.CoordX_;
    out_y = worldFpPt.CoordY_;
}

void MapCamera::screenToWorld(const QPointF& screenPt, RGeoPoint* coord) const
{
    if (isMapRenderServiceVisble()) {
        MapRenderScreenToMap(screenPt, coord);

    } else {
        RGeoPoint worldFpPt = m_worldCenter;
        worldFpPt.CoordX_ += (screenPt.x() - m_screenWidth / 2.0) * m_scale;
        worldFpPt.CoordY_ = m_yInvFunc((m_screenHeight / 2.0 - screenPt.y()) * m_scale + m_yFunc(m_worldCenter.CoordY_));
        *coord = worldFpPt;
    }
}

void MapCamera::screenToWorld(const QPointF& screenPt, GeoPoint* worldPt) const
{
    RGeoPoint worldFpPt;
    screenToWorld(screenPt, &worldFpPt);
    worldPt->CoordX_ = ConvertCoord(worldFpPt.CoordX_);
    worldPt->CoordY_ = ConvertCoord(worldFpPt.CoordY_);
}

void MapCamera::screenToWorld2(const QPointF& screenPt, GeoPoint* worldPt) const
{
    RGeoPoint worldFpPt;
    screenToWorld(screenPt, &worldFpPt);
    worldPt->CoordX_ = ConvertCoord2(worldFpPt.CoordX_);
    worldPt->CoordY_ = ConvertCoord2(worldFpPt.CoordY_);
}

static double _yFuncPlateCarree(double lat)
{
	return lat;
}
static double _yInvFuncPlateCarree(double y)
{
	return y;
}

static double _yFuncEquirect40(double lat)
{
	const double COS_PHI_1 = cos(40 * M_PI / 180);
	return lat / COS_PHI_1;
}
static double _yInvFuncEquirect40(double y)
{
	const double COS_PHI_1 = cos(40 * M_PI / 180);
	return y * COS_PHI_1;
}

static double _yFuncMercator(double lon)
{
	return log(tan(M_PI_4 + lon * M_PI / 360)) * 180 / M_PI;
}

static double _yInvFuncMercator(double y)
{
	return (atan(exp(y * M_PI / 180)) - M_PI_4) * 360 / M_PI;
}

/**
Get the DPI(dot per inch or pixel per inch) of the screen.
iPhone 3 has 163 pixel per inch.
iPhone 4 Retina has 326 pixel per inch.
*/
static uint32 _getScreenDpi()
{
	return 300;
}

bool MapCamera::setProjection(Projection projection)
{
	bool succ = true;

	switch (projection)
	{
	case PROJECTION_PLATE_CARREE:
		m_yFunc = _yFuncPlateCarree;
		m_yInvFunc = _yInvFuncPlateCarree;
		break;
	case PROJECTION_EQUIRECTANGULAR_40:
		m_yFunc = _yFuncEquirect40;
		m_yInvFunc = _yInvFuncEquirect40;
		break;
	case PROJECTION_MERCATOR:
		m_yFunc = _yFuncMercator;
		m_yInvFunc = _yInvFuncMercator;
		break;
	default:
		succ = false;
	}

	if (succ) {
		m_worldCenterConverCoord = m_yFunc(m_worldCenter.CoordY_); // 重新计算中心点位置
		m_projection = projection;
	}

	return succ;
}

#define EARTH_RADIUS_IN_METER	6371000

double MapCamera::getScaleDenominator() const
{
	return (m_scale * EARTH_RADIUS_IN_METER * M_PI / 180) / (METER_PER_INCH / _getScreenDpi());
}
// 计算以中心点为中心，宽高为width和height米的矩形区域
std::array<RGeoPoint, 4> MapCamera::calculateGeoRect(const RGeoPoint& center, double width, double height) {
    // 将角度转换为弧度
    auto deg2rad = [](double deg) {
        return deg * M_PI / 180.0;
    };

    // 将弧度转换为角度
    auto rad2deg = [](double rad) {
        return rad * 180.0 / M_PI;
    };

    // 计算南北方向的纬度偏移（单位：度）
    double latOffset = rad2deg(height / (2.0 * EARTH_RADIUS));

    // 计算东西方向的经度偏移（单位：度）
    // 需要考虑纬度对经度距离的影响（cos(latitude)）
    double lonOffset = rad2deg(width / (2.0 * EARTH_RADIUS * std::cos(deg2rad(center.CoordX_))));

    // 计算矩形的四个顶点
    std::array<RGeoPoint, 4> rect;
    rect[0] = {center.CoordX_ - lonOffset, center.CoordY_ + latOffset};  // 左上角
    rect[1] = {center.CoordX_ + lonOffset, center.CoordY_ + latOffset};  // 右上角
    rect[2] = {center.CoordX_ + lonOffset, center.CoordY_ - latOffset};  // 右下角
    rect[3] = {center.CoordX_ - lonOffset, center.CoordY_ - latOffset};  // 左下角

    return rect;
}

bool MapCamera::isScaleOutRange(bool zoomType)
{
    if (isMapRenderServiceVisble()) {
        if (zoomType) {
            if (m_render_layer_ptr->getRenderScale() < 3.0)
                return true;
        } else {
            if (m_render_layer_ptr->getRenderScale() > 17.0 )
                return true;
        }

    } else {

        if (zoomType) {
            if (m_scale >= MAX_SCALE_VALUE)
                return true;
        } else {
            if (m_scale <= MIN_SCALE_VALUE )
                return true;
        }
    }

    return false;
}

void MapCamera::setMapRenderService(RenderEngineLayer *render_layer_ptr,  aurora::IMapRenderService *map_render_service_ptr)
{
    m_render_layer_ptr = render_layer_ptr;
    m_render_service_ptr = map_render_service_ptr;
}

void MapCamera::MapRenderScreenToMap(const QPointF& screenPt, RGeoPoint* coord) const
{    if (m_render_service_ptr) {
        aurora::Point64 screen;
        screen.x_ = screenPt.x();
        screen.y_ = m_screenHeight -screenPt.y();
        aurora::Point64 latlon = m_render_service_ptr->ScreenToMap(screen);
        coord->CoordX_ = latlon.x_;
        coord->CoordY_ = latlon.y_;
    }
}

void MapCamera::MapRenderMapToScreen(const RGeoPoint* coord, QPointF& screenPt) const
{
    if (m_render_service_ptr) {
        aurora::Point64 map_pos;
        map_pos.x_ = coord->CoordX_;
        map_pos.y_ = coord->CoordY_;
        aurora::Point64 screen = m_render_service_ptr->MapToScreen(map_pos);
        screenPt.setX(screen.x_);
        screenPt.setY((m_screenHeight -screen.y_));
    }
}

bool MapCamera::isMapRenderServiceVisble() const
{
    if (m_render_layer_ptr) {
        return m_render_layer_ptr->isVisible();
    }

    return false;
}
