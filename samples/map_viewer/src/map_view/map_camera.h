﻿
#ifndef MAP_CAMERA_H
#define MAP_CAMERA_H

#include "map_engine_manage.h"
#include "geo_point.h"
#include <qpoint.h>
#include <QWheelEvent>

using namespace map_engine;

/**	@brief 地图窗体的摄像机
	@details
		提供以下功能：
			* 世界坐标与屏幕坐标之间相互转换。
			* 获取及修改投影方式
			* 获取及修改地图显示参数(中心点、显示比例等)
*/

class RenderEngineLayer;

class MapCamera
{
public:
	MapCamera();
	virtual ~MapCamera();

public:
	void worldToScreen(const RGeoPoint* coord, QPointF& screenPt) const;
	void pinTo(const RGeoPoint* coord, const QPointF& screenPt);

	void resetScreenSize(int w, int h);
	void getScreenSize(int& w, int& h);

	void setWorldCenter(const RGeoPoint* center);
    void setWorldCenter(const GeoPoint* center);
	void setWorldCenter2(const GeoPoint* center);
    void getWorldCenter(GeoPoint * center) const;
	void getWorldCenter2(GeoPoint * center) const;
	/* render engine view, s */
	void getWorldCenter(RGeoPoint * center) const;
	/* render engine view, e */

    void worldToScreen(const GeoPoint* worldPt, QPointF& screenPt) const;
    void screenToWorld(const QPointF& screenPt, GeoPoint* worldPt) const;
	void worldToScreen2(const GeoPoint* worldPt, QPointF& screenPt) const;
	void screenToWorld2(const QPointF& screenPt, GeoPoint* worldPt) const;
    void screenToWorld(const QPointF& screenPt, RGeoPoint* coord) const;
    void screenToWorld(int x, int y, double &out_x, double &out_y) const;

    void getWorldRect(Rect* worldRect) const;
    void getWorldRect2(Rect* worldRect) const;
    void getWorldRectLatLon(LLRect* worldRect) const;
    void getWorldRectLatLon(const RGeoPoint* center, int width, int height, LLRect *worldRect) const; //add new func
    void fitWorldRect(const Rect* worldRect);

	void zoomIn();
    void zoomIn(QWheelEvent *e);
	void zoomOut();
    void zoomOut(QWheelEvent *e);

    void setScale(double &scale) { m_scale = scale; }
	double getScale() const { return m_scale; }
    bool isScaleOutRange(bool zoomType);

	double getScaleDenominator() const; // 地图上距离与实际距离的比例分母，例如100，即地图上1cm表示实际距离为1m

    std::array<RGeoPoint, 4> calculateGeoRect(const RGeoPoint& center, double width, double height);
    void ConvertLonLanCoord(const RGeoPoint* coordLL, GeoPoint &coord);

	enum Projection {
		/** 等经纬度投影
		*/
		PROJECTION_PLATE_CARREE = 0,

		/**  */
		PROJECTION_EQUIRECTANGULAR_40 = 1,

		/** 墨卡托投影
		*/
		PROJECTION_MERCATOR = 2
	};

	Projection getProjection() const { return m_projection; }
	bool setProjection(Projection projection);

    void setMapRenderService(RenderEngineLayer *render_layer_ptr,  aurora::IMapRenderService *map_render_service_ptr);

private:
        bool isMapRenderServiceVisble() const;
        void MapRenderScreenToMap(const QPointF& screenPt, RGeoPoint* coord) const;
        void MapRenderMapToScreen(const RGeoPoint* coord, QPointF& screenPt) const;

public:
    static const double kDefaultScaleValue;
private:
	/** 地图中心点的经纬度坐标。 */
	RGeoPoint m_worldCenter;
	double     m_worldCenterConverCoord;

	/** 屏幕宽度，单位：像素。 */
	int m_screenWidth;

	/** 屏幕高度，单位：像素。 */
	int m_screenHeight;

	/** 用于计算的显示比例，含义是：屏幕中水平方向的一个像素表示的经度是多少度。 */
	double m_scale;

	/** 当前使用的投影方法。 */
	Projection m_projection;

	typedef double(*DoubleToDoubleFunc)(double);
	DoubleToDoubleFunc m_yFunc;
	DoubleToDoubleFunc m_yInvFunc;
    RenderEngineLayer * m_render_layer_ptr;
    aurora::IMapRenderService * m_render_service_ptr;
};

#endif // MAP_CAMERA_H
