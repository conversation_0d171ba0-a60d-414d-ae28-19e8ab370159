<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindowClass</class>
 <widget class="QMainWindow" name="MainWindowClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1158</width>
    <height>1038</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>Map Viewer</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QSplitter" name="splitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QToolBox" name="toolBox">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>300</width>
         <height>0</height>
        </size>
       </property>
       <property name="currentIndex">
        <number>2</number>
       </property>
       <widget class="QWidget" name="mapLayerPage">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>300</width>
          <height>561</height>
         </rect>
        </property>
        <attribute name="label">
         <string>Layers</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QListWidget" name="mapLayerList">
           <property name="contextMenuPolicy">
            <enum>Qt::NoContextMenu</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QTreeWidget" name="mapLayerTree">
           <column>
            <property name="text">
             <string>Display Info</string>
            </property>
           </column>
           <item>
            <property name="text">
             <string>level 0</string>
            </property>
            <property name="checkState">
             <enum>Unchecked</enum>
            </property>
            <property name="flags">
             <set>NoItemFlags</set>
            </property>
            <item>
             <property name="text">
              <string>link</string>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
            <item>
             <property name="text">
              <string>node</string>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
            <item>
             <property name="text">
              <string>tile id</string>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
            <item>
             <property name="text">
              <string>tile rect</string>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
           </item>
           <item>
            <property name="text">
             <string>level 1</string>
            </property>
            <property name="foreground">
             <brush brushstyle="NoBrush">
              <color alpha="255">
               <red>0</red>
               <green>170</green>
               <blue>255</blue>
              </color>
             </brush>
            </property>
            <property name="checkState">
             <enum>Unchecked</enum>
            </property>
            <property name="flags">
             <set>NoItemFlags</set>
            </property>
            <item>
             <property name="text">
              <string>link</string>
             </property>
             <property name="foreground">
              <brush brushstyle="NoBrush">
               <color alpha="255">
                <red>0</red>
                <green>170</green>
                <blue>255</blue>
               </color>
              </brush>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
            <item>
             <property name="text">
              <string>node</string>
             </property>
             <property name="foreground">
              <brush brushstyle="NoBrush">
               <color alpha="255">
                <red>0</red>
                <green>170</green>
                <blue>255</blue>
               </color>
              </brush>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
            <item>
             <property name="text">
              <string>tile id</string>
             </property>
             <property name="foreground">
              <brush brushstyle="NoBrush">
               <color alpha="255">
                <red>0</red>
                <green>170</green>
                <blue>255</blue>
               </color>
              </brush>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
            <item>
             <property name="text">
              <string>tile rect</string>
             </property>
             <property name="foreground">
              <brush brushstyle="NoBrush">
               <color alpha="255">
                <red>0</red>
                <green>170</green>
                <blue>255</blue>
               </color>
              </brush>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
           </item>
           <item>
            <property name="text">
             <string>level 2</string>
            </property>
            <property name="foreground">
             <brush brushstyle="NoBrush">
              <color alpha="255">
               <red>255</red>
               <green>0</green>
               <blue>255</blue>
              </color>
             </brush>
            </property>
            <property name="checkState">
             <enum>Unchecked</enum>
            </property>
            <property name="flags">
             <set>NoItemFlags</set>
            </property>
            <item>
             <property name="text">
              <string>link</string>
             </property>
             <property name="foreground">
              <brush brushstyle="NoBrush">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>255</blue>
               </color>
              </brush>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
            <item>
             <property name="text">
              <string>node</string>
             </property>
             <property name="foreground">
              <brush brushstyle="NoBrush">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>255</blue>
               </color>
              </brush>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
            <item>
             <property name="text">
              <string>tile id</string>
             </property>
             <property name="foreground">
              <brush brushstyle="NoBrush">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>255</blue>
               </color>
              </brush>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
            <item>
             <property name="text">
              <string>tile rect</string>
             </property>
             <property name="foreground">
              <brush brushstyle="NoBrush">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>255</blue>
               </color>
              </brush>
             </property>
             <property name="checkState">
              <enum>Unchecked</enum>
             </property>
             <property name="flags">
              <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
             </property>
            </item>
           </item>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="worldManagerPage">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>300</width>
          <height>561</height>
         </rect>
        </property>
        <attribute name="label">
         <string>Select City</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_7">
         <item>
          <widget class="QTreeWidget" name="cityTree">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>150</width>
             <height>0</height>
            </size>
           </property>
           <property name="contextMenuPolicy">
            <enum>Qt::NoContextMenu</enum>
           </property>
           <property name="animated">
            <bool>true</bool>
           </property>
           <attribute name="headerVisible">
            <bool>false</bool>
           </attribute>
           <column>
            <property name="text">
             <string notr="true">1</string>
            </property>
           </column>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="dataInfoPage">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>300</width>
          <height>561</height>
         </rect>
        </property>
        <attribute name="label">
         <string>Database</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QTreeWidget" name="dataInfoTree">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="contextMenuPolicy">
            <enum>Qt::NoContextMenu</enum>
           </property>
           <attribute name="headerVisible">
            <bool>false</bool>
           </attribute>
           <column>
            <property name="text">
             <string notr="true">1</string>
            </property>
           </column>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="routingPage">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>300</width>
          <height>561</height>
         </rect>
        </property>
        <attribute name="label">
         <string>Routing</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <item>
          <widget class="QTabWidget" name="tabWidget">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="currentIndex">
            <number>0</number>
           </property>
           <widget class="QWidget" name="tabRouteConfiguration">
            <attribute name="title">
             <string>Configuration</string>
            </attribute>
            <layout class="QVBoxLayout" name="verticalLayout_6">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_5">
               <item>
                <widget class="QLabel" name="label">
                 <property name="text">
                  <string>Routing Rule:</string>
                 </property>
                 <property name="buddy">
                  <cstring>routingRuleComboBox</cstring>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="routingRuleComboBox">
                 <item>
                  <property name="text">
                   <string>TimeFirst</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>DistanceFirst</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>HighWayFirst</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>AvoidToll</string>
                  </property>
                 </item>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
           <widget class="QWidget" name="tabAvoidArea">
            <attribute name="title">
             <string>AvoidArea</string>
            </attribute>
            <widget class="QTextEdit" name="avoidAreaLBTextEdit">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="geometry">
              <rect>
               <x>30</x>
               <y>1</y>
               <width>161</width>
               <height>16</height>
              </rect>
             </property>
             <property name="acceptDrops">
              <bool>true</bool>
             </property>
             <property name="verticalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
             <property name="horizontalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
             <property name="undoRedoEnabled">
              <bool>true</bool>
             </property>
            </widget>
            <widget class="QTextEdit" name="avoidAreaRTTextEdit">
             <property name="geometry">
              <rect>
               <x>30</x>
               <y>21</y>
               <width>161</width>
               <height>16</height>
              </rect>
             </property>
             <property name="verticalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
             <property name="horizontalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
            </widget>
            <widget class="QPushButton" name="avoidAreaPushButton">
             <property name="geometry">
              <rect>
               <x>200</x>
               <y>0</y>
               <width>41</width>
               <height>21</height>
              </rect>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::NoContextMenu</enum>
             </property>
             <property name="acceptDrops">
              <bool>true</bool>
             </property>
             <property name="autoFillBackground">
              <bool>true</bool>
             </property>
             <property name="text">
              <string>框选</string>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
            </widget>
            <widget class="QLabel" name="label_5">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>0</y>
               <width>21</width>
               <height>16</height>
              </rect>
             </property>
             <property name="text">
              <string>LB</string>
             </property>
             <property name="buddy">
              <cstring>avoidAreaPushButton</cstring>
             </property>
            </widget>
            <widget class="QLabel" name="label_6">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>20</y>
               <width>21</width>
               <height>16</height>
              </rect>
             </property>
             <property name="text">
              <string>RT</string>
             </property>
             <property name="buddy">
              <cstring>addAvoidAreaPushButton</cstring>
             </property>
            </widget>
            <widget class="QPushButton" name="addAvoidAreaPushButton">
             <property name="geometry">
              <rect>
               <x>200</x>
               <y>20</y>
               <width>41</width>
               <height>21</height>
              </rect>
             </property>
             <property name="focusPolicy">
              <enum>Qt::NoFocus</enum>
             </property>
             <property name="contextMenuPolicy">
              <enum>Qt::NoContextMenu</enum>
             </property>
             <property name="acceptDrops">
              <bool>true</bool>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="text">
              <string>添加</string>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
            </widget>
           </widget>
           <widget class="QWidget" name="tab">
            <attribute name="title">
             <string>AvoidLink</string>
            </attribute>
            <widget class="QTextEdit" name="avoidLinkTextEdit">
             <property name="geometry">
              <rect>
               <x>40</x>
               <y>10</y>
               <width>151</width>
               <height>21</height>
              </rect>
             </property>
             <property name="acceptDrops">
              <bool>false</bool>
             </property>
             <property name="verticalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
             <property name="horizontalScrollBarPolicy">
              <enum>Qt::ScrollBarAsNeeded</enum>
             </property>
            </widget>
            <widget class="QLabel" name="label_7">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>10</y>
               <width>31</width>
               <height>16</height>
              </rect>
             </property>
             <property name="text">
              <string>Link</string>
             </property>
            </widget>
            <widget class="QPushButton" name="addAvoidLinkPushButton">
             <property name="geometry">
              <rect>
               <x>200</x>
               <y>10</y>
               <width>41</width>
               <height>23</height>
              </rect>
             </property>
             <property name="text">
              <string>添加</string>
             </property>
            </widget>
           </widget>
           <widget class="QWidget" name="tab_2">
            <attribute name="title">
             <string>TestSetting</string>
            </attribute>
            <widget class="QWidget" name="layoutWidget">
             <property name="geometry">
              <rect>
               <x>0</x>
               <y>0</y>
               <width>92</width>
               <height>52</height>
              </rect>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_5">
              <item>
               <widget class="QLabel" name="label_8">
                <property name="text">
                 <string>point num</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="destNumComboBox">
                <item>
                 <property name="text">
                  <string>2</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>3</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>4</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>5</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>random</string>
                 </property>
                </item>
               </widget>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="layoutWidget1">
             <property name="geometry">
              <rect>
               <x>70</x>
               <y>0</y>
               <width>98</width>
               <height>52</height>
              </rect>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_8">
              <item>
               <widget class="QLabel" name="label_9">
                <property name="text">
                 <string>city</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="cityTestComboBox"/>
              </item>
             </layout>
            </widget>
            <widget class="QWidget" name="layoutWidget2">
             <property name="geometry">
              <rect>
               <x>170</x>
               <y>0</y>
               <width>81</width>
               <height>52</height>
              </rect>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_9">
              <item>
               <widget class="QLabel" name="label_10">
                <property name="text">
                 <string>run times</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="runRimesLineEdit"/>
              </item>
             </layout>
            </widget>
           </widget>
           <widget class="QWidget" name="tab_3">
            <attribute name="title">
             <string>CaseTest</string>
            </attribute>
            <widget class="QComboBox" name="caseTestComboBox">
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>10</y>
               <width>231</width>
               <height>22</height>
              </rect>
             </property>
            </widget>
           </widget>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="routePlanList">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="focusPolicy">
            <enum>Qt::ClickFocus</enum>
           </property>
           <property name="contextMenuPolicy">
            <enum>Qt::CustomContextMenu</enum>
           </property>
           <property name="acceptDrops">
            <bool>false</bool>
           </property>
           <property name="iconSize">
            <size>
             <width>24</width>
             <height>38</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="routeResultList">
           <property name="enabled">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="poiPage">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>320</width>
          <height>863</height>
         </rect>
        </property>
        <attribute name="label">
         <string>POI Search</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_10">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QLabel" name="searchTimeConsumptionLabel">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="sizeIncrement">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="baseSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>Status:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="timeLabel">
             <property name="text">
              <string>N/A</string>
             </property>
             <property name="wordWrap">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_15">
           <item>
            <widget class="QLabel" name="Lnglabel">
             <property name="text">
              <string>Lng:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="LnglineEdit"/>
           </item>
           <item>
            <widget class="QLabel" name="Latlabel">
             <property name="text">
              <string>Lat:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="LatlineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_17">
           <item>
            <widget class="QLabel" name="modeLabel">
             <property name="maximumSize">
              <size>
               <width>40</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Mode</string>
             </property>
             <property name="buddy">
              <cstring>modeComboBox</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="modeComboBox">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <item>
              <property name="text">
               <string>Online</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>Offline</string>
              </property>
             </item>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_21">
           <item>
            <widget class="QLabel" name="cityLabel">
             <property name="maximumSize">
              <size>
               <width>40</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>City</string>
             </property>
             <property name="buddy">
              <cstring>cityComboBox</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="cityComboBox">
             <property name="enabled">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_22">
           <item>
            <widget class="QLabel" name="typeLabel">
             <property name="maximumSize">
              <size>
               <width>40</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Type</string>
             </property>
             <property name="buddy">
              <cstring>typeComboBox</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="typeComboBox">
             <item>
              <property name="text">
               <string>Normal</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>Around</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>Byroute</string>
              </property>
             </item>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_23">
           <item>
            <widget class="QLabel" name="keywordLabel">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Keyword</string>
             </property>
             <property name="buddy">
              <cstring>keywordLineEdit</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="keywordLineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_29">
           <item>
            <widget class="QLabel" name="CategoryLabel">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Category</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="CategoryLineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_24">
           <item>
            <widget class="QLabel" name="radiusLabel">
             <property name="text">
              <string>Radius (m)</string>
             </property>
             <property name="buddy">
              <cstring>radiusLineEdit</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="radiusLineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_25">
           <item>
            <widget class="QLabel" name="widthLabel">
             <property name="text">
              <string>Count</string>
             </property>
             <property name="buddy">
              <cstring>widthLineEdit</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="widthLineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_26">
           <item>
            <widget class="QLabel" name="onlyRightLabel">
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Only right</string>
             </property>
             <property name="buddy">
              <cstring>onlyRightComboBox</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="onlyRightComboBox">
             <item>
              <property name="text">
               <string>false</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>true</string>
              </property>
             </item>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_27">
           <item>
            <widget class="QLabel" name="showTypeLabel">
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Show type </string>
             </property>
             <property name="buddy">
              <cstring>showTypeComboBox</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="showTypeComboBox">
             <item>
              <property name="text">
               <string>pin</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>mark</string>
              </property>
             </item>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QPushButton" name="searchPushButton">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="text">
            <string>Search</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_14">
           <item>
            <widget class="QPushButton" name="fileSugTestButton">
             <property name="text">
              <string>File Sug Test</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="fileSearchTestPushButton">
             <property name="text">
              <string>File Search Test</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="stopSearchTestPushButton">
             <property name="text">
              <string>Stop Test</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QListWidget" name="poiResultListWidget">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>360</height>
            </size>
           </property>
           <property name="contextMenuPolicy">
            <enum>Qt::NoContextMenu</enum>
           </property>
           <property name="acceptDrops">
            <bool>false</bool>
           </property>
           <property name="sizeAdjustPolicy">
            <enum>QAbstractScrollArea::AdjustIgnored</enum>
           </property>
           <property name="resizeMode">
            <enum>QListView::Fixed</enum>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QCheckBox" name="showOnMapCheckBox">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="text">
              <string>Show On Map</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="clearResultPushButton">
             <property name="text">
              <string>Clear Result</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QPushButton" name="loadPreviousPushButton">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="text">
              <string>Load Previous</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="loadMorePushButton">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="text">
              <string>Load More</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="cancelPushButton">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="text">
              <string>Cancel</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="viewPage">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>300</width>
          <height>561</height>
         </rect>
        </property>
        <attribute name="label">
         <string>View</string>
        </attribute>
        <widget class="QWidget" name="layoutWidget">
         <property name="geometry">
          <rect>
           <x>10</x>
           <y>10</y>
           <width>301</width>
           <height>293</height>
          </rect>
         </property>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label_2">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>Line Feature Filter:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QComboBox" name="comboBoxLine">
            <item>
             <property name="text">
              <string>All Features</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>Assigned Code</string>
             </property>
            </item>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLineEdit" name="lineEditLine">
            <property name="enabled">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_3">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>Area Feature Filter:</string>
            </property>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QComboBox" name="comboBoxArea">
            <item>
             <property name="text">
              <string>All Features</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>Assigned Code</string>
             </property>
            </item>
           </widget>
          </item>
          <item row="5" column="0">
           <widget class="QLineEdit" name="lineEditArea">
            <property name="enabled">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="6" column="0">
           <widget class="QLabel" name="label_4">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>Point Feature Filter:</string>
            </property>
           </widget>
          </item>
          <item row="7" column="0">
           <widget class="QComboBox" name="comboBoxPoint">
            <item>
             <property name="text">
              <string>All Features</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>Assigned Code</string>
             </property>
            </item>
           </widget>
          </item>
          <item row="8" column="0">
           <widget class="QLineEdit" name="lineEditPoint">
            <property name="enabled">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item row="9" column="0">
           <widget class="QPushButton" name="viewFilterSubmit">
            <property name="text">
             <string>Submit</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
       <widget class="QWidget" name="locationPage">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>318</width>
          <height>547</height>
         </rect>
        </property>
        <attribute name="label">
         <string>Location</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_13">
         <item>
          <widget class="QComboBox" name="logTypeComboBox">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <item>
            <property name="text">
             <string>tbt (log_yyyymmdd_hhmmss_gps.txt)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>tbt (DR_yyyymmdd_hhmmss.drd)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>naw (loc_gps_yyyy_mm_dd_hh_mm.dat)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>tbt (log_yyyymmdd_temp.txt)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>naw (loc_ccp_yyyy_mm_dd_hh_mm.loc)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>fd (FD_DR_yymmdd_hhmmss.txt)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>yun (loc_yyyy_mm_dd_hh_mm_ss.dat)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>tmc (tmc_test.txt)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>shp (.shp)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>link (.link)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>state (.state)</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>algo_nmea (.log)</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="loadLogPushButton">
           <property name="text">
            <string>导入轨迹文件或文件夹</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="LocComboBox"/>
         </item>
         <item>
          <widget class="QComboBox" name="speedComboBox">
           <property name="contextMenuPolicy">
            <enum>Qt::NoContextMenu</enum>
           </property>
           <property name="layoutDirection">
            <enum>Qt::LeftToRight</enum>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="insertPolicy">
            <enum>QComboBox::NoInsert</enum>
           </property>
           <property name="sizeAdjustPolicy">
            <enum>QComboBox::AdjustToContents</enum>
           </property>
           <property name="frame">
            <bool>true</bool>
           </property>
           <item>
            <property name="text">
             <string>1X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>2X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>4X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>8X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>16X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>32X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>64X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>128X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>256X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>512X</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1024X</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_6">
           <item>
            <widget class="QPushButton" name="playPushButton">
             <property name="text">
              <string>play</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="nextPushButton">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="text">
              <string>next</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="stopPushButton">
             <property name="text">
              <string>stop</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_12">
           <item>
            <widget class="QLabel" name="label_11">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="maximumSize">
              <size>
               <width>72</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Car Angle</string>
             </property>
             <property name="buddy">
              <cstring>carAngleLineEdit</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="carAngleLineEdit">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Ignored" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="setCarPosPushButton">
             <property name="text">
              <string>set car pos</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QPushButton" name="loadCompareLogPushButton">
           <property name="text">
            <string>导入匹配结果比较文件</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="doOtherRoadPushButton">
           <property name="text">
            <string>do other road</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="showDrCheckBox">
           <property name="text">
            <string>show dr trace points</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="showCcpCheckBox">
           <property name="text">
            <string>show ccp trace points (tmc shp)</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="showGpsCheckBox">
           <property name="text">
            <string>show gps trace points (link state)</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="showCompareCcpCheckBox">
           <property name="text">
            <string>show compare ccp trace points</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_18">
           <item>
            <widget class="QLabel" name="locationTimeConsumptionLabel">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>Location Time Consumption:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="locationTimeLabel">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>N/A</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_19">
           <item>
            <widget class="QLineEdit" name="srcFileLineEdit">
             <property name="sizePolicy">
              <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="selectFilePushButton">
             <property name="text">
              <string>...</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QPushButton" name="transferFilePushButton">
           <property name="text">
            <string>Transfer File</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_20">
           <item>
            <widget class="QLabel" name="transferTimeConsumptionLabel">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>Transfer Time Consumption:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="TransferTimeLabel">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>N/A</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="simulationPage">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>286</width>
          <height>770</height>
         </rect>
        </property>
        <attribute name="label">
         <string>Simulation</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_11">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_32">
           <item>
            <widget class="QLabel" name="label_17">
             <property name="text">
              <string>Pos Noise Mean (m)</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="PosNoiseMeanlineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_33">
           <item>
            <widget class="QLabel" name="label_18">
             <property name="text">
              <string>Pos Noise Std Dev (m)</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="PosNoiseStdlineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_34">
           <item>
            <widget class="QLabel" name="label_19">
             <property name="text">
              <string>Heading Noise Mean (°)</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="HeadingNoiseMeanlineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_36">
           <item>
            <widget class="QLabel" name="label_21">
             <property name="text">
              <string>Heading Noise Std Dev (°)</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="HeadingNoiseStdlineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_37">
           <item>
            <widget class="QLabel" name="label_22">
             <property name="text">
              <string>Sampling Interval (s)</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="SampleIntervallineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_35">
           <item>
            <widget class="QLabel" name="label_23">
             <property name="text">
              <string>Playback Interval</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="PlaybackIntervalcomboBox"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QPushButton" name="loadFilePushButton">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>加载轨迹文件</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="loadFolderPushButton">
           <property name="text">
            <string>加载轨迹文件夹</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="logFileComboBox"/>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_10">
           <item>
            <widget class="QLabel" name="ipLabel">
             <property name="text">
              <string>IP</string>
             </property>
             <property name="buddy">
              <cstring>ipLineEdit</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="ipLineEdit">
             <property name="text">
              <string>127.0.0.1</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QLineEdit" name="idLineEdit">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_38">
           <item>
            <widget class="QPushButton" name="goPushButton">
             <property name="text">
              <string>Start</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="SimulCancelpushButton">
             <property name="text">
              <string>Cancel</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QDateTimeEdit" name="dateTimeEdit">
           <property name="dateTime">
            <datetime>
             <hour>9</hour>
             <minute>0</minute>
             <second>0</second>
             <year>2018</year>
             <month>1</month>
             <day>1</day>
            </datetime>
           </property>
           <property name="displayFormat">
            <string>yyyy/MM/dd HH:mm:ss</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_7">
           <item>
            <widget class="QPushButton" name="prevPtPushButton">
             <property name="text">
              <string>Prev</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="nextPtPushButton">
             <property name="text">
              <string>Next</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="sendPtPushButton">
             <property name="text">
              <string>Send</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QScrollBar" name="verticalScrollBar">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_8">
           <item>
            <widget class="QCheckBox" name="autoCheckBox">
             <property name="text">
              <string>auto</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="rateComboBox">
             <item>
              <property name="text">
               <string>1X</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>2X</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>4X</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>8X</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>16X</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>32X</string>
              </property>
             </item>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="clipPushButton">
             <property name="text">
              <string>Clip</string>
             </property>
             <property name="autoDefault">
              <bool>true</bool>
             </property>
             <property name="default">
              <bool>true</bool>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_9">
           <item>
            <widget class="QCheckBox" name="showPointsCheckBox">
             <property name="text">
              <string>show all</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="showSelectedFileCheckBox">
             <property name="text">
              <string>show selected</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_13">
           <item>
            <widget class="QCheckBox" name="showSlopeCheckBox">
             <property name="text">
              <string>show slope</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="filterParkCheckBox">
             <property name="text">
              <string>filter park</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_11">
           <item>
            <widget class="QCheckBox" name="enableLocalSimCheckBox">
             <property name="text">
              <string>enable local</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="loopCheckBox">
             <property name="text">
              <string>loop</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QPushButton" name="quickMatchPushButton">
           <property name="text">
            <string>Quick Match</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="infoListWidget">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="contextMenuPolicy">
            <enum>Qt::NoContextMenu</enum>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="horizontalScrollBarPolicy">
            <enum>Qt::ScrollBarAsNeeded</enum>
           </property>
           <property name="sizeAdjustPolicy">
            <enum>QAbstractScrollArea::AdjustIgnored</enum>
           </property>
           <property name="autoScroll">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="page">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>300</width>
          <height>561</height>
         </rect>
        </property>
        <attribute name="label">
         <string>GuideInfo</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_12">
         <item>
          <widget class="QComboBox" name="pointTypeBox">
           <item>
            <property name="text">
             <string>GP</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>IP</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>Cross</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_16">
           <item>
            <widget class="QLabel" name="label_16">
             <property name="text">
              <string>uuid</string>
             </property>
             <property name="buddy">
              <cstring>pointIdEdit</cstring>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="pointIdEdit"/>
           </item>
           <item>
            <widget class="QPushButton" name="showGuidePoint">
             <property name="text">
              <string>添加引导点</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QListWidget" name="pointInfo">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>135</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="addGuideLabel">
           <property name="text">
            <string>添加成功</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="guideInfo">
           <property name="contextMenuPolicy">
            <enum>Qt::NoContextMenu</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="outFileName">
           <property name="text">
            <string>Filename</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="testFileName"/>
         </item>
         <item>
          <widget class="QRadioButton" name="voiceButton">
           <property name="text">
            <string>播报导出</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="outOneButton">
           <property name="text">
            <string>导出引导点用例</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tmcPage">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>300</width>
          <height>561</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <attribute name="label">
         <string>TMC</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_15">
         <item>
          <layout class="QFormLayout" name="formLayout_5">
           <item row="0" column="0">
            <widget class="QLabel" name="label_20">
             <property name="text">
              <string>Type</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QComboBox" name="reqTypeComboBox">
             <item>
              <property name="text">
               <string>link</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>city</string>
              </property>
             </item>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QFormLayout" name="formLayout_4">
           <item row="0" column="0">
            <widget class="QLabel" name="CityLabel">
             <property name="text">
              <string>City</string>
             </property>
             <property name="buddy">
              <cstring>tmcCityComboBox</cstring>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QComboBox" name="tmcCityComboBox"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QFormLayout" name="formLayout_3">
           <item row="0" column="0">
            <widget class="QLabel" name="UUIDLabel">
             <property name="text">
              <string>UUID</string>
             </property>
             <property name="buddy">
              <cstring>UUIDlineEdit</cstring>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="UUIDlineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QDateTimeEdit" name="tmcDateTimeEdit">
           <property name="dateTime">
            <datetime>
             <hour>9</hour>
             <minute>0</minute>
             <second>0</second>
             <year>2018</year>
             <month>1</month>
             <day>1</day>
            </datetime>
           </property>
           <property name="displayFormat">
            <string>yyyy/MM/dd HH:mm:ss</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="tmcSelectCheckBox">
           <property name="text">
            <string>select</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="InquireButton">
           <property name="text">
            <string>查询</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="tmcClearPushButton">
           <property name="text">
            <string>清除</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="TmcInfoList">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>300</height>
            </size>
           </property>
           <property name="contextMenuPolicy">
            <enum>Qt::NoContextMenu</enum>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="horizontalScrollBarPolicy">
            <enum>Qt::ScrollBarAsNeeded</enum>
           </property>
           <property name="sizeAdjustPolicy">
            <enum>QAbstractScrollArea::AdjustIgnored</enum>
           </property>
           <property name="autoScroll">
            <bool>true</bool>
           </property>
           <property name="resizeMode">
            <enum>QListView::Fixed</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="RoutingTest">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>300</width>
          <height>561</height>
         </rect>
        </property>
        <property name="whatsThis">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Routing test&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <attribute name="label">
         <string>Routing Test</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_14">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_30">
           <item>
            <widget class="QLabel" name="label_15">
             <property name="text">
              <string>File:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="FIleTestlineEdit"/>
           </item>
           <item>
            <widget class="QPushButton" name="FileTestSelectpushButton">
             <property name="text">
              <string>Select</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_31">
           <item>
            <widget class="QPushButton" name="RouteTestStartpushButton">
             <property name="text">
              <string>Start Test</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="RouteTestStopPushButton">
             <property name="text">
              <string>Cancel Test</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QTextEdit" name="RouteFileTesttextEdit"/>
         </item>
         <item>
          <widget class="QLabel" name="RouteFileTestStatuslabel">
           <property name="text">
            <string>Status:</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="locationTestPage">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>0</y>
          <width>300</width>
          <height>561</height>
         </rect>
        </property>
        <attribute name="label">
         <string>LocationTest</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_16">
         <item>
          <widget class="QPushButton" name="loadSimPushButton">
           <property name="text">
            <string>加载轨迹文件</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <item>
            <widget class="QLabel" name="label_14">
             <property name="text">
              <string>BugId</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="bugIDlineEdit"/>
           </item>
           <item>
            <widget class="QPushButton" name="locClipPushButton">
             <property name="text">
              <string>clip</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_28">
           <item>
            <widget class="QLineEdit" name="indexLineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_3">
           <item row="0" column="0">
            <widget class="QLabel" name="label_12">
             <property name="text">
              <string>Period</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="startLineEdit"/>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="label_13">
             <property name="text">
              <string>-</string>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLineEdit" name="endLineEdit"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QComboBox" name="locTestTypeComboBox">
           <item>
            <property name="text">
             <string>normal</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>cpp_stop</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>gradient</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>ground_parking</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>jump</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>offroad</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>offroute</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>other</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>overhead</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>path_match</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>road_change</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>route_keep</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>sapa</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>switch_road</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>tunnel</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>turn</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>underground_parking</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>uturn</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>small_angle_road</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>roundabout</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>straight</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="locTestKeyComboBox">
           <item>
            <property name="text">
             <string>match_status</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>mode_flag</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>road_rank</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>link_form</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>link_type</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>is_overhead</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>parallel_flag</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>is_dual_car</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>is_area_link</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>is_innerlink</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>is_ramp_link</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_53">
           <item>
            <widget class="QLineEdit" name="locTestValueLineEdit"/>
           </item>
           <item>
            <widget class="QPushButton" name="addLocTestPushButton">
             <property name="text">
              <string>添加数据</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QListWidget" name="locTestInputInfoListWidget">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>80</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="addLocTestLabel">
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="locTestResultInfoListWidget">
           <property name="contextMenuPolicy">
            <enum>Qt::NoContextMenu</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="locTestOutFileNameLabel">
           <property name="text">
            <string>Filename</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="locTestOutFileNameLineEdit">
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="locTestOutputPushButton">
           <property name="text">
            <string>导出匹配测试用例</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="MapWidget" name="mapWidget" native="true">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
         <horstretch>1</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="acceptDrops">
        <bool>true</bool>
       </property>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1158</width>
     <height>28</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuHelp">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="title">
     <string>&amp;Help</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <widget class="QMenu" name="menuView">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="title">
     <string>&amp;View</string>
    </property>
    <addaction name="actionShowCursor"/>
    <addaction name="actionAutoDisplay"/>
    <addaction name="actionMemoryView"/>
    <addaction name="actionShowToolBox"/>
   </widget>
   <widget class="QMenu" name="menuSettings">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="title">
     <string>&amp;Settings</string>
    </property>
    <addaction name="actionPreferences"/>
   </widget>
   <widget class="QMenu" name="menuRouting">
    <property name="enabled">
     <bool>true</bool>
    </property>
    <property name="title">
     <string>&amp;Routing</string>
    </property>
    <addaction name="actionSetStart"/>
    <addaction name="actionSetEnd"/>
    <addaction name="actionAddWayPoint"/>
    <addaction name="actionDeleteRoute"/>
    <addaction name="separator"/>
    <addaction name="actionCopyRouteCase"/>
    <addaction name="actionPaste"/>
    <addaction name="separator"/>
    <addaction name="actionReverseLink"/>
    <addaction name="separator"/>
    <addaction name="actionCompareWithBaidu"/>
    <addaction name="actionCompareWithAmap"/>
    <addaction name="actionMultiRoute"/>
    <addaction name="actionAvoidYunmapRoute"/>
    <addaction name="separator"/>
    <addaction name="actionCityTest"/>
    <addaction name="actionRandomTest"/>
    <addaction name="actionFileTest"/>
    <addaction name="actionCaseTest"/>
    <addaction name="actionStopTest"/>
   </widget>
   <widget class="QMenu" name="menuTools">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="title">
     <string>Tools</string>
    </property>
    <addaction name="actionOpenInQqMap"/>
    <addaction name="actionOpenInBaiduMap"/>
    <addaction name="actionOpenInGoogleMap"/>
    <addaction name="actionOpenInAMap"/>
   </widget>
   <widget class="QMenu" name="menuLocation">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="title">
     <string>Location</string>
    </property>
    <addaction name="actionShowGpsAndCcp"/>
    <addaction name="actionShowInfo"/>
    <addaction name="actionShowGpPoint"/>
    <addaction name="actionShowCameraPoints"/>
    <addaction name="actionShowTrfcPoints"/>
    <addaction name="actionShowPeriod"/>
    <addaction name="actionShowDrAlgoInfo"/>
    <addaction name="actionClip"/>
    <addaction name="actionResetClip"/>
    <addaction name="actionStaticMatch"/>
   </widget>
   <widget class="QMenu" name="menuMap">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="title">
     <string>Map</string>
    </property>
    <addaction name="actionPos"/>
    <addaction name="actionHighlightBuilding"/>
    <addaction name="actionSchema"/>
    <addaction name="actionGuideArrow"/>
   </widget>
   <addaction name="menuView"/>
   <addaction name="menuRouting"/>
   <addaction name="menuLocation"/>
   <addaction name="menuSettings"/>
   <addaction name="menuTools"/>
   <addaction name="menuMap"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="iconSize">
    <size>
     <width>40</width>
     <height>40</height>
    </size>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionSearchFeature"/>
   <addaction name="actionPosition"/>
   <addaction name="actionPtahCalculation"/>
   <addaction name="actionSelectSetting"/>
   <addaction name="actionMeasure"/>
   <addaction name="actionHighlightLink"/>
   <addaction name="actionHighlightNode"/>
   <addaction name="actionHighlightOther"/>
   <addaction name="actionStat"/>
   <addaction name="separator"/>
   <addaction name="actionStartSimulation"/>
   <addaction name="actionEndSimulation"/>
   <addaction name="actionStartDrive"/>
   <addaction name="separator"/>
   <addaction name="actionManual"/>
   <addaction name="actionRequestTxt"/>
   <addaction name="actionhome"/>
   <addaction name="actioncompany"/>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
  <action name="actionOpen">
   <property name="text">
    <string>&amp;Open...</string>
   </property>
  </action>
  <action name="actionProjections">
   <property name="text">
    <string>&amp;Projections</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;About</string>
   </property>
  </action>
  <action name="actionClearItems">
   <property name="text">
    <string>Clear Items</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>E&amp;xit</string>
   </property>
  </action>
  <action name="actionPreferences">
   <property name="text">
    <string>&amp;Preferences...</string>
   </property>
  </action>
  <action name="actionSetStart">
   <property name="text">
    <string>Set &amp;Start</string>
   </property>
   <property name="shortcut">
    <string>S</string>
   </property>
  </action>
  <action name="actionSetEnd">
   <property name="text">
    <string>Set &amp;End</string>
   </property>
   <property name="shortcut">
    <string>E</string>
   </property>
  </action>
  <action name="actionAddWayPoint">
   <property name="text">
    <string>Add &amp;Way Point</string>
   </property>
   <property name="toolTip">
    <string>Add Way Point</string>
   </property>
   <property name="shortcut">
    <string>W</string>
   </property>
  </action>
  <action name="actionCopyRouteCase">
   <property name="text">
    <string>&amp;Copy Route Case</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+C</string>
   </property>
  </action>
  <action name="actionPaste">
   <property name="text">
    <string>&amp;Paste Route Case</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+V</string>
   </property>
  </action>
  <action name="actionShowCursor">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Show &amp;Cursor</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionReverseLink">
   <property name="text">
    <string>&amp;Reverse Link</string>
   </property>
   <property name="iconText">
    <string>Reverse Link</string>
   </property>
   <property name="shortcut">
    <string>R</string>
   </property>
  </action>
  <action name="actionSelectDSegment">
   <property name="text">
    <string>Select &amp;DSegment...</string>
   </property>
  </action>
  <action name="actionSearchFeature">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/search_feature_50.png</normaloff>:/MainWindow/resources/search_feature_50.png</iconset>
   </property>
   <property name="text">
    <string>Search Feature</string>
   </property>
  </action>
  <action name="actionPosition">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/position_50.png</normaloff>:/MainWindow/resources/position_50.png</iconset>
   </property>
   <property name="text">
    <string>Position</string>
   </property>
   <property name="toolTip">
    <string>Position</string>
   </property>
  </action>
  <action name="actionSelectSetting">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/select_setting.png</normaloff>:/MainWindow/resources/select_setting.png</iconset>
   </property>
   <property name="text">
    <string>SelectSetting</string>
   </property>
   <property name="toolTip">
    <string>Select Setting</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionMeasure">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/measure.png</normaloff>:/MainWindow/resources/measure.png</iconset>
   </property>
   <property name="text">
    <string>Measure</string>
   </property>
   <property name="toolTip">
    <string>Measure</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionManual">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/manual.png</normaloff>:/MainWindow/resources/manual.png</iconset>
   </property>
   <property name="text">
    <string>Manual</string>
   </property>
   <property name="toolTip">
    <string>Manual</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionRequestTxt">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/test_park.png</normaloff>:/MainWindow/resources/test_park.png</iconset>
   </property>
   <property name="text">
    <string>RequestTxt</string>
   </property>
   <property name="toolTip">
    <string>RequestTxt</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionHighlightLink">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/highlight_segment.png</normaloff>:/MainWindow/resources/highlight_segment.png</iconset>
   </property>
   <property name="text">
    <string>Highlight Link</string>
   </property>
   <property name="toolTip">
    <string>Highlight Link</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionHighlightNode">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/highlight_intersection.png</normaloff>:/MainWindow/resources/highlight_intersection.png</iconset>
   </property>
   <property name="text">
    <string>Highlight Node</string>
   </property>
   <property name="toolTip">
    <string>Highlight Node</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionOpenInQqMap">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/qq_map.png</normaloff>:/MainWindow/resources/qq_map.png</iconset>
   </property>
   <property name="text">
    <string>Open in QqMap</string>
   </property>
   <property name="toolTip">
    <string>Open in QqMap</string>
   </property>
   <property name="shortcut">
    <string>T</string>
   </property>
  </action>
  <action name="actionOpenInBaiduMap">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/baidu_map.jpg</normaloff>:/MainWindow/resources/baidu_map.jpg</iconset>
   </property>
   <property name="text">
    <string>Open in BaiduMap</string>
   </property>
   <property name="shortcut">
    <string>B</string>
   </property>
  </action>
  <action name="actionPtahCalculation">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/path_calculation_50.png</normaloff>:/MainWindow/resources/path_calculation_50.png</iconset>
   </property>
   <property name="text">
    <string>PtahCalculation</string>
   </property>
   <property name="toolTip">
    <string>Path Calculation</string>
   </property>
  </action>
  <action name="actionAutoDisplay">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Auto Display</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionOpenInGoogleMap">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/google_map.jpg</normaloff>:/MainWindow/resources/google_map.jpg</iconset>
   </property>
   <property name="text">
    <string>Open in GoogleMap</string>
   </property>
   <property name="shortcut">
    <string>G</string>
   </property>
  </action>
  <action name="actionCompareWithBaidu">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Compare With Baidu</string>
   </property>
  </action>
  <action name="actionCompareWithAmap">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Compare With Amap</string>
   </property>
  </action>
  <action name="actionStartSimulation">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/start_simulation_50.png</normaloff>:/MainWindow/resources/start_simulation_50.png</iconset>
   </property>
   <property name="text">
    <string>Start simulation</string>
   </property>
   <property name="visible">
    <bool>true</bool>
   </property>
  </action>
  <action name="actionEndSimulation">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/stop_simulation_50.png</normaloff>:/MainWindow/resources/stop_simulation_50.png</iconset>
   </property>
   <property name="text">
    <string>End simulation</string>
   </property>
   <property name="visible">
    <bool>true</bool>
   </property>
  </action>
  <action name="actionOpenInAMap">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/a_map.png</normaloff>:/MainWindow/resources/a_map.png</iconset>
   </property>
   <property name="text">
    <string>Open in AMap</string>
   </property>
   <property name="shortcut">
    <string>A</string>
   </property>
  </action>
  <action name="actionCityTest">
   <property name="text">
    <string>City Test</string>
   </property>
  </action>
  <action name="actionRandomTest">
   <property name="text">
    <string>Random Test</string>
   </property>
  </action>
  <action name="actionDeleteRoute">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>&amp;Delete Route</string>
   </property>
   <property name="shortcut">
    <string>D</string>
   </property>
   <property name="visible">
    <bool>true</bool>
   </property>
  </action>
  <action name="actionShowInfo">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Show Info</string>
   </property>
  </action>
  <action name="actionMultiRoute">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Multi Route</string>
   </property>
  </action>
  <action name="actionStartDrive">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/start_drive.png</normaloff>:/MainWindow/resources/start_drive.png</iconset>
   </property>
   <property name="text">
    <string>StartDrive</string>
   </property>
   <property name="toolTip">
    <string>Start Drive</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionShowGpsAndCcp">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Show Gps And Ccp</string>
   </property>
  </action>
  <action name="actionStopTest">
   <property name="text">
    <string>Stop Test</string>
   </property>
  </action>
  <action name="actionAvoidYunmapRoute">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Close Yunmap Route</string>
   </property>
  </action>
  <action name="actionFileTest">
   <property name="text">
    <string>File Test</string>
   </property>
  </action>
  <action name="actionCaseTest">
   <property name="text">
    <string>Case Test</string>
   </property>
  </action>
  <action name="actionShowGpPoint">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Show Gp Point</string>
   </property>
  </action>
  <action name="actionShowCameraPoints">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Show Camera Points(blue)</string>
   </property>
  </action>
  <action name="actionShowTrfcPoints">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Show Trfc Points(green)</string>
   </property>
  </action>
  <action name="actionStat">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/stat.png</normaloff>:/MainWindow/resources/stat.png</iconset>
   </property>
   <property name="text">
    <string>stat</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionHighlightOther">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/star.png</normaloff>:/MainWindow/resources/star.png</iconset>
   </property>
   <property name="text">
    <string>Highlight Other</string>
   </property>
   <property name="toolTip">
    <string>Highlight Other</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionMemoryView">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Memory View</string>
   </property>
   <property name="visible">
    <bool>false</bool>
   </property>
  </action>
  <action name="actionPos">
   <property name="text">
    <string>定位(&amp;D)</string>
   </property>
  </action>
  <action name="actionHighlightBuilding">
   <property name="text">
    <string>高亮大楼(&amp;H)</string>
   </property>
  </action>
  <action name="actionSchema">
   <property name="text">
    <string>插件测试</string>
   </property>
  </action>
  <action name="actionGuideArrow">
   <property name="text">
    <string>测试引导箭头</string>
   </property>
  </action>
  <action name="actionShowPeriod">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Show Period</string>
   </property>
  </action>
  <action name="actionShowDrAlgoInfo">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Show Dr Algo Info</string>
   </property>
  </action>
  <action name="actionClip">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Clip File</string>
   </property>
   <property name="shortcut">
    <string>C</string>
   </property>
  </action>
  <action name="actionResetClip">
   <property name="text">
    <string>Reset Clip</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Z</string>
   </property>
  </action>
  <action name="actionStaticMatch">
   <property name="text">
    <string>Static Match</string>
   </property>
  </action>
  <action name="actionShowToolBox">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>Tool box</string>
   </property>
  </action>
  <action name="actionhome">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/sights.png</normaloff>:/MainWindow/resources/sights.png</iconset>
   </property>
   <property name="text">
    <string>home</string>
   </property>
  </action>
  <action name="actioncompany">
   <property name="icon">
    <iconset resource="../res/main_window.qrc">
     <normaloff>:/MainWindow/resources/buildings.png</normaloff>:/MainWindow/resources/buildings.png</iconset>
   </property>
   <property name="text">
    <string>company</string>
   </property>
   <property name="toolTip">
    <string>company</string>
   </property>
  </action>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MapWidget</class>
   <extends>QWidget</extends>
   <header>../src/map_view/map_widget.h</header>
   <container>1</container>
   <slots>
    <slot>onCityItemDoubleClicked(QTreeWidgetItem*)</slot>
   </slots>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../res/main_window.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>actionOpen</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionOpenTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>295</x>
     <y>199</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionAbout</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionAboutTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>295</x>
     <y>199</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionExit</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>close()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionClearItems</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionClearItemsTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionPreferences</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionPreferencesTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionSetStart</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionSetStartTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionSetEnd</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionSetEndTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionAddWayPoint</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionAddWayPointTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShowCursor</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionShowCursorTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionCopyRouteCase</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionCopyRouteCaseTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionPaste</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionPasteTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionReverseLink</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionReverseLinkTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionPosition</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionPositionTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionSearchFeature</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionSelectFeatureTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>368</x>
     <y>344</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionSelectSetting</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionSelectSettingTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionMeasure</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionMeasureTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionManual</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionManualTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionRequestTxt</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionRequestTxtTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionHighlightLink</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionHighlightSegmentTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionHighlightNode</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionHighlightIntersectionTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionOpenInQqMap</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>openInQqMap()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionOpenInBaiduMap</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>openInBaiduMap()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionPtahCalculation</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionPathCalculationTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionAutoDisplay</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionAutoDisplayTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionOpenInGoogleMap</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>openInGoogleMap()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionStartSimulation</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>startSimulation()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionCompareWithAmap</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionCompareWithAmapTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionOpenInAMap</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>openInAMap()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionCompareWithBaidu</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionCompareWithBaiduTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionEndSimulation</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>endSimulation()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionCityTest</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionCityTestTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionRandomTest</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionRandomTestTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionDeleteRoute</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionDeleteRouteTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShowInfo</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionShowLocInfoTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>298</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionMultiRoute</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionMultiRouteTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionStartDrive</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>StartDrive()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShowGpsAndCcp</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionShowGpsAndCcpTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionStopTest</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionStopTestTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionAvoidYunmapRoute</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionAvoidYunmapRouteToggled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionFileTest</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionFileTestTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionCaseTest</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionCaseTestTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShowGpPoint</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionShowGpPointTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShowCameraPoints</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionShowCameraPointsTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShowTrfcPoints</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionShowTrfcPointsTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionStat</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionStatTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionHighlightOther</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionHighlightOtherTriggered()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionMemoryView</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionMemoryViewTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>mapLayerList</sender>
   <signal>itemChanged(QListWidgetItem*)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLayerListItemChanged(QListWidgetItem*)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>45</x>
     <y>206</y>
    </hint>
    <hint type="destinationlabel">
     <x>295</x>
     <y>199</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>mapLayerList</sender>
   <signal>customContextMenuRequested(QPoint)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLayerListContextMenu(QPoint)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>212</x>
     <y>206</y>
    </hint>
    <hint type="destinationlabel">
     <x>295</x>
     <y>199</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>searchPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onPoiSearch()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>104</x>
     <y>626</y>
    </hint>
    <hint type="destinationlabel">
     <x>355</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>dataInfoTree</sender>
   <signal>customContextMenuRequested(QPoint)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onInfoTreeContextMenu(QPoint)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>145</x>
     <y>399</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>routePlanList</sender>
   <signal>customContextMenuRequested(QPoint)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRoutePlanListContextMenu(QPoint)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>132</x>
     <y>406</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>routePlanList</sender>
   <signal>itemDoubleClicked(QListWidgetItem*)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRoutePlanListItemDoubleClicked(QListWidgetItem*)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>132</x>
     <y>406</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>cityTree</sender>
   <signal>itemDoubleClicked(QTreeWidgetItem*,int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onCityItemDoubleClicked(QTreeWidgetItem*)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>79</x>
     <y>180</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>routingRuleComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRoutingRuleComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>278</x>
     <y>280</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>comboBoxArea</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onViewComboBoxAreaIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>79</x>
     <y>417</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>comboBoxLine</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onViewComboBoxLineIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>108</x>
     <y>329</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>comboBoxPoint</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onViewComboBoxPointIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>108</x>
     <y>505</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>viewFilterSubmit</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onViewFilterSubmit()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>108</x>
     <y>569</y>
    </hint>
    <hint type="destinationlabel">
     <x>520</x>
     <y>379</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>mapLayerTree</sender>
   <signal>itemChanged(QTreeWidgetItem*,int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLayerTreeChanged(QTreeWidgetItem*,int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>136</x>
     <y>457</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>routeResultList</sender>
   <signal>itemSelectionChanged()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRouteResultListChanged()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>123</x>
     <y>547</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>avoidAreaPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRoutingAvoidAreaPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>232</x>
     <y>265</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>addAvoidAreaPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRoutingAddAvoidAreaPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>232</x>
     <y>285</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>addAvoidLinkPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRoutingAddAvoidLinkPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>232</x>
     <y>277</y>
    </hint>
    <hint type="destinationlabel">
     <x>297</x>
     <y>268</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>keywordLineEdit</sender>
   <signal>textChanged(QString)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onkeywordLineEditTextChanged(QString)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>174</x>
     <y>429</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>298</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>modeComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onModeComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>154</x>
     <y>330</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>cityComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onCityComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>154</x>
     <y>363</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>typeComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onTypeComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>154</x>
     <y>396</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>loadLogPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLoadLogPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>124</x>
     <y>370</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>speedComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onSpeedComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>76</x>
     <y>432</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>playPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onPlayPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>62</x>
     <y>464</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>stopPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onStopPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>307</x>
     <y>464</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>logTypeComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLogTypeComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>124</x>
     <y>333</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>showDrCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowDrCheckBoxClicked(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>59</x>
     <y>595</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>nextPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onNextPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>206</x>
     <y>464</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>setCarPosPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onSetCarPosPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>299</x>
     <y>497</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>showCcpCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowCcpCheckBoxClicked(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>71</x>
     <y>624</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>showGpsCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowGpsCheckBoxClicked(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>101</x>
     <y>653</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>destNumComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onDestNumComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>101</x>
     <y>295</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>cityTestComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onCityTestComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>177</x>
     <y>295</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>runRimesLineEdit</sender>
   <signal>textChanged(QString)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRunTimesLineEditTextChanged(QString)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>220</x>
     <y>274</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>showTypeComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowTypeComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>194</x>
     <y>594</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>caseTestComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onCaseTestComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>137</x>
     <y>276</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>selectFilePushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onSelectFilePushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>307</x>
     <y>764</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>transferFilePushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onTransferFilePushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>112</x>
     <y>799</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>prevPtPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onPrevPtPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>48</x>
     <y>795</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>filterParkCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onFilterParkCheckBoxClicked(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>275</x>
     <y>950</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>nextPtPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onNextPtPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>184</x>
     <y>795</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>sendPtPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onSendPtPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>275</x>
     <y>795</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>autoCheckBox</sender>
   <signal>clicked(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onAutoCheckBoxClicked(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>69</x>
     <y>887</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>showSlopeCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowSlopeCheckBoxClicked(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>48</x>
     <y>950</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>rateComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRateComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>184</x>
     <y>888</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>loadFilePushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLoadFilePushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>133</x>
     <y>568</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>ipLineEdit</sender>
   <signal>textChanged(QString)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onIpLineEditTextChanged(QString)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>148</x>
     <y>668</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>showPointsCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowPointsCheckBoxToggled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>51</x>
     <y>919</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>enableLocalSimCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onEnableLocalSimCheckBoxToggled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>87</x>
     <y>981</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>showCompareCcpCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowCompareCcpCheckBoxToggled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>95</x>
     <y>682</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>loadCompareLogPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLoadCompareLogPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>135</x>
     <y>535</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>clipPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onClipPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>275</x>
     <y>888</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>logFileComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLogFileComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>134</x>
     <y>636</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>showSelectedFileCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowSelectedFileCheckBoxToggled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>275</x>
     <y>919</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>loadFolderPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLoadFolderPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>137</x>
     <y>605</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>carAngleLineEdit</sender>
   <signal>textChanged(QString)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onCarAngleLineEditTextChanged(QString)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>186</x>
     <y>497</y>
    </hint>
    <hint type="destinationlabel">
     <x>338</x>
     <y>346</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>goPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onGoPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>139</x>
     <y>732</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>doOtherRoadPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onDoOtherRoadPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>135</x>
     <y>566</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pointInfo</sender>
   <signal>itemDoubleClicked(QListWidgetItem*)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onPointInfoItemClicked(QListWidgetItem*)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>132</x>
     <y>575</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>showGuidePoint</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowGuidePointClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>289</x>
     <y>433</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>outOneButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>OnOutOneCaseButton()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>131</x>
     <y>913</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionPos</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onChangeMapPosition()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>508</x>
     <y>358</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionHighlightBuilding</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onChangeHighlightBuilding()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>508</x>
     <y>381</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionSchema</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onChangeSchemaMode()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>508</x>
     <y>381</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionGuideArrow</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowGuideArrow()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>508</x>
     <y>381</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>guideInfo</sender>
   <signal>customContextMenuRequested(QPoint)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onGuideItemMenuRequested(QPoint)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>131</x>
     <y>680</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShowPeriod</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionShowPeriodTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tmcCityComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onTmcCityComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>142</x>
     <y>460</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>InquireButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onInquireButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>150</x>
     <y>592</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>loopCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLoopCheckBoxToggled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>275</x>
     <y>981</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>quickMatchPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onQuickMatchPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>138</x>
     <y>1013</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tmcSelectCheckBox</sender>
   <signal>toggled(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onTmcSelectCheckBoxToggled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>33</x>
     <y>555</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>UUIDlineEdit</sender>
   <signal>textChanged(QString)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onUUIDlineEditTextChanged(QString)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>150</x>
     <y>493</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>TmcInfoList</sender>
   <signal>itemDoubleClicked(QListWidgetItem*)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onTmcInfoListItemDoubleClicked(QListWidgetItem*)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>150</x>
     <y>935</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tmcClearPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onTmcClearPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>150</x>
     <y>629</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>reqTypeComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onReqTypeComboBoxCurrentIndexChanged(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>147</x>
     <y>427</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShowDrAlgoInfo</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionShowDrAlgoInfoTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>LocComboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>clearPoiResult()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>148</x>
     <y>401</y>
    </hint>
    <hint type="destinationlabel">
     <x>624</x>
     <y>437</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>addLocTestPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onAddLocTestPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>289</x>
     <y>693</y>
    </hint>
    <hint type="destinationlabel">
     <x>624</x>
     <y>437</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>locTestOutputPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLocTestOutputPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>157</x>
     <y>1006</y>
    </hint>
    <hint type="destinationlabel">
     <x>624</x>
     <y>437</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>loadSimPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLoadFilePushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>146</x>
     <y>494</y>
    </hint>
    <hint type="destinationlabel">
     <x>544</x>
     <y>430</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>locClipPushButton</sender>
   <signal>clicked(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onlocClipPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>289</x>
     <y>526</y>
    </hint>
    <hint type="destinationlabel">
     <x>544</x>
     <y>430</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionClip</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onlocClipPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>544</x>
     <y>430</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionResetClip</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionResetClip()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>544</x>
     <y>430</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>locTestResultInfoListWidget</sender>
   <signal>customContextMenuRequested(QPoint)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onLocTestResultMenuRequested(QPoint)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>146</x>
     <y>879</y>
    </hint>
    <hint type="destinationlabel">
     <x>511</x>
     <y>399</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionStaticMatch</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionStaticMatch()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>511</x>
     <y>399</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShowToolBox</sender>
   <signal>triggered(bool)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onShowToolBoxTriggered(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>511</x>
     <y>399</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>poiResultListWidget</sender>
   <signal>itemClicked(QListWidgetItem*)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>jumpToPoi(QListWidgetItem*)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>185</x>
     <y>677</y>
    </hint>
    <hint type="destinationlabel">
     <x>121</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>loadMorePushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>loadNextPagePoiResult()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>211</x>
     <y>1090</y>
    </hint>
    <hint type="destinationlabel">
     <x>468</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>loadPreviousPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>loadPrevPagePoiResult()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>61</x>
     <y>1090</y>
    </hint>
    <hint type="destinationlabel">
     <x>280</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>clearResultPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>clearPoiResult()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>261</x>
     <y>1057</y>
    </hint>
    <hint type="destinationlabel">
     <x>227</x>
     <y>0</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>stopSearchTestPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onStopSearchTestPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>309</x>
     <y>658</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>fileSearchTestPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onFileSearchTestPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>208</x>
     <y>658</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>fileSugTestButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onFileSugTestPushButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>101</x>
     <y>658</y>
    </hint>
    <hint type="destinationlabel">
     <x>425</x>
     <y>364</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>FileTestSelectpushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onSelectFileTestButtonClicked()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>289</x>
     <y>458</y>
    </hint>
    <hint type="destinationlabel">
     <x>1032</x>
     <y>49</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>RouteTestStartpushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRouteTestStartClickSlot()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>79</x>
     <y>478</y>
    </hint>
    <hint type="destinationlabel">
     <x>1087</x>
     <y>193</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>RouteTestStopPushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRouteTestStopClickSlot()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>210</x>
     <y>471</y>
    </hint>
    <hint type="destinationlabel">
     <x>1077</x>
     <y>538</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>routePlanList</sender>
   <signal>itemClicked(QListWidgetItem*)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onRoutePlanListItemClicked(QListWidgetItem*)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>65</x>
     <y>349</y>
    </hint>
    <hint type="destinationlabel">
     <x>1209</x>
     <y>205</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>PlaybackIntervalcomboBox</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onPlaybackIntervalComboBoxCurrentIndexChangeslot(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>193</x>
     <y>514</y>
    </hint>
    <hint type="destinationlabel">
     <x>1353</x>
     <y>431</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>SimulCancelpushButton</sender>
   <signal>clicked()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onSimulCancelClickButtonslot()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>232</x>
     <y>723</y>
    </hint>
    <hint type="destinationlabel">
     <x>1313</x>
     <y>666</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actioncompany</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionCompanyTrigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>363</x>
     <y>56</y>
    </hint>
    <hint type="destinationlabel">
     <x>1480</x>
     <y>288</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionhome</sender>
   <signal>triggered()</signal>
   <receiver>MainWindowClass</receiver>
   <slot>onActionHomeTrigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>292</x>
     <y>51</y>
    </hint>
    <hint type="destinationlabel">
     <x>1446</x>
     <y>381</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>onActionOpenTriggered()</slot>
  <slot>onLayerListItemChanged(QListWidgetItem*)</slot>
  <slot>onLayerListContextMenu(QPoint)</slot>
  <slot>onActionAboutTriggered()</slot>
  <slot>onPoiSearch()</slot>
  <slot>onSelectPoiType()</slot>
  <slot>loadPrevPagePoiResult()</slot>
  <slot>loadNextPagePoiResult()</slot>
  <slot>clearPoiResult()</slot>
  <slot>jumpToPoi(QListWidgetItem*)</slot>
  <slot>onActionClearItemsTriggered()</slot>
  <slot>onInfoTreeContextMenu(QPoint)</slot>
  <slot>onActionPreferencesTriggered()</slot>
  <slot>onActionSetStartTriggered()</slot>
  <slot>onActionSetEndTriggered()</slot>
  <slot>onActionAddWayPointTriggered()</slot>
  <slot>onActionShowCursorTriggered(bool)</slot>
  <slot>onRoutePlanListContextMenu(QPoint)</slot>
  <slot>onActionCopyRouteCaseTriggered()</slot>
  <slot>onActionPasteTriggered()</slot>
  <slot>onRoutePlanListItemDoubleClicked(QListWidgetItem*)</slot>
  <slot>onCityItemDoubleClicked(QTreeWidgetItem*)</slot>
  <slot>onActionReverseLinkTriggered()</slot>
  <slot>onRoutingRuleComboBoxCurrentIndexChanged(int)</slot>
  <slot>onViewComboBoxAreaIndexChanged(int)</slot>
  <slot>onViewComboBoxLineIndexChanged(int)</slot>
  <slot>onViewComboBoxPointIndexChanged(int)</slot>
  <slot>onViewFilterSubmit()</slot>
  <slot>onGuideItemMenuRequested(QPoint)</slot>
  <slot>onShowToolBoxTriggered(bool)</slot>
  <slot>onSelectFileTestButtonClicked()</slot>
  <slot>onRouteTestStartClickSlot()</slot>
  <slot>onRouteTestStopClickSlot()</slot>
  <slot>onRoutePlanListItemClicked(QListWidgetItem*)</slot>
  <slot>onPlaybackIntervalComboBoxCurrentIndexChangeslot(int)</slot>
  <slot>onSimulCancelClickButtonslot()</slot>
  <slot>onActionHomeTrigger()</slot>
  <slot>onActionCompanyTrigger()</slot>
 </slots>
</ui>
