#include <resolv.h>

#include <memory>

#include "autocomplete_response_handler.h"
#include "get_detail_response_handler.h"
#include "logger.h"
#include "reverse_geo_response_handler.h"
#include "search/src/utils/file_utils.h"
#include "search_by_text_response_handler.h"
#include "search_def.h"
#include "search_service.h"

using namespace aurora;
using namespace aurora::search;
using namespace std;

std::shared_ptr<SearchService> search_service;

int InitLog(spdlog::level::level_enum level);
int InitSearchService(const std::string &config_file);
void SampleSearchByText(const SearchByTextRequestPtr &request);
void SampleGetDetail(const std::string &id);
void SampleAutocomplete(const std::string &text);
void SampleReverseGeocode(double lng, double lat);

string key = "肯德基";
double lat = 22.603646;
double lng = 114.304172;

// 上海 ./search_sample 肯德基 121.809867 31.497187  肯德基(名岛生活广场店)
// 深圳 ./search_sample 肯德基 114.304172 22.603646  肯德基(大侠谷店)
// 香港 ./search_sample 迪士尼 114.047326 22.309841  香港迪士尼乐园
// 苏州 ./search_sample 格林新材料 120.391281 30.893977  苏州格林新材料科技有限公司

int main(int argc, char *argv[]) {
  if (argc > 2) {
    key = argv[1];
    lng = std::stod(argv[2]);
    lat = std::stod(argv[3]);
    LOG_DEBUG("input lng = {}, lat = {}");
  }
  // 1. init log
  InitLog(spdlog::level::debug);
  // 2. init search service
  std::string config_path = "../data/search";
  if (InitSearchService(config_path) != 0) {
    LOG_FATAL("init search service failed 555");
    return -1;
  }
  // 3. start search sample
  // 3.1 get detail
  SampleGetDetail("16573862606423458048");
  // // 3.2 autocomplete
  // SampleAutocomplete("大");
  SampleAutocomplete("金艺文化");
  // SampleAutocomplete("kendeji");
  // 3.3 reverse geocode
  SampleReverseGeocode(114.076746, 22.527317);
  // 3.4 search by text
  auto request = std::make_shared<SearchByTextRequest>();
  request->search_mode = SearchMode::kOffline;
  request->page_size = 10;
  request->location_restriction = CreateCircularBounds({lng, lat}, 10000);
  request->query = key;
  request->current_location = std::make_shared<PointLL>();
  request->current_location->set_lng(lng);
  request->current_location->set_lat(lat);
  SampleSearchByText(request);

  // request->query = "有限公司";
  // SampleSearchByText(request);
  // request->included_categories.emplace_back("20000");
  // request->included_categories.emplace_back("60000");
  // SampleSearchByText(request);

  // // online services
  // SampleSearchByText("肯德基", SearchMode::kOnline);
  LOG_DEBUG("see you, search");
  while (1) {
    ;
  }
  return 0;
}

int InitLog(spdlog::level::level_enum level) {
  logger::get().set_level(level);
  LOG_DEBUG("hello search");
  return 0;
}

int InitSearchService(const std::string &config_file) {
  search_service = std::make_shared<SearchService>();
  LOG_DEBUG("init search service config_file [{}]", config_file);
  return search_service->Init(config_file);
}

void SampleSearchByText(const SearchByTextRequestPtr &request) {
  auto handler = std::make_shared<SearchByTextResponseHandlerSample>();
  int task_id = search_service->SearchByText(request, handler);
  LOG_INFO("************* SampleSearchByText taskid: {}", task_id);
}

void SampleGetDetail(const std::string &id) {
  GetDetailRequestPtr request = std::make_shared<GetDetailRequest>();
  // place_id can be obtained from other search interfaces or other modules
  request->place_ids.emplace_back(id);  // 智慧家园
  request->search_mode = SearchMode::kOffline;
  auto handler = std::make_shared<GetDetailResponseHandlerSample>();
  int task_id = search_service->GetDetail(request, handler);
  LOG_INFO("************* SampleGetDetail taskid: {}", task_id);
}

void SampleAutocomplete(const std::string &text) {
  auto request = std::make_shared<AutocompleteRequest>();
  request->search_mode = SearchMode::kOffline;
  request->query = text;
  request->max_results = 10;
  // request->current_location = std::make_shared<PointLL>();
  // request->current_location->set_lng(114.045716);
  // request->current_location->set_lat(22.605269);
  auto handler = std::make_shared<AutocompleteResponseHandlerSample>();
  int task_id = search_service->Autocomplete(request, handler);
  LOG_INFO("************* SampleAutocomplete taskid: {}", task_id);
}

void SampleReverseGeocode(double lng, double lat) {
  auto request = std::make_shared<ReverseGeocodeRequest>();
  request->search_mode = SearchMode::kOffline;

  // 31000 121.520772	31.239266 东方路121号	DongFang Lu 121 Hao
  // 全季酒店(上海东方明珠店) 440300 	皇御苑四栋201	 201
  // 金艺文化(皇御苑校区)	114.076746	22.527317
  // 22.527317,114.076746 ==> ws105uuphu70
  // 22.527317,114.076746 ==> ws105uuphu70
  // 22.52733699, 114.07681989 ==> ws105uupnxfp
  // 22.527315, 114.076824 ==> ws105uupnevh
  request->location = PointLL(lng, lat);
  auto handler = std::make_shared<ReverseGeocodeResponseHandlerSample>();
  int task_id = search_service->ReverseGeocode(request, handler);
  LOG_INFO("************* SampleReverseGeocode taskid: {}", task_id);
}