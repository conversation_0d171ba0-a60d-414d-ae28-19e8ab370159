cmake_minimum_required(VERSION 3.14)

project(data_provider)

set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

include(Fetch<PERSON>ontent)
FetchContent_Declare(
    rapidjson
    URL ${CMAKE_SOURCE_DIR}/third_party/rapidjson.tar.gz
)
FetchContent_MakeAvailable(rapidjson)
set(RAPIDJSON_SOURCE_DIR ${rapidjson_SOURCE_DIR})

include_directories(
    ${PROJECT_SOURCE_DIR}
    ${PROJECT_SOURCE_DIR}/../../third_party/zlib
    ${PROJECT_SOURCE_DIR}/../../third_party/sqlite
    ${RAPIDJSON_SOURCE_DIR}/include
)
message("ZLIB_INCLUDE_DIRS:${ZLIB_INCLUDE_DIRS}")
file(GLOB_RECURSE OUTPUT_INCS
    ${PROJECT_SOURCE_DIR}/include/*.h
    ${PROJECT_SOURCE_DIR}/include/display_data/*.h
    ${PROJECT_SOURCE_DIR}/include/route_data/*.h
)
file(GLOB_RECURSE PARSER_SRCS
    ${PROJECT_SOURCE_DIR}/*.cc
    ${PROJECT_SOURCE_DIR}/online_data/*.cc
    ${PROJECT_SOURCE_DIR}/online_data/*.h
    ${PROJECT_SOURCE_DIR}/display_data/*.cc
    ${PROJECT_SOURCE_DIR}/display_data/*.h
    ${PROJECT_SOURCE_DIR}/route_data/*.cc
    ${PROJECT_SOURCE_DIR}/route_data/*.h
    ${PROJECT_SOURCE_DIR}/route_data/feature/*.cc
    ${PROJECT_SOURCE_DIR}/route_data/feature/*.h
)

file(GLOB_RECURSE COMMON_SRC
    ${PROJECT_SOURCE_DIR}/common/*.cc
    ${PROJECT_SOURCE_DIR}/common/*.h
)

file(GLOB_RECURSE CONFIG_SRC
    ${PROJECT_SOURCE_DIR}/config/*.cc
    ${PROJECT_SOURCE_DIR}/config/*.h
)

add_library(data_provider SHARED
    ${PARSER_SRCS}
    ${OUTPUT_INCS}
    ${COMMON_SRC}
    ${CONFIG_SRC}
)

target_link_libraries(data_provider PRIVATE z aurora_base sqlite)

target_include_directories(data_provider
    PUBLIC ${PROJECT_SOURCE_DIR}/include
)
