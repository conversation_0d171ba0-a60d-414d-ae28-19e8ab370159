#include "data_provider.h"

#include "display_data/display_data_parser.h"
#include "route_data/route_data_parser.h"

namespace aurora {
namespace parser {

const uint32_t DISPLAY_CACHE_SIZE = 512;
const uint32_t ROUTE_CACHE_SIZE = 16;

DataProvider::DataProvider() {}

DataProvider::~DataProvider() {}

bool DataProvider::InitDisplayParser(const char* data_path) {
  return DisplayDataParser::Instance().Init(data_path, DISPLAY_CACHE_SIZE);
}

bool DataProvider::GetDisplayTileIDByMbr(uint8_t logic_level, GeoMbr mbr,
                                         DisplayTileIDSet& result) {
  return DisplayDataParser::Instance().GetTileIDByMbr(logic_level, mbr, result);
}

void DataProvider::RequestDisplayTilePackageByID(uint32_t track_id, const DisplayTileIDSet& ids,
                                                 TileDataReceiver& receiver) {
  DisplayDataParser::Instance().RequestTilePackageByID(track_id, ids, receiver);
}

void DataProvider::RequestDisplayTilePackage(uint8_t logic_level, GeoMbr mbr,
                                             TileDataReceiver& receiver) {
  DisplayDataParser::Instance().RequestTilePackage(0, logic_level, mbr, receiver);
}

bool DataProvider::InitRouteParser(const char* data_path) {
  return RouteDataParser::Instance().Init(data_path, ROUTE_CACHE_SIZE);
}
void DataProvider::GetRouteTileIDsByMBR(uint8_t level, LngLatMbr mbr,
                                        std::vector<RouteTileID>& result) {
  return RouteDataParser::Instance().GetRouteTileIDs(level, mbr, result);
}

std::vector<uint32_t> DataProvider::GetAdcodeByMBR(LngLatMbr mbr) {
    return RouteDataParser::Instance().GetAdcodeByMBR(mbr);
}

RouteTilePackagePtr DataProvider::GetRouteTileByID(RouteTileID tile_id) {
  return RouteDataParser::Instance().GetRouteTileByID(tile_id);
}

void DataProvider::SwitchRouteTileCacheMode(bool all_cache_flag) {
  RouteDataParser::Instance().SwitchCacheMode(all_cache_flag);
}

const AdministrativeInfoSet& DataProvider::GetAdminInfo() {
    return RouteDataParser::Instance().GetAdminInfo();
}
}  // namespace parser
}  // namespace aurora
