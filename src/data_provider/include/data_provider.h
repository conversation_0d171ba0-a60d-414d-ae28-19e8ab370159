/**
 * @file data_provider.h
 * @brief Data provider interface definition
 *
 * @details This file defines the @c DataProvider class, a singleton that manages display and route
 * data parsers. It provides initialization of parsers, retrieval of map tile data, and route data
 * query functionalities.
 *
 * <AUTHOR>
 * @date 2025-5-13
 */

#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_DATA_PROVIDER_H
#define MAP_SRC_DATA_PROVIDER_INCLUDE_DATA_PROVIDER_H

#include "display_data/display_data_def.h"
#include "export_type_def.h"
#include "route_data/route_data_def.h"

namespace aurora {
namespace parser {
/**
 * @brief Class providing access to display and route data through parser instances.
 *
 * The class uses the @c Instance() method to retrieve the singleton instance,
 * offering interfaces for initializing parsers and querying spatial data.
 */
class AURORA_EXPORT DataProvider {
public:
  /**
   * @brief Default constructor initializing parser pointers.
   */
  DataProvider();

  /**
   * @brief Destructor releasing parser resources.
   */
  ~DataProvider();

  /**
   * @brief Initialize the display data parser with the specified data path.
   * @param[in] data_path Path to the display data files.
   * @return @c true if initialization succeeds, @c false otherwise.
   */
  bool InitDisplayParser(const char* data_path);

  bool GetDisplayTileIDByMbr(uint8_t logic_level, GeoMbr mbr, DisplayTileIDSet& result);
  void RequestDisplayTilePackageByID(uint32_t track_id, const DisplayTileIDSet& ids, TileDataReceiver& receiver);

  /**
   * @brief Request display tile data based on logical level and geographic bounds.
   * @param[in] logic_level Hierarchical level of the display data.
   * @param[in] mbr Geographic bounding box (GeoMbr type).
   * @param[in,out] receiver Callback object to receive tile data.
   */
  void RequestDisplayTilePackage(uint8_t logic_level, GeoMbr mbr, TileDataReceiver& receiver);

  /**
   * @brief Initialize the route data parser with the specified data path.
   * @param[in] data_path Path to the route data files.
   * @return @c true if initialization succeeds, @c false otherwise.
   */
  bool InitRouteParser(const char* data_path);

  /**
   * @brief Retrieve route tile IDs within a specified geographic area.
   * @param[in] level Tile hierarchy level.
   * @param[in] mbr Geographic bounding box (LngLatMbr type).
   * @param[out] result Vector storing retrieved route tile IDs.
   */
  void GetRouteTileIDsByMBR(uint8_t level, LngLatMbr mbr, std::vector<RouteTileID>& result);

  std::vector<uint32_t> GetAdcodeByMBR(LngLatMbr mbr);

  /**
   * @brief Get the route tile data package by tile ID.
   * @param[in] tile_id Unique identifier of the route tile.
   * @return Shared pointer to the route tile package, or @c nullptr if not found.
   */
  RouteTilePackagePtr GetRouteTileByID(RouteTileID tile_id);

  /**
   * @brief Switch cache mode between limit cache and all cache.
   * @param[in] all_cache_flag, true means cache all tile, false means cache current used tile
   */
  void SwitchRouteTileCacheMode(bool all_cache_flag);

  const AdministrativeInfoSet& GetAdminInfo();
};

}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_DATA_PROVIDER_H
