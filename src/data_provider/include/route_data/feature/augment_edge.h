/**
 * @file augment_edge.h
 * @brief Augmented edge data structure definition
 *
 * @details This file defines the @c AugmentEdge class and some related structure, representing
 * augment edge data with geometric and administrative information. It contains detailed attributes
 * such as geographic coordinates, names in different languages, administrative regions, and
 * infrastructure characteristics like slopes and curves.
 *
 * <AUTHOR>
 * @date 2025-5-13
 */

#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_AUGMENT_EDGE_H
#define MAP_SRC_DATA_PROVIDER_INCLUDE_AUGMENT_EDGE_H

#include "export_type_def.h"
#include "route_data/route_data_def.h"

namespace aurora {
namespace parser {
#pragma pack(push, 1)
/**
 * @brief Flags indicating the presence of additional attributes for an edge in a routing graph.
 */
struct AugmentEdgeBase {
  uint32_t has_middle_point : 1;  ///< Flag indicating if the edge has a middle point
  uint32_t has_road_name : 1;     ///< Flag indicating if the edge has a road name
  uint32_t has_road_no : 1;       ///< Flag indicating if the edge has a road number
  uint32_t has_lane_info : 1;     ///< Flag indicating if lane information is available
  uint32_t has_signpost : 1;      ///< Flag indicating if signpost data exists
  uint32_t has_juncview : 1;      ///< Flag indicating if junction view data exists
  uint32_t has_facility : 1;      ///< Flag indicating if traffic facility data exists
  uint32_t has_tollgate : 1;      ///< Flag indicating if a tollgate is present
  uint32_t not_calc_mbr : 1;  ///< Flag indicating if the MBR (Minimum Bounding Rectangle) should
                              ///< not be calculated
  uint32_t expansion_address : 23;  ///< Reserved address for future extensions (23 bits)

  uint32_t slope_type : 2;    ///< Slope type (e.g., flat, uphill, downhill) encoded in 2 bits
  uint32_t special_flag : 2;  ///< Special attributes or restrictions (2 bits)
  uint32_t
      has_sapa : 1;  ///< Flag indicating if SAPA (Service Area Parking Area) information exists
  uint32_t has_adcode : 1;       ///< Flag indicating if administrative code is available
  uint32_t has_slope_point : 1;  ///< Flag indicating if slope points are defined
  uint32_t has_curve_point : 1;  ///< Flag indicating if curve points are defined
  uint32_t has_voice : 1;        ///< Flag indicating if voice guidance data exists
  uint32_t not_used : 7;         ///< Reserved bits (not used)
};
/**
 * @brief Detailed information about a Service Area Parking Area (SAPA).
 */
struct SaPaInfo {
  uint16_t name_addr;  ///< Memory address of the SAPA's name string
  uint16_t lng;        ///< Longitude coordinate of the SAPA (in units)
  uint16_t lat;        ///< Latitude coordinate of the SAPA (in units)

  uint16_t type : 2;                   ///< SAPA type (e.g., SA, GS, PA) encoded in 2 bits
  uint16_t has_gas : 1;                ///< Flag indicating availability of gasoline
  uint16_t has_natual_gas : 1;         ///< Flag indicating availability of natural gas
  uint16_t has_toilet : 1;             ///< Flag indicating availability of toilets
  uint16_t has_internal_rest : 1;      ///< Flag indicating availability of internal rest areas
  uint16_t has_external_rest : 1;      ///< Flag indicating availability of external rest areas
  uint16_t has_coffee : 1;             ///< Flag indicating availability of coffee services
  uint16_t has_dining : 1;             ///< Flag indicating availability of dining facilities
  uint16_t has_shop : 1;               ///< Flag indicating availability of retail shops
  uint16_t has_stay_service : 1;       ///< Flag indicating accommodation services
  uint16_t has_repair_service : 1;     ///< Flag indicating vehicle repair services
  uint16_t has_atm : 1;                ///< Flag indicating availability of ATMs
  uint16_t has_drug : 1;               ///< Flag indicating availability of pharmacies
  uint16_t has_speciality : 1;         ///< Flag indicating availability of specialty stores
  uint16_t has_disabled_facility : 1;  ///< Flag indicating accessibility for disabled individuals

  uint16_t has_public_phone : 1;     ///< Flag indicating availability of public telephones
  uint16_t has_vending_machine : 1;  ///< Flag indicating availability of vending machines
  uint16_t has_shower : 1;           ///< Flag indicating availability of shower facilities
  uint16_t has_parking : 1;          ///< Flag indicating availability of parking
  uint16_t not_used : 4;             ///< Reserved bits (not used)
};

struct Sapa {
  SaPaInfo* info;
  std::string name;
  PointLL lnglat;
};
using SapaPtr = std::shared_ptr<Sapa>;

/**
 * @brief Represents a sub-edge within a tile, containing its identifier and direction.
 */
struct SubEdge {
  uint32_t tile_id : 8;   ///< Identifier of the containing tile (8 bits)
  uint32_t edge_dir : 1;  ///< Edge direction flag (e.g., forward/reverse)
  uint32_t edge_id : 23;  ///< Unique identifier of the edge within the tile (23 bits)
};

/**
 * @brief Represents a point along a road with slope-related attributes.
 */
struct SlopePoint {
  uint64_t lng : 20;       ///< Longitude coordinate (20 bits of precision)
  uint64_t lat : 20;       ///< Latitude coordinate (20 bits of precision)
  uint64_t range : 9;      ///< Slope range or gradient value (9 bits)
  uint64_t flag : 1;       ///< Slope-related flag (e.g., uphill/downhill)
  uint64_t not_used : 14;  ///< Reserved bits (not used)
  uint32_t start_pos;      ///< Starting position of the slope segment along the edge
};
/**
 * @brief Represents a point along a road with curve-related attributes.
 */
struct CurvePoint {
  uint64_t lng : 20;      ///< Longitude coordinate (20 bits of precision)
  uint64_t lat : 20;      ///< Latitude coordinate (20 bits of precision)
  uint64_t curve : 20;    ///< Curve parameter (e.g., curvature radius) (20 bits)
  uint64_t dir_flag : 1;  ///< Direction flag indicating curve orientation
  uint64_t is_hd : 1;     ///< Flag indicating if the point is part of a high-definition (HD) map
  uint64_t not_used : 2;  ///< Reserved bits (not used)
};
#pragma pack(pop)

/**
 * @brief Represents administrative information for a region, including codes and names.
 */
struct AdminInfo {
  uint32_t adcode;                 ///< Administrative region code
  std::string local_name;    ///< Local name of the region
  std::string foreign_name;  ///< Foreign name of the region (e.g., English name)

  /**
   * @brief Default constructor initializing all fields to default values.
   */
  AdminInfo() : adcode(0), local_name(), foreign_name() {}
};

/**
 * @brief Augment edge data container with geometric and administrative details.
 *
 * The @c AugmentEdge class stores comprehensive information about a road edge, including:
 * - Unique identifier and geographic coordinates
 * - Names in local/foreign languages and road numbers
 * - Administrative region associations (e.g., provinces, cities)
 * - Infrastructure features like slopes, curves, and subedges
 */
class AURORA_EXPORT AugmentEdge {
public:
  /**
   * @brief Default constructor initializing empty data containers.
   */
  AugmentEdge();

  /**
   * @brief Get the unique identifier of this augmented edge.
   * @return 32-bit unsigned integer ID.
   */
  uint32_t GetID() { return id_; }

  /**
   * @brief Get the geographic bounding box (MBR) of the edge.
   * @return Const reference to the LngLatMbr structure.
   */
  const LngLatMbr& GetMbr() { return mbr_; }

  /**
   * @brief Get the list of geographic points defining the edge's path.
   * @return Const reference to the vector of LngLat.
   */
  const LngLatSet& GetGeoPoints() { return geo_points_; }

  /**
   * @brief Get base information of the edge (e.g., direction, type).
   * @return Pointer to the AugmentEdgeBase structure, or @c nullptr if unset.
   */
  const AugmentEdgeBase* GetBaseInfo() { return base_; }

  /**
   * @brief Get the localized name of the edge (e.g., in local language).
   * @return Const pointer to the first character of the name string.
   */
  const char* GetLocalName();

  /**
   * @brief Get the foreign name of the edge (e.g., in English).
   * @return Const pointer to the first character of the name string.
   */
  const char* GetForeignName();

  /**
   * @brief Get the official road number identifier (e.g., "US-101").
   * @return Const pointer to the first character of the road number string.
   */
  const char* GetRoadNo();

  /**
   * @brief Get subedges composing this augmented edge.
   * @return Const reference to the vector of SubEdge pointers.
   */
  const std::vector<SubEdge*>& GetSubedges() { return subedges_; }

  /**
   * @brief Get SAPA information.
   * @return Pointer to the SaPaInfo structure, or @c nullptr if unset.
   */
  const SapaPtr GetSaPa() { return sapa_; }

  /**
   * @brief Get administrative region information associated with this edge.
   * @return Shared pointer to the AdminInfo structure.
   */
  const std::shared_ptr<AdminInfo> GetAdminInfo() { return adcode_; }

  /**
   * @brief Get slope points along the edge's path.
   * @return Const reference to the vector of SlopePoint pointers.
   */
  const std::vector<SlopePoint*>& GetSlopePoint() { return slope_point_; }

  /**
   * @brief Get curve points along the edge's path.
   * @return Const reference to the vector of CurvePoint pointers.
   */
  const std::vector<CurvePoint*>& GetCurvePoint() { return curve_point_; }

private:
  /**
   * @brief Unique identifier of this augmented edge.
   */
  uint32_t id_;

  /**
   * @brief Base attributes of the edge (direction, type, etc.).
   */
  AugmentEdgeBase* base_;

  /**
   * @brief Localized name of the edge stored as a vector of characters.
   */
  std::string local_name_;

  /**
   * @brief Foreign name of the edge stored as a vector of characters.
   */
  std::string foreign_name_;

  /**
   * @brief Official road number stored as a vector of characters.
   */
  std::string road_no_;

  /**
   * @brief List of geographic coordinates defining the edge's path.
   */
  LngLatSet geo_points_;

  /**
   * @brief Minimum bounding rectangle (MBR) of the edge's geometry.
   */
  LngLatMbr mbr_;

  /**
   * @brief Subedges composing this augmented edge.
   */
  std::vector<SubEdge*> subedges_;

  /**
   * @brief Spatial analysis information (intersections, etc.).
   */
  SapaPtr sapa_;

  /**
   * @brief Administrative region information (province/city codes).
   */
  std::shared_ptr<AdminInfo> adcode_;

  /**
   * @brief List of slope points along the edge's path.
   */
  std::vector<SlopePoint*> slope_point_;

  /**
   * @brief List of curve points along the edge's path.
   */
  std::vector<CurvePoint*> curve_point_;

  // Friend declaration for access to private members
  friend class RouteTileParser;
};
typedef std::vector<AugmentEdge> AugmentEdgeSet;

}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_AUGMENT_EDGE_H
