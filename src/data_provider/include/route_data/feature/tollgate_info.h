#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_TOLLGATE_INFO_H
#define MAP_SRC_DATA_PROVIDER_INCLUDE_TOLLGATE_INFO_H

#include "export_type_def.h"
#include "route_data/route_data_def.h"

namespace aurora {
namespace parser {
#pragma pack(push, 1)

struct TollgateBase {
  uint32_t in_edge_id : 23;
  uint32_t in_edge_dir : 1;
  uint32_t is_same_mesh : 1;
  uint32_t is_same_tile : 1;
  uint32_t not_used : 6;

  uint16_t out_mesh_col;
  uint16_t out_mesh_row;
  uint32_t out_tile_id : 8;
  uint32_t out_edge_dir : 1;
  uint32_t out_edge_id : 23;

  char background_img_no[IMAGE_NO_LENGTH];
  uint16_t name_offset;
  uint16_t toll_gate_start_id;
  uint8_t toll_gate_count;
  TollgateBase() { ::memset(this, 0, sizeof(*this)); }
};

struct TollgateGateInfo {
  uint8_t cash : 1;       // Cash Lane
  uint8_t etc : 1;        // ETC (SunTong) Lane
  uint8_t auto_card : 1;  // Auto Card Issuance
  uint8_t alipay : 1;     // Alipay Payment
  uint8_t wechat : 1;     // WeChat Payment
  uint8_t itc : 1;        // ITC (Intelligent Toll Collection) Lane
  uint8_t not_used1 : 2;  // Reserved (2 bits)

  uint8_t normal : 1;     // Normal Lane
  uint8_t hk_macao : 1;   // Hong Kong/Macau (Right-hand Drive) Lane
  uint8_t general : 1;    // General Lane
  uint8_t wide_lane : 1;  // Wide Lane
  uint8_t not_used2 : 4;  // Reserved (4 bits)
  TollgateGateInfo() { ::memset(this, 0, sizeof(*this)); }
};
#pragma pack(pop)
class TollgateInfo {
public:
  TollgateInfo();
  TollgateInfo(const TollgateBase* base);
  const TollgateBase* GetBaseInfo() { return base_; }
  uint8_t GetTollgateGateCount();
  const TollgateGateInfo* GetTollgateGateInfo(uint8_t index);
  ImagePtr GetBackground();
  const char* GetName();
  const char* GetForeignName();

  bool operator<(const TollgateInfo& other) const {
    if (base_ != nullptr && other.base_ != nullptr) {
      if (base_->in_edge_id != other.base_->in_edge_id) {
        return base_->in_edge_id < other.base_->in_edge_id;
      } else if (base_->in_edge_dir != other.base_->in_edge_dir) {
        return base_->in_edge_dir < other.base_->in_edge_dir;
      } else if (base_->out_mesh_col != other.base_->out_mesh_col) {
        return base_->out_mesh_col < other.base_->out_mesh_col;
      } else if (base_->out_mesh_row != other.base_->out_mesh_row) {
        return base_->out_mesh_row < other.base_->out_mesh_row;
      } else if (base_->out_tile_id != other.base_->out_tile_id) {
        return base_->out_tile_id < other.base_->out_tile_id;
      } else if (base_->out_edge_id != other.base_->out_edge_id) {
        return base_->out_edge_id < other.base_->out_edge_id;
      } else if (base_->out_edge_dir != other.base_->out_edge_dir) {
        return base_->out_edge_dir < other.base_->out_edge_dir;
      } else {
      }
    }
    return this < &other;
  }
  bool operator==(const TollgateInfo& other) const {
    if (base_ != nullptr && other.base_ != nullptr) {
      return base_->in_edge_id == other.base_->in_edge_id &&
             base_->in_edge_dir == other.base_->in_edge_dir &&
             base_->out_mesh_col == other.base_->out_mesh_col &&
             base_->out_mesh_row == other.base_->out_mesh_row &&
             base_->out_tile_id == other.base_->out_tile_id &&
             base_->out_edge_id == other.base_->out_edge_id &&
             base_->out_edge_dir == other.base_->out_edge_dir;
    }
    return this == &other;
  }

private:
  uint32_t adcode_;
  const TollgateBase* base_;
  std::vector<TollgateGateInfo> gates_;
  std::string name_;
  std::string foreign_name_;
  // Friend declaration for access to private members
  friend class RouteTileParser;
};
typedef std::vector<TollgateInfo> TollgateInfoSet;
}  // namespace parser
}  // namespace aurora

#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_TOLLGATE_INFO_H
