/**
 * @file route_data_def.h
 * @brief Route data type defination
 *
 * @details This file defines some structure for route data.
 *
 * <AUTHOR>
 * @date 2025-5-15
 */
#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_DATA_DEF_H
#define MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_DATA_DEF_H

#include <cstdint>
#include <memory>
#include <vector>

#include "aabb2.h"
#include "point2.h"

namespace aurora {
namespace parser {
/**
 * @brief Enumeration representing different road function classes.
 */
enum FunctionClass {
  kFunctionClass0 = 0,  ///< Highway
  kFunctionClass1,      ///< City expressway
  kFunctionClass2,      ///< Primary road
  kFunctionClass3,      ///< Secondary road
  kFunctionClass4,      ///< General road
  kFunctionClass5,      ///< Intra-area road

  FC_Count  ///< Total count of function classes
};

/**
 * @brief Enumeration representing different types of edges (road segments).
 */
enum EdgeType {
  kEdgeTypeNormal = 0,  ///< Normal road segment
  kEdgeTypeFerry,       ///< Ferry route
  kEdgeTypeTunnel,      ///< Tunnel segment
  kEdgeTypeBridge,      ///< Bridge segment

  kEdgeTypeCount  ///< Total count of edge types
};

/**
 * @brief Enumeration representing the directionality of edge (road) access.
 */
enum EdgeDirection {
  kEdgeDirectionNoPass = 0,   ///< Bidirectional prohibition (no passage allowed)
  kEdgeDirectionForwardPass,  ///< Forward single-direction passage
  kEdgeDirectionReversePass,  ///< Reverse single-direction passage
  kEdgeDirectionAllPass,      ///< Bidirectional passage allowed

  kEdgeDirectionCount  ///< Total count of direction types
};

/**
 * @brief Enumeration representing different forms/structures of edges (road segments).
 */
enum EdgeForm {
  kEdgeFormNormal = 0,          ///< Standard road segment
  kEdgeFormIC,                  ///< Interchange (IC)
  kEdgeFormJCT,                 ///< Junction (JCT)
  kEdgeFormRoundabout,          ///< Roundabout
  kEdgeFormSAPA,                ///< Service Area Parking Area (SAPA)
  kEdgeFormSideRoad,            ///< Side road (auxiliary lane)
  kEdgeFormPedestrianStreet,    ///< Pedestrian-only street
  kEdgeFormRightTurnA,          ///< Right-turn lane A
  kEdgeFormRightTurnB,          ///< Right-turn lane B
  kEdgeFormLeftTurnA,           ///< Left-turn lane A
  kEdgeFormLeftTurnB,           ///< Left-turn lane B
  kEdgeFormLeftRightTurn,       ///< Combined left-right turn lane
  kEdgeFormNoMotor,             ///< Non-motorized vehicle lane
  kEdgeFormMainRoad,            ///< Main road segment
  kEdgeFormUTurn,               ///< U-turn lane
  kEdgeFormSideRoadRightTurnB,  ///< Right-turn lane B + side road
  kEdgeFormSideRoadLeftTurnB,   ///< Left-turn lane B + side road
  kEdgeFormSAPAJCT,             ///< SAPA + junction
  KEdgeFormManoeuvre,           ///< Manoeuvre road

  kEdgeFormCount  ///< Total count of edge forms
};

/**
 * @brief Enumeration representing speed grades for road segments.
 */
enum SpeedGrade {
  kSpeedGrade0 = 0,  ///< Speed grade 0: 130 km/h (>130 km/h)
  kSpeedGrade1,      ///< Speed grade 1: 100 km/h (101-130 km/h)
  kSpeedGrade2,      ///< Speed grade 2: 90 km/h (91-100 km/h)
  kSpeedGrade3,      ///< Speed度 grade 3: 70 km/h (71-90 km/h)
  kSpeedGrade4,      ///< Speed grade 4: 50 km/h (51-70 km/h)
  kSpeedGrade5,      ///< Speed grade 5: 30 km/h (31-50 km/h)
  kSpeedGrade6,      ///< Speed grade 6: 20 km/h (11-30 km/h)
  kSpeedGrade7,      ///< Speed grade 7: 10 km/h (<11 km/h)

  kSpeedGradeCount  ///< Total count of speed grades
};

/**
 * @brief Enumeration representing different road classes.
 */
enum RoadClass {
  kRoadClassHighway = 0,  ///< Highway (freeway)

  kRoadClassNationWay,     ///< National highway
  kRoadClassProvinceWay,   ///< Provincial road
  kRoadClassCountyWay,     ///< County road
  kRoadClassTownWay,       ///< Township road
  kRoadClassInCountyRoad,  ///< Township/county internal road

  kRoadClassCityFastWay,    ///< City expressway
  kRoadClassMainRoad,       ///< Main arterial road
  kRoadClassSecondaryRoad,  ///< Secondary road
  kRoadClassGeneralRoad,    ///< General road
  kRoadClassPath,           ///< Path

  kRoadClassCount  ///< Total count of road classes
};

/**
 * @brief Enumeration representing slope types for road segments.
 */
enum SlopeType {
  kSlopeTypeFlat = 0,  ///< Flat slope (no incline)
  kSlopeTypeUpHill,    ///< Uphill slope
  kSlopeTypeDownHill,  ///< Downhill slope

  kSlopeTypeCount  ///< Total count of slope types
};

/**
 * @brief Enumeration representing different types of Service Area Parking Areas (SAPA).
 */
enum SAPAType {
  kSAPATypeSA = 0,  ///< Service Area (SA)
  kSAPATypeGS,      ///< Gas Station (GS)
  kSAPATypePA,      ///< Parking Area (PA)

  kSAPATypeCount  ///< Total count of SAPA types
};

/**
 * @brief Enumeration representing different access control types for road segments.
 */
enum AccessCtrlType {
  kAccessCtrlType0 = 0,  ///< Non-access controlled (no restrictions)
  kAccessCtrlType1,      ///< Unrestricted access control (open to all)
  kAccessCtrlType2,      ///< Card-based access control (requires card)
  kAccessCtrlType3,      ///< Authorized access control (requires permission)
  kAccessCtrlType4,      ///< Toll-based access control (requires payment)
  kAccessCtrlType5,      ///< Emergency access control (for emergencies)
  kAccessCtrlType6,      ///< Non-passable access control (blocked)

  kAccessCtrlTypeCount  ///< Total count of access control types
};

/**
 * @brief Enumeration representing relationships between access control and restrictions.
 */
enum AccessCtrlRelation {
  kAccessCtrlRelation0 = 0,  ///< Only access control or only prohibition
  kAccessCtrlRelation1,      ///< Access control + prohibition

  kAccessCtrlRelationCount  ///< Total count of access control relations
};

/**
 * @brief Represents a longitude-latitude coordinate pair (double precision).
 */
// typedef PointXY<double> LngLat;
using LngLat = PointLL;
typedef std::vector<LngLat> LngLatSet;

/**
 * @brief Axis-Aligned Bounding Box (AABB) structure using LngLat coordinates for spatial queries.
 */
typedef AABB2<LngLat> LngLatMbr;

/**
 * @brief Represents the positional flags of a tile within a mesh.
 */
union TilePositionInMesh {
  uint8_t code;  ///< Combined 8-bit value representing all flags

  struct {
    uint8_t is_top_in_mesh : 1;     ///< Flag indicating if the tile is at the top of the mesh
    uint8_t is_bottom_in_mesh : 1;  ///< Flag indicating if the tile is at the bottom of the mesh
    uint8_t is_left_in_mesh : 1;    ///< Flag indicating if the tile is at the left edge of the mesh
    uint8_t is_right_in_mesh : 1;  ///< Flag indicating if the tile is at the right edge of the mesh
    uint8_t not_used : 4;          ///< Reserved bits (not used)
  };
};
/**
 * @brief Represents time constraints (hours and minutes) for a limit period.
 */
struct LimitTime {
  uint16_t clock_hour : 5;     ///< Hour component (0-23)
  uint16_t clock_minute : 3;   ///< Minute component in 10-minute increments (0-5)
  uint16_t period_hour : 5;    ///< Duration hour component
  uint16_t period_minute : 3;  ///< Duration minute component in 10-minute increments

  /**
   * @brief Default constructor initializing all fields to zero.
   */
  LimitTime() { ::memset(this, 0, sizeof(*this)); }
};
/**
 * @brief Represents day-of-week constraints for a limit period.
 */
struct LimitDay {
  uint8_t start_weekday : 4;  ///< Start weekday (Sunday=1, Monday=2, ..., Saturday=7)
  uint8_t period_day : 4;     ///< Duration in days

  /**
   * @brief Default constructor initializing all fields to zero.
   */
  LimitDay() { ::memset(this, 0, sizeof(*this)); }
};

/**
 * @brief Represents month and day constraints for a limit period.
 */
struct LimitMonth {
  uint32_t start_month : 4;   ///< Start month (1-12)
  uint32_t start_day : 5;     ///< Start day of the month (1-31)
  uint32_t period_month : 4;  ///< Duration in months
  uint32_t period_day : 5;    ///< Duration in days
  uint32_t reserved : 14;     ///< Reserved bits for future use

  /**
   * @brief Default constructor initializing all fields to zero.
   */
  LimitMonth() { ::memset(this, 0, sizeof(*this)); }
};

/**
 * @brief Represents a collection of time, day, and month constraints for a limit period.
 */
struct LimitPeriod {
  LimitTime time[8];            ///< Array of time constraints
  LimitDay day[8];              ///< Array of day constraints
  LimitMonth month[8];          ///< Array of month constraints
  uint16_t timeCnt : 4;         ///< Number of valid time constraints
  uint16_t dayCnt : 4;          ///< Number of valid day constraints
  uint16_t monthCnt : 4;        ///< Number of valid month constraints
  uint16_t holidayExclude : 2;  ///< Flag indicating exclusion of holidays
  uint16_t holidayInclude : 2;  ///< Flag indicating inclusion of holidays

  /**
   * @brief Default constructor initializing all fields to zero.
   */
  LimitPeriod() { ::memset(this, 0, sizeof(*this)); }
};

typedef std::shared_ptr<LimitPeriod> LimitPeriodPtr;

class RouteTilePackage;
/**
 * @brief Represents pointer type for route tile package.
 */
typedef std::shared_ptr<RouteTilePackage> RouteTilePackagePtr;
typedef std::vector<RouteTilePackagePtr> RouteTilePackages;

/**
 * @brief Represents the identifier for a routing tile, combining administrative code, level, and
 * position.
 */
struct RouteTileID {
  union {
    uint64_t value;  ///< Combined 64-bit value representing all fields
    struct {
      uint32_t adcode : 30;    ///< Administrative code (30 bits)
      uint32_t level : 2;      ///< Tile level (2 bits)
      uint32_t mesh_col : 12;  ///< Column index within the mesh (12 bits)
      uint32_t mesh_row : 12;  ///< Row index within the mesh (12 bits)
      uint32_t tile_id : 8;    ///< Unique tile identifier (8 bits)
    };
  };

  /**
   * @brief Default constructor initializing all fields to zero.
   */
  RouteTileID() : value(0U) {}

  RouteTileID(uint64_t val) : value(val) {}

  /**
   * @brief Constructor to initialize the tile ID with specific parameters.
   * @param adcode Administrative code (30 bits)
   * @param level Tile level (2 bits)
   * @param col Column index within the mesh (12 bits)
   * @param row Row index within the mesh (12 bits)
   * @param tile_id Unique tile identifier (8 bits)
   */
  RouteTileID(uint32_t adcode, uint8_t level, uint16_t col, uint16_t row, uint16_t tile_id) {
    set(adcode, level, col, row, tile_id);
  }

  /**
   * @brief Sets the tile ID fields with specific parameters.
   * @param adcode Administrative code (30 bits)
   * @param level Tile level (2 bits)
   * @param col Column index within the mesh (12 bits)
   * @param row Row index within the mesh (12 bits)
   * @param tile_id Unique tile identifier (8 bits)
   */
  void set(uint32_t adcode, uint8_t level, uint16_t col, uint16_t row, uint16_t tile_id) {
    this->adcode = adcode;
    this->level = level;
    this->mesh_col = col;
    this->mesh_row = row;
    this->tile_id = tile_id;
  }

  bool operator==(const RouteTileID& tile) const { return this->value == tile.value; }
  bool operator!=(const RouteTileID& tile) const { return this->value != tile.value; }
  bool operator<(const RouteTileID& tile) const { return this->value < tile.value; }
  bool operator>(const RouteTileID& tile) const { return this->value > tile.value; }
};

/**
 * @brief Administrative information for city list.
 */
struct AdministrativeInfo {
  std::string province_name;
  std::string city_name;
  LngLat center;
  int32_t adcode;

  bool operator<(const AdministrativeInfo& info) const { return adcode < info.adcode; }
};
typedef std::vector<AdministrativeInfo> AdministrativeInfoSet;

/**
 * @brief Represents a unique identifier for a feature within a routing tile.
 */
struct FeatureID {
  RouteTileID tile_id;  ///< Tile identifier containing the feature
  uint32_t feature_id;  ///< Unique feature identifier within the tile

  /**
   * @brief Default constructor initializing all fields to zero.
   */
  FeatureID() : tile_id(), feature_id(0) {}

  /**
   * @brief Constructor to initialize the feature ID with a tile ID and feature ID.
   * @param tile_id The containing tile identifier
   * @param feature_id Unique feature identifier within the tile
   */
  FeatureID(RouteTileID tile_id, uint32_t feature_id) { set(tile_id, feature_id); }

  /**
   * @brief Sets the feature ID fields with specific parameters.
   * @param tile_id The containing tile identifier
   * @param feature_id Unique feature identifier within the tile
   */
  void set(RouteTileID tile_id, uint32_t feature_id) {
    this->tile_id = tile_id;
    this->feature_id = feature_id;
  }

  bool operator==(const FeatureID& feature) const {
    return this->tile_id == feature.tile_id && this->feature_id == feature.feature_id;
  }

  bool operator<(const FeatureID& feature) const {
    if (this->tile_id != feature.tile_id) {
      return (this->tile_id < feature.tile_id);
    }
    return (this->feature_id < feature.feature_id);
  }
};

/**
 * @brief Type alias for a routing edge identifier (based on FeatureID).
 */
typedef FeatureID RouteEdgeID;
typedef std::vector<RouteEdgeID> RouteEdgeIDSet;

/**
 * @brief Type alias for a routing node identifier (based on FeatureID).
 */
typedef FeatureID RouteNodeID;
typedef std::vector<RouteNodeID> RouteNodeIDSet;

const uint8_t IMAGE_NO_LENGTH = 16;
typedef std::vector<int8_t> ImageBuf;
typedef std::shared_ptr<ImageBuf> ImagePtr;
}  // namespace parser
}  // namespace aurora

namespace std {
template <>
struct hash<aurora::parser::RouteTileID> {
  size_t operator()(const aurora::parser::RouteTileID& id) const {
    return std::hash<int64_t>{}(id.value);
  }
};

template <>
struct hash<aurora::parser::FeatureID> {
  size_t operator()(const aurora::parser::FeatureID& id) const {
    return std::hash<int64_t>{}(id.tile_id.value) ^ std::hash<int32_t>{}(id.feature_id);
  }
};

}  // namespace std
#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_DATA_DEF_H
