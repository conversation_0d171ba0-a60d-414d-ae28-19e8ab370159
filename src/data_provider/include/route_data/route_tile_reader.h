/**
 * @file route_tile_reader_def.h
 * @brief Defines the RouteTileReader class for accessing route tile data.
 * <AUTHOR> Development Team
 * @date 2023
 * @copyright Copyright (c) 2023 DeepSeek. All rights reserved.
 *
 * This file defines the RouteTileReader class, which provides an interface for accessing
 * route tile data including nodes, edges, and topological relationships. It supports:
 * - Spatial queries based on geographic boundaries
 * - Hierarchical edge traversal between different network layers
 * - Adjacent tile ID resolution using boundary nodes
 * - Direct access to data structures via ID lookups
 *
 * The class operates on a RouteTilePackage instance set via SetTarget(). All data access
 * methods require a valid package to be set before use.
 */

#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_TILE_READER_DEF_H
#define MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_TILE_READER_DEF_H

#include "export_type_def.h"
#include "route_data/feature/augment_edge.h"
#include "route_data/feature/facility_info.h"
#include "route_data/feature/juncview_info.h"
#include "route_data/feature/lane_info.h"
#include "route_data/feature/limit_pass.h"
#include "route_data/feature/route_node.h"
#include "route_data/feature/signpost_info.h"
#include "route_data/feature/tollgate_info.h"
#include "route_data/feature/topol_edge.h"
#include "route_data/feature/voice_info.h"
#include "route_data/route_data_def.h"

namespace aurora {
namespace parser {

/**
 * @class RouteTileReader
 * @brief Route tile data access interface
 *
 * This class provides read-only access to route tile data including:
 * - Nodes, edges, and topological relationships
 * - Spatial queries using geographic boundaries
 * - Hierarchical edge navigation between different network layers
 * - Adjacent tile ID resolution through boundary nodes
 *
 * The reader operates on a RouteTilePackage instance set via SetTarget(). All data
 * access methods require a valid package to be set prior to use.
 */
class AURORA_EXPORT RouteTileReader {
public:
  /**
   * @brief Default constructor for unbound reader instance
   *
   * Creates an instance without associated route tile package. Must call SetTarget()
   * before any data access operations.
   */
  RouteTileReader();

  /**
   * @brief Set the target route tile package
   * @param ptr Shared pointer to the target route tile package
   *
   * Binds the reader to the specified route tile package. Passing a null pointer will:
   * - Clear current package association
   * - Invalidate all subsequent data access operations until new package is set
   */
  void SetTarget(RouteTilePackagePtr ptr);

  /**
   * @brief Get the current route tile ID
   * @return Route tile ID of the current package (0 indicates no package set)
   */
  RouteTileID GetTileID();

//  /**
//   * @brief Get the geographic bounding box of the current tile
//   * @return Pointer to LngLatMbr boundary box (nullptr if no package set)
//   */
//  const LngLatMbr* GetMbr();

  /**
   * @brief Query augment edges within specified geographic boundary
   * @param mbr Geographic boundary for spatial query
   * @return Vector of augment edge IDs within the specified boundary
   *
   * Performs spatial search for edges whose geometry intersects with the input MBR.
   */
  std::vector<uint32_t> GetAugmentEdgesByMbr(LngLatMbr mbr);

  /**
   * @brief Get all augment edges in current tile
   * @return Reference to the augment edge collection
   */
  AugmentEdgeSet& GetAugmentEdges();

  /**
   * @brief Get all topological edges in current tile
   * @return Reference to the topological edge collection
   */
  TopolEdgeSet& GetTopolEdges();

  /**
   * @brief Get all route nodes in current tile
   * @return Reference to the route node collection
   */
  RouteNodeSet& GetNodes();

  /**
   * @brief Get all limit pass rules in current tile
   * @return Reference to the limit pass collection
   */
  LimitPassSet& GetLimitPass();
  LaneInfoSet& GetLaneInfo();
  FacilityInfoSet& GetFacilityInfo();
  JuncviewInfoSet& GetJuncviewInfo();
  SignpostInfoSet& GetSignpostInfo();
  TollgateInfoSet& GetTollgateInfo();
  VoiceInfoSet& GetVoiceInfo();

  /**
   * @brief Get augment edge by ID
   * @param id Unique edge identifier
   * @return Pointer to the augment edge (nullptr if not found)
   */
  AugmentEdge* GetAugmentEdgeByID(uint32_t id);

  /**
   * @brief Get topological edge by ID
   * @param id Unique edge identifier
   * @return Pointer to the topological edge (nullptr if not found)
   */
  TopolEdge* GetTopolEdgeByID(uint32_t id);

  /**
   * @brief Get route node by ID
   * @param id Unique node identifier
   * @return Pointer to the route node (nullptr if not found)
   */
  RouteNode* GetNodeByID(uint32_t id);

  /**
   * @brief Resolve adjacent tile ID via boundary node
   * @param boundary_node Boundary node reference
   * @return Adjacent tile ID (0 indicates no adjacent tile found)
   */
  RouteTileID GetAdjacentTileIDByBoundaryNode(const BoundaryNode* boundary_node);

  /**
   * @brief Get incoming edge IDs terminating at the specified node
   * @param[in] src_node Node ID where edges terminate
   * @param[out] dst_edges Vector to store retrieved edge IDs
   * @param[out] is_forward Vector to store edge IDs's end node is src_node(true) or not
   * @return true if at least one edge was found, false otherwise
   *
   * Retrieves all edges where the specified node is the destination endpoint.
   */
  bool GetInEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges, std::vector<bool>& is_forward);

  /**
   * @brief Get upward hierarchical incoming edges
   * @param[in] src_node Node ID for edge query
   * @param[out] dst_edges Vector to store edge IDs from lower layers
   * @param[out] is_forward Vector to store edge IDs's end node is src_node(true) or not
   * @return true if edges found from lower network layers, false otherwise
   *
   * Returns edges originating from lower network layers (e.g., street level)
   * terminating at the specified node in higher layer.
   */
  bool GetTransUpInEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                          std::vector<bool>& is_forward);

  /**
   * @brief Get downward hierarchical incoming edges
   * @param[in] src_node Node ID for edge query
   * @param[out] dst_edges Vector to store edge IDs from upper layers
   * @param[out] is_forward Vector to store edge IDs's end node is src_node(true) or not
   * @return true if edges found from upper network layers, false otherwise
   *
   * Returns edges originating from higher network layers (e.g., highway level)
   * terminating at the specified node in lower layer.
   */
  bool GetTransDownInEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                            std::vector<bool>& is_forward);

  /**
   * @brief Get outgoing edge IDs originating from the specified node
   * @param[in] src_node Node ID where edges originate
   * @param[out] dst_edges Vector to store retrieved edge IDs
   * @param[out] is_forward Vector to store edge IDs's start node is src_node(true) or not
   * @return true if at least one edge was found, false otherwise
   */
  bool GetOutEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges, std::vector<bool>& is_forward);

  /**
   * @brief Get upward hierarchical outgoing edges
   * @param[in] src_node Node ID for edge query
   * @param[out] dst_edges Vector to store edge IDs to higher layers
   * @param[out] is_forward Vector to store edge IDs's start node is src_node(true) or not
   * @return true if edges found to higher network layers, false otherwise
   *
   * Returns edges from the specified node to higher network layers (e.g., highways).
   */
  bool GetTransUpOutEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                           std::vector<bool>& is_forward);

  /**
   * @brief Get downward hierarchical outgoing edges
   * @param[in] src_node Node ID for edge query
   * @param[out] dst_edges Vector to store edge IDs to lower layers
   * @param[out] is_forward Vector to store edge IDs's start node is src_node(true) or not
   * @return true if edges found to lower network layers, false otherwise
   *
   * Returns edges from the specified node to lower network layers (e.g., streets).
   */
  bool GetTransDownOutEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                             std::vector<bool>& is_forward);

  /**
   * @brief Get topological edge by ID
   * @param[in] edge_id Unique edge identifier
   * @return pointer of topological edge if edge is founded, nullptr if edge is not in target tile
   *
   * Returns edges from the specified node to lower network layers (e.g., streets).
   */
  TopolEdge* GetTopolEdgeByEdgeID(RouteEdgeID edge_id);

private:
  RouteTilePackagePtr ptr_;  ///< Current route tile package reference
  AugmentEdgeSet no_augment_edge_;
  TopolEdgeSet no_topol_edge_;
  RouteNodeSet no_node_;
  LimitPassSet no_limit_pass_;
  LaneInfoSet no_lane_info_;
  FacilityInfoSet no_facility_info_;
  JuncviewInfoSet no_juncview_info_;
  SignpostInfoSet no_signpost_info_;
  TollgateInfoSet no_tollgate_info_;
  VoiceInfoSet no_voice_info_;
};
}  // namespace parser
}  // namespace aurora

#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_TILE_READER_DEF_H
