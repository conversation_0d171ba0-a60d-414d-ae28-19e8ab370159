#include "online_data/db_accessor.h"

#include <string.h>

#include <chrono>

namespace aurora {
namespace parser {
DBAccessor::DBAccessor() : db_(nullptr), max_item_(0) {}
DBAccessor::~DBAccessor() {
  if (db_) {
    sqlite3_close(db_);
  }
}

bool DBAccessor::Init(const char* file_name, uint32_t max_item) {
  if (OpenDB(file_name)) {
    max_item_ = max_item;
    return true;
  }
  return false;
}

bool DBAccessor::GetTileData(uint64_t id, int32_t& version, std::vector<int8_t>& buf) {
  return GetData(id, version, buf);
}

bool DBAccessor::SaveTileData(uint64_t id, int32_t version, const std::vector<int8_t>& buf) {
  if (!IsExist(id)) {
    return AddData(id, version, buf);
  } else {
    uint32_t v = GetVersion(id);
    if (v < version) {
      return UpdateData(id, version, buf);
    }
  }
  return false;
}

bool DBAccessor::OpenDB(const char* file_name) {
  int rc = sqlite3_open(file_name, &db_);
  if (rc) {
    sqlite3_close(db_);
    return false;
  }

  if (HasTable()) {
    return true;
  }

  const char* create_table_sql =
      "CREATE TABLE IF NOT EXISTS data_table ("
      "id INTEGER PRIMARY KEY,"
      "data BLOB,"
      "version INTEGER,"
      "time INTEGER"
      ");";
  rc = sqlite3_exec(db_, create_table_sql, nullptr, nullptr, nullptr);
  return rc == SQLITE_OK;
}

bool DBAccessor::IsExist(uint64_t id) {
  if (!db_) {
      return false;
  }

  std::string sql = "SELECT id FROM data_table WHERE id = ?;";
  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK) {
      return false;
  }

  rc = sqlite3_bind_int64(stmt, 1, id);
  if (rc != SQLITE_OK) {
      return false;
  }

  rc = sqlite3_step(stmt);
  sqlite3_finalize(stmt);
  return rc == SQLITE_ROW;
}

bool DBAccessor::HasTable() {
  if (!db_) {
    return false;
  }

  std::string sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='data_table';";
  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_step(stmt);
  sqlite3_finalize(stmt);
  return rc == SQLITE_ROW;
}

bool DBAccessor::AddData(uint64_t id, int32_t version, const std::vector<int8_t>& buf) {
  if (!db_) {
    return false;
  }
  DeleteHalfDataByTime();

  std::string sql = "INSERT INTO data_table (id, data, version, time) VALUES (?, ?, ?, ?);";
  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_int64(stmt, 1, id);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_blob(stmt, 2, buf.data(), buf.size(), nullptr);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_int(stmt, 3, version);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_int64(stmt, 4, GetCurrentSecond());
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_step(stmt);
  if (rc != SQLITE_DONE) {
    return false;
  }

  sqlite3_finalize(stmt);
  return true;
}

bool DBAccessor::UpdateData(uint64_t id, int32_t version, const std::vector<int8_t>& buf) {
  if (!db_) {
    return false;
  }

  std::string sql = "UPDATE data_table SET data = ?, version = ?, time = ? WHERE id = ?;";
  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_blob(stmt, 1, buf.data(), buf.size(), nullptr);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_int(stmt, 2, version);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_int64(stmt, 3, GetCurrentSecond());
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_int64(stmt, 4, id);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_step(stmt);
  if (rc != SQLITE_DONE) {
    return false;
  }

  sqlite3_finalize(stmt);
  return true;
}

bool DBAccessor::GetData(uint64_t id, int32_t& version, std::vector<int8_t>& buf) {
  if (!db_) {
    return false;
  }

  std::string sql = "SELECT data, version FROM data_table WHERE id = ?;";
  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_int64(stmt, 1, id);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_step(stmt);
  if (rc != SQLITE_ROW) {
    sqlite3_finalize(stmt);
    return false;
  }

  const int8_t* data_blob = (int8_t*)sqlite3_column_blob(stmt, 0);
  int datasize = sqlite3_column_bytes(stmt, 0);
  version = sqlite3_column_int(stmt, 1);

  buf.assign(data_blob, data_blob + datasize);
  sqlite3_finalize(stmt);
  return true;
}

uint32_t DBAccessor::GetDataCount() {
  if (!db_) {
    return 0;
  }
  std::string sql = "SELECT COUNT(*) FROM data_table;";
  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK) {
    return 0;
  }

  rc = sqlite3_step(stmt);
  if (rc != SQLITE_ROW) {
    sqlite3_finalize(stmt);
    return 0;
  }

  int count = sqlite3_column_int(stmt, 0);
  sqlite3_finalize(stmt);
  return count;
}

bool DBAccessor::DeleteHalfDataByTime() {
  if (!db_) {
    return false;
  }

  uint32_t count = GetDataCount();
  if (count < max_item_) {
    return false;
  }

  int half_count = max_item_ / 2;
  std::string sql =
      "DELETE FROM data_table WHERE id IN (SELECT id FROM data_table ORDER BY time ASC LIMIT ?);";
  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_bind_int(stmt, 1, half_count);
  if (rc != SQLITE_OK) {
    return false;
  }

  rc = sqlite3_step(stmt);
  if (rc != SQLITE_DONE) {
    return false;
  }

  sqlite3_finalize(stmt);

  return true;
}

int32_t DBAccessor::GetVersion(long id) {
  if (!db_) {
    return -1;
  }

  std::string sql = "SELECT version FROM data_table WHERE id = ?;";
  sqlite3_stmt* stmt;
  int rc = sqlite3_prepare_v2(db_, sql.c_str(), -1, &stmt, nullptr);
  if (rc != SQLITE_OK) {
      return -1;
  }

  rc = sqlite3_bind_int64(stmt, 1, id);
  if (rc != SQLITE_OK) {
      return -1;
  }

  rc = sqlite3_step(stmt);
  if (rc != SQLITE_ROW) {
    sqlite3_finalize(stmt);
    return -1;
  }

  int32_t version = sqlite3_column_int(stmt, 0);
  sqlite3_finalize(stmt);
  return version;
}

int64_t DBAccessor::GetCurrentSecond() {
  auto current_time = std::chrono::system_clock::now();
  std::chrono::seconds sec =
      std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch());
  return sec.count();
}
}  // namespace parser
}  // namespace aurora
