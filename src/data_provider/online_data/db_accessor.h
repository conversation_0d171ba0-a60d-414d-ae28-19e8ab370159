#ifndef MAP_SRC_DATA_PROVIDER_ONLINE_DATA_DB_ACCESSOR_H
#define MAP_SRC_DATA_PROVIDER_ONLINE_DATA_DB_ACCESSOR_H

#include <cstdint>
#include <mutex>
#include <string>
#include <vector>

#include "sqlite3.h"

namespace aurora {
namespace parser {

class DBAccessor {
public:
  DBAccessor();
  ~DBAccessor();
  bool Init(const char* file_name, uint32_t max_item);
  bool GetTileData(uint64_t id, int32_t& version, std::vector<int8_t>& buf);
  bool SaveTileData(uint64_t id, int32_t version, const std::vector<int8_t>& buf);

private:
  bool OpenDB(const char* file_name);
  bool IsExist(uint64_t id);
  bool HasTable();
  bool AddData(uint64_t id, int32_t version, const std::vector<int8_t>& buf);
  bool UpdateData(uint64_t id, int32_t version, const std::vector<int8_t>& buf);
  bool GetData(uint64_t id, int32_t& version, std::vector<int8_t>& buf);
  uint32_t GetDataCount();
  bool DeleteHalfDataByTime();
  int64_t GetCurrentSecond();
  int32_t GetVersion(long id);

private:
  uint32_t max_item_;
  sqlite3* db_;
  std::mutex data_mutex_;
};
}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_ONLINE_DATA_DB_ACCESSOR_H
