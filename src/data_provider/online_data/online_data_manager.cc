#include "online_data/online_data_manager.h"

namespace aurora {
namespace parser {
const int DB_MAX_ITEM_COUNT = 5000;
const uint32_t DEFAULT_TILE_WIDTH = 8192;

struct RouteTileIndex {
  LngLatMbr mbr;
  TilePositionInMesh pos;
};

OnlineDataManager::OnlineDataManager() : data_version_(0), disp_tile_width_(DEFAULT_TILE_WIDTH) {}

OnlineDataManager::~OnlineDataManager() {}

bool OnlineDataManager::Init(const char* path) {
  path_ = path;
  std::string disp_db = path_ + "/disp_db";
  std::string route_db = path_ + "/route_db";
  bool result1 = disp_data_.Init(disp_db.c_str(), DB_MAX_ITEM_COUNT);
  bool result2 = route_data_.Init(route_db.c_str(), DB_MAX_ITEM_COUNT);
  return result1 && result2;
}
bool OnlineDataManager::GetDisplayTileData(DisplayTileID tile_id, uint16_t& pixel_num,
                                           DataBuffer& buf) {
  if (disp_data_.GetTileData(tile_id.value, data_version_, buf)) {
    pixel_num = disp_tile_width_;
    return true;
  }
  return false;
}
bool OnlineDataManager::GetRouteTileData(RouteTileID tile_id, TilePositionInMesh& pos_in_mesh,
                                         LngLatMbr& mbr, DataBuffer& buf) {
  std::vector<int8_t> db_buf;
  if (route_data_.GetTileData(tile_id.value, data_version_, db_buf)) {
    RouteTileIndex index;
    uint32_t index_size = sizeof(index);
    if (db_buf.size() <= index_size) {
      return false;
    }
    uint32_t buf_size = db_buf.size() - index_size;
    buf.resize(buf_size);
    memcpy(&index, db_buf.data(), index_size);
    memcpy(buf.data(), db_buf.data() + index_size, buf_size);
    pos_in_mesh = index.pos;
    mbr = index.mbr;
    return true;
  }
  return false;
}

}  // namespace parser
}  // namespace aurora
