#ifndef MAP_SRC_DATA_PROVIDER_ONLINE_DATA_ONLINE_DATA_MANAGER_H
#define MAP_SRC_DATA_PROVIDER_ONLINE_DATA_ONLINE_DATA_MANAGER_H

#include "common/common_def.h"
#include "display_data/display_data_def.h"
#include "online_data/db_accessor.h"
#include "route_data/route_data_def.h"

namespace aurora {
namespace parser {
class OnlineDataManager {
public:
  OnlineDataManager();
  ~OnlineDataManager();
  bool Init(const char* path);
  bool GetDisplayTileData(DisplayTileID tile_id, uint16_t& pixel_num, DataBuffer& buf);
  bool GetRouteTileData(RouteTileID tile_id, TilePositionInMesh& pos_in_mesh, LngLatMbr& mbr,
                        DataBuffer& buf);

private:
  std::string path_;
  int32_t data_version_;
  int32_t disp_tile_width_;
  DBAccessor disp_data_;
  DBAccessor route_data_;
};
}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_ONLINE_DATA_ONLINE_DATA_MANAGER_H
