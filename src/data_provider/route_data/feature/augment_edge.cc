#include "route_data/feature/augment_edge.h"

namespace aurora {
namespace parser {
AugmentEdge::AugmentEdge()
    : id_(0),
      base_(nullptr),
      local_name_(),
      foreign_name_(),
      road_no_(),
      geo_points_(),
      mbr_(),
      subedges_(),
      sapa_(nullptr),
      adcode_(),
      slope_point_(),
      curve_point_() {}

const char* AugmentEdge::GetLocalName() {
  if (local_name_.empty()) {
    return nullptr;
  }
  return local_name_.c_str();
}

const char* AugmentEdge::GetForeignName() {
  if (foreign_name_.empty()) {
    return nullptr;
  }
  return foreign_name_.c_str();
}

const char* AugmentEdge::GetRoadNo() {
  if (road_no_.empty()) {
    return nullptr;
  }
  return road_no_.c_str();
}
}  // namespace parser
}  // namespace aurora
