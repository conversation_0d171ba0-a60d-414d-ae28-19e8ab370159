#include "route_data/feature/tollgate_info.h"

#include "route_data/route_data_parser.h"
namespace aurora {
namespace parser {
TollgateInfo::TollgateInfo() : adcode_(0), base_(nullptr) {}
TollgateInfo::TollgateInfo(const TollgateBase* base) : adcode_(0), base_(base) {}
uint8_t TollgateInfo::GetTollgateGateCount() { return gates_.size(); }
const TollgateGateInfo* TollgateInfo::GetTollgateGateInfo(uint8_t index) {
  if (index >= gates_.size()) {
    return nullptr;
  }
  return &gates_[index];
}

ImagePtr TollgateInfo::GetBackground() {
  if (base_ == nullptr) {
    return nullptr;
  }
  return RouteDataParser::Instance().GetResource(adcode_, 1, 0, base_->background_img_no);
}
const char* TollgateInfo::GetName() {
  if (name_.empty()) {
    return nullptr;
  }
  return name_.c_str();
}
const char* TollgateInfo::GetForeignName() {
  if (foreign_name_.empty()) {
    return nullptr;
  }
  return foreign_name_.c_str();
}
}  // namespace parser
}  // namespace aurora
