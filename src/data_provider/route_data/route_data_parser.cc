#include "route_data/route_data_parser.h"

#include <logger.h>
namespace aurora {
namespace parser {
const uint8_t CONNECT_EDGE_COUNT = 8;
RouteDataParser::RouteDataParser()
    : need_all_cache_(false), offline_parser_(), limit_tile_cache_(1) {}

bool RouteDataParser::Init(const char* path, uint32_t cache_limit) {
  need_all_cache_ = false;
  all_tile_cache_.clear();
  limit_tile_cache_.Init(cache_limit);
  resource_parser_.SetDataPath(path);
  if (offline_parser_.Init(path)) {
    offline_parser_.CacheAllMeshIndex();
    return true;
  }
  return false;
}

void RouteDataParser::SwitchCacheMode(bool all_cache) {
  need_all_cache_ = all_cache;
  if (!all_cache) {
    all_tile_cache_.clear();
  }
  offline_parser_.SetPathMode(all_cache);
}

void RouteDataParser::GetRouteTileIDs(uint8_t level, LngLatMbr mbr,
                                      std::vector<RouteTileID>& tiles) {
  std::vector<uint32_t> codes = offline_parser_.GetAdCodeByMbr(mbr);
  for (size_t i = 0; i < codes.size(); ++i) {
    offline_parser_.GetRouteTileIDs(codes[i], level, mbr, tiles);
  }
}

std::vector<uint32_t> RouteDataParser::GetAdcodeByMBR(LngLatMbr mbr) {
  // temp code
  std::vector<uint32_t> result;
  std::vector<uint32_t> codes = offline_parser_.GetAdCodeByMbr(mbr);
  result.reserve(codes.size());
  for (size_t i = 0; i < codes.size(); ++i) {
    std::vector<RouteTileID> tiles;
    offline_parser_.GetRouteTileIDs(codes[i], 0, mbr, tiles);
    if (!tiles.empty()) {
      result.push_back(codes[i]);
    }
  }
  return result;
}

RouteTilePackagePtr RouteDataParser::GetRouteTileByID(RouteTileID tile_id) {
  RouteTilePackagePtr tile_ptr = GetTileFromCache(tile_id);
  if (tile_ptr != nullptr) {
    return tile_ptr;
  }
  tile_ptr = std::make_shared<RouteTilePackage>();
  if (tile_ptr != nullptr) {
    DataBuffer buf;
    offline_parser_.GetTileData(tile_id, tile_ptr->position_in_mesh_, tile_ptr->mbr_, buf);
    tile_ptr->tile_id_ = tile_id;
#if 0
    tile_ptr->parser_.SetTileBuffer(buf);
#else
    tile_ptr->parser_.CopyHeader(buf);
    tile_ptr->parser_.SetTopolBuffer(buf);
#endif
    CacheTile(tile_ptr);
    return tile_ptr;
  }
  return nullptr;
}

bool RouteDataParser::GetTileBuffer(RouteTileID tile_id, DataBuffer& buf) {
  TilePositionInMesh pos_in_mesh;
  LngLatMbr mbr;
  return offline_parser_.GetTileData(tile_id, pos_in_mesh, mbr, buf);
}

bool RouteDataParser::GetEdgeIDByNode(bool is_in_edge, RouteNodeID src_node,
                                      RouteEdgeIDSet& dst_edges, std::vector<bool>& is_forward) {
  RouteTilePackagePtr src_tile = GetRouteTileByID(src_node.tile_id);
  if (src_tile != nullptr) {
    RouteNode* node_ptr = src_tile->GetNodeByID(src_node.feature_id);
    if (node_ptr != nullptr) {
      dst_edges.reserve(CONNECT_EDGE_COUNT);
      is_forward.reserve(CONNECT_EDGE_COUNT);
      const std::vector<ConnectedEdge*>& connected_edges = node_ptr->GetConnectedEdge();
      bool result = GetEdgeIDByConnectedEdge(is_in_edge, src_tile, node_ptr, connected_edges,
                                             dst_edges, is_forward);
      const std::vector<BoundaryNode*>& bound_nodes = node_ptr->GetBoundaryNode();
      if (!bound_nodes.empty()) {
        result |= GetEdgeIDByBoundaryNode(is_in_edge, src_tile, bound_nodes, dst_edges, is_forward);
      }
      const AdminBoundaryNode* admin_bound_node = node_ptr->GetAdBoundaryNode();
      if (admin_bound_node != nullptr) {
        result |= GetEdgeIDByAdminBoundaryNode(is_in_edge, src_tile, node_ptr, admin_bound_node,
                                               dst_edges, is_forward);
      }
      return result;
    }
  }
  return false;
}
bool RouteDataParser::GetTransUpEdgeIDByNode(bool is_in_edge, RouteNodeID src_node,
                                             RouteEdgeIDSet& dst_edges,
                                             std::vector<bool>& is_forward) {
  RouteTilePackagePtr src_tile = GetRouteTileByID(src_node.tile_id);
  if (src_tile != nullptr) {
    uint8_t dst_level = src_node.tile_id.level + 1;
    RouteNode* node_ptr = src_tile->GetNodeByID(src_node.feature_id);
    if (node_ptr != nullptr) {
      const TransNodeInfo* trans_node = node_ptr->GetTransUpNodeInfo();
      return GetEdgeIDByTransNode(is_in_edge, dst_level, src_node.tile_id, trans_node, dst_edges,
                                  is_forward);
    }
  }
  return false;
}
bool RouteDataParser::GetTransDownEdgeIDByNode(bool is_in_edge, RouteNodeID src_node,
                                               RouteEdgeIDSet& dst_edges,
                                               std::vector<bool>& is_forward) {
  RouteTilePackagePtr src_tile = GetRouteTileByID(src_node.tile_id);
  if (src_tile != nullptr) {
    if (src_node.tile_id.level == 0) {
      return false;
    }
    uint8_t dst_level = src_node.tile_id.level - 1;
    RouteNode* node_ptr = src_tile->GetNodeByID(src_node.feature_id);
    if (node_ptr != nullptr) {
      const TransNodeInfo* trans_node = node_ptr->GetTransDownNodeInfo();
      return GetEdgeIDByTransNode(is_in_edge, dst_level - 1, src_node.tile_id, trans_node,
                                  dst_edges, is_forward);
    }
  }
  return false;
}

ImagePtr RouteDataParser::GetResource(uint32_t adcode, uint32_t type, uint32_t img_level,
                                      const char* img_no) {
  return resource_parser_.GetResource(adcode, type, img_level, img_no);
}

const AdministrativeInfoSet& RouteDataParser::GetAdminInfo() {
  if (admin_infos_.empty()) {
    offline_parser_.GetAdminInfo(admin_infos_);
  }
  return admin_infos_;
}

RouteTilePackagePtr RouteDataParser::GetTileFromCache(RouteTileID tile_id) {
  std::lock_guard<std::mutex> lock(cache_mtx_);
  if (need_all_cache_) {
    auto it = all_tile_cache_.find(tile_id);
    if (it != all_tile_cache_.end()) {
      return it->second;
    }
  }
  RouteTilePackagePtr ptr = nullptr;
  if (limit_tile_cache_.Find(tile_id, ptr)) {
    return ptr;
  }
  return nullptr;
}

void RouteDataParser::CacheTile(RouteTilePackagePtr tile_ptr) {
  if (tile_ptr == nullptr) {
    return;
  }
  std::lock_guard<std::mutex> lock(cache_mtx_);
  if (need_all_cache_) {
    auto it = all_tile_cache_.find(tile_ptr->GetTileID());
    if (it == all_tile_cache_.end()) {
      all_tile_cache_.emplace(tile_ptr->GetTileID(), tile_ptr);
    }
  } else {
    limit_tile_cache_.Add(tile_ptr->GetTileID(), tile_ptr);
  }
}
bool RouteDataParser::IsInEdge(RouteNode* node, TopolEdge* edge, bool& is_forward) {
  if (node == nullptr || edge == nullptr) {
    return false;
  }
  EdgeDirection dir = (EdgeDirection)edge->GetBaseInfo()->direction;
  uint32_t start_node_id = edge->GetBaseInfo()->start_node_id;
  uint32_t end_node_id = edge->GetBaseInfo()->end_node_id;
  if (start_node_id == node->GetID()) {
    if (dir == kEdgeDirectionReversePass || dir == kEdgeDirectionAllPass) {
      return true;
    }
  } else if (end_node_id == node->GetID()) {
    is_forward = true;
    if (dir == kEdgeDirectionForwardPass || dir == kEdgeDirectionAllPass) {
      return true;
    }
  } else {
  }
  return false;
}
bool RouteDataParser::IsOutEdge(RouteNode* node, TopolEdge* edge, bool& is_forward) {
  if (node == nullptr || edge == nullptr) {
    return false;
  }
  EdgeDirection dir = (EdgeDirection)edge->GetBaseInfo()->direction;
  uint32_t start_node_id = edge->GetBaseInfo()->start_node_id;
  uint32_t end_node_id = edge->GetBaseInfo()->end_node_id;
  if (start_node_id == node->GetID()) {
    is_forward = true;
    if (dir == kEdgeDirectionForwardPass || dir == kEdgeDirectionAllPass) {
      return true;
    }
  } else if (end_node_id == node->GetID()) {
    if (dir == kEdgeDirectionReversePass || dir == kEdgeDirectionAllPass) {
      return true;
    }
  } else {
  }
  return false;
}
bool RouteDataParser::GetEdgeIDByConnectedEdge(bool is_in_edge, RouteTilePackagePtr tile,
                                               RouteNode* node_ptr,
                                               const std::vector<ConnectedEdge*>& connected_edges,
                                               RouteEdgeIDSet& dst_edges,
                                               std::vector<bool>& is_forward) {
  TopolEdgeSet& edges = tile->GetTopolEdges();
  bool result = false;
  RouteEdgeID edge_id(tile->GetTileID(), 0);
  for (size_t i = 0; i < connected_edges.size(); ++i) {
    TopolEdge& edge = edges.at(connected_edges[i]->edge_id);
    bool judge_result = false;
    bool forward = false;
    if (is_in_edge) {
      judge_result = IsInEdge(node_ptr, &edge, forward);
    } else {
      judge_result = IsOutEdge(node_ptr, &edge, forward);
    }
    if (judge_result) {
      edge_id.feature_id = edge.GetID();
      dst_edges.push_back(edge_id);
      is_forward.push_back(forward);
      result = true;
    }
  }
  return result;
}

bool RouteDataParser::GetEdgeIDByBoundaryNode(bool is_in_edge, RouteTilePackagePtr tile,
                                              const std::vector<BoundaryNode*>& boundary_nodes,
                                              RouteEdgeIDSet& dst_edges,
                                              std::vector<bool>& is_forward) {
  bool result = false;
  RouteTilePackagePtr next_tile = nullptr;
  std::vector<RouteNode*> nodes;
  GetNodeByBoundaryNode(tile, boundary_nodes, next_tile, nodes);
  for (size_t i = 0; i < nodes.size(); ++i) {
    RouteNode* opposit_node_ptr = nodes[i];
    if (opposit_node_ptr != nullptr) {
      const std::vector<ConnectedEdge*>& connected_edges = opposit_node_ptr->GetConnectedEdge();
      result |= GetEdgeIDByConnectedEdge(is_in_edge, next_tile, opposit_node_ptr, connected_edges,
                                         dst_edges, is_forward);
    }
  }
  return result;
}
bool RouteDataParser::GetNodeByBoundaryNode(RouteTilePackagePtr tile,
                                            const std::vector<BoundaryNode*>& boundary_nodes,
                                            RouteTilePackagePtr& dst_tile,
                                            std::vector<RouteNode*>& nodes) {
  bool result = false;
  nodes.reserve(boundary_nodes.size());
  for (size_t i = 0; i < boundary_nodes.size(); ++i) {
    BoundaryNode* bound_node = boundary_nodes[i];
    if (bound_node != nullptr) {
      RouteTileID next_tile_id = tile->GetAdjacentTileIDByBoundaryNode(bound_node);
      if (next_tile_id != tile->GetTileID()) {
        dst_tile = GetRouteTileByID(next_tile_id);
        if (dst_tile != nullptr) {
          RouteNode* opposit_node_ptr = dst_tile->GetNodeByID(bound_node->adj_node_id);
          if (opposit_node_ptr != nullptr) {
            nodes.push_back(opposit_node_ptr);
            result = true;
          }
        }
      }
    }
  }
  return result;
}
bool RouteDataParser::GetEdgeIDByAdminBoundaryNode(bool is_in_edge, RouteTilePackagePtr tile,
                                                   RouteNode* node_ptr,
                                                   const AdminBoundaryNode* admin_bound_node,
                                                   RouteEdgeIDSet& dst_edges,
                                                   std::vector<bool>& is_forward) {
  RouteTilePackagePtr next_tile = nullptr;
  RouteNode* opposit_node_ptr =
      GetNodeByAdminBoundaryNode(tile, node_ptr, admin_bound_node, next_tile);
  if (opposit_node_ptr != nullptr) {
    const std::vector<ConnectedEdge*>& connected_edges = opposit_node_ptr->GetConnectedEdge();
    return GetEdgeIDByConnectedEdge(is_in_edge, next_tile, opposit_node_ptr, connected_edges,
                                    dst_edges, is_forward);
  }
  return false;
}
RouteNode* RouteDataParser::GetNodeByAdminBoundaryNode(RouteTilePackagePtr tile,
                                                       RouteNode* node_ptr,
                                                       const AdminBoundaryNode* admin_bound_node,
                                                       RouteTilePackagePtr& dst_tile) {
  if (admin_bound_node != nullptr) {
    uint32_t src_version = offline_parser_.GetDataVersion(tile->GetTileID().adcode);
    uint32_t dst_version = offline_parser_.GetDataVersion(admin_bound_node->adcode);
    if (src_version == dst_version) {
      RouteTileID next_tile_id(admin_bound_node->adcode, tile->GetTileID().level,
                               admin_bound_node->adj_mesh_col, admin_bound_node->adj_mesh_row,
                               admin_bound_node->adj_tile_id);
      dst_tile = GetRouteTileByID(next_tile_id);
      if (dst_tile != nullptr) {
        return dst_tile->GetNodeByID(admin_bound_node->adj_node_id);
      }
    } else {
      LngLatMbr mbr(node_ptr->GetPosition().x() - 0.0001, node_ptr->GetPosition().y() - 0.0001,
                    node_ptr->GetPosition().x() + 0.0001, node_ptr->GetPosition().y() + 0.0001);
      std::vector<RouteTileID> tile_ids;
      GetRouteTileIDs(tile->GetTileID().level, mbr, tile_ids);
      for (size_t i = 0; i < tile_ids.size(); ++i) {
        dst_tile = GetRouteTileByID(tile_ids[i]);
        if (dst_tile != nullptr) {
          return dst_tile->GetNodeByPositionFromAdminBoundaryNodeList(dst_tile->GetTileID().level,
                                                                      node_ptr->GetPosition());
        }
      }
    }
  }
  return nullptr;
}

bool RouteDataParser::GetEdgeIDByTransNode(bool is_in_edge, uint8_t level, RouteTileID tile,
                                           const TransNodeInfo* trans_node,
                                           RouteEdgeIDSet& dst_edges,
                                           std::vector<bool>& is_forward) {
  if (trans_node == nullptr) {
    return false;
  }
  RouteTileID dst_tile_id(tile.adcode, level, tile.mesh_col, tile.mesh_row,
                          trans_node->opp_tile_id);
  RouteNodeID node_id(dst_tile_id, trans_node->opp_node_id);
  return GetEdgeIDByNode(is_in_edge, node_id, dst_edges, is_forward);
}

}  // namespace parser
}  // namespace aurora
