#ifndef MAP_SRC_DATA_PROVIDER_ROUTE_DATA_ROUTE_DATA_PARSER_H
#define MAP_SRC_DATA_PROVIDER_ROUTE_DATA_ROUTE_DATA_PARSER_H

#include <map>
#include <mutex>
#include <unordered_map>

#include "common/cache.h"
#include "route_data/offline/offline_resource_parser.h"
#include "route_data/offline/offline_route_data_parser.h"
#include "route_data/route_tile_package.h"

namespace aurora {
namespace parser {
class RouteDataParser {
public:
  static RouteDataParser& Instance() {
    static RouteDataParser parser;
    return parser;
  }
  RouteDataParser();
  bool Init(const char* path, uint32_t cache_limit);
  void SwitchCacheMode(bool all_cache);
  void GetRouteTileIDs(uint8_t level, LngLatMbr mbr, std::vector<RouteTileID>& tiles);
  std::vector<uint32_t> GetAdcodeByMBR(LngLatMbr mbr);
  RouteTilePackagePtr GetRouteTileByID(RouteTileID tile_id);
  bool GetTileBuffer(RouteTileID tile_id, DataBuffer& buf);
  bool GetEdgeIDByNode(bool is_in_edge, RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                       std::vector<bool>& is_forward);
  bool GetTransUpEdgeIDByNode(bool is_in_edge, RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                              std::vector<bool>& is_forward);
  bool GetTransDownEdgeIDByNode(bool is_in_edge, RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                                std::vector<bool>& is_forward);
  ImagePtr GetResource(uint32_t adcode, uint32_t type, uint32_t img_level, const char* img_no);

  const AdministrativeInfoSet& GetAdminInfo();

private:
  RouteTilePackagePtr GetTileFromCache(RouteTileID tile_id);
  void CacheTile(RouteTilePackagePtr tile_ptr);
  bool IsInEdge(RouteNode* node, TopolEdge* edge, bool& is_forward);
  bool IsOutEdge(RouteNode* node, TopolEdge* edge, bool& is_forward);
  bool GetEdgeIDByConnectedEdge(bool is_in_edge, RouteTilePackagePtr tile, RouteNode* node_ptr,
                                const std::vector<ConnectedEdge*>& connected_edges,
                                RouteEdgeIDSet& dst_edges, std::vector<bool>& is_forward);
  bool GetEdgeIDByBoundaryNode(bool is_in_edge, RouteTilePackagePtr tile,
                               const std::vector<BoundaryNode*>& boundary_nodes,
                               RouteEdgeIDSet& dst_edges, std::vector<bool>& is_forward);
  bool GetNodeByBoundaryNode(RouteTilePackagePtr tile,
                             const std::vector<BoundaryNode*>& boundary_nodes,
                             RouteTilePackagePtr& dst_tile, std::vector<RouteNode*>& nodes);
  bool GetEdgeIDByAdminBoundaryNode(bool is_in_edge, RouteTilePackagePtr tile, RouteNode* node_ptr,
                                    const AdminBoundaryNode* admin_bound_node,
                                    RouteEdgeIDSet& dst_edges, std::vector<bool>& is_forward);
  RouteNode* GetNodeByAdminBoundaryNode(RouteTilePackagePtr tile, RouteNode* node_ptr,
                                        const AdminBoundaryNode* admin_bound_node,
                                        RouteTilePackagePtr& dst_tile);
  bool GetEdgeIDByTransNode(bool is_in_edge, uint8_t level, RouteTileID tile,
                            const TransNodeInfo* trans_node, RouteEdgeIDSet& dst_edges,
                            std::vector<bool>& is_forward);

  bool need_all_cache_;
  OfflineRouteDataParser offline_parser_;
  OfflineResourceParser resource_parser_;
  std::unordered_map<RouteTileID, RouteTilePackagePtr> all_tile_cache_;
  LRUCache<RouteTileID, RouteTilePackagePtr> limit_tile_cache_;
  std::mutex cache_mtx_;
  AdministrativeInfoSet admin_infos_;
};

}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_ROUTE_DATA_ROUTE_DATA_PARSER_H
