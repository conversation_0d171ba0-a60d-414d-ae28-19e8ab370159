#include "route_data/route_tile_package.h"

#include <algorithm>
#include <queue>

#include "common/common_def.h"

namespace aurora {
namespace parser {
RouteTilePackage::RouteTilePackage() : tile_id_() {}
RouteTileID RouteTilePackage::GetTileID() { return tile_id_; }
// const LngLatMbr& RouteTilePackage::GetMbr() { return mbr_; }
std::vector<uint32_t> RouteTilePackage::GetAugmentEdgesByMbr(LngLatMbr mbr) {
  if (nodes_.empty()) {
    parser_.ParseAllNode(tile_id_, mbr_, nodes_);
  }
  if (topol_edges_.empty()) {
    parser_.ParseAllTopolEdge(tile_id_, topol_edges_);
  }
  if (augment_edges_.empty()) {
    parser_.ParseAllAugmentEdge(tile_id_, mbr_, nodes_, topol_edges_, augment_edges_);
  }
  std::vector<uint32_t> result;
  for (size_t i = 0; i < augment_edges_.size(); ++i) {
    if (augment_edges_.at(i).GetMbr().Intersects(mbr)) {
      result.push_back(i);
    }
  }
  return result;
}
AugmentEdgeSet& RouteTilePackage::GetAugmentEdges() {
  if (nodes_.empty()) {
    parser_.ParseAllNode(tile_id_, mbr_, nodes_);
  }
  if (topol_edges_.empty()) {
    parser_.ParseAllTopolEdge(tile_id_, topol_edges_);
  }
  if (augment_edges_.empty()) {
    parser_.ParseAllAugmentEdge(tile_id_, mbr_, nodes_, topol_edges_, augment_edges_);
  }
  return augment_edges_;
}
TopolEdgeSet& RouteTilePackage::GetTopolEdges() {
  if (topol_edges_.empty()) {
    parser_.ParseAllTopolEdge(tile_id_, topol_edges_);
  }
  return topol_edges_;
}
RouteNodeSet& RouteTilePackage::GetNodes() {
  if (nodes_.empty()) {
    parser_.ParseAllNode(tile_id_, mbr_, nodes_);
  }
  return nodes_;
}
LimitPassSet& RouteTilePackage::GetLimitPass() {
  if (limit_pass_array_.empty()) {
    parser_.ParseAllLimitPass(tile_id_, limit_pass_array_);
  }
  return limit_pass_array_;
}
AugmentEdge* RouteTilePackage::GetAugmentEdgeByID(uint32_t id) {
  if (nodes_.empty()) {
    parser_.ParseAllNode(tile_id_, mbr_, nodes_);
  }
  if (topol_edges_.empty()) {
    parser_.ParseAllTopolEdge(tile_id_, topol_edges_);
  }
  if (augment_edges_.empty()) {
    parser_.ParseAllAugmentEdge(tile_id_, mbr_, nodes_, topol_edges_, augment_edges_);
  }
  if (id >= augment_edges_.size()) {
    return nullptr;
  }
  return &(augment_edges_.at(id));
}
TopolEdge* RouteTilePackage::GetTopolEdgeByID(uint32_t id) {
  if (topol_edges_.empty()) {
    parser_.ParseAllTopolEdge(tile_id_, topol_edges_);
  }
  if (id >= topol_edges_.size()) {
    return nullptr;
  }
  return &(topol_edges_.at(id));
}
RouteNode* RouteTilePackage::GetNodeByID(uint32_t id) {
  if (nodes_.empty()) {
    parser_.ParseAllNode(tile_id_, mbr_, nodes_);
  }
  if (id >= nodes_.size()) {
    return nullptr;
  }
  return &(nodes_.at(id));
}
LimitPass* RouteTilePackage::GetLimitPass(RouteEdgeID in_edge, uint32_t in_dir,
                                          RouteEdgeID out_edge, uint32_t out_dir) {
  if (limit_pass_array_.empty()) {
    parser_.ParseAllLimitPass(tile_id_, limit_pass_array_);
  }
  LimitPassBase base;
  base.in_tile_id = in_edge.tile_id.tile_id;
  base.in_edge_dir = in_dir;
  base.in_edge_id = in_edge.feature_id;
  base.out_tile_id = out_edge.tile_id.tile_id;
  base.out_edge_dir = out_dir;
  base.out_edge_id = out_edge.feature_id;
  LimitPass target(&base);

  auto it = std::lower_bound(limit_pass_array_.begin(), limit_pass_array_.end(), target);
  if (it != limit_pass_array_.end()) {
    if ((*it.base()) == target) {
      return it.base();
    }
  }
  return nullptr;
}
RouteTileID RouteTilePackage::GetAdjacentTileIDByBoundaryNode(const BoundaryNode* boundary_node) {
  RouteTileID id = tile_id_;
  if (boundary_node != nullptr) {
    id.mesh_col = boundary_node->adj_mesh_col;
    id.mesh_row = boundary_node->adj_mesh_row;
    id.tile_id = boundary_node->adj_tile_id;
  }
  return id;
}
typedef std::pair<int32_t, uint32_t> AlternateItem;
RouteNode* RouteTilePackage::GetNodeByPositionFromAdminBoundaryNodeList(uint8_t level, LngLat pos) {
  int32_t x = func::LngLat2MD5(pos.x() - mbr_.minx());
  int32_t y = func::LngLat2MD5(pos.y() - mbr_.miny());

  uint32_t count = parser_.GetAdminBoundNodeCount();
  std::priority_queue<AlternateItem, std::vector<AlternateItem>, std::greater<AlternateItem>>
      alternate;
  for (uint32_t i = 0; i < count; ++i) {
    BoundaryNodeIndex* index = parser_.GetAdminBoundNode(i);
    if (index != nullptr) {
      int32_t dx = index->lng - x;
      int32_t dy = index->lat - y;
      if (dx == 0 && dy == 0) {
        uint32_t id = 0;
        ::memcpy(&id, index->boundary_node_id, 3);
        return GetNodeByID(id);
      } else if (dx >= -1 && dx <= 1 && dy >= -1 && dy <= 1) {
        alternate.emplace(dx * dx + dy * dy, i);
      }
    }
  }
  if (!alternate.empty()) {
    uint32_t i = alternate.top().second;
    BoundaryNodeIndex* index = parser_.GetAdminBoundNode(i);
    uint32_t id = 0;
    ::memcpy(&id, index->boundary_node_id, 3);
    return GetNodeByID(id);
  }

  return nullptr;
}

LaneInfoSet& RouteTilePackage::GetLaneInfo() {
  if (lane_infos_.empty()) {
    parser_.ParseLaneInfo(tile_id_, lane_infos_);
  }
  return lane_infos_;
}
FacilityInfoSet& RouteTilePackage::GetFacilityInfo() {
  if (facility_infos_.empty()) {
    parser_.ParseFacilityInfo(tile_id_, mbr_, facility_infos_);
  }
  return facility_infos_;
}
JuncviewInfoSet& RouteTilePackage::GetJuncviewInfo() {
  if (juncview_infos_.empty()) {
    parser_.ParseJuncviewInfo(tile_id_, juncview_infos_);
  }
  return juncview_infos_;
}
SignpostInfoSet& RouteTilePackage::GetSignpostInfo() {
  if (signpost_infos_.empty()) {
    parser_.ParseSignpostInfo(tile_id_, signpost_infos_);
  }
  return signpost_infos_;
}
TollgateInfoSet& RouteTilePackage::GetTollgateInfo() {
  if (tollgate_infos_.empty()) {
    parser_.ParseTollgateInfo(tile_id_, tollgate_infos_);
  }
  return tollgate_infos_;
}
VoiceInfoSet& RouteTilePackage::GetVoiceInfo() {
  if (voice_infos_.empty()) {
    parser_.ParseVoiceInfo(tile_id_, voice_infos_);
  }
  return voice_infos_;
}

}  // namespace parser
}  // namespace aurora
