/**
 * @file route_tile_package.h
 * @brief Route tile data container and parser interface
 *
 * @details This file defines the @c RouteTilePackage class, which encapsulates
 *          the parsed data of a route tile including nodes, topological edges,
 *          augment edges, and limit pass information. It provides methods to
 *          query data by IDs or geographic bounds, and internally handles data parsing.
 *
 * <AUTHOR>
 * @date 2025-5-13
 */

#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_TILE_PACKAGE_H
#define MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_TILE_PACKAGE_H

#include <memory>
#include <vector>

#include "route_data/route_tile_parser.h"

namespace aurora {
namespace parser {

/**
 * @brief Container class for parsed route tile data.
 *
 * The @c RouteTilePackage holds all parsed data of a single route tile, including:
 * - Geographic metadata (ID, MBR)
 * - Nodes and topological edges
 * - Augmented edges and limit pass rules
 * - Raw buffer data for internal parsing
 *
 * Public methods provide access to data elements by ID or geographic filtering.
 */
class RouteTilePackage {
public:
  /**
   * @brief Default constructor initializing empty data containers.
   */
  RouteTilePackage();

  /**
   * @brief Get the unique ID of this route tile.
   * @return The tile ID.
   */
  RouteTileID GetTileID();

  /**
   * @brief Get the geographic bounding box (MBR) of this tile.
   * @return Const reference to the MD5Mbr structure.
   */
  //const LngLatMbr& GetMbr();

  /**
   * @brief Retrieve augment edges within a specified geographic area.
   * @param[in] mbr LngLatMbr defining the search area.
   * @return Vector of augment edge IDs matching the MBR.
   */
  std::vector<uint32_t> GetAugmentEdgesByMbr(LngLatMbr mbr);

  /**
   * @brief Get all augment edges in this tile.
   * @return Reference to the vector of augment edges.
   */
  AugmentEdgeSet& GetAugmentEdges();

  /**
   * @brief Get all topological edges in this tile.
   * @return Reference to the vector of topological edges.
   */
  TopolEdgeSet& GetTopolEdges();

  /**
   * @brief Get all nodes in this tile.
   * @return Reference to the vector of route nodes.
   */
  RouteNodeSet& GetNodes();

  /**
   * @brief Get all limit pass rules in this tile.
   * @return Reference to the vector of limit pass entries.
   */
  LimitPassSet& GetLimitPass();

  /**
   * @brief Get augment edge by index.
   * @param[in] id Augment edge identifier.
   * @return Pointer to the found AugmentEdge, or @c nullptr if not found.
   */
  AugmentEdge* GetAugmentEdgeByID(uint32_t id);

  /**
   * @brief Get topological edge by index.
   * @param[in] id Topological edge identifier.
   * @return Pointer to the found TopolEdge, or @c nullptr if not found.
   */
  TopolEdge* GetTopolEdgeByID(uint32_t id);

  /**
   * @brief Get route node by index.
   * @param[in] id Node identifier.
   * @return Pointer to the found RouteNode, or @c nullptr if not found.
   */
  RouteNode* GetNodeByID(uint32_t id);

  LimitPass* GetLimitPass(RouteEdgeID in_edge, uint32_t in_dir, RouteEdgeID out_edge,
                          uint32_t out_dir);

  /**
   * @brief Get adjacent tile ID by pointer to a boundary node.
   * @param[in] Pointer to a boundary node.
   * @return The opposit tile ID, or @c current tile ID if not found.
   */
  RouteTileID GetAdjacentTileIDByBoundaryNode(const BoundaryNode* boundary_node);

  /**
   * @brief Get route node by geographic point from administrative boundary node list.
   * @param[in] level Tile hierarchy level.
   * @param[in] pos LngLat defining (x,y) coordinates.
   * @return The opposit tile ID, or @c current tile ID if not found.
   */
  RouteNode* GetNodeByPositionFromAdminBoundaryNodeList(uint8_t level, LngLat pos);

  LaneInfoSet& GetLaneInfo();
  FacilityInfoSet& GetFacilityInfo();
  JuncviewInfoSet& GetJuncviewInfo();
  SignpostInfoSet& GetSignpostInfo();
  TollgateInfoSet& GetTollgateInfo();
  VoiceInfoSet& GetVoiceInfo();

private:
  // Member variables
  /**
   * @brief Unique identifier of this route tile.
   */
  RouteTileID tile_id_;

  /**
   * @brief Geographic bounding box (minimum bounding rectangle) of the tile.
   */
  LngLatMbr mbr_;

  /**
   * @brief Position information within the mesh grid system.
   */
  TilePositionInMesh position_in_mesh_;

  /**
   * @brief Vector of parsed route nodes in this tile.
   */
  RouteNodeSet nodes_;

  /**
   * @brief Vector of parsed topological edges in this tile.
   */
  TopolEdgeSet topol_edges_;

  /**
   * @brief Vector of parsed augment edges in this tile.
   */
  AugmentEdgeSet augment_edges_;

  LaneInfoSet lane_infos_;
  FacilityInfoSet facility_infos_;
  JuncviewInfoSet juncview_infos_;
  SignpostInfoSet signpost_infos_;
  TollgateInfoSet tollgate_infos_;
  VoiceInfoSet voice_infos_;

  /**
   * @brief Vector of parsed limit pass rules in this tile.
   */
  LimitPassSet limit_pass_array_;

  RouteTileParser parser_;

  // Friend declaration
  friend class RouteDataParser;
};
}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_TILE_PACKAGE_H
