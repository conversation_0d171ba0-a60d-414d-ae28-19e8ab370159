#include "route_data/route_tile_parser.h"

#include "common/decompress_util.h"
#include "logger.h"
#include "route_data/route_data_parser.h"

namespace aurora {
namespace parser {

RouteTileParser::RouteTileParser() {}

#if 0
void RouteTileParser::SetTileBuffer(DataBuffer& buf) {
  if (buf.size() <= sizeof(RouteTileHeader)) {
    return;
  }
  RouteTileHeader* header = (RouteTileHeader*)&(buf[0]);
  size_t copy_size = sizeof(RouteTileHeader);
  if (header->tile_header_size < copy_size) {
    copy_size = header->tile_header_size;
  }
  ::memcpy(&tile_header_, &(buf[0]), copy_size);
  DataSegmentInfo& topol_info = header->topol_data;
  if (!CopyBuf(topol_info.compress_flag, topol_info.buff_size, &buf[topol_info.address],
               topol_data_buff_)) {
    LOG_ERROR_TAG("RouteTileParser", "Copy topol buffer failed!!");
  }
  DataSegmentInfo& painting_info = header->painting_data;
  if (!CopyBuf(painting_info.compress_flag, painting_info.buff_size, &buf[painting_info.address],
               paint_data_buff_)) {
    LOG_ERROR_TAG("RouteTileParser", "Copy paint buffer failed!!");
  }
  DataSegmentInfo& dir_info = header->direction_data;
  if (!CopyBuf(dir_info.compress_flag, dir_info.buff_size, &buf[dir_info.address],
               direction_data_buff_)) {
    LOG_ERROR_TAG("RouteTileParser", "Copy direction buffer failed!!");
  }
}
#endif

bool RouteTileParser::CopyHeader(DataBuffer& buf) {
  if (buf.size() <= sizeof(RouteTileHeader)) {
    return false;
  }
  RouteTileHeader* header = (RouteTileHeader*)&(buf[0]);
  size_t copy_size = sizeof(RouteTileHeader);
  if (header->tile_header_size < copy_size) {
    copy_size = header->tile_header_size;
  }
  void* result = ::memcpy(&tile_header_, &(buf[0]), copy_size);
  return result != nullptr;
}

bool RouteTileParser::SetTopolBuffer(DataBuffer& buf) {
  if (buf.size() <= sizeof(RouteTileHeader)) {
    return false;
  }
  if (tile_header_.tile_header_size == 0) {
    CopyHeader(buf);
  }
  RouteTileHeader* header = (RouteTileHeader*)&(buf[0]);
  DataSegmentInfo& topol_info = header->topol_data;
  if (!CopyBuf(topol_info.compress_flag, topol_info.buff_size, &buf[topol_info.address],
               topol_data_buff_)) {
    LOG_ERROR_TAG("RouteTileParser", "Copy topol buffer failed!!");
    return false;
  }
  return true;
}

bool RouteTileParser::SetPaintBuffer(DataBuffer& buf) {
  if (buf.size() <= sizeof(RouteTileHeader)) {
    return false;
  }
  if (tile_header_.tile_header_size == 0) {
    CopyHeader(buf);
  }
  RouteTileHeader* header = (RouteTileHeader*)&(buf[0]);
  DataSegmentInfo& painting_info = header->painting_data;
  if (!CopyBuf(painting_info.compress_flag, painting_info.buff_size, &buf[painting_info.address],
               paint_data_buff_)) {
    LOG_ERROR_TAG("RouteTileParser", "Copy paint buffer failed!!");
    return false;
  }
  return true;
}

bool RouteTileParser::SetDirectionBuffer(DataBuffer& buf) {
  if (buf.size() <= sizeof(RouteTileHeader)) {
    return false;
  }
  if (tile_header_.tile_header_size == 0) {
    CopyHeader(buf);
  }
  RouteTileHeader* header = (RouteTileHeader*)&(buf[0]);
  DataSegmentInfo& dir_info = header->direction_data;
  if (!CopyBuf(dir_info.compress_flag, dir_info.buff_size, &buf[dir_info.address],
               direction_data_buff_)) {
    LOG_ERROR_TAG("RouteTileParser", "Copy direction buffer failed!!");
    return false;
  }
  return true;
}

bool RouteTileParser::ParseAllNode(RouteTileID id, const LngLatMbr& mbr, RouteNodeSet& nodes) {
  if (tile_header_.nodes.feature_count == 0) {
    return false;
  }
  if (tile_header_.nodes.feature_struct_size < sizeof(NodeBase)) {
    return false;
  }
  if (topol_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetTopolBuffer(buf)) {
      return false;
    }
  }
  uint32_t node_start = tile_header_.nodes.data_address - tile_header_.tile_header_size;
  nodes.resize(tile_header_.nodes.feature_count);
  uint32_t expansion_address =
      node_start + tile_header_.nodes.feature_count * tile_header_.nodes.feature_struct_size;

  std::lock_guard<std::mutex> lock(parse_mtx_);
  for (size_t i = 0; i < tile_header_.nodes.feature_count; ++i) {
    uint32_t offset = node_start + i * tile_header_.nodes.feature_struct_size;
    RouteNode& node = nodes[i];
    node.id_ = i;
    node.base_ = (NodeBase*)&(topol_data_buff_[offset]);
    double x = mbr.minx() + func::MD52LngLat(node.base_->lng);
    double y = mbr.miny() + func::MD52LngLat(node.base_->lat);
    node.lnglat_.Set(x, y);

    uint32_t ext_buf_offset = expansion_address + node.base_->expansion_address;
    if (node.base_->is_admin_boundary) {
      node.admin_bound_nodes_ = (AdminBoundaryNode*)&(topol_data_buff_[ext_buf_offset]);
      ext_buf_offset += sizeof(AdminBoundaryNode);
    }
    if (node.base_->is_trans_down) {
      node.trans_down_node_infos_ = (TransNodeInfo*)&(topol_data_buff_[ext_buf_offset]);
      ext_buf_offset += sizeof(TransNodeInfo);
    }
    if (node.base_->is_trans_up) {
      node.trans_up_node_infos_ = (TransNodeInfo*)&(topol_data_buff_[ext_buf_offset]);
      ext_buf_offset += sizeof(TransNodeInfo);
    }
    if (node.base_->boundary_node_count > 0) {
      node.boundary_nodes_.resize(node.base_->boundary_node_count, 0);
      for (uint8_t j = 0; j < node.base_->boundary_node_count; ++j) {
        node.boundary_nodes_[j] = (BoundaryNode*)&(topol_data_buff_[ext_buf_offset]);
        ext_buf_offset += sizeof(BoundaryNode);
      }
    }
    node.connected_edges_.resize(node.base_->connect_edge_count + 1, 0);
    for (uint8_t j = 0; j <= node.base_->connect_edge_count; ++j) {
      node.connected_edges_[j] = (ConnectedEdge*)&(topol_data_buff_[ext_buf_offset]);
      ext_buf_offset += sizeof(ConnectedEdge);
    }
    if (node.base_->is_trans_down) {
      node.trans_down_edge_infos_.resize(node.base_->connect_edge_count + 1, 0);
      for (uint8_t j = 0; j <= node.base_->connect_edge_count; ++j) {
        node.trans_down_edge_infos_[j] = (TransEdgeInfo*)&(topol_data_buff_[ext_buf_offset]);
        ext_buf_offset += sizeof(TransEdgeInfo);
      }
    }
    if (node.base_->is_trans_up) {
      node.trans_up_edge_infos_.resize(node.base_->connect_edge_count + 1, 0);
      for (uint8_t j = 0; j <= node.base_->connect_edge_count; ++j) {
        node.trans_up_edge_infos_[j] = (TransEdgeInfo*)&(topol_data_buff_[ext_buf_offset]);
        ext_buf_offset += sizeof(TransEdgeInfo);
      }
    }
  }
  return true;
}
bool RouteTileParser::ParseAllTopolEdge(RouteTileID id, TopolEdgeSet& topol_edges) {
  if (tile_header_.topol_edges.feature_count == 0) {
    return false;
  }
  if (tile_header_.topol_edges.feature_struct_size < sizeof(TopolEdgeBase)) {
    return false;
  }
  std::lock_guard<std::mutex> lock(parse_mtx_);
  if (topol_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetTopolBuffer(buf)) {
      return false;
    }
  }
  uint32_t topol_edge_start = tile_header_.topol_edges.data_address - tile_header_.tile_header_size;
  topol_edges.resize(tile_header_.topol_edges.feature_count);
  for (size_t i = 0; i < tile_header_.topol_edges.feature_count; ++i) {
    uint32_t offset = topol_edge_start + i * tile_header_.topol_edges.feature_struct_size;
    topol_edges[i].id_ = i;
    topol_edges[i].base_ = (TopolEdgeBase*)&(topol_data_buff_[offset]);
  }
  return true;
}
bool RouteTileParser::ParseAllLimitPass(RouteTileID id, LimitPassSet& limit_pass_array) {
  if (tile_header_.limit_pass.feature_count == 0) {
    return false;
  }
  if (tile_header_.limit_pass.feature_struct_size < sizeof(LimitPassBase)) {
    return false;
  }
  std::lock_guard<std::mutex> lock(parse_mtx_);
  if (topol_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetTopolBuffer(buf)) {
      return false;
    }
  }
  uint32_t limit_pass_start = tile_header_.limit_pass.data_address - tile_header_.tile_header_size;
  uint32_t time_domain_start =
      tile_header_.time_domains.data_address - tile_header_.tile_header_size;
  LimitPass default_item;
  limit_pass_array.resize(tile_header_.limit_pass.feature_count, default_item);
  for (size_t i = 0; i < tile_header_.limit_pass.feature_count; ++i) {
    uint32_t offset = limit_pass_start + i * tile_header_.limit_pass.feature_struct_size;
    limit_pass_array[i].base_ = (LimitPassBase*)&(topol_data_buff_[offset]);
    uint16_t time_domain_address = limit_pass_array[i].base_->time_domain_address;
    if (time_domain_address != 0xFFFF) {
      uint16_t time_domain_size =
          (uint8_t)(topol_data_buff_[time_domain_start + time_domain_address]);
      limit_pass_array[i].time_domain_.resize(time_domain_size + 1, 0);
      int8_t* src = (int8_t*)&(topol_data_buff_[time_domain_start + time_domain_address + 1]);
      ::memcpy(&(limit_pass_array[i].time_domain_[0]), src, time_domain_size);
      limit_pass_array[i].ParseLimitTime();
    }
  }
  return true;
}
bool RouteTileParser::ParseAllAugmentEdge(RouteTileID id, const LngLatMbr& mbr,
                                          const RouteNodeSet& nodes,
                                          const TopolEdgeSet& topol_edges,
                                          AugmentEdgeSet& augment_edges) {
  if (tile_header_.augment_edges.feature_count == 0) {
    return false;
  }
  if (tile_header_.augment_edges.feature_struct_size < sizeof(AugmentEdgeBase)) {
    return false;
  }

  if (nodes.empty() || topol_edges.empty()) {
    return false;
  }

  std::lock_guard<std::mutex> lock(parse_mtx_);
  if (paint_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetPaintBuffer(buf)) {
      return false;
    }
  }
  AlignGeoBuf();

  uint32_t expansion_address =
      tile_header_.augment_edges.feature_count * tile_header_.augment_edges.feature_struct_size;
  uint32_t name_list_address =
      tile_header_.names.data_address - tile_header_.augment_edges.data_address;
  uint32_t subedge_list_address =
      tile_header_.subedges.data_address - tile_header_.augment_edges.data_address;
  augment_edges.resize(tile_header_.augment_edges.feature_count);
  uint32_t slope_points_address =
      tile_header_.slope_points.data_address - tile_header_.lane_infos.data_address;
  uint32_t curve_points_address =
      tile_header_.curve_points.data_address - tile_header_.lane_infos.data_address;

  for (size_t i = 0; i < tile_header_.augment_edges.feature_count; ++i) {
    uint32_t offset = i * tile_header_.augment_edges.feature_struct_size;
    augment_edges[i].id_ = i;
    augment_edges[i].base_ = (AugmentEdgeBase*)&(paint_data_buff_[offset]);
    uint32_t start_node = topol_edges[i].base_->start_node_id;
    uint32_t end_node = topol_edges[i].base_->end_node_id;
    augment_edges[i].mbr_.Create({nodes[start_node].lnglat_, nodes[end_node].lnglat_});

    // Geometry
    uint32_t ext_buf_offset = expansion_address + augment_edges[i].base_->expansion_address;
    if (augment_edges[i].base_->has_middle_point) {
      uint16_t start_id = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      uint16_t point_count = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);

      augment_edges[i].geo_points_.resize(point_count + 2);
      augment_edges[i].geo_points_[0] = nodes[start_node].lnglat_;
      augment_edges[i].geo_points_[point_count + 1] = nodes[end_node].lnglat_;
      for (uint16_t j = 0; j < point_count; ++j) {
        double x =
            augment_edges[i].geo_points_[j].x() + func::MD52LngLat(geo_buf_[(start_id + j) * 2]);
        double y = augment_edges[i].geo_points_[j].y() +
                   func::MD52LngLat(geo_buf_[(start_id + j) * 2 + 1]);
        augment_edges[i].geo_points_[j + 1].Set(x, y);
        if (!augment_edges[i].base_->not_calc_mbr) {
          augment_edges[i].mbr_.Expand(augment_edges[i].geo_points_[j + 1]);
        }
      }
    } else {
      augment_edges[i].geo_points_.resize(2);
      augment_edges[i].geo_points_[0] = nodes[start_node].lnglat_;
      augment_edges[i].geo_points_[1] = nodes[end_node].lnglat_;
    }
    // road name
    if (augment_edges[i].base_->has_road_name) {
      uint16_t name_offset = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      uint8_t local_name_size = paint_data_buff_[name_list_address + name_offset];
      if (local_name_size > 0) {
        int8_t* name = paint_data_buff_.data() + name_list_address + name_offset + 2;
        augment_edges[i].local_name_ = std::string((char*)name, local_name_size);
      }
      uint8_t foreign_name_size = paint_data_buff_[name_list_address + name_offset + 1];
      if (foreign_name_size > 0) {
        int8_t* name =
            paint_data_buff_.data() + name_list_address + name_offset + 2 + local_name_size;
        augment_edges[i].foreign_name_ = std::string((char*)name, foreign_name_size);
      }
    }
    // road no
    if (augment_edges[i].base_->has_road_no) {
      uint16_t no_offset = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      uint8_t road_no_size = paint_data_buff_[name_list_address + no_offset];
      if (road_no_size > 0) {
        int8_t* str = paint_data_buff_.data() + name_list_address + no_offset + 2;
        augment_edges[i].road_no_ = std::string((char*)str, road_no_size);
      }
    }
    // subedge info
    if (id.level > 0) {
      uint16_t subedge_start_id = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      uint16_t subedge_count = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      augment_edges[i].subedges_.resize(subedge_count, 0);
      for (uint16_t j = 0; j < subedge_count; ++j) {
        augment_edges[i].subedges_[j] = (SubEdge*)&(
            paint_data_buff_[subedge_list_address +
                             tile_header_.subedges.feature_struct_size * (j + subedge_start_id)]);
      }
    }
    // sapa
    if (augment_edges[i].base_->has_sapa) {
      augment_edges[i].sapa_ = std::make_shared<Sapa>();
      augment_edges[i].sapa_->info = (SaPaInfo*)&(paint_data_buff_[ext_buf_offset]);
      double x = mbr.minx() + func::MD52LngLat(augment_edges[i].sapa_->info->lng);
      double y = mbr.miny() + func::MD52LngLat(augment_edges[i].sapa_->info->lat);
      augment_edges[i].sapa_->lnglat.Set(x, y);

      uint32_t name_addr = name_list_address + augment_edges[i].sapa_->info->name_addr;
      uint8_t name_size = paint_data_buff_[name_addr];
      if (name_size > 0) {
        char* name_str = (char*)(paint_data_buff_.data() + name_addr + 2);
        augment_edges[i].sapa_->name = std::string(name_str, name_size);
      }
      ext_buf_offset += sizeof(SaPaInfo);
    }
    // adcode info
    if (augment_edges[i].base_->has_adcode) {
      augment_edges[i].adcode_ = std::make_shared<AdminInfo>();
      augment_edges[i].adcode_->adcode = *((uint32_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint32_t);
      uint16_t name_offset = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      uint8_t local_name_size = paint_data_buff_[name_list_address + name_offset];
      if (local_name_size > 0) {
        int8_t* str = paint_data_buff_.data() + name_list_address + name_offset + 2;
        augment_edges[i].adcode_->local_name = std::string((char*)str, local_name_size);
      }
      uint8_t foreign_name_size = paint_data_buff_[name_list_address + name_offset + 1];
      if (foreign_name_size > 0) {
        int8_t* str =
            paint_data_buff_.data() + name_list_address + name_offset + 2 + local_name_size;
        augment_edges[i].adcode_->foreign_name = std::string((char*)str, foreign_name_size);
      }
    }
    // slope point info
    if (augment_edges[i].base_->has_slope_point) {
      uint16_t point_start = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      uint16_t point_count = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      augment_edges[i].slope_point_.resize(point_count, 0);
      for (uint16_t j = 0; j < point_count; ++j) {
        augment_edges[i].slope_point_[j] =
            (SlopePoint*)&(direction_data_buff_[slope_points_address +
                                                tile_header_.slope_points.feature_struct_size *
                                                    (j + point_start)]);
      }
    }
    // curve point info
    if (augment_edges[i].base_->has_curve_point) {
      uint16_t point_start = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      uint16_t point_count = *((uint16_t*)&(paint_data_buff_[ext_buf_offset]));
      ext_buf_offset += sizeof(uint16_t);
      augment_edges[i].curve_point_.resize(point_count, 0);
      for (uint16_t j = 0; j < point_count; ++j) {
        augment_edges[i].curve_point_[j] =
            (CurvePoint*)&(direction_data_buff_[curve_points_address +
                                                tile_header_.curve_points.feature_struct_size *
                                                    (j + point_start)]);
      }
    }
  }
  return true;
}
bool RouteTileParser::ParseLaneInfo(RouteTileID id, LaneInfoSet& lane_infos) {
  if (tile_header_.lane_infos.feature_count == 0) {
    return false;
  }
  if (tile_header_.lane_infos.feature_struct_size < sizeof(LaneBase)) {
    return false;
  }

  std::lock_guard<std::mutex> lock(parse_mtx_);
  if (direction_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetDirectionBuffer(buf)) {
      return false;
    }
  }
  uint32_t lane_info_start = 0;
  uint32_t lane_relation_start =
      tile_header_.lane_infos.feature_struct_size * tile_header_.lane_infos.feature_count;
  lane_infos.resize(tile_header_.lane_infos.feature_count);
  for (size_t i = 0; i < tile_header_.lane_infos.feature_count; ++i) {
    uint32_t offset = lane_info_start + i * tile_header_.lane_infos.feature_struct_size;
    lane_infos[i].id_ = i;
    lane_infos[i].base_ = (LaneBase*)&(direction_data_buff_[offset]);

    uint8_t lane_relation_count = lane_infos[i].base_->lane_relation_count;
    lane_infos[i].relations_.resize(lane_relation_count);
    for (u_int8_t j = 0; j < lane_relation_count; ++j) {
      uint32_t relation_offset =
          lane_relation_start + tile_header_.lane_infos.lane_relation_size *
                                    (j + lane_infos[i].base_->lane_relation_start_id);
      lane_infos[i].relations_[j] = (LaneRelation*)&(direction_data_buff_[relation_offset]);
    }
  }
  return true;
}
bool RouteTileParser::ParseFacilityInfo(RouteTileID id, const LngLatMbr& mbr,
                                        FacilityInfoSet& facility_infos) {
  if (tile_header_.facilities.feature_count == 0) {
    return false;
  }
  if (tile_header_.facilities.feature_struct_size < sizeof(FacilityBase)) {
    return false;
  }

  std::lock_guard<std::mutex> lock(parse_mtx_);
  if (direction_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetDirectionBuffer(buf)) {
      return false;
    }
  }
  uint32_t facility_info_start =
      tile_header_.facilities.data_address - tile_header_.lane_infos.data_address;
  facility_infos.resize(tile_header_.facilities.feature_count);
  for (size_t i = 0; i < tile_header_.facilities.feature_count; ++i) {
    uint32_t offset = facility_info_start + i * tile_header_.facilities.feature_struct_size;
    facility_infos[i].base_ = (FacilityBase*)&(direction_data_buff_[offset]);
    double x = mbr.minx() + func::MD52LngLat(facility_infos[i].base_->lng);
    double y = mbr.miny() + func::MD52LngLat(facility_infos[i].base_->lat);
    facility_infos[i].position_.Set(x, y);
  }
  return true;
}
bool RouteTileParser::ParseJuncviewInfo(RouteTileID id, JuncviewInfoSet& juncview_infos) {
  if (tile_header_.juncviews.feature_count == 0) {
    return false;
  }
  if (tile_header_.juncviews.feature_struct_size < sizeof(JuncviewBase)) {
    return false;
  }

  std::lock_guard<std::mutex> lock(parse_mtx_);
  if (direction_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetDirectionBuffer(buf)) {
      return false;
    }
  }
  uint32_t juncview_info_start =
      tile_header_.juncviews.data_address - tile_header_.lane_infos.data_address;
  juncview_infos.resize(tile_header_.juncviews.feature_count);
  for (size_t i = 0; i < tile_header_.juncviews.feature_count; ++i) {
    juncview_infos[i].adcode_ = id.adcode;
    uint32_t offset = juncview_info_start + i * tile_header_.juncviews.feature_struct_size;
    juncview_infos[i].base_ = (JuncviewBase*)&(direction_data_buff_[offset]);
  }
  return true;
}
bool RouteTileParser::ParseSignpostInfo(RouteTileID id, SignpostInfoSet& signpost_infos) {
  if (tile_header_.signposts.feature_count == 0) {
    return false;
  }
  if (tile_header_.signposts.feature_struct_size < sizeof(SignpostBase)) {
    return false;
  }

  std::lock_guard<std::mutex> lock(parse_mtx_);
  if (paint_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetPaintBuffer(buf)) {
      return false;
    }
  }
  if (direction_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetDirectionBuffer(buf)) {
      return false;
    }
  }
  uint32_t name_list_address =
      tile_header_.names.data_address - tile_header_.augment_edges.data_address;
  uint32_t signpost_info_start =
      tile_header_.signposts.data_address - tile_header_.lane_infos.data_address;
  signpost_infos.resize(tile_header_.signposts.feature_count);
  for (size_t i = 0; i < tile_header_.signposts.feature_count; ++i) {
    uint32_t offset = signpost_info_start + i * tile_header_.signposts.feature_struct_size;
    signpost_infos[i].id_ = i;
    signpost_infos[i].base_ = (SignpostBase*)&(direction_data_buff_[offset]);

    std::vector<int8_t*> info_names;
    info_names.reserve(3);
    uint8_t j = 0;
    while (j < 3 && signpost_infos[i].base_->info_offset[j] != 0xFFFF) {
      uint32_t name_offset = name_list_address + signpost_infos[i].base_->info_offset[j];
      int8_t* name_start = &(paint_data_buff_[name_offset]);
      info_names.push_back(name_start);
      ++j;
    }

    uint8_t sign_info_count = info_names.size();
    signpost_infos[i].infos_.resize(sign_info_count);
    signpost_infos[i].foreign_infos_.resize(sign_info_count);
    for (uint8_t j = 0; j < sign_info_count; ++j) {
      uint8_t info_size = info_names[j][0];
      if (info_size > 0) {
        signpost_infos[i].infos_[j].resize(info_size + 1, 0);
        ::memcpy(&(signpost_infos[i].infos_[j][0]), info_names[j] + 2, info_size);
      }
      uint8_t foreign_info_size = info_names[j][1];
      if (foreign_info_size > 0) {
        signpost_infos[i].foreign_infos_[j].resize(foreign_info_size + 1, 0);
        ::memcpy(&(signpost_infos[i].foreign_infos_[j][0]), info_names[j] + info_size + 2,
                 foreign_info_size);
      }
    }
  }
  return true;
}
bool RouteTileParser::ParseTollgateInfo(RouteTileID id, TollgateInfoSet& tollgate_infos) {
  if (tile_header_.tollgates.feature_count == 0) {
    return false;
  }
  if (tile_header_.tollgates.feature_struct_size < sizeof(TollgateBase)) {
    return false;
  }

  std::lock_guard<std::mutex> lock(parse_mtx_);
  if (paint_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetPaintBuffer(buf)) {
      return false;
    }
  }
  if (direction_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetDirectionBuffer(buf)) {
      return false;
    }
  }
  uint32_t name_list_address =
      tile_header_.names.data_address - tile_header_.augment_edges.data_address;
  uint32_t tollgate_info_start =
      tile_header_.tollgates.data_address - tile_header_.lane_infos.data_address;
  uint32_t tollgate_gate_start =
      tollgate_info_start +
      tile_header_.tollgates.feature_count * tile_header_.tollgates.feature_struct_size;
  tollgate_infos.resize(tile_header_.tollgates.feature_count);
  for (size_t i = 0; i < tile_header_.tollgates.feature_count; ++i) {
    tollgate_infos[i].adcode_ = id.adcode;
    uint32_t offset = tollgate_info_start + i * tile_header_.tollgates.feature_struct_size;
    tollgate_infos[i].base_ = (TollgateBase*)&(direction_data_buff_[offset]);

    tollgate_infos[i].gates_.resize(tollgate_infos[i].base_->toll_gate_count);
    uint32_t gate_offset = tollgate_gate_start + tollgate_infos[i].base_->toll_gate_start_id *
                                                     tile_header_.tollgates.tollgate_gate_size;
    ::memcpy(&tollgate_infos[i].gates_[0], &direction_data_buff_[gate_offset],
             tile_header_.tollgates.tollgate_gate_size * tollgate_infos[i].base_->toll_gate_count);

    if (tollgate_infos[i].base_->name_offset != 0xFFFF) {
      uint32_t name_offset = name_list_address + tollgate_infos[i].base_->name_offset;
      uint8_t local_name_size = paint_data_buff_[name_offset];
      if (local_name_size > 0) {
        int8_t* str = paint_data_buff_.data() + name_offset + 2;
        tollgate_infos[i].name_ = std::string((char*)str, local_name_size);
      }
      uint8_t foreign_name_size = paint_data_buff_[name_offset + 1];
      if (foreign_name_size > 0) {
        int8_t* str = paint_data_buff_.data() + name_offset + 2 + local_name_size;
        tollgate_infos[i].foreign_name_ = std::string((char*)str, foreign_name_size);
      }
    }
  }
  return true;
}
bool RouteTileParser::ParseVoiceInfo(RouteTileID id, VoiceInfoSet& voice_infos) {
  if (tile_header_.voice_infos.feature_count == 0) {
    return false;
  }
  if (tile_header_.voice_infos.feature_struct_size < sizeof(VoiceBase)) {
    return false;
  }

  std::lock_guard<std::mutex> lock(parse_mtx_);
  if (direction_data_buff_.empty()) {
    DataBuffer buf;
    RouteDataParser::Instance().GetTileBuffer(id, buf);
    if (!SetDirectionBuffer(buf)) {
      return false;
    }
  }
  uint32_t voice_info_start =
      tile_header_.voice_infos.data_address - tile_header_.lane_infos.data_address;
  voice_infos.resize(tile_header_.voice_infos.feature_count);
  for (size_t i = 0; i < tile_header_.voice_infos.feature_count; ++i) {
    uint32_t offset = voice_info_start + i * tile_header_.voice_infos.feature_struct_size;
    voice_infos[i].base_ = (VoiceBase*)&(direction_data_buff_[offset]);
  }
  return true;
}

bool RouteTileParser::CopyBuf(bool compress_flag, size_t buf_size, int8_t* src, DataBuffer& dst) {
  if (buf_size == 0) {
    return false;
  }
  if (compress_flag) {
    size_t decompress_buf_size = DecompressMaxsize(src);
    dst.resize(decompress_buf_size, 0);
    DecompressBuff(src, &dst[0], buf_size, decompress_buf_size);
  } else {
    dst.resize(buf_size, 0);
    ::memcpy(&dst[0], src, buf_size);
  }
  return true;
}
void RouteTileParser::AlignGeoBuf() {
  uint32_t count = 2 * tile_header_.geo_points.feature_count;
  uint32_t geo_data_address =
      tile_header_.geo_points.data_address - tile_header_.augment_edges.data_address;
  int8_t* ori_buf = &(paint_data_buff_[geo_data_address]);
  func::AlignGeoBuf<int8_t, int16_t>(ori_buf, count, geo_buf_);
}

uint32_t RouteTileParser::GetAdminBoundNodeCount() {
  return tile_header_.admin_boundary_nodes.feature_count;
}
BoundaryNodeIndex* RouteTileParser::GetAdminBoundNode(uint32_t index) {
  if (index >= tile_header_.admin_boundary_nodes.feature_count) {
    return nullptr;
  }
  uint32_t adress = tile_header_.admin_boundary_nodes.data_address - tile_header_.tile_header_size +
                    tile_header_.admin_boundary_nodes.feature_struct_size * index;
  return (BoundaryNodeIndex*)&topol_data_buff_[adress];
}

}  // namespace parser
}  // namespace aurora
