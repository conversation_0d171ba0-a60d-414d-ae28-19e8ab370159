#ifndef MAP_SRC_DATA_PROVIDER_ROUTE_DATA_ROUTE_TILE_PARSER_H
#define MAP_SRC_DATA_PROVIDER_ROUTE_DATA_ROUTE_TILE_PARSER_H

#include <mutex>

#include "common/common_def.h"
#include "route_data/feature/augment_edge.h"
#include "route_data/feature/facility_info.h"
#include "route_data/feature/juncview_info.h"
#include "route_data/feature/lane_info.h"
#include "route_data/feature/limit_pass.h"
#include "route_data/feature/route_node.h"
#include "route_data/feature/signpost_info.h"
#include "route_data/feature/tollgate_info.h"
#include "route_data/feature/topol_edge.h"
#include "route_data/feature/voice_info.h"
#include "route_data/route_data_def.h"
#include "route_data/route_tile_feature_def.h"

namespace aurora {
namespace parser {

class RouteTileParser {
public:
  RouteTileParser();
#if 0
  void SetTileBuffer(DataBuffer& buf);
#endif

  bool CopyHeader(DataBuffer& buf);

  bool SetTopolBuffer(DataBuffer& buf);

  bool SetPaintBuffer(DataBuffer& buf);

  bool SetDirectionBuffer(DataBuffer& buf);

  bool ParseAllNode(RouteTileID id, const LngLatMbr& mbr, RouteNodeSet& nodes);

  bool ParseAllTopolEdge(RouteTileID id, TopolEdgeSet& topol_edges);

  bool ParseAllLimitPass(RouteTileID id, LimitPassSet& limit_pass_array);

  bool ParseAllAugmentEdge(RouteTileID id, const LngLatMbr& mbr, const RouteNodeSet& nodes,
                           const TopolEdgeSet& topol_edges, AugmentEdgeSet& augment_edges);

  bool ParseLaneInfo(RouteTileID id, LaneInfoSet& lane_infos);

  bool ParseFacilityInfo(RouteTileID id, const LngLatMbr& mbr, FacilityInfoSet& facility_infos);

  bool ParseJuncviewInfo(RouteTileID id, JuncviewInfoSet& juncview_infos);

  bool ParseSignpostInfo(RouteTileID id, SignpostInfoSet& signpost_infos);

  bool ParseTollgateInfo(RouteTileID id, TollgateInfoSet& tollgate_infos);

  bool ParseVoiceInfo(RouteTileID id, VoiceInfoSet& voice_infos);

  bool CopyBuf(bool compress_flag, size_t buf_size, int8_t* src, DataBuffer& dst);

  void AlignGeoBuf();

  uint32_t GetAdminBoundNodeCount();
  BoundaryNodeIndex* GetAdminBoundNode(uint32_t index);

private:
  /**
   * @brief Header information of the route data tile.
   */
  RouteTileHeader tile_header_;

  /**
   * @brief Raw buffer containing topological edge data.
   */
  DataBuffer topol_data_buff_;

  /**
   * @brief Raw buffer containing visual painting data.
   */
  DataBuffer paint_data_buff_;

  /**
   * @brief Raw buffer containing directional navigation data.
   */
  DataBuffer direction_data_buff_;

  /**
   * @brief Aligned geographic coordinate buffer.
   */
  GeoBuffer geo_buf_;

  std::mutex parse_mtx_;
};
}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_ROUTE_DATA_ROUTE_TILE_PARSER_H
