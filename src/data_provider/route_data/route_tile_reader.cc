#include "route_data/route_tile_reader.h"

#include "route_data/route_data_parser.h"
#include "route_data/route_tile_package.h"
namespace aurora {
namespace parser {
RouteTileReader::RouteTileReader() : ptr_(nullptr) {}

void RouteTileReader::SetTarget(RouteTilePackagePtr ptr) { ptr_ = ptr; }

RouteTileID RouteTileReader::GetTileID() {
  if (ptr_ == nullptr) {
    RouteTileID id;
    return id;
  } else {
    return ptr_->GetTileID();
  }
}
//const LngLatMbr* RouteTileReader::GetMbr() {
//  if (ptr_ == nullptr) {
//    return nullptr;
//  } else {
//    return &ptr_->GetMbr();
//  }
//}
std::vector<uint32_t> RouteTileReader::GetAugmentEdgesByMbr(LngLatMbr mbr) {
  if (ptr_ == nullptr) {
    std::vector<uint32_t> no_data;
    return no_data;
  } else {
    return ptr_->GetAugmentEdgesByMbr(mbr);
  }
}
AugmentEdgeSet& RouteTileReader::GetAugmentEdges() {
  if (ptr_ == nullptr) {
    return no_augment_edge_;
  } else {
    return ptr_->GetAugmentEdges();
  }
}
TopolEdgeSet& RouteTileReader::GetTopolEdges() {
  if (ptr_ == nullptr) {
    return no_topol_edge_;
  } else {
    return ptr_->GetTopolEdges();
  }
}
RouteNodeSet& RouteTileReader::GetNodes() {
  if (ptr_ == nullptr) {
    return no_node_;
  } else {
    return ptr_->GetNodes();
  }
}
LimitPassSet& RouteTileReader::GetLimitPass() {
  if (ptr_ == nullptr) {
    return no_limit_pass_;
  } else {
    return ptr_->GetLimitPass();
  }
}

LaneInfoSet& RouteTileReader::GetLaneInfo() {
  if (ptr_ == nullptr) {
    return no_lane_info_;
  } else {
    return ptr_->GetLaneInfo();
  }
}
FacilityInfoSet& RouteTileReader::GetFacilityInfo() {
  if (ptr_ == nullptr) {
    return no_facility_info_;
  } else {
    return ptr_->GetFacilityInfo();
  }
}
JuncviewInfoSet& RouteTileReader::GetJuncviewInfo() {
  if (ptr_ == nullptr) {
    return no_juncview_info_;
  } else {
    return ptr_->GetJuncviewInfo();
  }
}
SignpostInfoSet& RouteTileReader::GetSignpostInfo() {
  if (ptr_ == nullptr) {
    return no_signpost_info_;
  } else {
    return ptr_->GetSignpostInfo();
  }
}
TollgateInfoSet& RouteTileReader::GetTollgateInfo() {
  if (ptr_ == nullptr) {
    return no_tollgate_info_;
  } else {
    return ptr_->GetTollgateInfo();
  }
}
VoiceInfoSet& RouteTileReader::GetVoiceInfo() {
  if (ptr_ == nullptr) {
    return no_voice_info_;
  } else {
    return ptr_->GetVoiceInfo();
  }
}
AugmentEdge* RouteTileReader::GetAugmentEdgeByID(uint32_t id) {
  if (ptr_ == nullptr) {
    return nullptr;
  } else {
    return ptr_->GetAugmentEdgeByID(id);
  }
}
TopolEdge* RouteTileReader::GetTopolEdgeByID(uint32_t id) {
  if (ptr_ == nullptr) {
    return nullptr;
  } else {
    return ptr_->GetTopolEdgeByID(id);
  }
}
RouteNode* RouteTileReader::GetNodeByID(uint32_t id) {
  if (ptr_ == nullptr) {
    return nullptr;
  } else {
    return ptr_->GetNodeByID(id);
  }
}
RouteTileID RouteTileReader::GetAdjacentTileIDByBoundaryNode(const BoundaryNode* boundary_node) {
  if (ptr_ == nullptr) {
    RouteTileID id;
    return id;
  } else {
    return ptr_->GetAdjacentTileIDByBoundaryNode(boundary_node);
  }
}
bool RouteTileReader::GetInEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                                  std::vector<bool>& is_forward) {
  return RouteDataParser::Instance().GetEdgeIDByNode(true, src_node, dst_edges, is_forward);
}

bool RouteTileReader::GetTransUpInEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                                         std::vector<bool>& is_forward) {
  return RouteDataParser::Instance().GetTransUpEdgeIDByNode(true, src_node, dst_edges, is_forward);
}

bool RouteTileReader::GetTransDownInEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                                           std::vector<bool>& is_forward) {
  return RouteDataParser::Instance().GetTransDownEdgeIDByNode(true, src_node, dst_edges,
                                                              is_forward);
}

bool RouteTileReader::GetOutEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                                   std::vector<bool>& is_forward) {
  return RouteDataParser::Instance().GetEdgeIDByNode(false, src_node, dst_edges, is_forward);
}

bool RouteTileReader::GetTransUpOutEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                                          std::vector<bool>& is_forward) {
  return RouteDataParser::Instance().GetTransUpEdgeIDByNode(false, src_node, dst_edges, is_forward);
}

bool RouteTileReader::GetTransDownOutEdgeID(RouteNodeID src_node, RouteEdgeIDSet& dst_edges,
                                            std::vector<bool>& is_forward) {
  return RouteDataParser::Instance().GetTransDownEdgeIDByNode(false, src_node, dst_edges,
                                                              is_forward);
}

TopolEdge* RouteTileReader::GetTopolEdgeByEdgeID(RouteEdgeID edge_id) {
  if (ptr_ == nullptr) {
    return nullptr;
  }
  if (edge_id.tile_id != ptr_->GetTileID()) {
    return nullptr;
  }
  return ptr_->GetTopolEdgeByID(edge_id.feature_id);
}
}  // namespace parser
}  // namespace aurora
