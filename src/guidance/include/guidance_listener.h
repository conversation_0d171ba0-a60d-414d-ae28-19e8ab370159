#ifndef MAP_SRC_GUIDE_INCLUDE_GUIDE_LISTENER_H
#define MAP_SRC_GUIDE_INCLUDE_GUIDE_LISTENER_H

#include <cstdint>
#include <string>
#include <vector>
#include <memory>

#include "export_type_def.h"
#include "guidance_def.h"
#include "path_module.h"

namespace aurora {
namespace guide {

enum class GuidanceListenerType {
    kGuidanceListenerTypeNone = 0,
    kGuidanceListenerTypeNavigation,
    kGuidanceListenerTypeCruise,
    kGuidanceListenerTypeSound
};

class AURORA_EXPORT IGuidanceListener {
public:
    IGuidanceListener(const GuidanceListenerType type)
    : type_(type) {
    }

    inline GuidanceListenerType GetType() const {
        return type_;
    }

    virtual ~IGuidanceListener() = default;

private:
    GuidanceListenerType type_;
};

/**
 * @brief 导航模式的对外通知回调
 */
class AURORA_EXPORT INavigationListener : public IGuidanceListener { 
public:
    INavigationListener() : IGuidanceListener(GuidanceListenerType::kGuidanceListenerTypeNavigation) {}
    virtual ~INavigationListener() = default;

    /**
     * @brief 路线转向详情
     * @note  触发条件: 收到路线结果之后会触发该回调
     */
    virtual void OnPathManeuverDetail(int64_t path_id, const std::vector<ManeuverDetail> &details) {}

#if 1
    /**
     * @brief 导航路线基础信息(ETA,距离，速度，限速，红绿灯***)
     * @param nav_info: 导航面板信息，如果为空，停止显示导航面板
     * @note 刷新频率：1hz
     */
    virtual void OnUpdateNavigationInfo(NavigationInfoPtr nav_info) {}

    /**
     * @brief 方向路牌信息
     */
    virtual void OnUpdateExitDirectionInfo(NavigationExitDirectionInfoPtr info) {}

    /**
     * @brief 路口放大图信息显示、隐藏
     */
    virtual void OnShowJunctionView(JunctionViewInfoPtr info) {}

    /**
     * @brief LaneInfo信息显示、隐藏
     */
    virtual void OnShowLaneInfo(NavigationLaneInfoPtr lane_info) {}

    /**
     * @brief 显示camera相关信息
     */
    virtual void OnShowCamerasInfo(const std::vector<CameraInfoPtr> &cam_list) {}

    /**
     * @brief 更新道路设施信息
     */
    virtual void OnUpdateFacilityInfo(const std::vector<NavigationFacilityPtr>& infos) {}
    
    /**
     * @brief 更新服务区检查站等信息
     */
    virtual void OnUpdateSAPAInfo(const std::vector<SAPAInfoPtr> &sapa_list) {}

    /**
     * @brief 导航过程中经过途径地时通知途径地索引
     */
    virtual void OnUpdateWayPointPass(const NavigationMode type, int32_t via_idx) {}

    /**
     * @brief 导航到达目的地
     */
    virtual void OnNavigationArrive(const NavigationMode type) {}

    /**
     * @brief 导航开始
     */
    virtual void OnNavigationStart(const NavigationMode type, uint64_t path_id) {}

    /**
     * @brief 导航暂停
     */
    virtual void OnNavigationPause(const NavigationMode type) {}

    /**
     * @brief 导航恢复
     */
    virtual void OnNavigationResume(const NavigationMode type) {}

    /**
     * @brief 导航结束
     */
    virtual void OnNavigationStop(const NavigationMode type, const NavigationStopCode code) {}

    /**
     * @brief guide通知路径规划模块重新规划
     * @note 因偏航，道路限行，tmc路况拥堵等原因，guide引擎会通知外界进行路线重算
     */
    virtual void OnReroute(const path::PathQuery *req) {}

    /**
     * @brief 更新路线光柱图
     */
    virtual void OnUpdateTmcLightBar(LightBarDetailPtr detail) {}

    /**
     * @brief 道路路线中的拥堵路段时长&原因
     */
    virtual void OnUpdateTmcCongestionInfo(NavigationCongestionInfoPtr info) {}

    /**
     * @brief 更新路线交通事件信息
     */
    virtual void OnUpdateTmcEvent(const std::vector<PathTmcEventInfoPtr> &info) {}

    /**
     * @brief 切换路线回调
     */
    virtual void OnSwitchPathStatus(uint64_t path_id, bool success) {}

    /**
     * @brief 更新收费站信息
     */
    virtual void OnUpdateTollStationInfo(TollStationInfoPtr info) {}
#endif
};

/**
 * @brief 巡航模式的对外通知回调
 */
class AURORA_EXPORT ICruiseListener : public IGuidanceListener {
public:
    ICruiseListener() : IGuidanceListener(GuidanceListenerType::kGuidanceListenerTypeCruise) {}

    // TODO: 
    // 前方转向、路口、红绿灯信息 
    // 关闭特定引导信息<电子眼、限速、>
    // 机动点开启/关闭可配置
    virtual ~ICruiseListener() = default;

    /**
     * @brief 更新巡航面板信息，1hz
     */
    virtual void OnUpdateCruiseInfo(CruiseInfoPtr info) = 0;
    
    /**
     * @brief 更新camera信息
     */
    virtual void OnShowCamerasInfo(const std::vector<CameraInfoPtr> &info) = 0;
    
    /**
     * @brief 更新前方路况信息
     */
    virtual void OnUpdateCongestionInfo(CruiseCongestionInfoPtr info) = 0;
};

/**
 * @brief 需要语音播报的通知回调
 */
class AURORA_EXPORT ISoundListener : public IGuidanceListener {
public:
    ISoundListener() : IGuidanceListener(GuidanceListenerType::kGuidanceListenerTypeSound) {}

    virtual ~ISoundListener() = default;

    /**
     * @brief 提示音播报
     */
    virtual void OnPlayRing(const TTSScenePlay scene_id) = 0;

    /**
     * @brief 请求播报TTS
     */
    virtual void OnPlayTTS(SoundInfoPtr info) = 0;
    
    /**
     * @brief 当前是否在播报
     */
    virtual bool IsPlaying() = 0;

};  // class ISoundObserver

using IGuidanceListenerPtr = std::shared_ptr<IGuidanceListener>;
using INavigationListenerPtr = std::shared_ptr<INavigationListener>;
using ICruiseListenerPtr = std::shared_ptr<ICruiseListener>;
using ISoundListenerPtr = std::shared_ptr<ISoundListener>;

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_INCLUDE_GUIDE_LISTENER_H
/* EOF */
