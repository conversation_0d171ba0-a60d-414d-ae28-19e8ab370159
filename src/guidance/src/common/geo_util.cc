#include "guidance/src/common/geo_util.h"
#include <cmath>

#include "guidance/src/common/common_def.h"

namespace aurora {
namespace guide {
namespace geo {

static constexpr float kMinMetersOffsetForHeading = 15.0f;

float GetOffsetForHeading(int road_class) {
    // TODO: 根据RoadClass做灵活调整
    return kMinMetersOffsetForHeading*2;
}

int32_t CalcTurnDegree360(int32_t from_heading, int32_t to_heading) {
    return ((to_heading - from_heading) + 360) % 360;
}

int32_t CalcTurnDegree180(int32_t from_heading, int32_t to_heading) {
    int32_t delta = std::fabs(to_heading - from_heading);
    return (delta < 180) ? delta : (360 - delta);
}

bool IsSimilarTurnDegree(uint32_t path_turn_degree, uint32_t intersecting_turn_degree,
    bool is_right, uint32_t turn_degree_threshold) {

    uint32_t turn_degree_delta = 0;
    if (is_right) {
        turn_degree_delta = (((intersecting_turn_degree - path_turn_degree) + 360) % 360);
    } else {
        turn_degree_delta = (((path_turn_degree - intersecting_turn_degree) + 360) % 360);
    }

    return (turn_degree_delta <= turn_degree_threshold);
}


bool IsRelativeStraight(uint32_t turn_degree) {
    return ((turn_degree > 329) || (turn_degree < 31));
}

bool IsForward(uint32_t turn_degree) {
    return ((turn_degree > 314) || (turn_degree < 46));
}

bool IsWideForward(uint32_t turn_degree) {
    return ((turn_degree > 304) || (turn_degree < 56));
}

bool IsForkForward(uint32_t turn_degree) {
    return ((turn_degree > 339) || (turn_degree < 21));
}

Maneuver::CardinalDirection DetermineCardinalDirection(uint32_t heading) {
    if ((heading > 336) || (heading < 24)) {
        return Maneuver::CardinalDirection::kNorth;
    } else if ((heading > 23) && (heading < 67)) {
        return Maneuver::CardinalDirection::kNorthEast;
    } else if ((heading > 66) && (heading < 114)) {
        return Maneuver::CardinalDirection::kEast;
    } else if ((heading > 113) && (heading < 157)) {
        return Maneuver::CardinalDirection::kEastSouth;
    } else if ((heading > 156) && (heading < 204)) {
        return Maneuver::CardinalDirection::kSouth;
    } else if ((heading > 203) && (heading < 247)) {
        return Maneuver::CardinalDirection::kSouthWest;
    } else if ((heading > 246) && (heading < 294)) {
        return Maneuver::CardinalDirection::kWest;
    } else if ((heading > 293) && (heading < 337)) {
        return Maneuver::CardinalDirection::kNorthWest;
    } else {
        // impossible case
        GUIDE_ASSERT(false);
        return Maneuver::CardinalDirection::kEast;
    }
}

Maneuver::RelativeDirection DetermineRelativeDirection(uint32_t turn_degree) {
    // TODO: may be modify later by product define
    if ((turn_degree > 329) || (turn_degree < 31)) { // [-30, +30]
        return Maneuver::RelativeDirection::kKeepStraight;
    } else if ((turn_degree > 30) && (turn_degree < 160)) {
        return Maneuver::RelativeDirection::kRight;
    } else if ((turn_degree > 159) && (turn_degree < 201)) { // [-20, +20]
        return Maneuver::RelativeDirection::KReverse;
    } else if ((turn_degree > 200) && (turn_degree < 330)) {
        return Maneuver::RelativeDirection::kLeft;
    } else {
        GUIDE_ASSERT(false);
        return Maneuver::RelativeDirection::kNone;
    }
}

void PointProject2Polyline(const PointLL &pt, const std::vector<PointLL> &geo, double &proj_dis, PointLL& proj_pt, int32_t& proj_index) {
    // Initialize with a large value
    proj_dis = std::numeric_limits<double>::max();
    
    // Handle edge cases
    if (geo.size() < 2) {
        if (geo.size() == 1) {
            // If only one point, calculate direct distance
            proj_pt = geo[0];
            proj_dis = pt.Distance(geo[0]);
        }
        return;
    }
    
    // Check each segment of the edge
    for (size_t i = 0; i < geo.size() - 1; i++) {
        // Create line segment
        aurora::LineSegment2<PointLL> segment(geo[i], geo[i+1]);
        
        // Calculate closest point and distance
        PointLL closest;
        double dist = segment.Distance(pt, closest);
        
        // Update if this is closer than previous best
        if (dist < proj_dis) {
            proj_dis = dist;
            proj_pt = closest;
            proj_index = i;
        }
    }
}

float CalcPolylineDistance(const std::vector<PointLL> &geo) {
    int32_t num = geo.size();
    if (num <= 1) {
        GUIDE_ASSERT(num > 1);
        return 0;
    }

    float distance = 0;
    for (int32_t idx = 0; (idx + 1) < geo.size(); ++idx) {
        distance += geo[idx].Distance(geo[idx + 1]);
    }
    return distance;
}

}  // namespace geo
}  // namespace guide
}  // namespace aurora
/* EOF */
