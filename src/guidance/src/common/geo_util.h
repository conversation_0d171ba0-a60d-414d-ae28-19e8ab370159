#ifndef MAP_SRC_GUIDE_SRC_COMMOM_GEO_UTIL_H
#define MAP_SRC_GUIDE_SRC_COMMOM_GEO_UTIL_H

#include <cstdint>
#include <vector>
#include "pointll.h"
#include "guidance/src/common/maneuver.h"

namespace aurora {
namespace guide {

namespace geo {
/**
 * @brief 计算道路Heading应该使用的道路长度偏移
 */
float GetOffsetForHeading(int road_class);

/**
 * @brief 计算顺时针方向转向角度从FromHeading到ToHeading
 * @param from_heading: heading at the end of the "from" edge.
 * @param to_heading: heading at the begin of the "to" edge.
 * @return 转向角度 (degree)
 */
int32_t CalcTurnDegree360(int32_t from_heading, int32_t to_heading);


int32_t CalcTurnDegree180(int32_t from_heading, int32_t to_heading);

bool IsSimilarTurnDegree(uint32_t path_turn_degree,
                        uint32_t intersecting_turn_degree,
                        bool is_right,
                        uint32_t turn_degree_threshold = 40);


bool IsRelativeStraight(uint32_t turn_degree);
bool IsForward(uint32_t turn_degree);
bool IsWideForward(uint32_t turn_degree);
bool IsForkForward(uint32_t turn_degree);

Maneuver::CardinalDirection DetermineCardinalDirection(uint32_t heading);
Maneuver::RelativeDirection DetermineRelativeDirection(uint32_t turn_degree);

void PointProject2Polyline(const PointLL &pt, const std::vector<PointLL> &geo, double &proj_dis, PointLL& proj_pt, int32_t& proj_index);

float CalcPolylineDistance(const std::vector<PointLL> &geo);

}  // namespace geo
}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_COMMOM_GEO_UTIL_H
/* EOF */
