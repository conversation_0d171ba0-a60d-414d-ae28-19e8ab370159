#include "guidance/src/common/maneuver.h"
#include <map>

#include "guidance/src/common/common_def.h"


namespace aurora {
namespace guide {

Maneuver::Maneuver() {
    type_ = ManeuverType::kTypeNone;
    aux_type_ = AuxManeuverType::kAuxTypeNone;

    length_meter_ = 0;
    length_meter_to_enode_ = 0;
    basic_time_sec_ = 0;
    ramp = 0;
    turn_channel = 0;
    round_about = 0;
    round_about_exit_count_ = 0;
    street_intersection = 0;
    portion_toll = 0;
    turn_restriction = 0;
    portion_unpaved = 0;
    portion_highway = 0;
    fork = 0;
    intersecting_forward_edge = 0;
    tee = 0;


    internal_left_turn_count_ = 0;
    internal_right_turn_count_ = 0;
    begin_intersecting_xedge_name_consistency = 0;

    contains_obvious_maneuver = 0;
    turn_degree_ = 0;

    has_left_out_xedge = 0;
    has_right_out_xedge = 0;
    to_stay_on = 0;
    has_collapsed_small_end_ramp_fork = 0;

    street_names_ = std::make_shared<StreetNames>();
    begin_street_names_ = std::make_shared<StreetNames>();
    cross_street_names_ = std::make_shared<StreetNames>();

    include_verbal_pre_transition_length = 0;

    distant_verbal_multi_cue_ = 0;
    imminent_verbal_multi_cue_ = 0;

    instruction_func_ = nullptr;
    verbal_pre_instruction_func_ = nullptr;
    verbal_post_instruction_func_ = nullptr;
    verbal_alert_instruction_func_ = nullptr;
    verbal_succinct_instruction_func_ = nullptr;
}

void Maneuver::SetType(ManeuverType type) {
    type_ = type;
}

void Maneuver::SetAuxType(AuxManeuverType aux_type) {
    aux_type_ = aux_type;
}

void Maneuver::SetLengthMeter(float meter) {
    length_meter_ = meter;
    length_meter_to_enode_ = length_meter_;
}

void Maneuver::SetLengthMeterToENode(float meter) {
    GUIDE_ASSERT(meter >= 0);
    length_meter_to_enode_ = meter;
}

void Maneuver::SetBeginHeading(uint16_t begin_heading) {
    begin_heading_ = begin_heading;
}
    
void Maneuver::SetEndHeading(uint16_t end_heading) {
    end_heading_ = end_heading;
}
    
void Maneuver::SetTurnDegree(uint16_t turn_degree) {
    turn_degree_ = turn_degree;
}

void Maneuver::SetBasicTime(float sec) {
    basic_time_sec_ = sec;
}

void Maneuver::SetBeginNodeIndex(int32_t node_idx) {
    begin_node_idx_ = node_idx;
}

void Maneuver::SetEndNodeIndex(int32_t node_idx) {
    end_node_idx_ = node_idx;
}

void Maneuver::SetRamp(bool is_ramp) {
    ramp = (is_ramp ? 1:0);
}

void Maneuver::SetTurnChannel(bool turn) {
    turn_channel = (turn ? 1:0);
}

void Maneuver::SetRoundAbout(bool flag) {
    round_about = (flag ? 1:0); 
}

void Maneuver::SetRoundAboutExitCount(uint8_t count) {
    round_about_exit_count_ = count;
}

void Maneuver::SetStreetIntersection(bool flag) {
    street_intersection= (flag ? 1:0);
}

void Maneuver::SetDriveOnRight(bool flag) {
    drive_right = (flag ? 1:0);
}

void Maneuver::SetPortionToll(bool flag) {
    portion_toll = (flag ? 1:0);
}

void Maneuver::SetTurnRestriction(bool flag) {
    turn_restriction = (flag ? 1:0);
}

void Maneuver::SetPortionUnpaved(bool flag) {
    portion_unpaved = (flag ? 1:0);
}

void Maneuver::SetPortionHighway(bool flag) {
    portion_highway = (flag ? 1:0);
}

void Maneuver::SetFork(bool flag) {
    fork = (flag ? 1:0);
}

void Maneuver::SetTee(bool flag) {
    tee = (flag ? 1:0);
}

void Maneuver::SetIntersectingForwardEdge(bool flag) {
    intersecting_forward_edge = flag;
}

void Maneuver::SetVerbalFormatter(std::shared_ptr<VerbalTextFormatter> formatter) {
    formatter_ = formatter;
}

void Maneuver::SetInternalLeftTurnCount(int32_t count) {
    internal_left_turn_count_ = count;
}

void Maneuver::SetInternalRightTurnCount(int32_t count) {
    internal_right_turn_count_ = count;
}

void Maneuver::SetBeginCardinalDirection(CardinalDirection begin_cardinal_dir) {
    begin_cardinal_direction_ = begin_cardinal_dir;
}

void Maneuver::SetBeginRelativeDirection(RelativeDirection begin_relative_direction) {
    begin_relative_direction_ = begin_relative_direction;
}

void Maneuver::SetBeginIntersectingEdgeNameConsistency(bool consistency) {
    begin_intersecting_xedge_name_consistency = (consistency ? 1:0);   
}

void Maneuver::SetMergeToRelativeDirection(RelativeDirection merge_to_relative_direction) {
    merge_to_relative_direction_ = merge_to_relative_direction;
}

void Maneuver::SetContainObviousManeuver(bool flag) {
    contains_obvious_maneuver = flag;
}

void Maneuver::SetPoints(const std::vector<PointXY<double>> &points) {
    geos_ = points;
}

void Maneuver::SetPoints(std::vector<PointXY<double>> &&points) {
    geos_ = std::move(points);
}


void Maneuver::SetHasRightOutXEdge(bool flag) {
    has_right_out_xedge = (flag ? 1:0);
}

void Maneuver::SetHasLeftOutXEdge(bool flag) {
    has_left_out_xedge = (flag ? 1:0);
}

void Maneuver::SetToStayOn(bool flag) {
    to_stay_on = (flag ? 1:0);
}

void Maneuver::SetHasCollapsedSmallEndRampFork(bool flag) {
    has_collapsed_small_end_ramp_fork = flag;
}

void Maneuver::SetSignPosts(std::vector<EnhanceSignPostPtr> &&sign_posts) {
    sign_posts_ = std::move(sign_posts);
}

void Maneuver::SetInstruction(std::string &&instruction) {
    instruction_ = std::move(instruction);
}

void Maneuver::SetVerbalAlertInstruction(std::string &&instruction) {
    verbal_alert_instruction_ = std::move(instruction);
}

void Maneuver::SetVerbalPreInstruction(std::string &&instruction) {
    verbal_pre_instruction_ = std::move(instruction);
}

void Maneuver::SetVerbalPostInstruction(std::string &&instruction) {
    verbal_post_instruction_ = std::move(instruction);
}

void Maneuver::SetVerbalSuccinctInstruction(std::string &&instruction) {
    verbal_succinct_instruction_ = std::move(instruction);
}

void Maneuver::SetInstructionUpdator(VerbalTextUpdator &&instruction_func) {
    instruction_func_ = std::move(instruction_func);
}

void Maneuver::SetVerbalAlertInstructionUpdator(VerbalTextUpdator &&alert_func) {
    verbal_alert_instruction_func_ = std::move(alert_func);
}

void Maneuver::SetVerbalPreInstructionUpdator(VerbalTextUpdator &&pre_func) {
    verbal_pre_instruction_func_ = std::move(pre_func);
}

void Maneuver::SetVerbalPostInstructionUpdator(VerbalTextUpdator &&post_func) {
    verbal_post_instruction_func_ = std::move(post_func);
}

void Maneuver::SetVerbalSuccinctInstructionUpdator(VerbalTextUpdator &&succi_func) {
    verbal_succinct_instruction_func_ = std::move(succi_func);
}

ManeuverType Maneuver::GetType() const {
    return type_;
}
    
AuxManeuverType Maneuver::GetAuxType() const {
    return aux_type_;
}
    
float Maneuver::GetLengthMeter() const {
    return length_meter_;
}

float Maneuver::GetLengthMiles() const {
    static double rator = kKmPerMeter * kMilePerKm;
    return length_meter_ * rator;
}

float Maneuver::GetLengthKilometers() const {
    return length_meter_ * kKmPerMeter;
}

float Maneuver::GetLengthMeterToENode() const {
    return length_meter_to_enode_;
}

float Maneuver::GetLengthMilesToENode() const {
    static double rator = kKmPerMeter * kMilePerKm;
    return length_meter_to_enode_ * rator;
}

float Maneuver::GetLengthKilometersToENode() const {
    return length_meter_to_enode_ * kKmPerMeter;
}

float Maneuver::GetBasicTime() const {
    return basic_time_sec_;
}

uint16_t Maneuver::GetBeginHeading() const {
    return begin_heading_;
}

uint16_t Maneuver::GetEndHeading() const {
    return end_heading_;
}

uint16_t  Maneuver::GetTurnDegree() const {
    return turn_degree_;
}

int32_t Maneuver::GetBeginNodeIndex() const {
    return begin_node_idx_;
}

int32_t Maneuver::GetEndNodeIndex() const {
    return end_node_idx_;
}

const VerbalTextFormatter* Maneuver::GetVerbalFormatter() const {
    return formatter_.get();
}

bool Maneuver::GetRamp() const {
    return (ramp > 0);
}

bool Maneuver::GetTurnChannel() const {
    return (turn_channel > 0);
}

bool Maneuver::GetRoundAbout() const {
    return round_about;
}

uint8_t Maneuver::GetRoundAboutExitCount() const {
    return round_about_exit_count_;
}

bool Maneuver::GetStreetIntersection() const {
    return street_intersection;
}

bool Maneuver::GetDriveOnRight() const {
    return drive_right;
}

bool  Maneuver::GetPortionToll() const {
    return portion_toll;
}

bool Maneuver::GetTurnRestriction() const {
    return turn_restriction;
}

bool Maneuver::GetPortionUnpaved() const {
    return portion_unpaved;
}

bool Maneuver::GetPortionHighway() const {
    return portion_highway;
}

bool Maneuver::GetFork() const {
#if 1
    return fork;
#else
    // TODO: tbt
    return false;
#endif
}

bool Maneuver::GetTee() const {
    return tee;
}

bool Maneuver::GetIntersectingForwardEdge() const {
    return intersecting_forward_edge;
}

uint16_t Maneuver::GetInternalLeftTurnCount() const {
    return internal_left_turn_count_;
}

uint16_t Maneuver::GetInternalRightTurnCount() const {
    return internal_right_turn_count_;
}

Maneuver::CardinalDirection Maneuver::GetBeginCardinalDirection() const {
    return begin_cardinal_direction_;
}

Maneuver::RelativeDirection Maneuver::GetBeginRelativeDirection() const {
    return begin_relative_direction_;
}

bool Maneuver::GetBeginIntersectingEdgeNameConsistency() const {
    return begin_intersecting_xedge_name_consistency;
}

Maneuver::RelativeDirection Maneuver::GetMergeToRelativeDirection() const {
    return merge_to_relative_direction_;
}

bool Maneuver::GetContainObviousManeuver() const {
    return contains_obvious_maneuver;
}

bool Maneuver::HasInstruction() const {
    return !instruction_.empty();
}

bool Maneuver::HasVerbalAlertInstruction() const {
    return !verbal_alert_instruction_.empty();
}

bool Maneuver::HasVerbalPreInstruction() const {
    return !verbal_pre_instruction_.empty();
}

bool Maneuver::HasVerbalPostInstruction() const {
    return !verbal_post_instruction_.empty();
}

bool Maneuver::HasVerbalSuccinctInstruction() const {
    return !verbal_succinct_instruction_.empty();
}

std::string  Maneuver::GetInstruction() const {
    if (instruction_func_ != nullptr) {
        return instruction_func_(this, instruction_);
    }
    return instruction_;
}

std::string Maneuver::GetVerbalAlertInstruction() const {
    if (verbal_alert_instruction_func_ != nullptr) {
        return verbal_alert_instruction_func_(this, verbal_alert_instruction_);
    }
    return verbal_alert_instruction_;
}

std::string  Maneuver::GetVerbalPreInstruction() const {
    if (verbal_pre_instruction_func_ != nullptr) {
        return verbal_pre_instruction_func_(this, verbal_pre_instruction_);
    }
    return verbal_pre_instruction_;
}
    
std::string  Maneuver::GetVerbalPostInstruction() const {
    if (verbal_post_instruction_func_ != nullptr) {
        return verbal_post_instruction_func_(this, verbal_post_instruction_);
    }
    return verbal_post_instruction_;
}

std::string  Maneuver::GetVerbalSuccinctInstruction() const {
    if (verbal_succinct_instruction_func_ != nullptr) {
        return verbal_succinct_instruction_func_(this, verbal_succinct_instruction_);
    }
    return verbal_succinct_instruction_;
}

bool Maneuver::IsStartType() const {
    return (type_ == ManeuverType::kTypeStart ||
            type_ == ManeuverType::kTypeStartLeft ||
            type_ == ManeuverType::kTypeStartRight);
}

bool Maneuver::IsDestinationType() const {
    return (type_ == ManeuverType::kTypeDestination ||
            type_ == ManeuverType::kTypeDestinationLeft ||
            type_ == ManeuverType::kTypeDestinationRight);
}

bool Maneuver::IsMergeType() const {
    return (type_ == ManeuverType::kTypeMerge ||
            type_ == ManeuverType::kTypeMergeToLeft ||
            type_ == ManeuverType::kTypeMergeToRight);
}

bool Maneuver::IsRoundabout() const {
    return ((type_ >= ManeuverType::kTypeRoundStart) && 
            (type_ <= ManeuverType::kTypeRoundEnd));
}

bool Maneuver::HasSimilarName(const Maneuver *other_maneuver) const {
    if (other_maneuver == nullptr) {
        return false;
    }

    if (!street_names_->empty()) {
        std::shared_ptr<StreetNames> common_base_names = 
            street_names_->FindCommonBaseNames(*(other_maneuver->GetStreetNames()));
        if (!common_base_names->empty() && (common_base_names->size() == street_names_->size())) {
            return true;
        }
    }
    return false;
}

std::string Maneuver::GetCommonBaseNameUtf8(const std::string &lhs, const std::string &rhs) {
    const char *p1 = lhs.c_str();
    const char *p2 = rhs.c_str();
    size_t prefix_len = 0;
    
    while (*p1 && *p2 && *p1 == *p2) {
        // 处理多字节UTF-8字符
        if ((*p1 & 0xC0) == 0xC0) { // 检查是否是UTF-8多字节字符开头
            int len = 0;
            if ((*p1 & 0xF0) == 0xF0) len = 4;
            else if ((*p1 & 0xE0) == 0xE0) len = 3;
            else if ((*p1 & 0xC0) == 0xC0) len = 2;
            
            for (int i = 1; i < len; ++i) {
                if (p1[i] != p2[i]) {
                    return lhs.substr(0, prefix_len);
                }
            }
            p1 += len;
            p2 += len;
            prefix_len += len;
        } else {
            // ASCII字符
            p1++;
            p2++;
            prefix_len++;
        }
    }
    return lhs.substr(0, prefix_len);
}

const std::vector<PointXY<double>>& Maneuver::GetPoints() const {
    return geos_;
}

bool Maneuver::HasLeftOutXEdge() const {
    return (has_left_out_xedge > 0);
}
    
bool Maneuver::HasRightOutXEdge() const {
    return (has_right_out_xedge > 0);
}

bool Maneuver::GetToStayOn() const {
    return (to_stay_on > 0);
}

bool Maneuver::GetHasCollapsedSmallEndRampFork() const {
    return (has_collapsed_small_end_ramp_fork > 0);
}

bool Maneuver::HasLaneInfo() const {
    return (!lane_infos_.empty());
}

const std::vector<EnhanceLaneInfoPtr>& Maneuver::GetLaneInfos() const {
    return lane_infos_;
}
    
std::vector<EnhanceLaneInfoPtr>& Maneuver::GetLaneInfos() {
    return lane_infos_;
}

bool Maneuver::HasSignPost() const {
    return (!sign_posts_.empty());
}

const std::vector<EnhanceSignPostPtr>& Maneuver::GetSignPosts() const {
    return sign_posts_;
}
    
std::vector<EnhanceSignPostPtr>& Maneuver::GetSignPosts() {
    return sign_posts_;
}

bool Maneuver::HasJunctionView() const {
    return (!junction_views_.empty());
}
    
const std::vector<EnhanceJunctionViewPtr>& Maneuver::GetJunctionViews() const {
    return junction_views_;
}
    
std::vector<EnhanceJunctionViewPtr>& Maneuver::GetJunctionViews() {
    return junction_views_;
}

bool Maneuver::HasFacility() const {
    return (!facilities_.empty());
}

const std::vector<EnhanceFacilityPtr>& Maneuver::GetFacilities() const {
    return facilities_;
}
    
std::vector<EnhanceFacilityPtr>& Maneuver::GetFacilities() {
    return facilities_;
}
    
bool Maneuver::HasTollStation() const {
    return (!toll_stations_.empty());
}
    
const std::vector<EnhanceTollStationPtr>& Maneuver::GetTollStations() const {
    return toll_stations_;
}
    
std::vector<EnhanceTollStationPtr>& Maneuver::GetTollStations() {
    return toll_stations_;
}

std::shared_ptr<StreetNames> Maneuver::GetStreetNames() {
    return street_names_;
}

const std::shared_ptr<StreetNames> Maneuver::GetStreetNames() const {
    return street_names_;
}

void Maneuver::SetStreetNames(std::shared_ptr<StreetNames> street_names) {
    street_names_ = street_names;
}
    
void  Maneuver::SetStreetNames(const std::vector<std::pair<std::string, bool>>& names) {
    street_names_ = std::make_shared<StreetNames>(names);
}
    
void  Maneuver::ClearStreetNames() {
    street_names_->clear();
}

bool  Maneuver::HasStreetNames() const {
    return !street_names_->empty();
}

bool Maneuver::GetIncludeVerbalPreTransitionLength() const {
    return (include_verbal_pre_transition_length > 0);
}

std::shared_ptr<StreetNames> Maneuver::GetBeginStreetNames() {
    return begin_street_names_;
}

void Maneuver::SetBeginStreetNames(std::shared_ptr<StreetNames> begin_street_names) {
    begin_street_names_ = begin_street_names;
}

void  Maneuver::SetBeginStreetNames(const std::vector<std::pair<std::string, bool>>& begin_street_names) {
    begin_street_names_ = std::make_shared<StreetNames>(begin_street_names);
}

void  Maneuver::ClearBeginStreetNames() {
    return begin_street_names_->clear();
}

bool  Maneuver::HasBeginStreetNames() const {
    return !begin_street_names_->empty();
}

std::shared_ptr<StreetNames> Maneuver::GetCrossStreetNames() {
    return cross_street_names_;
}

void  Maneuver::SetCrossStreetNames(std::shared_ptr<StreetNames> cross_street_names) {
    cross_street_names_ = cross_street_names;
}

void  Maneuver::SetCrossStreetNames(const std::vector<std::pair<std::string, bool>>& names) {
    cross_street_names_ = std::make_shared<StreetNames>(names);
}

void  Maneuver::ClearCrossStreetNames() {
    return cross_street_names_->clear();
}

bool  Maneuver::HasCrossStreetNames() const {
    return !cross_street_names_->empty();
}

void  Maneuver::SetHasLongStreetName(bool flag) {
    has_long_street_name_ =  flag ? 1:0;
}

bool  Maneuver::GetHasLongStreetName() const {
    return (has_long_street_name_ > 0);
}

void  Maneuver::SetDistantVerbalMultiCue(bool flag) {
    distant_verbal_multi_cue_ = flag;
}

bool Maneuver::GetDistantVerbalMultiCue() {
    return (distant_verbal_multi_cue_ > 0);
}

void Maneuver::SetImminentVerbalMultiCue(bool flag) {
    imminent_verbal_multi_cue_ = flag;
}
    
bool Maneuver::GetImminentVerbalMultiCue() {
    return (imminent_verbal_multi_cue_ > 0);
}

std::string ManeuverTypeToString(ManeuverType type) {
    static std::map<ManeuverType, std::string> table = {
       {ManeuverType::kTypeNone,         "未知"},
       {ManeuverType::kTypeContinue,     "直行"},
       {ManeuverType::kTypeSlightRight,  "右前方"},
       {ManeuverType::kTypeRight,        "右转"},
       {ManeuverType::kTypeSharpRight,   "右后"},
       {ManeuverType::kTypeLeftUTurn,    "左掉头"},
       {ManeuverType::kTypeRightUTurn,   "右掉头"},
       {ManeuverType::kTypeSharpLeft,    "左后"},
       {ManeuverType::kTypeLeft,         "左转"},
       {ManeuverType::kTypeSlightLeft,   "左前方"},
       {ManeuverType::kTypeStart,        "起始位置"},
       {ManeuverType::kTypeStartLeft,    "起始位置(起点在自车左侧)"},
       {ManeuverType::kTypeStartRight,   "起始位置(起点在自车右侧)"},

       {ManeuverType::kTypeViaPoint,        "途径地"},
       {ManeuverType::kTypeViaPointLeft,    "途径地(途径点在左侧)"},
       {ManeuverType::kTypeViaPointRight,   "途径地(途径点在右侧)"},

       {ManeuverType::kTypeDestination,        "目的地"},
       {ManeuverType::kTypeDestinationLeft,    "目的地(目的地在左侧)"},
       {ManeuverType::kTypeDestinationRight,   "目的地(目的地在右侧)"},

       {ManeuverType::kTypeStayLeft,          "靠左侧行驶"},
       {ManeuverType::kTypeStayRight,         "靠右侧行驶"},
       {ManeuverType::kTypeStayStraight,      "保持直行"},

       {ManeuverType::kTypeStraightToRamp,    "直行上匝道"},
       {ManeuverType::kTypeRightToRamp,       "右转上匝道"},
       {ManeuverType::kTypeLeftToRamp,        "左转上匝道"},

       {ManeuverType::kTypeExitLeft,          "左侧出高速"},
       {ManeuverType::kTypeExitRight,         "右侧出高速"},

       {ManeuverType::kTypeMerge,             "道路合并"},
       {ManeuverType::kTypeMergeToLeft,       "向左合并"},
       {ManeuverType::kTypeMergeToRight,      "向右合并"},
    };

    if (table.count(type) > 0) {
        return table.at(type);
    }
    // TODO: cancel for tmp
    GUIDE_ASSERT(false);
    return "";
}


}  // namespace guide
}  // namespace aurora
/* EOF */
