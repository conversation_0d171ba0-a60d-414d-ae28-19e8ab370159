#ifndef MAP_SRC_GUIDE_SRC_DATA_DIRECT_EDGE_H
#define MAP_SRC_GUIDE_SRC_DATA_DIRECT_EDGE_H

#include <cstdint>
#include <memory>

#include "pointll.h"
#include "guidance/src/common/common_def.h"
#include "guidance/src/data/enhance_data_provider.h"
#include "guidance/src/common/map_data_util.h"
#include "guidance/src/data/enhance_lane_info.h"
#include "guidance/src/data/enhance_facility.h"

#include "guidance/src/common/street_names.h"

namespace aurora {
namespace guide {

class DirectedEdge {
public:
    DirectedEdge();
    explicit DirectedEdge(const RouteEdgeId &edge_id, uint8_t drive_dir, EnhanceDataProvider *provider);
    
    void BuildFacilities(EnhanceDataProvider *provider, float start_offset, float end_offset);
    void BuildLaneInfos(EnhanceDataProvider *provider, DirectEdgeId *to_edge_ids, int32_t to_edge_num, const std::set<DirectEdgeId> &edge_index);

    bool IsValid() const {return valid_;}
    DirectEdgeId GetDirectEdgeId() const;
    bool IsOneWay() const { return (is_one_way > 0);}
    RouteTileId GetTileId() const {return edge_id_.tile_id;}
    uint32_t GetIndexInTile() const {return edge_id_.feature_id;}
    uint32_t GetStartNodeIdInTile() const {return start_node_id_;}
    uint32_t GetEndNodeIdInTile() const { return end_node_id_;}

    const std::vector<PointLL>& GetGeoPoints() const {return geo_;}

    bool IsRamp() const { return is_ramp_;}
    bool IsJct() const { return MapDataUtil::IsJCT(edge_form_);};
    bool IsIC() const {return MapDataUtil::IsIC(edge_form_);}
    bool IsTurnChannel() const;
    bool IsToll() const {return need_toll_;}
#if 0
    bool HasTurnRestriction() const {return has_turn_rule_;}
#else
    bool HasTurnRestriction() const {return (is_limit_in_edge_ | is_limit_out_edge_) > 0;}
#endif
    bool IsInnerEdge() const { return is_inner_edge_;}
    bool IsRoundAbout() const { return edge_form_ == parser::EdgeForm::kEdgeFormRoundabout;}
    bool IsUnpaved() const {return is_paved_ == 0;}
    bool IsHighway() const;
    bool HasSign() const;
    int8_t GetRoadClass() const {return road_class_;}

    void SetBeginHeading(int16_t heading);
    void SetEndHeading(int16_t heading);
    void SetLaneInfo(EnhanceLaneInfoPtr lane_info) { lane_info_ = lane_info;}
    int16_t GetBeginHeading() const { return begin_heading_;}
    int16_t GetEndHeading() const { return end_heading_; }
    bool DriveOnRight() const {return !is_left_;}

    float GetLengthMeter() const;
    float GetLength(DistaneUnit unit = DistaneUnit::kKilometer) const;
    float GetDefaultSpeed() const; // m/s
    float GetBasicTime() const; // sec

    uint32_t GetLaneNum() const { return lane_count_;}
    uint32_t GetForwardLaneNum() const { return forward_lane_count_;}
    uint32_t GetBackwardLaneNum() const { return backward_lane_count_;}

    bool HasActiveTurnLane() const {return false;}
    bool HasNonDirectionalTurnLane() const {return false;}
    bool HasTurnLane(uint16_t turn_lane_direction) const {return false;}
    EnhanceLaneInfoPtr GetLaneInfo() const { return lane_info_;}

    uint32_t GetFacilityNum() const;
    EnhanceFacilityPtr GetFacility(int32_t index) const;
    const std::vector<EnhanceFacilityPtr> GetFacilities() const;

    // std::string GetRoadName() const { return road_name_;}
    // std::string GetRoadNo() const { return road_no_;}

    const std::shared_ptr<StreetNames> GetStreetNames() const { return street_names_; }
    std::shared_ptr<StreetNames> GetStreetNames() { return street_names_; }

    bool IsStraightest(uint32_t prev2cur_turn_degree, uint32_t straight_xedge_turn_degree) const;

    uint8_t GetEdgeType() const { return edge_type_;}
    uint8_t GetEdgeForm() const { return edge_form_;}
    bool    IsAreaEdge() const {return (is_area_edge_ > 0);}
    bool    IsCityEdge() const {return (is_city_edge_ > 0);}

    bool    CutHead(float offset_m);
    bool    CutTail(float offset_m);
    void    RefreshLength();

protected:
    void BuildGraphEdge(EnhanceDataProvider *provider);

protected:
    RouteEdgeId edge_id_;

    uint8_t valid_      :1;
    uint8_t drive_dir_  :3;
    uint8_t is_one_way  :1; 
    uint8_t reverse2    :3;
    
    uint8_t function_class_ : 3;  ///< Road function class
    uint8_t edge_type_ : 2;       ///< Edge type (0-3)
    uint8_t direction_ : 2;       ///< Direction type (0-3)
    uint8_t need_toll_ : 1;       ///< Flag indicating if the edge requires toll payment (0/1)

    uint32_t start_node_id_ : 24;        ///< Start node ID
    uint32_t positive_speed_limit_ : 5;  ///< Positive direction speed limit
    uint32_t is_overhead_ : 1;           ///< Flag indicating if edge is overhead structure (0/1)
    uint32_t is_inner_edge_ : 1;         ///< Flag indicating if edge is part of internal network (0/1)
    uint32_t is_separate_ : 1;  ///< Flag indicating if edge is separated from other lanes (0/1)

    uint32_t end_node_id_ : 24;         ///< End node ID
    uint32_t negtive_speed_limit_ : 5;  ///< Reverse direction speed limit
    uint8_t is_area_edge_ : 1;          ///< Flag indicating if edge is part of area boundary (0/1)
    uint8_t is_city_edge_ : 1;          ///< Flag indicating if edge is within city limits (0/1)
    uint8_t is_ramp_ : 1;               ///< Flag indicating if edge is a ramp (0/1)

    uint8_t edge_form_ : 5;    ///< Edge geometry type
    uint8_t speed_grade_ : 3;  ///< Speed grade
    float length_;          ///< Edge length in meters

    uint8_t forward_lane_count_ : 2;   ///< Forward direction lanes
    uint8_t backward_lane_count_ : 2;  ///< Backward direction lanes
    uint8_t lane_count_ : 4;           ///< Total lanes
    

    uint8_t road_class_ : 4;        ///< Road hierarchy class
    uint8_t is_left_ : 1;           ///< Flag indicating left-hand traffic (0/1)
    uint8_t is_limit_in_edge_ : 1;     ///< Flag indicating presence of turn restrictions (0/1)
    uint8_t is_limit_out_edge_ : 1;     ///< Flag indicating time-based restrictions (0/1)
    uint8_t not_used1_ : 1;  ///< Flag indicating 24/7 restrictions (0/1)

    uint8_t is_building_ : 1;  ///< Flag indicating edge is adjacent to buildings (0/1)
    uint8_t is_paved_ : 1;     ///< Flag indicating surface is paved (0/1)
    uint8_t is_gate_ : 1;      ///< Flag indicating presence of controlled gate (0/1)
    uint8_t no_crossing_ : 1;  ///< Flag indicating no pedestrian crossing (0/1)
    uint8_t is_private_ : 1;   ///< Flag indicating private access only (0/1)
    uint8_t not_used2_ : 3;     ///< Reserved field (unused bits)
    
    float   speed_;
    // std::string road_name_;
    // std::string road_no_;

    std::shared_ptr<StreetNames> street_names_;

    int16_t begin_heading_;
    int16_t end_heading_;
    EnhanceLaneInfoPtr lane_info_;
    std::vector<EnhanceFacilityPtr> facilities_;
    //std::vector<PointLL<double>> geo_;
    std::vector<PointLL> geo_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_DIRECT_EDGE_H
/* EOF */
