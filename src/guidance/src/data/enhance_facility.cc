#include "guidance/src/data/enhance_facility.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/data/facility_type_wrapper.h"

namespace aurora {
namespace guide {

EnhanceFacility::EnhanceFacility(FacilityInfo *info, const DirectEdgeId &edge_id, float cut_head) {
    edge_id_ = edge_id;
    GUIDE_ASSERT(info != nullptr);
    if (info != nullptr) {
        BuildFacility(info, cut_head);
    } else {
        GUIDE_LOG_ERROR("info is nullptr .");
    }
}

FacilityType EnhanceFacility::GetType() const {
    return type_;
}

NavFacilityType EnhanceFacility::GetNavType() const {
    return FacilityTypeWrapper::Convert(type_);
}

PointLL EnhanceFacility::GetPosition() const {
    return pos_;
}
    
float  EnhanceFacility::GetOffset() const {
    return offset_;
}
    
int16_t EnhanceFacility::GetHeading() const {
    return heading_;
}

uint8_t EnhanceFacility::GetLimitSpeed() const {
    return limit_speed_;
}
    
DirectEdgeId EnhanceFacility::GetEdgeId() const {
    return edge_id_;
}

std::string EnhanceFacility::GetId() const {
    return edge_id_.ToString() + "_" + std::to_string(offset_) + "_" + std::to_string(static_cast<uint32_t>(type_));
}

void EnhanceFacility::BuildFacility(FacilityInfo *info, float cut_head) {
    auto base_info = info->GetBaseInfo();
    GUIDE_ASSERT(base_info != nullptr);

    GUIDE_ASSERT(base_info->in_edge_id == edge_id_.edge_id.feature_id);
    GUIDE_ASSERT(!(base_info->in_edge_dir) == edge_id_.forward);

    if (base_info == nullptr) {
        GUIDE_ASSERT("Facility base info is nullptr .");
    } else {
        type_ = static_cast<FacilityType>(base_info->type);
        limit_speed_ = base_info->speed_limit;
        offset_ = base_info->distance_to_start - cut_head;

        if (offset_ < 0) {
            offset_ = 0;
            GUIDE_ASSERT(false);
            GUIDE_LOG_ERROR("BuildFacility  offset is less than 0. cut_head={}", cut_head);
        }

        GUIDE_ASSERT(edge_id_.forward == (!base_info->in_edge_dir));
        GUIDE_ASSERT(edge_id_.edge_id.feature_id == base_info->in_edge_id);
    }

    heading_ = info->GetHeading();
    pos_.first = info->GetPosition().first;
    pos_.second = info->GetPosition().second;
}

}  // namespace guide
}  // namespace aurora