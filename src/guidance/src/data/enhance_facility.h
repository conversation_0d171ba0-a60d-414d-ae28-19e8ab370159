#ifndef MAP_SRC_GUIDE_SRC_DATA_ENHANCE_FACILITY_H
#define MAP_SRC_GUIDE_SRC_DATA_ENHANCE_FACILITY_H

#include <cstdint>
#include <memory>
#include "pointll.h"
#include "guidance_def.h"
#include "route_data/route_data_def.h"
#include "guidance/src/data/enhance_data_provider.h"

namespace aurora {
namespace guide {

class EnhanceFacility {
public:
    EnhanceFacility(FacilityInfo *info, const DirectEdgeId &edge_id, float cut_head);

    FacilityType GetType() const;
    NavFacilityType GetNavType() const;
    PointLL GetPosition() const;
    float   GetOffset() const;
    int16_t GetHeading() const;
    DirectEdgeId GetEdgeId() const;

    uint8_t GetLimitSpeed() const;

    std::string GetId() const;

protected:
    void BuildFacility(FacilityInfo *info, float cut_head);

protected:
    FacilityType type_;
    PointLL pos_;
    float   offset_;
    int16_t heading_;
    uint8_t limit_speed_;   // 0表示无限速
    DirectEdgeId edge_id_;
}; // class

using EnhanceFacilityPtr = std::shared_ptr<EnhanceFacility>;

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_ENHANCE_FACILITY_H
/* EOF */
