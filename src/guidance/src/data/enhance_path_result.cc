#include "guidance/src/data/enhance_path_result.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

EnhancePathQuery::EnhancePathQuery(PathQueryPtr ptr) 
: ptr_(ptr) {

}

PathMode EnhancePathQuery::GetMode() const {
    return ptr_->mode;
}
    
PathTrigger EnhancePathQuery::GetTrigger() const {
    return ptr_->trigger;
}
    
uint32_t EnhancePathQuery::GetLandmarkNum() const {
    return ptr_->path_points.size();
}
    
PathLandMarkPtr EnhancePathQuery::GetLandMark(int32_t index) const {
    if (ptr_ != nullptr && index < ptr_->path_points.size()) {
        return ptr_->path_points.at(index);
    }
    return nullptr;
}

PathLandMarkPtr EnhancePathQuery::GetStart() const {
    if (ptr_ != nullptr && ptr_->path_points.size() > 1) {
        return ptr_->path_points.at(0);
    }
    return nullptr;
}

PathLandMarkPtr  EnhancePathQuery::GetDestination() const {
    if (ptr_ != nullptr && ptr_->path_points.size() > 1) {
        return ptr_->path_points.back();
    }
    return nullptr;
}

uint32_t EnhancePathQuery::GetWayPointNum() const {
    if (ptr_ != nullptr && ptr_->path_points.size() > 1) {
        return ptr_->path_points.size() - 2U;
    }
    return 0U;
}
    
PathLandMarkPtr EnhancePathQuery::GetWayPoint(int32_t index) const {
    return GetLandMark(index + 1);
}


EnhancePathResult::EnhancePathResult(EnhanceDataProvider *provider)
: provider_(provider) {
    total_length_  = 0;
}

bool EnhancePathResult::Build(EnhancePathQueryPtr query_ptr, const path::PathInfo &path) {
    if (provider_ == nullptr) {
        GUIDE_LOG_ERROR("provider is nullptr");
        return false;
    }

    query_ = query_ptr;

    // build section
    if (!BuildPathSections(path)) {
        GUIDE_LOG_ERROR("BuildPathSections error .");
        return false;
    }

    if (sections_.size() != 1U) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("current not support via point ");
        return false;
    }

    nodes_.clear();

    path_id_ = path.path_id;

    provider_->SetCacheOption(true);

    // build edge
    uint32_t idx = 0;
    DirectEdgeId prev_direct_id;
    for (const path::EdgeInfoPtr edge : path.links) {
        DirectedEdge direct_edge(RouteEdgeId(edge->tile_id, edge->id), 
                        edge->forward ? EdgeDirection::kEdgeDirectionForwardPass : EdgeDirection::kEdgeDirectionReversePass, provider_);

        DirectEdgeId next_non_inner_direct_id;
        bool ret = GetNextNonInnerDirectEdgeId(path, idx, next_non_inner_direct_id);
        RouteNodeId route_node_id(edge->tile_id, direct_edge.GetStartNodeIdInTile());
        nodes_.emplace_back(route_node_id, provider_, 
                            (ret ? &next_non_inner_direct_id : nullptr),
                            (idx == 0) ? nullptr : &prev_direct_id);

        nodes_.back().SetDirectEdge(std::move(direct_edge));
        
        prev_direct_id.edge_id.tile_id = edge->tile_id;
        prev_direct_id.edge_id.feature_id = edge->id;
        prev_direct_id.forward = edge->forward;

        ++idx;
    }

    // final node
    path::EdgeInfoPtr final_edge = path.links.back();
    RouteEdgeId final_edge_id(final_edge->tile_id, final_edge->id);
    TopolEdge *final_topo_edge = provider_->GetTopoEdge(final_edge_id);
    GUIDE_ASSERT(final_topo_edge != nullptr);

    if (final_topo_edge != nullptr) {
        uint32_t final_node_id = final_topo_edge->GetBaseInfo()->end_node_id;
        if (final_edge->direction == parser::EdgeDirection::kEdgeDirectionReversePass) {
            final_node_id = final_topo_edge->GetBaseInfo()->start_node_id;
        }

        RouteNodeId route_node_id(final_edge->tile_id, final_node_id);
        nodes_.emplace_back(route_node_id, provider_, nullptr, &prev_direct_id);
    }

    AdjustEndianEdgeLengthAndGeo();
    for (int32_t index = 1; (index + 1) < GetEdgeNum(); ++index) {
        GetEdge(index)->RefreshLength();
    }

    int32_t node_num = nodes_.size();

    // update length
    total_length_ = 0;
    for (int32_t node_idx = 0; (node_idx + 1) < node_num; ++node_idx) {
        total_length_ += GetEdge(node_idx)->GetLength();
    }

    UpdateLaneInfoAndFacility();
    UpdateJunctionAndTollStation();
    ProcessNameConsistency();
   
    provider_->SetCacheOption(false);
    return true;
}

bool EnhancePathResult::UpdateLaneInfoAndFacility() {
    // update laneinfo & facility
    int32_t node_num = nodes_.size();
    if (node_num <= 0) {
        return false;
    }

    std::vector<DirectEdgeId> edge_ids(node_num - 1);
    std::set<DirectEdgeId> edge_id_index;
    for (int32_t node_index = 0; (node_index + 1) < node_num; ++node_index) {
        edge_ids[node_index] = GetCurrentEdge(node_index)->GetDirectEdgeId();
        edge_id_index.insert(edge_ids[node_index]);
    }

    for (int32_t node_index = 0; (node_index + 1) < node_num; ++node_index) {
        DirectedEdge *edge = GetCurrentEdge(node_index);
        int32_t to_edge_num = 8;
        if (node_index + to_edge_num > node_num) {
            to_edge_num = node_num - node_index;
        }
        edge->BuildLaneInfos(provider_, edge_ids.data() + node_index, to_edge_num, edge_id_index);
        
        float start_offst = 0;
        float end_offset = edge->GetLengthMeter();

        if ((node_index == 0) && (node_index + 2) == node_num) { // only one link
            start_offst = sections_.front().GetStartOffset();
            end_offset = edge->GetLengthMeter() + start_offst;

        } else if (node_index == 0) {
            start_offst = sections_.front().GetStartOffset();
        } else if ((node_index + 2) == node_num) {
            // do nothing
        }
        edge->BuildFacilities(provider_, start_offst, end_offset);
    }
    return true;
}

bool EnhancePathResult::UpdateJunctionAndTollStation() {
    // update sign posts && junction views
    int32_t node_num = nodes_.size();
    if (node_num <= 0) {
        return false;
    }

    std::vector<DirectEdgeId> edge_ids(node_num - 1);
    for (int32_t node_index = 0; (node_index + 1) < node_num; ++node_index) {
        edge_ids[node_index] = GetCurrentEdge(node_index)->GetDirectEdgeId();
    }

    for (int32_t node_index = 1; (node_index + 1) < node_num; ++node_index) {
        int32_t to_edge_num = 8;
        if (node_index + to_edge_num > node_num) {
            to_edge_num = node_num - node_index;
        }

        DirectEdgeId from_edge_id = GetPrevEdge(node_index)->GetDirectEdgeId();
        DirectEdgeId to_edge_id = GetCurrentEdge(node_index)->GetDirectEdgeId();
        nodes_[node_index].UpdateSignPosts(provider_, from_edge_id, to_edge_id);

        nodes_[node_index].UpdateJunctionViews(provider_, from_edge_id, edge_ids.data() + node_index, to_edge_num);
        nodes_[node_index].UpdateTollStations(provider_, from_edge_id, to_edge_id);
    }

    return true;
}

bool EnhancePathResult::AdjustEndianEdgeLengthAndGeo() {
    if (sections_.empty()) {
        return false;
    }

    int32_t head_edge_idx = sections_.front().GetStartEdgeIndex();
    float   head_offset = sections_.front().GetStartOffset();

    int32_t tail_edge_idx = sections_.back().GetStartEdgeIndex() + sections_.back().GetEdgeNum() - 1;
    float   tail_offset = sections_.back().GetEndOffset();

    if (head_edge_idx != tail_edge_idx) {
        DirectedEdge *head_edgde = GetEdge(head_edge_idx);
        DirectedEdge *tail_edge = GetEdge(tail_edge_idx);
        
        GUIDE_ASSERT(head_offset > 0);
        if (head_offset > 0) {
            head_edgde->CutHead(head_offset);
        }

        float tail_cut_len = tail_edge->GetLengthMeter() - tail_offset;
        GUIDE_ASSERT(tail_cut_len > 0);
        if (tail_cut_len > 0) {
            tail_edge->CutTail(tail_cut_len);
        }
    }

    if (head_edge_idx == tail_edge_idx) {
        DirectedEdge *edge = GetEdge(head_edge_idx);

        float tail_cut_len = edge->GetLengthMeter() - tail_offset;
        GUIDE_ASSERT(tail_cut_len > 0);
        if (tail_cut_len > 0) {
            edge->CutTail(tail_cut_len);
        }

        if (head_offset > 0) {
            edge->CutHead(head_offset);
        }
    }

    return true;
}

bool EnhancePathResult::ProcessNameConsistency() {
    // update name consistency
    for (uint32_t index = 0; index < nodes_.size(); ++index) {
        if (IsFirstNodeIndex(index)) {
            continue;
        }

        EnhanceNode *node = GetNode(index);
        if (node->HasOutIntersectionEdge()) {
            DirectedEdge *prev_edge = GetPrevEdge(index);
            if ((prev_edge != nullptr) && !(prev_edge->GetStreetNames()->empty())) {
                node->UpdateXEdgePrevNameConsistency(prev_edge->GetStreetNames());
            }
        }

        if (node->HasInIntersectionEdge()) {
            DirectedEdge *curr_edge = GetCurrentEdge(index);
            if ((curr_edge != nullptr) && !(curr_edge->GetStreetNames()->empty())) {
                node->UpdateXEdgeCurrNameConsistency(curr_edge->GetStreetNames());
            }
        }
    }
    return true;
}

double EnhancePathResult::GetTotalLength() const {
    return total_length_;
}

bool EnhancePathResult::GetNextNonInnerDirectEdgeId(const path::PathInfo &path, int32_t start_edge_idx, DirectEdgeId &direct_id) {
    
    for (int idx = start_edge_idx; idx < path.links.size(); ++idx) {
        if (!path.links[idx]->is_inner_link) {
            direct_id.edge_id.tile_id = path.links[idx]->tile_id;
            direct_id.edge_id.feature_id = path.links[idx]->id;
            direct_id.forward = (path.links[idx]->forward);
            return true;
        }
    }
    return false;
}

bool EnhancePathResult::BuildPathSections(const path::PathInfo &path) {
    auto& sections = path.sections;

    sections_.clear();
    sections_.reserve(sections.size());
    for (uint32_t index = 0; index < sections.size(); ++index) {
        sections_.emplace_back(index, sections[index]);
    }

    return true;
}

const std::string& EnhancePathResult::GetCountryCode() const {
    // TODO:
    return country_code_;
}
    
const std::string& EnhancePathResult::GetStateCode() const {
    // TODO:
    return state_code_;
}

uint64_t EnhancePathResult::GetPathId() const {
    return path_id_;
}

int32_t  EnhancePathResult::GetEdgeNum() const {
    if (!nodes_.empty()) {
        return (nodes_.size() -1U);
    }
    return 0;
}
    
int32_t  EnhancePathResult::GetNodeNum() const {
    return nodes_.size();
}
    
int32_t  EnhancePathResult::GetSectionNum() const {
    return sections_.size();
}

EnhancePathQueryPtr EnhancePathResult::GetEnhancePathQuery() const {
    return query_;
}

PathSectionInfo* EnhancePathResult::GetSectionInfo(int32_t index)  const {
    if (index < sections_.size()) {
        return &(sections_[index]);
    }
    GUIDE_ASSERT(false);
    return nullptr;
}

DirectedEdge* EnhancePathResult::GetEdge(int32_t index) const {
    if ((index + 1) < nodes_.size() ) {
        auto edge = nodes_[index].GetDirectEdge();
        if (edge->IsValid()) {
            return edge;
        }
    }
    GUIDE_ASSERT(false);
    return nullptr;
}

EnhanceNode* EnhancePathResult::GetNode(int32_t index) const {
    if (index < nodes_.size()) {
        return &(nodes_[index]);
    }
    GUIDE_ASSERT(false);
    return nullptr;
}

int32_t EnhancePathResult::GetLastNodeIndex() const {
    return (nodes_.size() - 1U);
}

bool EnhancePathResult::IsValidNodeIndex(int32_t index) const {
    if (index >= 0 && index < nodes_.size()) {
        return true;
    }
    return false;
}

bool EnhancePathResult::IsLastNodeIndex(int32_t index) const {
    if (index + 1 == nodes_.size()) {
        return true;
    }
    return false;
}

bool EnhancePathResult::IsFirstNodeIndex(int32_t index) const {
    return (index == 0U);
}


DirectedEdge* EnhancePathResult::GetPrevEdge(int32_t node_idx, int delta) const {
    int index = node_idx - delta;
    if (IsValidNodeIndex(index)) {
        return nodes_[index].GetDirectEdge();
    }
    // assert(false);
    return nullptr;
}

DirectedEdge* EnhancePathResult::GetCurrentEdge(int32_t node_idx) {
    return GetNextEdge(node_idx, 0);
}

DirectedEdge* EnhancePathResult::GetNextEdge(int32_t node_idx, int delta) const {
    int index = node_idx + delta;
    if (IsValidNodeIndex(index) && !IsLastNodeIndex(index)) {
        return nodes_[index].GetDirectEdge();
    }
    return nullptr;
}


PathSectionInfo::PathSectionInfo(int32_t index, const path::Section &section)
: index_(index)
, section_(section) {

}

float  PathSectionInfo::GetLength(DistaneUnit unit) {
    return section_.length;
}
    
float  PathSectionInfo::GetTravelTime() const {
    return section_.time;
}

float  PathSectionInfo::GetStartOffset() const {
    return section_.start_offset;
}
    
float  PathSectionInfo::GetEndOffset() const {
    return section_.end_offset;
}

int32_t PathSectionInfo::GetStartEdgeIndex() const {
    return section_.index;
}

int32_t PathSectionInfo::GetEdgeNum() const {
    return section_.num;
}

}  // namespae guide
}  // namespace aurora
/* EOF */
