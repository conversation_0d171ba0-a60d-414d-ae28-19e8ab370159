#ifndef MAP_SRC_GUIDE_SRC_DATA_ENHANCE_PATH_RESULT_H
#define MAP_SRC_GUIDE_SRC_DATA_ENHANCE_PATH_RESULT_H

#include <cstdint>
#include <string>
#include <vector>
#include <memory>

#include "point2.h"
#include "pointll.h"
#include "constants.h"

#include "path_def.h"

#include "guidance/src/data/directed_edge.h"
#include "guidance/src/data/enhance_node.h"

namespace aurora {
namespace guide {

using PathLandMarkPtr = path::PathLandmarkPtr;
using PathCandidate = path::Candidate;
using PathStrategy = path::PathStrategy;
using PathMode = path::PathMode;
using PathTrigger = path::PathTrigger;
using PathQueryPtr = std::shared_ptr<path::PathQuery>;
using PathResultPtr = std::shared_ptr<path::PathResult>;

class EnhancePathQuery {
public:
    EnhancePathQuery(PathQueryPtr ptr);
    ~EnhancePathQuery() = default;

    PathMode         GetMode() const;
    PathTrigger      GetTrigger() const;

    uint32_t         GetLandmarkNum() const;
    PathLandMarkPtr  GetLandMark(int32_t index) const;

    PathLandMarkPtr  GetStart() const;
    PathLandMarkPtr  GetDestination() const;

    uint32_t         GetWayPointNum() const;
    PathLandMarkPtr  GetWayPoint(int32_t ubdex) const;

protected:
    PathQueryPtr ptr_;
};
using EnhancePathQueryPtr = std::shared_ptr<EnhancePathQuery>;

class PathSectionInfo {
public:
    explicit PathSectionInfo(int32_t index, const path::Section &section);

    float  GetLength(DistaneUnit unit = DistaneUnit::kKilometer);
    float  GetTravelTime() const;

    float  GetStartOffset() const;
    float  GetEndOffset() const;

    int32_t GetStartEdgeIndex() const;
    int32_t GetEdgeNum() const;

protected:
    int32_t index_;
    path::Section section_;
};

class EnhancePathResult {
public:
    EnhancePathResult(EnhanceDataProvider *provider);

    bool Build(EnhancePathQueryPtr query_ptr, const path::PathInfo &info);

    double GetTotalLength() const;

    // TODO: may get country code from node index
    const std::string& GetCountryCode() const;
    const std::string& GetStateCode() const;

    uint64_t GetPathId() const;
    int32_t  GetEdgeNum() const;
    int32_t  GetNodeNum() const;
    int32_t  GetSectionNum() const;

    EnhancePathQueryPtr GetEnhancePathQuery() const;
    PathSectionInfo* GetSectionInfo(int32_t index) const;

    DirectedEdge* GetEdge(int32_t index) const;
    EnhanceNode* GetNode(int32_t index) const;

    DirectedEdge* GetPrevEdge(int32_t node_idx, int delta = 1) const;
    DirectedEdge* GetCurrentEdge(int32_t node_idx);
    DirectedEdge* GetNextEdge(int32_t node_idx, int delta = 1) const;

    int32_t GetLastNodeIndex() const;
    bool IsValidNodeIndex(int32_t index) const;
    bool IsLastNodeIndex(int32_t index) const;
    bool IsFirstNodeIndex(int32_t index) const;

protected:
    bool BuildPathSections(const path::PathInfo &path);
    bool GetNextNonInnerDirectEdgeId(const path::PathInfo &path, int32_t start_edge_idx, DirectEdgeId &direct_id);
    bool AdjustEndianEdgeLengthAndGeo();
    bool ProcessNameConsistency();
    bool UpdateLaneInfoAndFacility();
    bool UpdateJunctionAndTollStation();

protected:
    uint64_t path_id_;
    double total_length_;
    std::string country_code_;
    std::string state_code_;
    EnhancePathQueryPtr query_;
    mutable std::vector<EnhanceNode> nodes_;
    mutable std::vector<PathSectionInfo> sections_;
    EnhanceDataProvider *provider_;
};
using EnhancePathResultPtr = std::shared_ptr<EnhancePathResult>;

} // namespace guide
} // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_ENHANCE_PATH_RESULT_H
/* EOF */
