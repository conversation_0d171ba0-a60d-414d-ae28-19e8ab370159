#include "guidance/src/data/enhance_toll_station.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

EnhanceTollStation::EnhanceTollStation(TollGateInfo *info, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id) {
    GUIDE_ASSERT(info != nullptr);

    from_edge_id_ = from_edge_id;
    to_edge_id_ = to_edge_id;

    if (info != nullptr) {
        BuildTollStation(info);
    }
}

ImagePtr EnhanceTollStation::GetImage() const {
    return image_;
}
    
std::string EnhanceTollStation::GetName() const {
    return name_;
}

    
DirectEdgeId EnhanceTollStation::GetFromEdgeId() const {
    return from_edge_id_;
}
    
DirectEdgeId EnhanceTollStation::GetToEdgeId() const {
    return to_edge_id_;
}

    
uint32_t EnhanceTollStation::GetTollPortNum() const {
    return ports_.size();
}

    
TollgateGateInfo* EnhanceTollStation::GetTollPort(int32_t index) {
    if (index < ports_.size()) {
        return (ports_.data() + index);
    }

    GUIDE_ASSERT(false);
    return nullptr;
}

const std::vector<TollgateGateInfo>& EnhanceTollStation::GetTollPorts() const {
    return ports_;
}

std::string EnhanceTollStation::GetId() const {
    return from_edge_id_.ToString() + "_" + to_edge_id_.ToString();
}

void EnhanceTollStation::BuildTollStation(TollGateInfo *info) {
    GUIDE_ASSERT(info != nullptr);
    GUIDE_ASSERT(from_edge_id_.edge_id.feature_id == info->GetBaseInfo()->in_edge_id);
    GUIDE_ASSERT(from_edge_id_.forward == !(info->GetBaseInfo()->in_edge_dir));
    GUIDE_ASSERT(info->GetBaseInfo()->is_same_mesh);
    GUIDE_ASSERT(info->GetBaseInfo()->is_same_tile);


    GUIDE_ASSERT(to_edge_id_.edge_id.feature_id == info->GetBaseInfo()->out_edge_id);
    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.mesh_col == info->GetBaseInfo()->out_mesh_col);
    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.mesh_row == info->GetBaseInfo()->out_mesh_row);

    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.tile_id == info->GetBaseInfo()->out_tile_id);
    GUIDE_ASSERT(to_edge_id_.edge_id.feature_id == info->GetBaseInfo()->out_edge_id);
    GUIDE_ASSERT(to_edge_id_.forward == !(info->GetBaseInfo()->out_edge_dir));

    name_ =  info->GetName();
    image_ = info->GetBackground();

    GUIDE_ASSERT(!name_.empty());

    int32_t port_num = info->GetTollgateGateCount();
    GUIDE_ASSERT(port_num > 0);
    ports_.reserve(port_num);
    for (int32_t port_index = 0; port_index < port_num; ++port_index) {
        auto port_info = info->GetTollgateGateInfo(port_index);
        GUIDE_ASSERT(port_info != nullptr);

        if (port_info != nullptr) {
            ports_.emplace_back(*port_info);
        } else {
            GUIDE_LOG_ERROR("toll station port is nullptr .");
        }
    }
}

}  // namespace guide
}  // namespace aurora
/* EOF */
