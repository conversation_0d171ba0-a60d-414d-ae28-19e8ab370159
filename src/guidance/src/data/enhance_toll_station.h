#ifndef MAP_SRC_GUIDE_SRC_DATA_ENHANCE_TOLL_STATION_H
#define MAP_SRC_GUIDE_SRC_DATA_ENHANCE_TOLL_STATION_H

#include <cstdint>
#include <string>
#include <memory>
#include <vector>

#include "route_data/route_data_def.h"
#include "guidance/src/data/enhance_data_provider.h"

namespace aurora {
namespace guide {

class EnhanceTollStation {
public:
    EnhanceTollStation(TollGateInfo *info, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id);
    ~EnhanceTollStation() = default;

    ImagePtr GetImage() const;
    std::string GetName() const;

    DirectEdgeId GetFromEdgeId() const;
    DirectEdgeId GetToEdgeId() const;

    uint32_t GetTollPortNum() const;

    TollgateGateInfo* GetTollPort(int32_t index);

    const std::vector<TollgateGateInfo>& GetTollPorts() const;

    std::string GetId() const;

protected:
    void BuildTollStation(TollGateInfo *info);

protected:
    DirectEdgeId from_edge_id_;
    DirectEdgeId to_edge_id_;
    std::string name_;
    std::vector<TollgateGateInfo> ports_;
    ImagePtr image_;
};

using EnhanceTollStationPtr = std::shared_ptr<EnhanceTollStation>;

}  // namespace guide
}  // namespace aorora
#endif  // MAP_SRC_GUIDE_SRC_DATA_ENHANCE_TOLL_STATION_H
/* EOF */
