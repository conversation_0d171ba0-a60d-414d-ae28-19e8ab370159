#include "guidance/src/data/facility_type_wrapper.h"
#include <map>

namespace aurora {
namespace guide {

static std::map<FacilityType, bool> facility_flag_table = {
    {FacilityType::kUnknown,                    false},
    {FacilityType::kMaxSpeedSign,               false},   ///< 最高限速标志
    {FacilityType::kMinSpeedSign,               false},   ///< 最低限速标志
    {FacilityType::kSpeedReleaseSign,           false},   ///< 限速解除标志
    {FacilityType::kSpeedCamera,                false},    ///< 测速摄像头
    {FacilityType::kViolationCamera,            false},    ///< 违章摄像头
    {FacilityType::kLaneLight,                  false},   ///< 车道指示灯
    {FacilityType::kWaitSignal,                 false},   ///< 等待信号灯
    {FacilityType::kPedestrianLight,            false},   ///< 人行横道灯
    {FacilityType::kPedestrianCrossing,         false},   ///< 人行横道
    {FacilityType::kVariableSpeedLimit,         false},   ///< 可变限速
    {FacilityType::kSchoolZone,                 true},    ///< 注意儿童
    {FacilityType::kRailwayCrossing,            true},    ///< 铁路道口
    {FacilityType::kRockFallLeft,               true},    ///< 注意落石（左侧）
    {FacilityType::kAccidentProne,              true},    ///< 事故易发路段
    {FacilityType::kSlipperyRoad,               true},    ///< 路面易滑
    {FacilityType::kVillage,                    true},    ///< 村庄
    {FacilityType::kOverpass,                   false},   ///< 过街天桥
    {FacilityType::kMannedRailwayCrossing,      true},    ///< 有人看管铁路道口
    {FacilityType::kUnmannedRailwayCrossing,    true},    ///< 无人看管铁路道口
    {FacilityType::kRoadNarrowsBoth,            true},   ///< 道路两侧变窄
    {FacilityType::kSharpLeftTurn,              true},    ///< 向左急转弯
    {FacilityType::kSharpRightTurn,             true},    ///< 向右急转弯
    {FacilityType::kReverseCurve,               false},    ///< 反向弯路
    {FacilityType::kMultipleCurves,             true},    ///< 连续弯路
    {FacilityType::kMergeLeft,                  true},    ///< 左侧合流看板
    {FacilityType::kMergeRight,                 true},    ///< 右侧合流看板
    {FacilityType::kMonitorCamera,              false},    ///< 监控摄像头
    {FacilityType::kBusLaneCamera,              false},    ///< 公交专用道摄像头
    {FacilityType::kNoOvertaking,               true},    ///< 禁止超车
    {FacilityType::kNoOvertakingRelease,        false},   ///< 禁止超车解除
    {FacilityType::kYieldSign,                  false},   ///< 停车让行
    {FacilityType::kSlowYield,                  false},   ///< 减速让行
    {FacilityType::kMeetingYield,               false},   ///< 会车让行
    {FacilityType::kRoadNarrowsRight,           true},    ///< 右侧变窄
    {FacilityType::kRoadNarrowsLeft,            true},    ///< 左侧变窄
    {FacilityType::kNarrowBridge,               true},   ///< 窄桥
    {FacilityType::kAroundRouteBoth,            true},   ///< 左右绕行
    {FacilityType::kAroundRouteLeft,            true},   ///< 左侧绕行
    {FacilityType::kAroundRouteRight,           true},   ///< 右侧绕行
    {FacilityType::kRockFallRight,              true},    ///< 注意落石（右侧）
    {FacilityType::kDangerousCurveLeft,         true},   ///< 榜山险路（左侧）
    {FacilityType::kDangerousCurveRight,        true},   ///< 榜山险路（右侧）
    {FacilityType::kDampingRoadLeft,            false},   ///< 堤坝路（左侧）
    {FacilityType::kDampingRoadRight,           false},   ///< 堤坝路（右侧）
    {FacilityType::kSteepAscent,                true},    ///< 上陡坡
    {FacilityType::kSteepDescent,               true},    ///< 下陡坡
    {FacilityType::kFordRoad,                   false},   ///< 过水路面
    {FacilityType::kUnlevelRoad,                true},   ///< 路面不平
    {FacilityType::kSlowDownHump,               false},   ///< 驼峰路
    {FacilityType::kSlowDown,                   true},    ///< 慢行
    {FacilityType::kDangerAhead,                true},   ///< 注意危险
    {FacilityType::kCrosswind,                  true},   ///< 注意横风
    {FacilityType::kLivestock,                  false},   ///< 注意牲畜
    {FacilityType::kBicycleWarning,             false},   ///< 注意非机动车
    {FacilityType::kTwoWayTraffic,              false},   ///< 双向交通
    {FacilityType::kTunnel,                     true},   ///< 隧道
    {FacilityType::kFerry,                      true},   ///< 渡口
    {FacilityType::kFourwayIntersection,        false},   ///< 十字交叉
    {FacilityType::kTIntersectionLeft,          false},   ///< T形交叉（左侧）
    {FacilityType::kTIntersectionRight,         false},   ///< T形交叉（右侧）
    {FacilityType::kTIntersectionBoth,          false},   ///< T形交叉（左右侧）
    {FacilityType::kYIntersection,              false},   ///< Y形交叉
    {FacilityType::kRoundabout,                 false},   ///< 环形交叉
    {FacilityType::kConstructionZone,           false},   ///< 施工
    {FacilityType::kSlowDownBump,               false},   ///< 减速带
    {FacilityType::kPedestrianWarning,          false},   ///< 注意行人
    {FacilityType::kZoneSpeedStart,             false},   ///< 区间测速开始
    {FacilityType::kZoneSpeedEnd,               false},   ///< 区间测速解除
    {FacilityType::kZoneSpeedSection,           false},   ///< 区间测速路段
    {FacilityType::kEmergencyCamera,            false},   ///< 应急车道电子眼
    {FacilityType::kBicycleLaneCamera,          false},   ///< 非机动车道电子眼
    {FacilityType::kTrafficLightCamera,         false},   ///< 红绿灯电子眼
    {FacilityType::kAccelerationLaneLimit,      false}    ///< 加减速车道限速
};


// TODO:后续会走locale文言配置
static std::map<FacilityType, std::string> facility_lang_table = {
    {FacilityType::kUnknown,                    ""},
    {FacilityType::kMaxSpeedSign,               ""},   ///< 最高限速标志
    {FacilityType::kMinSpeedSign,               ""},   ///< 最低限速标志
    {FacilityType::kSpeedReleaseSign,           ""},   ///< 限速解除标志
    {FacilityType::kSpeedCamera,                ""},    ///< 测速摄像头
    {FacilityType::kViolationCamera,            ""},    ///< 违章摄像头
    {FacilityType::kLaneLight,                  ""},   ///< 车道指示灯
    {FacilityType::kWaitSignal,                 ""},   ///< 等待信号灯
    {FacilityType::kPedestrianLight,            ""},   ///< 人行横道灯
    {FacilityType::kPedestrianCrossing,         ""},   ///< 人行横道
    {FacilityType::kVariableSpeedLimit,         ""},   ///< 可变限速
    {FacilityType::kSchoolZone,                 "前方学校区域,请减速慢行"},    ///< 注意儿童
    {FacilityType::kRailwayCrossing,            "即将经过铁道口,注意减速观察"},    ///< 铁路道口
    {FacilityType::kRockFallLeft,               "前方左侧可能有落石区域，注意行车安全"},    ///< 注意落石（左侧）
    {FacilityType::kAccidentProne,              "前方事故易发路段,注意减速谨慎驾驶"},    ///< 事故易发路段
    {FacilityType::kSlipperyRoad,               "前方道路易滑,注意控制车速"},    ///< 路面易滑
    {FacilityType::kVillage,                    "进入村庄区域,注意行人"},    ///< 村庄
    {FacilityType::kOverpass,                   ""},   ///< 过街天桥
    {FacilityType::kMannedRailwayCrossing,      "即将进入铁道口,注意交通指挥"},    ///< 有人看管铁路道口
    {FacilityType::kUnmannedRailwayCrossing,    "即将进入铁道口,注意观察确保安全通过"},    ///< 无人看管铁路道口
    {FacilityType::kRoadNarrowsBoth,            "前方道路变窄,注意减速并保持车距"},   ///< 道路两侧变窄
    {FacilityType::kSharpLeftTurn,              "前方左急转弯,减速并注意转向"},    ///< 向左急转弯
    {FacilityType::kSharpRightTurn,             "前方右急转弯,减速并注意转向"},    ///< 向右急转弯
    {FacilityType::kReverseCurve,               ""},    ///< 反向弯路
    {FacilityType::kMultipleCurves,             "即将进入连续转弯路段,注意减速保持车距"},    ///< 连续弯路
    {FacilityType::kMergeLeft,                  "左侧车辆交汇点,注意观察并避让"},    ///< 左侧合流看板
    {FacilityType::kMergeRight,                 "右侧车辆交互迪安,注意观察并避让"},    ///< 右侧合流看板
    {FacilityType::kMonitorCamera,              ""},    ///< 监控摄像头
    {FacilityType::kBusLaneCamera,              ""},    ///< 公交专用道摄像头
    {FacilityType::kNoOvertaking,               "前方禁止超车路段,注意遵守交通规则"},    ///< 禁止超车
    {FacilityType::kNoOvertakingRelease,        ""},   ///< 禁止超车解除
    {FacilityType::kYieldSign,                  ""},   ///< 停车让行
    {FacilityType::kSlowYield,                  ""},   ///< 减速让行
    {FacilityType::kMeetingYield,               ""},   ///< 会车让行
    {FacilityType::kRoadNarrowsRight,           "前方右侧道路变窄,注意行车安全"},    ///< 右侧变窄
    {FacilityType::kRoadNarrowsLeft,            "前方左侧道路变窄,注意行车安全"},    ///< 左侧变窄
    {FacilityType::kNarrowBridge,               "即将通过窄桥,注意行车安全"},   ///< 窄桥
    {FacilityType::kAroundRouteBoth,            "前方需左右绕行,注意观察并谨慎驾驶"},   ///< 左右绕行
    {FacilityType::kAroundRouteLeft,            "前方需向左绕行,注意安全通过"},   ///< 左侧绕行
    {FacilityType::kAroundRouteRight,           "前方需向右绕行,注意安全通过"},   ///< 右侧绕行
    {FacilityType::kRockFallRight,              "前方右侧可能有落石区域,注意行车安全"},    ///< 注意落石（右侧）
    {FacilityType::kDangerousCurveLeft,         "前方左侧为靠山险路,注意减速并保持安全车距"},   ///< 榜山险路（左侧）
    {FacilityType::kDangerousCurveRight,        "前方右侧为靠山险路,注意减速并保持安全车距"},   ///< 榜山险路（右侧）
    {FacilityType::kDampingRoadLeft,            ""},   ///< 堤坝路（左侧）
    {FacilityType::kDampingRoadRight,           ""},   ///< 堤坝路（右侧）
    {FacilityType::kSteepAscent,                "前方陡坡上行路段,注意车速"},    ///< 上陡坡
    {FacilityType::kSteepDescent,               "前方陡坡下行路段,注意车速"},    ///< 下陡坡
    {FacilityType::kFordRoad,                   ""},   ///< 过水路面
    {FacilityType::kUnlevelRoad,                "前方路面不平,注意减速"},   ///< 路面不平
    {FacilityType::kSlowDownHump,               ""},   ///< 驼峰路
    {FacilityType::kSlowDown,                   "前方减速慢行,注意行车安全"},    ///< 慢行
    {FacilityType::kDangerAhead,                "前方危险路段,注意观察并谨慎驾驶"},   ///< 注意危险
    {FacilityType::kCrosswind,                  "前方横风区域,注意握紧方向盘并减速"},   ///< 注意横风
    {FacilityType::kLivestock,                  ""},   ///< 注意牲畜
    {FacilityType::kBicycleWarning,             ""},   ///< 注意非机动车
    {FacilityType::kTwoWayTraffic,              ""},   ///< 双向交通
    {FacilityType::kTunnel,                     "前方进入隧道,注意开启车道并保持车距"},   ///< 隧道
    {FacilityType::kFerry,                      "前方渡口位置,注意减速并遵守渡口交通规则"},   ///< 渡口
    {FacilityType::kFourwayIntersection,        ""},   ///< 十字交叉
    {FacilityType::kTIntersectionLeft,          ""},   ///< T形交叉（左侧）
    {FacilityType::kTIntersectionRight,         ""},   ///< T形交叉（右侧）
    {FacilityType::kTIntersectionBoth,          ""},   ///< T形交叉（左右侧）
    {FacilityType::kYIntersection,              ""},   ///< Y形交叉
    {FacilityType::kRoundabout,                 ""},   ///< 环形交叉
    {FacilityType::kConstructionZone,           ""},   ///< 施工
    {FacilityType::kSlowDownBump,               ""},   ///< 减速带
    {FacilityType::kPedestrianWarning,          ""},   ///< 注意行人
    {FacilityType::kZoneSpeedStart,             ""},   ///< 区间测速开始
    {FacilityType::kZoneSpeedEnd,               ""},   ///< 区间测速解除
    {FacilityType::kZoneSpeedSection,           ""},   ///< 区间测速路段
    {FacilityType::kEmergencyCamera,            ""},   ///< 应急车道电子眼
    {FacilityType::kBicycleLaneCamera,          ""},   ///< 非机动车道电子眼
    {FacilityType::kTrafficLightCamera,         ""},   ///< 红绿灯电子眼
    {FacilityType::kAccelerationLaneLimit,      ""}    ///< 加减速车道限速
};


bool FacilityTypeWrapper::IsFilter(FacilityType type) {
    if (facility_flag_table.count(type) > 0) {
        return facility_flag_table.at(type);
    }

    GUIDE_ASSERT(false);
    return false;
}
    
std::string FacilityTypeWrapper::ToString(FacilityType type) {
    if (facility_lang_table.count(type) > 0) {
        return facility_lang_table.at(type);
    }

    GUIDE_ASSERT(false);
    return "";
}

NavFacilityType FacilityTypeWrapper::Convert(FacilityType type) {
    NavFacilityType result = NavFacilityType::kNone;

    switch(type) {
        case FacilityType::kSchoolZone: result = NavFacilityType::kSchoolZone; break;
        case FacilityType::kRailwayCrossing: result = NavFacilityType::kRailroadCrossing; break;
        case FacilityType::kRockFallLeft: result = NavFacilityType::kRockFallLeft; break;
        case FacilityType::kAccidentProne: result = NavFacilityType::kAccidentProne; break;
        case FacilityType::kSlipperyRoad: result = NavFacilityType::kSlipperyRoad; break;
        case FacilityType::kVillage: result = NavFacilityType::kVillage; break;
        case FacilityType::kMannedRailwayCrossing: result = NavFacilityType::kMannedRailroadCrossing; break;
        case FacilityType::kUnmannedRailwayCrossing: result = NavFacilityType::kUnmannedRailroadCrossing; break;
        case FacilityType::kRoadNarrowsBoth: result = NavFacilityType::kRoadNarrowsBoth; break;
        case FacilityType::kSharpLeftTurn: result = NavFacilityType::kSharpLeftTurn; break;
        case FacilityType::kSharpRightTurn: result = NavFacilityType::kSharpRightTurn; break;
        case FacilityType::kMultipleCurves: result = NavFacilityType::kMultipleCurves; break;
        case FacilityType::kMergeLeft: result = NavFacilityType::kMergeLeft; break;
        case FacilityType::kMergeRight: result = NavFacilityType::kMergeRight; break;
        case FacilityType::kNoOvertaking: result = NavFacilityType::kNoOvertaking; break;
        case FacilityType::kRoadNarrowsRight: result = NavFacilityType::kRoadNarrowsRight; break;
        case FacilityType::kRoadNarrowsLeft: result = NavFacilityType::kRoadNarrowsLeft; break;
        case FacilityType::kNarrowBridge: result = NavFacilityType::kNarrowBridge; break;
        case FacilityType::kAroundRouteBoth: result = NavFacilityType::kAroundRouteBoth; break;
        case FacilityType::kAroundRouteLeft: result = NavFacilityType::kAroundRouteLeft; break;
        case FacilityType::kAroundRouteRight: result = NavFacilityType::kAroundRouteRight; break;
        case FacilityType::kRockFallRight: result = NavFacilityType::kRockFallRight; break;
        case FacilityType::kDangerousCurveLeft: result = NavFacilityType::kDangerousCurveLeft; break;
        case FacilityType::kDangerousCurveRight: result = NavFacilityType::kDangerousCurveRight; break;
        case FacilityType::kSteepAscent: result = NavFacilityType::kSteepAscent; break;
        case FacilityType::kSteepDescent: result = NavFacilityType::kSteepDescent; break;
        case FacilityType::kUnlevelRoad: result = NavFacilityType::kUnlevelRoad; break;
        case FacilityType::kSlowDown: result = NavFacilityType::kSlowDown; break;
        case FacilityType::kDangerAhead: result = NavFacilityType::kDangerAhead; break;
        case FacilityType::kCrosswind: result = NavFacilityType::kCrosswind; break;
        case FacilityType::kTunnel: result = NavFacilityType::kTunnel; break;
        case FacilityType::kFerry: result = NavFacilityType::kFerry; break;
        default: break;
    }
    return result;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
