#ifndef MAP_SRC_GUIDE_SRC_DATA_FACILITY_TYPE_WRAPPER_H
#define MAP_SRC_GUIDE_SRC_DATA_FACILITY_TYPE_WRAPPER_H

#include <string>
#include "guidance_def.h"
#include "guidance/src/common/common_def.h"


namespace aurora {
namespace guide {

class FacilityTypeWrapper {
public:
    static bool IsFilter(FacilityType type);
    static std::string ToString(FacilityType type);
    static NavFacilityType Convert(FacilityType type);
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_FACILITY_TYPE_WRAPPER_H
/* EOF */
