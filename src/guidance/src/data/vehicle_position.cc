#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/common/common_def.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/common/map_data_util.h"

namespace aurora {
namespace guide {

VehiclePosition::VehiclePosition() {
    valid_ = 0;
}

VehiclePosition::VehiclePosition(const MatchResult *ccp) {
    Build(ccp);
}

bool VehiclePosition::IsValid() const {
    return (valid_ > 0);
}
 
uint64_t VehiclePosition::GetStamp() const {
    return stamp_;
}

bool VehiclePosition::IsOnRoad() const {
    return (on_road_ > 0);
}

bool VehiclePosition::IsReroute() const {
    return (reroute_ > 0);
}
    
bool VehiclePosition::IsForward() const {
    return (edge_id_.forward);
}

bool VehiclePosition::IsHighway() const {
    return MapDataUtil::IsHighway(edge_base_.road_class, edge_base_.edge_form, edge_base_.is_ramp);
}

const PointLL&  VehiclePosition::GetLngLat() const {
    return lnglat_;
}

const PointLL&  VehiclePosition::GetProj() const {
    return proj_;
}

const DirectEdgeId VehiclePosition::GetEdgeId() const {
    return edge_id_;
}
    
uint64_t VehiclePosition::GetPathId() const {
    return path_id_;
}
 
uint32_t VehiclePosition::GetPathLinkIndex() const {
    return path_link_idx_;
}

float  VehiclePosition::GetSpeed() const {
    return speed_;
}
    
float VehiclePosition::GetLinkOffset() const {
    return offset_;
}

uint16_t VehiclePosition::GetHeading() const {
    return heading_;
}

void VehiclePosition::Build(const MatchResult *ccp) {
    GUIDE_ASSERT(ccp != nullptr);

    valid_ = 1;
        
    stamp_ = ccp->timestamp;
    on_road_ = ccp->on_road;
    reroute_ = ccp->reroute;

    lnglat_ = ccp->origin_pos.lnglat;
    proj_ = ccp->road_match_info.proj_pos;

    edge_id_.edge_id.tile_id = ccp->road_match_info.tile_id;
    edge_id_.edge_id.feature_id = ccp->road_match_info.link_id;
    edge_id_.forward = (ccp->road_match_info.dir == 0) ? 1:0;

    path_id_ = ccp->road_match_info.path_id;
    path_link_idx_ = ccp->road_match_info.path_link_idx;
    offset_ = ccp->road_match_info.offset;

    speed_ = ccp->car_pos_info.speed;
    heading_ = ccp->car_pos_info.heading;

    edge_base_ = ccp->road_match_info.topo_edge;
}


}  // namespace guide
}  // namespace aurora
/* EOF */
