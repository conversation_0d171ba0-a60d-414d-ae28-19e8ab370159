#ifndef MAP_SRC_GUIDE_SRC_DATA_VEHICLE_POSITION_H
#define MAP_SRC_GUIDE_SRC_DATA_VEHICLE_POSITION_H

#include <cstdint>
#include "pointll.h"
#include "guidance/src/common/common_def.h"

namespace aurora {
namespace guide {

class VehiclePosition {
public:
    VehiclePosition();
    VehiclePosition(const MatchResult *ccp);

    bool     IsValid() const;
    uint64_t GetStamp() const;
    bool     IsOnRoad() const;
    bool     IsReroute() const;
    bool     IsForward() const;
    bool     IsHighway() const;

    const PointLL&  GetLngLat() const;
    const PointLL&  GetProj() const;
    const DirectEdgeId GetEdgeId() const;
    
    uint64_t GetPathId() const;
    uint32_t GetPathLinkIndex() const;
    float    GetSpeed() const;
    float    GetLinkOffset() const;
    uint16_t GetHeading() const;

protected:
    void Build(const MatchResult *ccp);

private:
    uint64_t  stamp_;
    uint8_t   on_road_    :1;
    uint8_t   reroute_    :1;
    uint8_t   valid_      :1;
    uint8_t   reserve     :6;

    PointLL   lnglat_;
    PointLL   proj_;

    DirectEdgeId edge_id_;
    uint32_t  path_link_idx_;
    uint64_t  path_id_;

    float     speed_;   // units: m/s  
    float     offset_;  // units: meter
    uint16_t  heading_; // units: degree

    TopoEdgeBase edge_base_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_COMMOM_VEHICLE_POSITION_H
/* EOF */
