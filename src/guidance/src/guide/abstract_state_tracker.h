#ifndef MAP_SRC_GUIDE_SRC_GUIDE_ABSTRACT_STATE_TRACKER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_ABSTRACT_STATE_TRACKER_H

#include <cstdint>
#include <memory>
#include "guidance_def.h"
#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/data/path_data_manager.h"
#include "guidance/src/common/common_def.h"
#include "guidance/src/common/guide_data.h"

namespace aurora {
namespace guide {

class AbstractStateTracker {
public:
    static std::shared_ptr<AbstractStateTracker> Create(const NavigationMode mode, PathDataManagerPtr path_manager);

    AbstractStateTracker(NavigationMode mode, PathDataManagerPtr path_manager);
    virtual ~AbstractStateTracker() = default;
    
    virtual int32_t DoGuide(const VehiclePosition &vehicle_pos, GuideData &guide_data) = 0;

protected:
    NavigationMode mode_;
    PathDataManagerPtr path_manager_;
}; // class IGuideStateTracker

using AbstractStateTrackerPtr = std::shared_ptr<AbstractStateTracker>;

}  // namespace guide 
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_ABSTRACT_STATE_TRACKER_H
/* EOF */
