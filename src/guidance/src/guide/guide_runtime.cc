#include "guidance/src/guide/guide_runtime.h"
#include "guidance_def.h"
#include "guidance/src/common/guide_log.h"
#include "errorcode.h"

namespace aurora {
namespace guide {

GuideRuntime::GuideRuntime(GuidanceEvent *guide_event)
: guide_event_(guide_event) {
    nav_status_ = NavigationStatus::kNavigationStatusNone;
    mode_ = NavigationMode::kNavigationModeNone;
}

GuideRuntime::~GuideRuntime() {

}

int32_t GuideRuntime::Start() {
    return 0;
}

int32_t GuideRuntime::Stop() {
    return 0;
}

bool GuideRuntime::IsGuide() const {
    if ((mode_ == NavigationMode::kNavigationModeGPS || mode_ == NavigationMode::kNavigationModeSimulation ||
            mode_ == NavigationMode::kNavigationModeCruise) && 
                (nav_status_ == NavigationStatus::kNavigationStatusOn)) {
        return true;
    }
    return false;
}


int32_t GuideRuntime::StartNavigation(NavigationMode mode, PathDataManagerPtr path_manager) {

    if (mode == NavigationMode::kNavigationModeGPS ||
        mode == NavigationMode::kNavigationModeSimulation ||
        mode == NavigationMode::kNavigationModeCruise) {

        mode_ = mode;
        nav_status_ = NavigationStatus::kNavigationStatusOn;
        path_manager_ = path_manager;
        tracker_ = AbstractStateTracker::Create(mode, path_manager);
        guide_event_->StartNavigation(mode, path_manager->GetMainPathId());

    } else {
        // ignore
    }

    // TODO:
    return 0;
}
    
int32_t GuideRuntime::StopNavigation(NavigationStopCode code) {
    
    // #1. interrupt

    // #2. clear 

    // #3. callback

    tracker_ = nullptr;
    mode_ = NavigationMode::kNavigationModeNone;
    nav_status_ = NavigationStatus::KNavigationStatusOff;
    path_manager_ = nullptr;
    guide_event_->StopNavigation(mode_, code);

    debug_ccps_.clear();
    debug_ccps_.shrink_to_fit();

    debug_guide_datas_.clear();
    debug_guide_datas_.shrink_to_fit();

    prev_guide_data_ = GuideData();

    return 0;
}

int32_t GuideRuntime::PauseNavigation() {
    nav_status_ = NavigationStatus::kNavigationStatusPause;
    // #1. callback
    guide_event_->PauseNavigation(mode_);
    return 0;
}
    
int32_t GuideRuntime::ResumeNavigation() {
    nav_status_ = NavigationStatus::kNavigationStatusOn;
    guide_event_->ResumeNavigation(mode_);
    return 0;
}

int32_t GuideRuntime::DoGuide(const VehiclePosition &ccp) {
    if (tracker_ == nullptr) {
        GUIDE_ASSERT(tracker_ != nullptr);
        GUIDE_LOG_ERROR("tracker_ is nullptr .");
        return ErrorCode::kErrorCodeParamNullPointer;
    }

    if (path_manager_ == nullptr) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("path_manager_ is nullptr .");
        return ErrorCode::kErrorCodeParamNullPointer;
    }

    GuideData guide_data;
    tracker_->DoGuide(ccp, guide_data);
    ProcessDebug(ccp, guide_data);
    ProcessGuideData(guide_data);
    prev_guide_data_ = guide_data;

    return ErrorCode::kErrorCodeOk;
}

void GuideRuntime::ProcessGuideData(const GuideData &guide_data) {
    if (guide_event_ == nullptr) {
        return;
    }

    if (!guide_data.voice_text.empty()) {
        guide_event_->PlayVoice(guide_data.voice_text);
    }
    
    // TODO: 后续检测到LaneInfo变更在发送通知，而不是定频发送
    if (guide_data.lane_info_state != nullptr && guide_data.lane_info_state->ptr != nullptr) {
        NavigationLaneInfoPtr lane_info = std::make_shared<NavigationLaneInfo>();
        EnhanceLaneInfoPtr ptr = guide_data.lane_info_state->ptr;
        lane_info->id = ptr->GetId();
        lane_info->back_lanes = ptr->GetBackLaneActions();
        lane_info->front_lanes = ptr->GetFrontLaneActions();
        guide_event_->ShowLaneInfo(lane_info);
    } else {
        guide_event_->ShowLaneInfo(nullptr);
    }

    // TODO: 后续检测到JunctionView变更在发送通知，而不是定频发送
    if (guide_data.junction_view_state != nullptr && guide_data.junction_view_state->ptr != nullptr) {
        JunctionViewInfoPtr junc_info = std::make_shared<JunctionViewInfo>();
        EnhanceJunctionViewPtr ptr = guide_data.junction_view_state->ptr;
        junc_info->id = ptr->GetId();
        junc_info->type = ptr->GetType();
        junc_info->distance = guide_data.junction_view_state->curr_distance;
        if (junc_info->distance < 0) {
            junc_info->distance = 0;
            GUIDE_ASSERT(false);
        }

        if (ptr->GetBackgroundImage() != nullptr) {
            junc_info->back_view_data = *(ptr->GetBackgroundImage());
        }

        if (ptr->GetForegroundImage() != nullptr) {
            junc_info->front_view_data = *(ptr->GetForegroundImage());
        }
        guide_event_->ShowJunctionView(junc_info);

    } else {
        guide_event_->ShowJunctionView(nullptr);
    }

    // process toll station
    if (guide_data.toll_station_state != nullptr && guide_data.toll_station_state->ptr != nullptr) {
        TollStationInfoPtr toll_station_info = std::make_shared<TollStationInfo>();
        EnhanceTollStationPtr ptr = guide_data.toll_station_state->ptr;

        toll_station_info->id = ptr->GetId();
        toll_station_info->distance = guide_data.toll_station_state->curr_distance;
        if (toll_station_info->distance < 0) {
            GUIDE_ASSERT(toll_station_info->distance >= 0);
            toll_station_info->distance = 0;
        }

        toll_station_info->station_name = ptr->GetName();
        if (ptr->GetImage() != nullptr) {
            toll_station_info->view_data = *(ptr->GetImage());
        }
        toll_station_info->gate_infos = ptr->GetTollPorts();
        guide_event_->UpdateTollStation(toll_station_info);
    } else {
        guide_event_->UpdateTollStation(nullptr);
    }

    // process nav guide board
    if (guide_data.path_id != PathID::kInvalidPathId) {
        NavigationInfoPtr nav_info = std::make_shared<NavigationInfo>();
        nav_info->mode = guide_data.mode;
        nav_info->path_id = guide_data.path_id;
        nav_info->road_name = guide_data.road_name;
        nav_info->path_remain = guide_data.path_remain;
        nav_info->next_guide_points = guide_data.next_guide_points;

        // GUIDE_LOG_INFO("MapMatching: PathLindIdx={}, offset={}", guide_data.pos.GetPathLinkIndex(), guide_data.pos.GetLinkOffset());
        guide_event_->UpdateNavigationInfo(nav_info);
    }

    // process facility
    std::vector<NavigationFacilityPtr> facility_infos;
    if (guide_data.facility_state != nullptr) {
        NavigationFacilityPtr  facility_info = std::make_shared<NavigationFacility>();
        EnhanceFacilityPtr enhance_facility = guide_data.facility_state->ptr;
        GUIDE_ASSERT(enhance_facility != nullptr);
        facility_info->id = enhance_facility->GetId();
        facility_info->type = enhance_facility->GetNavType();
        facility_info->remain_dist = guide_data.facility_state->curr_distance;
        facility_info->lnglat = enhance_facility->GetPosition();
        facility_infos.emplace_back(facility_info);
    }
    guide_event_->UpdateFacilityInfos(facility_infos);

    // process destination
    if (guide_data.is_arrive_dest) {
        guide_event_->ArriveDestiontion(mode_);
        StopNavigation(NavigationStopCode::kStopCodeDestination);
    }
}

void GuideRuntime::ProcessDebug(const VehiclePosition &ccp, const GuideData &guide_data) {
    if (!writter_.IsDebugOn()) {
        return;
    }

    debug_ccps_.emplace_back(ccp);
    if (debug_ccps_.size() % 30 == 0 || guide_data.is_arrive_dest) {
        writter_.WriteMatching(path_manager_->GetMainPathId(), debug_ccps_);
    }

    if (guide_data.is_arrive_dest || !guide_data.voice_text.empty()) {
        debug_guide_datas_.emplace_back(guide_data);
        writter_.WriteGuideData(path_manager_->GetMainPathId(), debug_guide_datas_);
    }

    if (guide_data.junction_view_state != nullptr && guide_data.junction_view_state->ptr != nullptr) {
        writter_.WriteJunctionView(path_manager_->GetMainPathId(), guide_data.junction_view_state->ptr);
    }

    if (guide_data.toll_station_state != nullptr && guide_data.toll_station_state->ptr->GetImage() != nullptr) {
        writter_.WriteTollStation(path_manager_->GetMainPathId(), guide_data.toll_station_state->ptr);
    }
}

}  // namespace guide
}  // namespace aurora
/* EOF */
