#ifndef MAP_SRC_GUIDE_SRC_GUIDE_GUIDE_RUNTIME_H
#define MAP_SRC_GUIDE_SRC_GUIDE_GUIDE_RUNTIME_H

#include <cstdint>
#include "guidance_def.h"
#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/guide/abstract_state_tracker.h"
#include "guidance/src/guidance_event.h"
#include "guidance/src/debug/geojson_writter.h"

namespace aurora {
namespace guide {

class GuideRuntime {
public:
    GuideRuntime(GuidanceEvent *guide_event);
    ~GuideRuntime();

    int32_t Start();
    int32_t Stop();

    bool   IsGuide() const;

    int32_t StartNavigation(NavigationMode mode, PathDataManagerPtr path_manager);
    int32_t StopNavigation(NavigationStopCode code);
    int32_t PauseNavigation();
    int32_t ResumeNavigation(); 

    int32_t DoGuide(const VehiclePosition &ccp);

protected:
    void ProcessGuideData(const GuideData &guide_data);
    void ProcessDebug(const VehiclePosition &ccp, const GuideData &guide_data);

protected:
    GuidanceEvent *guide_event_;
    NavigationStatus  nav_status_;
    NavigationMode    mode_;
    GuideData         prev_guide_data_;
    AbstractStateTrackerPtr tracker_;
    PathDataManagerPtr path_manager_;
    GeojsonWritter   writter_;

    std::vector<VehiclePosition> debug_ccps_;
    std::vector<GuideData> debug_guide_datas_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_GUIDE_RUNTIME_H
/* EOF */
