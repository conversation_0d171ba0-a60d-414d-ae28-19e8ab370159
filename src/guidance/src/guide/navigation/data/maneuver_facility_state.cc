#include "guidance/src/guide/navigation/data/maneuver_facility_state.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/data/facility_type_wrapper.h"

namespace aurora {
namespace guide {

ManeuverFacilityState::ManeuverFacilityState(ManeuverState &state) 
: state_(state) {

    EnhancePathResultPtr result = state.GetPathManager()->GetEnhancePathResult();
    if (result == nullptr) {
        GUIDE_ASSERT(result != nullptr);
        GUIDE_LOG_ERROR("path result is nullptr .");
        return;
    }

    int32_t snode_indx = state.GetManeuverIt()->GetBeginNodeIndex();
    int32_t enode_index = state.GetManeuverIt()->GetEndNodeIndex();

    float acc_dist = 0;
    for (int32_t index = snode_indx; index < enode_index; ++index) {
        DirectedEdge *edge = result->GetEdge(index);
        if (edge == nullptr) {
            GUIDE_ASSERT(edge != nullptr);
            GUIDE_LOG_ERROR("edge is nullptr .");
            return;
        }

        int32_t facility_num = edge->GetFacilityNum();
        for (int32_t index = 0; index < facility_num; ++index) {       
            FacilityState facility_state;
            facility_state.ptr = edge->GetFacility(index);
            facility_state.total_distance = state.GetDistanceToSNode() + acc_dist;
            facility_state.curr_distance = 0;
            facility_state.voice = FacilityTypeWrapper::ToString(facility_state.ptr->GetType());

            GUIDE_ASSERT(!facility_state.voice.empty());
            facility_states_.emplace_back(facility_state);
        }

        acc_dist += edge->GetLengthMeter();
    }
}

void ManeuverFacilityState::UpdateFacilities() {
    for (auto &facility_state : facility_states_) {
        facility_state.curr_distance = facility_state.total_distance - state_.GetDriveDistance();
    }
}

int32_t ManeuverFacilityState::GetFacilityNum() const {
    return facility_states_.size();   
}

const FacilityState* ManeuverFacilityState::GetFirstFacility() const {
    if (facility_states_.empty()) {
        return nullptr;
    }

    return &(facility_states_.front());
}

const FacilityState* ManeuverFacilityState::GetLastFacility() const {
    if (facility_states_.empty()) {
        return nullptr;
    }

    return &(facility_states_.back());
}

const FacilityState* ManeuverFacilityState::GetNextFacility() const {
    auto status = state_.GetVehicleManeuverStatus();

    if (status == VehicleOnManeuverStatus::kVehicleOnManeuver) {
        for (int index = 0; index < facility_states_.size(); ++index) {
            if (facility_states_.at(index).curr_distance > 0) {
                return &(facility_states_[index]);
            }
        }
    }

    if (status == VehicleOnManeuverStatus::kVehicleNotArriveManeuver) {
        return GetFirstFacility();
    }

    return nullptr;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
