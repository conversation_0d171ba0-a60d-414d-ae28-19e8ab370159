#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_STATE_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_STATE_H

#include <cstdint>
#include <list>

#include "guidance/src/common/maneuver.h"
#include "guidance/src/data/path_data_manager.h"

namespace aurora {
namespace guide {

class ManeuverState;

using ManeuverStateIt = std::list<ManeuverState>::iterator;

enum VehicleOnManeuverStatus {
    kVehiclePassedManeuver = 0,
    kVehicleOnManeuver,
    kVehicleNotArriveManeuver
};

class ManeuverState {
public:
    ManeuverState(PathDataManagerPtr path_manager, ManeuverIt &it);
    ~ManeuverState() = default;

    void  UpdateDriveDistance(float drive_distance);
    float GetDriveDistance() const;

    void  InitTotalDistane(float distance);
    float GetTotalDistance() const;

    float GetDistanceToSNode() const;
    float GetDistanceToENode() const;

    float GetTimeToENode() const;

    VehicleOnManeuverStatus GetVehicleManeuverStatus() const;

    bool IsFarInstructionTag() const;
    std::string GetFarInstruction() const; 
    std::string GetFarInstructionWithTag();

    bool IsMiddleInstructionTag() const;
    std::string GetMiddleInstruction() const;
    std::string GetMiddleInstructionWithTag();

    bool IsNearInstructionTag() const;
    std::string GetNearInstruction() const;
    std::string GetNearInstructionWithTag();

    bool IsCloseToInstructionTag() const;
    std::string GetCloseToInstruction() const;
    std::string GetCloseToInstructionWithTag();

    bool IsArriveInstructionTag() const;
    std::string GetArriveInstruction() const;
    std::string GetArriveInstructionWithTag();

    bool IsPostInstructionTag() const;
    std::string GetPostInstruction() const;
    std::string GetPostInstructionWithTag();

    int32_t GetVehicleLinkIndex() const;

    ManeuverIt GetManeuverIt();
    PathDataManagerPtr GetPathManager();

protected:
    PathDataManagerPtr path_manager_;
    ManeuverIt maneuver_it_;

    uint64_t far_stamp_;
    uint64_t mid_stamp_;
    uint64_t near_stamp_;
    uint64_t closeto_stamp_;
    uint64_t post_stamp_;
    uint64_t arrive_stamp_;

    uint64_t remain_time_;    // ms
    float    total_distance_;
    float    drive_distance_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_STATE_H
/* EOF */
