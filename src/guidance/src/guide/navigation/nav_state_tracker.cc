#include "guidance/src/guide/navigation/nav_state_tracker.h"
#include "errorcode.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

NavStateTracker::NavStateTracker(NavigationMode mode, PathDataManagerPtr path_manager)
: AbstractStateTracker(mode, path_manager)
, state_manager_(path_manager_) {

}

int32_t NavStateTracker::DoGuide(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    GUIDE_ASSERT(path_manager_ != nullptr);

    if (path_manager_ == nullptr) {
        return ErrorCode::kErrorCodeParamNullPointer;    
    }

    if (vehicle_pos.GetPathId() != path_manager_->GetMainPathId()) {
        GUIDE_LOG_INFO("no valid path id ...");
        return 0;
    }

    state_manager_.UpdateStateByVehiclePos(vehicle_pos);
    
    DoGuideManeuvers(vehicle_pos, guide_data);
    DoGuideLaneInfos(vehicle_pos, guide_data);
    DoGuideJunctionViews(vehicle_pos, guide_data);
    DoGuideNavBoard(vehicle_pos, guide_data);
    DoGuideTollStations(vehicle_pos, guide_data);
    DoGuideFacilities(vehicle_pos, guide_data);
    return 0;
}

bool NavStateTracker::DoGuideManeuvers(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    ManeuverState *curr_man_state = state_manager_.GetCurrManeuverState();
    ManeuverState *next_man_state = state_manager_.GetNextManeuverState();

#if 0
    if (curr_man_state != nullptr) {
        GUIDE_LOG_INFO("current maneuver: total={}, drive={}, dis_to_snode={}, snode_idx={}, enode_idx={}, path_index={}",
                curr_man_state->GetTotalDistance(), 
                curr_man_state->GetDriveDistance(),
                curr_man_state->GetDistanceToSNode(),
                curr_man_state->GetManeuverIt()->GetBeginNodeIndex(),
                curr_man_state->GetManeuverIt()->GetEndNodeIndex(),
                vehicle_pos.GetPathLinkIndex());
    }

    if (next_man_state != nullptr) {
        GUIDE_LOG_INFO("next maneuver: total={}, drive={}, dis_to_snode={}, snode_idx={}, enode_idx={}, path_index={}",
            next_man_state->GetTotalDistance(), 
            next_man_state->GetDriveDistance(),
            next_man_state->GetDistanceToSNode(),
            next_man_state->GetManeuverIt()->GetBeginNodeIndex(),
            next_man_state->GetManeuverIt()->GetEndNodeIndex(),
            vehicle_pos.GetPathLinkIndex());
    }

#endif

    GUIDE_ASSERT(curr_man_state != nullptr);
    if (curr_man_state == nullptr) {
        GUIDE_LOG_ERROR("curr_man_state is nullptr .");
        return false;
    }

    if (!curr_man_state->GetManeuverIt()->IsDestinationType()) {
        GUIDE_ASSERT(next_man_state != nullptr);
        if (next_man_state == nullptr) {
            GUIDE_LOG_ERROR("next_man_state is nullptr .");
            return false;
        }
    }

    guide_data.pos = vehicle_pos;
    if (voice_policy_.DoPlay(curr_man_state, next_man_state, guide_data)) {
        GUIDE_ASSERT(!guide_data.voice_text.empty());
        if (guide_data.voice_text.empty()) {
            GUIDE_LOG_ERROR("guide_data voice data is empty ...");
        }
    }
    return true;
}

bool NavStateTracker::DoGuideLaneInfos(const VehiclePosition &vehicle_pos, GuideData &guide_data) {

    ManeuverLaneInfoState *man_lane_info_state = state_manager_.GetCurrManeuverLaneInfoState();
    if (man_lane_info_state == nullptr) {
        GUIDE_ASSERT(false);
        return false;
    }

    const LaneInfoState *lane_state = man_lane_info_state->GetNextLaneInfo();
    if (lane_state == nullptr) {
        return false;
    }

    if (lane_state->curr_distance >= 0 && lane_state->curr_distance <= 200) {
        guide_data.lane_info_state = lane_state;
        return true;
    }

    guide_data.lane_info_state = nullptr;
    return false;
}

bool NavStateTracker::DoGuideJunctionViews(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    ManeuverJunctionViewState * man_junction_view_state = state_manager_.GetCurrManeuverJunctionViewState();
    if (man_junction_view_state == nullptr) {
        GUIDE_ASSERT(false);
        return false;
    }

    const JunctionViewState *junction_view_state = man_junction_view_state->GetNextJunctionView();
    if (junction_view_state == nullptr) {
        return false;
    }

    float distance = 200;
    ManeuverState *state = state_manager_.GetCurrManeuverState();
    if (state != nullptr) {
        if (state->GetManeuverIt()->GetPortionHighway()) {
            distance = 300;
        }
    }

    if (junction_view_state->curr_distance >= 0 && junction_view_state->curr_distance <= distance) {
        guide_data.junction_view_state = junction_view_state;
        return true;
    }

    guide_data.junction_view_state = nullptr;
    return false;
}

bool NavStateTracker::DoGuideTollStations(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    ManeuverTollStationState * man_toll_station_state = state_manager_.GetCurrManeuverStationState();
    if (man_toll_station_state == nullptr) {
        GUIDE_ASSERT(false);
        return false;
    }

    const TollStationState *toll_station_state = man_toll_station_state->GetNextTollStation();
    if (toll_station_state == nullptr) {
        return false;
    }

    // TODO: 收费站距离播报提醒，待补充
    float distance = 300;
    ManeuverState *state = state_manager_.GetCurrManeuverState();
    if (state != nullptr) {
        if (state->GetManeuverIt()->GetPortionHighway()) {
            distance = 500;
        }
    }

    if (toll_station_state->curr_distance >= 0 && toll_station_state->curr_distance <= distance) {
        guide_data.toll_station_state = toll_station_state;
        return true;
    }

    guide_data.toll_station_state = nullptr;
    return false;
}

bool NavStateTracker::DoGuideNavBoard(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    // mode
    guide_data.mode = mode_;
    guide_data.path_id = path_manager_->GetMainPathId();

    // distance
    guide_data.path_remain.dis = path_manager_->GetEnhancePathResult()->GetTotalLength() - state_manager_.GetDriveDistance();
    GUIDE_ASSERT(guide_data.path_remain.dis > 0);

    if (guide_data.path_remain.dis < 0) {
        guide_data.path_remain.dis = 0;
    }

    // time
    ManeuverState *cur_state = state_manager_.GetCurrManeuverState();
    if (cur_state != nullptr) {
        guide_data.path_remain.sec = cur_state->GetTimeToENode();

        for (auto it = ++cur_state->GetManeuverIt(); it != path_manager_->GetManeuvers().end(); ++it) {
            guide_data.path_remain.sec += it->GetBasicTime();
        }

    } else {
        GUIDE_ASSERT(cur_state !=nullptr);
        GUIDE_LOG_ERROR("cur_state is nullptr .");
        guide_data.path_remain.sec = 0;
    }

    // road name
    DirectedEdge *edge = path_manager_->GetEnhancePathResult()->GetEdge(vehicle_pos.GetPathLinkIndex());
    if (edge == nullptr) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("invalid path link index");
    } else {
        auto names = edge->GetStreetNames();
        if (names != nullptr && !names->empty()) {
            guide_data.road_name = names->front()->Value();
        }
    }

    ManeuverState *next_man_state = state_manager_.GetNextManeuverState();
    if (next_man_state != nullptr) {
        ManeuverPoint guide_point;
        guide_point.type = next_man_state->GetManeuverIt()->GetType();
        guide_point.distance = next_man_state->GetDistanceToSNode();
        guide_data.next_guide_points.emplace_back(guide_point);
    }
    return true;
}


bool NavStateTracker::DoGuideFacilities(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    ManeuverFacilityState *man_facility_state = state_manager_.GetCurrManeuverFacilityState();
    if (man_facility_state == nullptr) {
        GUIDE_ASSERT(false);
        return false;
    }

    const FacilityState *next_state = man_facility_state->GetNextFacility();
    if (next_state == nullptr) {
        return false;
    }

    if (next_state->curr_distance >= 0 && next_state->curr_distance <= 200) {
        guide_data.facility_state = next_state;
        return true;
    } else {
        guide_data.facility_state = nullptr;
    }
    return false;
}


}  // namespace guide
}  // namespace aurora
/* EOF */
