#include "guidance/src/guide/navigation/voice_play_policy.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

bool VoicePlayPolicy::DoPlay(ManeuverState *curr_man_state, ManeuverState *next_man_state, GuideData &guide_data) {
    
    GUIDE_ASSERT(curr_man_state != nullptr);

    if (curr_man_state == nullptr) {
        return false;
    }

    if (curr_man_state != nullptr) {
        if (IsPassed(guide_data.pos, curr_man_state)) {
            if (!curr_man_state->GetPostInstruction().empty() && !curr_man_state->IsPostInstructionTag()) {
                guide_data.voice_text = curr_man_state->GetPostInstructionWithTag();
                GUIDE_ASSERT(!guide_data.voice_text.empty());
                return true;
            }
        } else {
            if (IsOverFar(guide_data.pos, curr_man_state)) {
                return false;
            }
        }
    }

    if (next_man_state != nullptr) {
        if (next_man_state->GetManeuverIt()->IsDestinationType()) {
            if (IsArrived(guide_data.pos, curr_man_state) && !next_man_state->IsArriveInstructionTag()) {
                guide_data.voice_text = next_man_state->GetArriveInstructionWithTag();
                guide_data.is_arrive_dest = true;

                if (curr_man_state != nullptr) {
                    curr_man_state->GetPostInstructionWithTag();
                }

                GUIDE_ASSERT(!guide_data.voice_text.empty());
                return true;
            }
        }

        if (IsCloseTo(guide_data.pos, curr_man_state)) {
            if (!next_man_state->IsCloseToInstructionTag()) {
                guide_data.voice_text = next_man_state->GetCloseToInstructionWithTag();

                if (curr_man_state != nullptr) {
                    curr_man_state->GetPostInstructionWithTag();
                }

                GUIDE_ASSERT(!guide_data.voice_text.empty());
                return true;
            }
        }

        if (IsNear(guide_data.pos, curr_man_state)) {
            if (!next_man_state->IsNearInstructionTag()) {
                guide_data.voice_text = next_man_state->GetNearInstructionWithTag();
                if (curr_man_state != nullptr) {
                    curr_man_state->GetPostInstructionWithTag();
                }

                GUIDE_ASSERT(!guide_data.voice_text.empty());
                return true;
            }
        }

        if (IsMiddle(guide_data.pos, curr_man_state)) {
            if (!next_man_state->IsMiddleInstructionTag()) {
                guide_data.voice_text = next_man_state->GetMiddleInstructionWithTag();
                if (curr_man_state != nullptr) {
                    curr_man_state->GetPostInstructionWithTag();
                }

                GUIDE_ASSERT(!guide_data.voice_text.empty());
                return true;
            }
        }

        if (IsFar(guide_data.pos, curr_man_state)) {
            if (!next_man_state->IsFarInstructionTag()) {
                guide_data.voice_text = curr_man_state->GetFarInstructionWithTag();
                next_man_state->GetFarInstructionWithTag();
                if (curr_man_state != nullptr) {
                    curr_man_state->GetPostInstructionWithTag();
                }

                GUIDE_ASSERT(!guide_data.voice_text.empty());
                return true;
            }
        }
    }
    return false;
}

bool VoicePlayPolicy::IsOverFar(const VehiclePosition &ccp, ManeuverState *state) const {
    if (state == nullptr) {
        return false;
    }

    if (ccp.IsHighway()) {
        return (state->GetDistanceToENode() > 3000);
    }
    return (state->GetDistanceToENode() > 800);
}

bool VoicePlayPolicy::IsFar(const VehiclePosition &ccp, ManeuverState *state) const {
    if (state == nullptr) {
        return false;
    }

    if (ccp.IsHighway()) {
        return (state->GetDistanceToENode() <= 3000) && (state->GetDistanceToSNode() < -30);
    }
    return (state->GetDistanceToENode() <= 800) && (state->GetDistanceToSNode() < -30);
}

bool VoicePlayPolicy::IsMiddle(const VehiclePosition &ccp, ManeuverState *state) const {
    if (state == nullptr) {
        return false;
    }

    if (ccp.IsHighway()) {
        return (state->GetDistanceToENode() <= 1000) && (state->GetDistanceToSNode() < -30);
    }
    return (state->GetDistanceToENode() <= 300) && (state->GetDistanceToSNode() < -30);
}

bool VoicePlayPolicy::IsNear(const VehiclePosition &ccp, ManeuverState *state) const {
    if (state == nullptr) {
        return false;
    }

    if (ccp.IsHighway()) {
        return (state->GetDistanceToENode() <= 500) && (state->GetDistanceToSNode() < -30);
    }
    return (state->GetDistanceToENode() <= 100) && (state->GetDistanceToSNode() < -30);
}

bool VoicePlayPolicy::IsCloseTo(const VehiclePosition &ccp, ManeuverState *state) const {
    if (state == nullptr) {
        return false;
    }

    if (ccp.IsHighway()) {
        return (state->GetDistanceToENode() <= 100) && (state->GetDistanceToSNode() < -30);
    }
    return (state->GetDistanceToENode() <= 40) && (state->GetDistanceToSNode() <= -30) || 
            (state->GetDistanceToENode() <= 30);
}


bool VoicePlayPolicy::IsPassed(const VehiclePosition &ccp, ManeuverState *state) const {
    if (state == nullptr) {
        return false;
    }

    if (ccp.IsHighway()) {
        return (state->GetDistanceToSNode() < -60);        
    }
    return (state->GetDistanceToSNode() < -40);
}

bool VoicePlayPolicy::IsArrived(const VehiclePosition &ccp, ManeuverState *state) const {
    if (state == nullptr) {
        return false;
    }

    return (state->GetDistanceToSNode() < -20 && state->GetDistanceToENode() <= 30) || 
            (state->GetDistanceToENode() < 20);
}

}  // namespace guide
}  // namespace aurora
/* EOF */
