#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_NAVIGATION_VOICE_PLAY_POLICY_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_NAVIGATION_VOICE_PLAY_POLICY_H

#include <cstdint>
#include <string>
#include "guidance/src/guide/navigation/data_state_manager.h"
#include "guidance/src/common/guide_data.h"

namespace aurora {
namespace guide {

class VoicePlayPolicy {
public:
    VoicePlayPolicy() = default;
    virtual ~VoicePlayPolicy() = default;

    virtual bool DoPlay(ManeuverState *curr_man_state, ManeuverState *next_man_state, GuideData &guide_data);

    bool IsOverFar(const VehiclePosition &ccp, ManeuverState *state) const;
    bool IsFar(const VehiclePosition &ccp, ManeuverState *state) const;
    bool IsMiddle(const VehiclePosition &ccp, ManeuverState *state) const;
    bool Is<PERSON>ear(const VehiclePosition &ccp, ManeuverState *state) const;
    bool IsCloseTo(const VehiclePosition &ccp, ManeuverState *state) const;
    bool IsPassed(const VehiclePosition &ccp, ManeuverState *state) const;
    bool IsArrived(const VehiclePosition &ccp, ManeuverState *state) const;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_NAVIGATION_VOICE_PLAY_POLICY_H
/* EOF */
