#include "guidance/src/maker/narrative/narrative_builder.h"
#include "boost/algorithm/string/replace.hpp"
#include "guidance/src/maker/narrative/narrative_dictionary.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/data/data_manager.h"
#include "guidance/src/maker/narrative/multi_cue_builder.h"
#include "errorcode.h"



namespace aurora {
namespace guide{

NarrativeBuilder::NarrativeBuilder(GuidanceOptions *options, 
                                   PathDataManagerPtr path_manager,
                                   NarrativeDictionaryPtr dictionary)
: articulated_preposition_enabled_(false)
, options_(*options)
, dictionary_(dictionary)
, path_data_manager_(path_manager) {
    enhance_path_ = path_manager->GetEnhancePathResult();
}

int32_t NarrativeBuilder::Build(std::list<Maneuver> &maneuvers) {
    Maneuver *prev_maneuver = nullptr;

    for (auto &maneuver : maneuvers) {
        switch(maneuver.GetType()) {
        case ManeuverType::kTypeStart:
        case ManeuverType::kTypeStartLeft:
        case ManeuverType::kTypeStartRight:  {
            // Set instruction
            maneuver.SetInstruction(FormStartInstruction(maneuver));

            // Set verbal succinct transition instruction
            VerbalTextUpdator succi_updator;
            maneuver.SetVerbalSuccinctInstruction(
                FormVerbalSuccinctStartTransitionInstruction(succi_updator, maneuver));
            maneuver.SetVerbalSuccinctInstructionUpdator(std::move(succi_updator));

            // Set verbal pre transition instruction
            VerbalTextUpdator pre_updator;
            maneuver.SetVerbalPreInstruction(FormVerbalStartInstruction(pre_updator, maneuver));
            maneuver.SetVerbalPreInstructionUpdator(std::move(pre_updator));

            // Set verbal post transition instruction
            VerbalTextUpdator post_updator;
            maneuver.SetVerbalPostInstruction(
                FormVerbalPostTransitionInstruction(post_updator, maneuver, maneuver.HasBeginStreetNames()));
            maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
            
            break;
        }

        case ManeuverType::kTypeDestination:
        case ManeuverType::kTypeDestinationLeft:
        case ManeuverType::kTypeDestinationRight: {
            // Set instruction
            maneuver.SetInstruction(FormDestinationInstruction(maneuver));

            // Set verbal transition alert instruction
            maneuver.SetVerbalAlertInstruction(
            FormVerbalAlertDestinationInstruction(maneuver));

            // Set verbal pre transition instruction
            maneuver.SetVerbalPreInstruction(FormVerbalDestinationInstruction(maneuver));
            break;
        }

        case ManeuverType::kTypeSlightLeft:
        case ManeuverType::kTypeSlightRight:
        case ManeuverType::kTypeLeft:
        case ManeuverType::kTypeRight:
        case ManeuverType::kTypeSharpLeft:
        case ManeuverType::kTypeSharpRight: {
            // Set instruction
            maneuver.SetInstruction(FormTurnInstruction(maneuver));

            // Set verbal succinct transition instruction
            maneuver.SetVerbalSuccinctInstruction(
                FormVerbalSuccinctTurnTransitionInstruction(maneuver));

            // Set verbal transition alert instruction
            maneuver.SetVerbalAlertInstruction(FormVerbalAlertTurnInstruction(maneuver));

            // Set verbal pre transition instruction
            maneuver.SetVerbalPreInstruction(FormVerbalTurnInstruction(maneuver));

            // Set verbal post transition instruction
            VerbalTextUpdator post_updator;
            maneuver.SetVerbalPostInstruction(
                FormVerbalPostTransitionInstruction(post_updator, maneuver, maneuver.HasBeginStreetNames()));
            maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
            break;
        }

        case ManeuverType::kTypeLeftUTurn:
        case ManeuverType::kTypeRightUTurn: {
            // Set instruction
            maneuver.SetInstruction(FormUturnInstruction(maneuver));

            // Set verbal succinct transition instruction
            maneuver.SetVerbalSuccinctInstruction(
                FormVerbalSuccinctUturnTransitionInstruction(maneuver));

            // Set verbal transition alert instruction
            maneuver.SetVerbalAlertInstruction(FormVerbalAlertUturnInstruction(maneuver));

            // Set verbal pre transition instruction
            maneuver.SetVerbalPreInstruction(FormVerbalUturnInstruction(maneuver));

            // Set verbal post transition instruction
            VerbalTextUpdator post_updator;
            maneuver.SetVerbalPostInstruction(
                FormVerbalPostTransitionInstruction(post_updator, maneuver));
            maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
            break;
        }

        case ManeuverType::kTypeStraightToRamp: {
            // Set instruction
            maneuver.SetInstruction(FormRampStraightInstruction(maneuver));

            // Set verbal transition alert instruction
            maneuver.SetVerbalAlertInstruction(
                FormVerbalAlertRampStraightInstruction(maneuver));

            // Set verbal pre transition instruction
            maneuver.SetVerbalPreInstruction(FormVerbalRampStraightInstruction(maneuver));

            // Only set verbal post if > min ramp length
            // or contains obvious maneuver
            // or has collapsed merge maneuver
            #if 0 // TODO:
            // TODO: 下面的条件后续得重新适配
            if ((maneuver.GetLengthKilometers() > kVerbalPostMinimumRampLength) /*||
                maneuver.contains_obvious_maneuver() || maneuver.has_collapsed_merge_maneuver()*/) {
            
                // Set verbal post transition instruction
                maneuver.SetVerbalPostInstruction(
                    FormVerbalPostTransitionInstruction(maneuver));
            }
            #else
            VerbalTextUpdator post_updator;
            maneuver.SetVerbalPostInstruction(
                    FormVerbalPostTransitionInstruction(post_updator, maneuver));
            maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
            #endif

            break;
        }

        case ManeuverType::kTypeLeftToRamp:
        case ManeuverType::kTypeRightToRamp: {
            // Set instruction
            maneuver.SetInstruction(FormRampInstruction(maneuver));

            // Set verbal transition alert instruction
            maneuver.SetVerbalAlertInstruction(FormVerbalAlertRampInstruction(maneuver));

            // Set verbal pre transition instruction
            maneuver.SetVerbalPreInstruction(FormVerbalRampInstruction(maneuver));

            // Only set verbal post if > min ramp length
            // or contains obvious maneuver
            // or has collapsed merge maneuver
            // TODO: modify later
            if ((maneuver.GetLengthKilometers() > kVerbalPostMinimumRampLength) /* ||
                maneuver.contains_obvious_maneuver() || maneuver.has_collapsed_merge_maneuver()*/) {
                // Set verbal post transition instruction
                VerbalTextUpdator post_updator;
                maneuver.SetVerbalPostInstruction(
                    FormVerbalPostTransitionInstruction(post_updator, maneuver));
                maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
            }
            break;
        }

        case ManeuverType::kTypeExitLeft:
        case ManeuverType::kTypeExitRight: {
            // Set instruction
            maneuver.SetInstruction(FormExitInstruction(maneuver));

            // Set verbal transition alert instruction
            maneuver.SetVerbalAlertInstruction(FormVerbalAlertExitInstruction(maneuver));

            // Set verbal pre transition instruction
            maneuver.SetVerbalPreInstruction(FormVerbalExitInstruction(maneuver));

            // Only set verbal post if > min ramp length
            // or contains obvious maneuver
            // or has collapsed merge maneuver
            // TODO: modify later
            if ((maneuver.GetLengthKilometers() > kVerbalPostMinimumRampLength) /* ||
                maneuver.contains_obvious_maneuver() || maneuver.has_collapsed_merge_maneuver()*/) {
                // Set verbal post transition instruction
                VerbalTextUpdator post_updator;
                maneuver.SetVerbalPostInstruction(
                    FormVerbalPostTransitionInstruction(post_updator, maneuver));
                maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
            }
            break;
        }

        case ManeuverType::kTypeStayLeft:
        case ManeuverType::kTypeStayRight:
        case ManeuverType::kTypeStayStraight: {
            if (maneuver.GetToStayOn()) {
                // Set stay on instruction
                maneuver.SetInstruction(FormKeepToStayOnInstruction(maneuver));

                // Set verbal transition alert instruction
                maneuver.SetVerbalAlertInstruction(
                    FormVerbalAlertKeepToStayOnInstruction(maneuver));

                // Set verbal pre transition instruction
                maneuver.SetVerbalPreInstruction(FormVerbalKeepToStayOnInstruction(maneuver));

                // For a ramp - only set verbal post if > min ramp length
                // TODO: modify later
                if (maneuver.GetRamp() /*&& !maneuver.has_collapsed_merge_maneuver() */) {
                    if (maneuver.GetLengthKilometers() > kVerbalPostMinimumRampLength) {
                        // Set verbal post transition instruction
                        VerbalTextUpdator post_updator;
                        maneuver.SetVerbalPostInstruction(
                            FormVerbalPostTransitionInstruction(post_updator, maneuver));
                        maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
                    }
                } else {
                    // Set verbal post transition instruction
                    VerbalTextUpdator post_updator;
                    maneuver.SetVerbalPostInstruction(
                        FormVerbalPostTransitionInstruction(post_updator, maneuver));
                    maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
                    }
                } else {
                // Set instruction
                maneuver.SetInstruction(FormKeepInstruction(maneuver));

                // Set verbal transition alert instruction
                maneuver.SetVerbalAlertInstruction(FormVerbalAlertKeepInstruction(maneuver));

                // Set verbal pre transition instruction
                maneuver.SetVerbalPreInstruction(FormVerbalKeepInstruction(maneuver));

                // For a ramp - only set verbal post if > min ramp length
                // TODO: modify later
                if (maneuver.GetRamp() /* && !maneuver.has_collapsed_merge_maneuver() */) {
                    if (maneuver.GetLengthKilometers() > kVerbalPostMinimumRampLength) {
                    // Set verbal post transition instruction
                    VerbalTextUpdator post_updator;
                    maneuver.SetVerbalPostInstruction(
                        FormVerbalPostTransitionInstruction(post_updator, maneuver));
                    maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
                    }
                } else {
                    // Set verbal post transition instruction
                    VerbalTextUpdator post_updator;
                    maneuver.SetVerbalPostInstruction(
                        FormVerbalPostTransitionInstruction(post_updator, maneuver));
                    maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
                }
            }
            break;
        }

        case ManeuverType::kTypeMerge:
        case ManeuverType::kTypeMergeToLeft:
        case ManeuverType::kTypeMergeToRight: {
            // Set instruction
            maneuver.SetInstruction(FormMergeInstruction(maneuver));

            // Set verbal succinct transition instruction
            maneuver.SetVerbalAlertInstruction(
                FormVerbalSuccinctMergeTransitionInstruction(maneuver));

            // Set verbal transition alert instruction if previous maneuver
            // is greater than 2 km
            if (prev_maneuver && (prev_maneuver->GetLengthKilometers() >
                                kVerbalAlertMergePriorManeuverMinimumLength)) {
                maneuver.SetVerbalAlertInstruction(FormVerbalAlertMergeInstruction(maneuver));
            }

            // Set verbal pre transition instruction
            maneuver.SetVerbalPreInstruction(FormVerbalMergeInstruction(maneuver));

            // Set verbal post transition instruction
            VerbalTextUpdator post_updator;
            maneuver.SetVerbalPostInstruction(
                FormVerbalPostTransitionInstruction(post_updator, maneuver));
            maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
            break;
        }

        case ManeuverType::kTypeContinue:
        default: {
            // Set instruction
            maneuver.SetInstruction(FormContinueInstruction(maneuver));

            // Set verbal transition alert instruction
            maneuver.SetVerbalAlertInstruction(
                FormVerbalAlertContinueInstruction(maneuver));

            // Set verbal pre transition instruction
            VerbalTextUpdator pre_updator;
            maneuver.SetVerbalPreInstruction(FormVerbalContinueInstruction(pre_updator, maneuver));
            maneuver.SetVerbalPreInstructionUpdator(std::move(pre_updator));

            // Set verbal post transition instruction
            VerbalTextUpdator post_updator;
            maneuver.SetVerbalPostInstruction(
                FormVerbalPostTransitionInstruction(post_updator, maneuver));
            maneuver.SetVerbalPostInstructionUpdator(std::move(post_updator));
            break;
        }
        }

        prev_maneuver = &maneuver;
    }

    MultiCueBuilder multi_cue_builder(dictionary_, options_.distance_unit, this);
    multi_cue_builder.FormVerbalMultiCue(maneuvers);
    
    return ErrorCode::kErrorCodeOk;
}


std::string NarrativeBuilder::FormStartInstruction(Maneuver& maneuver) {
    // "0": "Drive <CARDINAL_DIRECTION>.",
    // "1": "Drive <CARDINAL_DIRECTION> on <STREET_NAMES>.",
    // "2": "Drive <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>. Continue on <STREET_NAMES>."

    std::string instruction;

    instruction.reserve(kInstructionInitialCapacity);

    std::string cardinal_direction = 
        dictionary_->start_subset.cardinal_directions.at(static_cast<int32_t>(maneuver.GetBeginCardinalDirection()));
    
    std::string street_names = FormStreetNames(maneuver.GetStreetNames());
    std::string begin_street_names = FormStreetNames(maneuver.GetBeginStreetNames());

    uint8_t phrase_id = 0;
    if (!street_names.empty()) {
        phrase_id += 1;
    }

    if (!begin_street_names.empty()) {
        phrase_id += 1;
    }

    GUIDE_ASSERT(phrase_id < dictionary_->sharp_subset.phrases.size());

    if (phrase_id < dictionary_->sharp_subset.phrases.size()) {
        instruction = dictionary_->start_subset.phrases.at(std::to_string(phrase_id));
    } else {
        instruction = "";
        GUIDE_LOG_ERROR("dictionary_->sharp_subset.phrases phrase_id is overflow");
    }

    // Replace phrase tags with values
    boost::replace_all(instruction, kCardinalDirectionTag, cardinal_direction);
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kBeginStreetNamesTag, begin_street_names);

    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }
    return instruction;
}

std::string NarrativeBuilder::FormVerbalStartInstruction(VerbalTextUpdator& updator, 
                                                         Maneuver& maneuver,
                                                         uint32_t element_max_count,
                                                         const std::string& delim) {
    // "0": "Drive <CARDINAL_DIRECTION>.",
    // "1": "Drive <CARDINAL_DIRECTION> for <LENGTH>.",
    // "2": "Drive <CARDINAL_DIRECTION> on <STREET_NAMES>.",
    // "3": "Drive <CARDINAL_DIRECTION> on <STREET_NAMES> for <LENGTH>.",
    // "4": "Drive <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Set cardinal_direction value
    std::string cardinal_direction =
        dictionary_->start_verbal_subset.cardinal_directions.at(maneuver.GetBeginCardinalDirection());

    // Set street_names value
    std::string street_names =
        FormStreetNames(maneuver.GetStreetNames(), 
                        element_max_count, delim, maneuver.GetVerbalFormatter());

    // Set begin_street_names value
    std::string begin_street_names =
        FormStreetNames(maneuver.GetBeginStreetNames(), 
                        element_max_count, delim, maneuver.GetVerbalFormatter());

    // Update street names for maneuvers that contain obvious maneuvers
    UpdateObviousManeuverStreetNames(maneuver, begin_street_names, street_names);

    // Determine which phrase to use
    uint8_t phrase_id = 0;

    if (!street_names.empty()) {
        // Increment phrase id for street names
        phrase_id += 2;
    }

    if (!begin_street_names.empty()) {
        // Increment phrase id for begin street names
        phrase_id += 2;
    } else if (maneuver.GetIncludeVerbalPreTransitionLength()) {
        // For non begin street names, increment phrase id for length
        phrase_id += 1;
    }

    // Set instruction to the determined tagged phrase
    GUIDE_ASSERT(phrase_id < dictionary_->start_verbal_subset.phrases.size());
    instruction = dictionary_->start_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kCardinalDirectionTag, cardinal_direction);
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kBeginStreetNamesTag, begin_street_names);

#if 0
    boost::replace_all(instruction, kLengthTag,
                        length_builder_.FormLength(maneuver, dictionary_->start_verbal_subset.metric_lengths,
                                    dictionary_->start_verbal_subset.us_customary_lengths));
#else
    DistanceUnits dist_unit = options_.distance_unit;
    NarrativeDictionaryPtr dictionary = dictionary_;
    updator = [dist_unit, dictionary](const Maneuver *maneuver, 
                                      const std::string& instruction) {
        LengthBuilder length_builder(dist_unit, dictionary);
        std::string result = instruction;
        boost::replace_all(result, kLengthTag,
                        length_builder.FormLength(*maneuver, dictionary->start_verbal_subset.metric_lengths,
                                    dictionary->start_verbal_subset.us_customary_lengths));
        return result;
    };
#endif

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormVerbalSuccinctStartTransitionInstruction(VerbalTextUpdator& updator, Maneuver& maneuver) {
    // "0": "Drive <CARDINAL_DIRECTION>.",
    // "1": "Drive <CARDINAL_DIRECTION> for <LENGTH>.",

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Set cardinal_direction value
    std::string cardinal_direction =
        dictionary_->start_verbal_subset.cardinal_directions.at(maneuver.GetBeginCardinalDirection());

    // Determine which phrase to use
    uint8_t phrase_id = 0;


    if (maneuver.GetIncludeVerbalPreTransitionLength()) {
        // Increment phrase id for length
        phrase_id += 1;
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->start_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kCardinalDirectionTag, cardinal_direction);
#if 0
    boost::replace_all(instruction, kLengthTag,
                        length_builder_.FormLength(maneuver, dictionary_->start_verbal_subset.metric_lengths,
                                    dictionary_->start_verbal_subset.us_customary_lengths));
#else
    DistanceUnits dist_unit = options_.distance_unit;
    NarrativeDictionaryPtr dictionary = dictionary_;
    updator = [dist_unit, dictionary](const Maneuver *maneuver, 
                                      const std::string& instruction) {
        LengthBuilder length_builder(dist_unit, dictionary);
        std::string result = instruction;
        boost::replace_all(result, kLengthTag,
                        length_builder.FormLength(*maneuver, dictionary->start_verbal_subset.metric_lengths,
                                    dictionary->start_verbal_subset.us_customary_lengths));
        return result;
    };
#endif

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

  return instruction;
}

std::string NarrativeBuilder::FormVerbalPostTransitionInstruction(VerbalTextUpdator& updator, 
                                                                  Maneuver& maneuver,
                                                                  bool include_street_names,
                                                                  uint32_t element_max_count,
                                                                  const std::string& delim) {
  
    // "0": "Continue for <LENGTH>.",
    // "1": "Continue on <STREET_NAMES> for <LENGTH>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names if maneuver does not contain an obvious maneuver
    std::string street_names;
    if (!maneuver.GetContainObviousManeuver() && !maneuver.GetHasLongStreetName()) {
        // Use the maneuver roundabout_exit_street_names
        // if the maneuver has a combined enter/exit roundabout instruction
        // otherwise use the maneuver street names

        if (maneuver.IsRoundabout()) {
            GUIDE_ASSERT(false);
            GUIDE_LOG_ERROR("not realize ...");
        }

        street_names =
            FormStreetNames(maneuver.GetStreetNames(),
                            element_max_count, delim, maneuver.GetVerbalFormatter());
    }

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    if (include_street_names && !street_names.empty()) {
        phrase_id = 1;
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->post_transition_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
#if 0
    boost::replace_all(instruction, kLengthTag,
                        length_builder_.FormLength(maneuver, dictionary_->post_transition_verbal_subset.metric_lengths,
                                    dictionary_->post_transition_verbal_subset.us_customary_lengths));
#else
    DistanceUnits dist_unit = options_.distance_unit;
    NarrativeDictionaryPtr dictionary = dictionary_;
    updator = [dist_unit, dictionary](const Maneuver *maneuver, 
                                      const std::string& instruction) {
        LengthBuilder length_builder(dist_unit, dictionary);
        std::string result = instruction;
        boost::replace_all(result, kLengthTag,
                        length_builder.FormLength(*maneuver, dictionary->post_transition_verbal_subset.metric_lengths,
                                    dictionary->post_transition_verbal_subset.us_customary_lengths));
        return result;
    };
#endif
    boost::replace_all(instruction, kStreetNamesTag, street_names);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }
    return instruction;
}

std::string NarrativeBuilder::FormRampInstruction(Maneuver& maneuver,
                                                  bool limit_by_consecutive_count,
                                                  uint32_t element_max_count) {
    // "0": "Take the ramp on the <RELATIVE_DIRECTION>.",
    // "1": "Take the <BRANCH_SIGN> ramp on the <RELATIVE_DIRECTION>.",
    // "2": "Take the ramp on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "3": "Take the <BRANCH_SIGN> ramp on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "4": "Take the <NAME_SIGN> ramp on the <RELATIVE_DIRECTION>.",
    // "5": "Turn <RELATIVE_DIRECTION> to take the ramp.",
    // "6": "Turn <RELATIVE_DIRECTION> to take the <BRANCH_SIGN> ramp.",
    // "7": "Turn <RELATIVE_DIRECTION> to take the ramp toward <TOWARD_SIGN>.",
    // "8": "Turn <RELATIVE_DIRECTION> to take the <BRANCH_SIGN> ramp toward <TOWARD_SIGN>.",
    // "9": "Turn <RELATIVE_DIRECTION> to take the <NAME_SIGN> ramp."
    // "10": "Take the ramp.",
    // "11": "Take the <BRANCH_SIGN> ramp.",
    // "12": "Take the ramp toward <TOWARD_SIGN>.",
    // "13": "Take the <BRANCH_SIGN> ramp toward <TOWARD_SIGN>.",
    // "14": "Take the <NAME_SIGN> ramp."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string exit_branch_sign;
    std::string exit_toward_sign;
    std::string exit_name_sign;

    // Determine if turn, else it's a "Take" instruction
    if ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kRight) ||
        (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kLeft)) {
        phrase_id = 5;
        // Determine if driving side matches relative direction
    } else if (((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepRight) &&
                maneuver.GetDriveOnRight()) ||
                ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepLeft) &&
                !maneuver.GetDriveOnRight())) {
        phrase_id = 10;
    }

    // TODO: modify later
#if 0
    if (maneuver.HasExitBranchSign()) {
        phrase_id += 1;
        // Assign branch sign
        exit_branch_sign =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count);
    }
    if (maneuver.HasExitTowardSign()) {
        phrase_id += 2;
        // Assign toward sign
        exit_toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count);
    }
    if (maneuver.HasExitNameSign() && !maneuver.HasExitBranchSign() && !maneuver.HasExitTowardSign()) {
        phrase_id += 4;
        // Assign name sign
        exit_name_sign =
            maneuver.signs().GetExitNameString(element_max_count, limit_by_consecutive_count);
    }
#endif 

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->ramp_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag,
                        FormRelativeTwoDirection(maneuver.GetType(),
                                                dictionary_->ramp_subset.relative_directions));
    boost::replace_all(instruction, kBranchSignTag, exit_branch_sign);
    boost::replace_all(instruction, kTowardSignTag, exit_toward_sign);
    boost::replace_all(instruction, kNameSignTag, exit_name_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertRampInstruction(Maneuver& maneuver,
                                                             bool limit_by_consecutive_count,
                                                             uint32_t element_max_count,
                                                             const std::string& delim) {
    // "0": "Take the ramp on the <RELATIVE_DIRECTION>.",
    // "1": "Take the <BRANCH_SIGN> ramp on the <RELATIVE_DIRECTION>.",
    // "2": "Take the ramp on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "3": "Take the <BRANCH_SIGN> ramp on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "4": "Take the <NAME_SIGN> ramp on the <RELATIVE_DIRECTION>.",
    // "5": "Turn <RELATIVE_DIRECTION> to take the ramp.",
    // "6": "Turn <RELATIVE_DIRECTION> to take the <BRANCH_SIGN> ramp.",
    // "7": "Turn <RELATIVE_DIRECTION> to take the ramp toward <TOWARD_SIGN>.",
    // "8": "Turn <RELATIVE_DIRECTION> to take the <BRANCH_SIGN> ramp toward <TOWARD_SIGN>.",
    // "9": "Turn <RELATIVE_DIRECTION> to take the <NAME_SIGN> ramp."
    // "10": "Take the ramp.",
    // "11": "Take the <BRANCH_SIGN> ramp.",
    // "12": "Take the ramp toward <TOWARD_SIGN>.",
    // "13": "Take the <BRANCH_SIGN> ramp toward <TOWARD_SIGN>.",
    // "14": "Take the <NAME_SIGN> ramp."

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string exit_branch_sign;
    std::string exit_toward_sign;
    std::string exit_name_sign;

    // Determine if turn, else it's a "Take" instruction
    if ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kRight) ||
        (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kLeft)) {
        phrase_id = 5;
        // Determine if driving side matches relative direction
    } else if (((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepRight) &&
                maneuver.GetDriveOnRight()) ||
                ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepLeft) &&
                !maneuver.GetDriveOnRight())) {
        phrase_id = 10;
    }

    // TODO: modify later
#if 0
  if (maneuver.HasExitBranchSign()) {
    phrase_id += 1;
    // Assign branch sign
    exit_branch_sign =
        maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                             maneuver.verbal_formatter(), &markup_formatter_);
  } else if (maneuver.HasExitTowardSign()) {
    phrase_id += 2;
    // Assign toward sign
    exit_toward_sign =
        maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count, delim,
                                             maneuver.verbal_formatter(), &markup_formatter_);
  } else if (maneuver.HasExitNameSign()) {
    phrase_id += 4;
    // Assign name sign
    exit_name_sign =
        maneuver.signs().GetExitNameString(element_max_count, limit_by_consecutive_count, delim,
                                           maneuver.verbal_formatter(), &markup_formatter_);
  }
#endif

  return FormVerbalRampInstruction(phrase_id,
                                   FormRelativeTwoDirection(maneuver.GetType(),
                                                            dictionary_->ramp_verbal_subset
                                                                .relative_directions),
                                   exit_branch_sign, exit_toward_sign, exit_name_sign);
}

std::string NarrativeBuilder::FormVerbalRampInstruction(Maneuver& maneuver,
                                                        bool limit_by_consecutive_count,
                                                        uint32_t element_max_count,
                                                        const std::string& delim) {
    // "0": "Take the ramp on the <RELATIVE_DIRECTION>.",
    // "1": "Take the <BRANCH_SIGN> ramp on the <RELATIVE_DIRECTION>.",
    // "2": "Take the ramp on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "3": "Take the <BRANCH_SIGN> ramp on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "4": "Take the <NAME_SIGN> ramp on the <RELATIVE_DIRECTION>.",
    // "5": "Turn <RELATIVE_DIRECTION> to take the ramp.",
    // "6": "Turn <RELATIVE_DIRECTION> to take the <BRANCH_SIGN> ramp.",
    // "7": "Turn <RELATIVE_DIRECTION> to take the ramp toward <TOWARD_SIGN>.",
    // "8": "Turn <RELATIVE_DIRECTION> to take the <BRANCH_SIGN> ramp toward <TOWARD_SIGN>.",
    // "9": "Turn <RELATIVE_DIRECTION> to take the <NAME_SIGN> ramp."
    // "10": "Take the ramp.",
    // "11": "Take the <BRANCH_SIGN> ramp.",
    // "12": "Take the ramp toward <TOWARD_SIGN>.",
    // "13": "Take the <BRANCH_SIGN> ramp toward <TOWARD_SIGN>.",
    // "14": "Take the <NAME_SIGN> ramp."

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string exit_branch_sign;
    std::string exit_toward_sign;
    std::string exit_name_sign;

    // Determine if turn, else it's a "Take" instruction
    if ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kRight) ||
        (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kLeft)) {
        phrase_id = 5;
        // Determine if driving side matches relative direction
    } else if (((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepRight) &&
                maneuver.GetDriveOnRight()) ||
                ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepLeft) &&
                !maneuver.GetDriveOnRight())) {
        phrase_id = 10;
    }

    // TODO: modify later
#if 0
    if (maneuver.HasExitBranchSign()) {
        phrase_id += 1;
        // Assign branch sign
        exit_branch_sign =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    }
    if (maneuver.HasExitTowardSign()) {
        phrase_id += 2;
        // Assign toward sign
        exit_toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    }
    if (maneuver.HasExitNameSign() && !maneuver.HasExitBranchSign() && !maneuver.HasExitTowardSign()) {
        phrase_id += 4;
        // Assign name sign
        exit_name_sign =
            maneuver.signs().GetExitNameString(element_max_count, limit_by_consecutive_count, delim,
                                            maneuver.verbal_formatter(), &markup_formatter_);
    }
#endif

    return FormVerbalRampInstruction(phrase_id,
                                    FormRelativeTwoDirection(maneuver.GetType(),
                                                                dictionary_->ramp_verbal_subset
                                                                    .relative_directions),
                                    exit_branch_sign, exit_toward_sign, exit_name_sign);
}

std::string NarrativeBuilder::FormVerbalRampInstruction(uint8_t phrase_id,
                                                        const std::string& relative_dir,
                                                        const std::string& exit_branch_sign,
                                                        const std::string& exit_toward_sign,
                                                        const std::string& exit_name_sign) {

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->ramp_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag, relative_dir);
    boost::replace_all(instruction, kBranchSignTag, exit_branch_sign);
    boost::replace_all(instruction, kTowardSignTag, exit_toward_sign);
    boost::replace_all(instruction, kNameSignTag, exit_name_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormExitInstruction(Maneuver& maneuver,
                                                  bool limit_by_consecutive_count,
                                                  uint32_t element_max_count) {
    // "0": "Take the exit on the <RELATIVE_DIRECTION>.",
    // "1": "Take exit <NUMBER_SIGN> on the <RELATIVE_DIRECTION>.",
    // "2": "Take the <BRANCH_SIGN> exit on the <RELATIVE_DIRECTION>.",
    // "3": "Take exit <NUMBER_SIGN> on the <RELATIVE_DIRECTION> onto <BRANCH_SIGN>.",
    // "4": "Take the exit on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "5": "Take exit <NUMBER_SIGN> on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "6": "Take the <BRANCH_SIGN> exit on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "7": "Take exit <NUMBER_SIGN> on the <RELATIVE_DIRECTION> onto <BRANCH_SIGN> toward
    // <TOWARD_SIGN>.", "8": "Take the <NAME_SIGN> exit on the <RELATIVE_DIRECTION>.", "10": "Take the
    // <NAME_SIGN> exit on the <RELATIVE_DIRECTION> onto <BRANCH_SIGN>.", "12": "Take the <NAME_SIGN>
    // exit on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "14": "Take the <NAME_SIGN> exit on the
    // <RELATIVE_DIRECTION> onto <BRANCH_SIGN> toward <TOWARD_SIGN>."
    // "15": "Take the exit.",
    // "16": "Take exit <NUMBER_SIGN>.",
    // "17": "Take the <BRANCH_SIGN> exit.",
    // "18": "Take exit <NUMBER_SIGN> onto <BRANCH_SIGN>.",
    // "19": "Take the exit toward <TOWARD_SIGN>.",
    // "20": "Take exit <NUMBER_SIGN> toward <TOWARD_SIGN>.",
    // "21": "Take the <BRANCH_SIGN> exit toward <TOWARD_SIGN>.",
    // "22": "Take exit <NUMBER_SIGN> onto <BRANCH_SIGN> toward <TOWARD_SIGN>.",
    // "23": "Take the <NAME_SIGN> exit.",
    // "25": "Take the <NAME_SIGN> exit onto <BRANCH_SIGN>.",
    // "27": "Take the <NAME_SIGN> exit toward <TOWARD_SIGN>.",
    // "29": "Take the <NAME_SIGN> exit onto <BRANCH_SIGN> toward <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string exit_number_sign;
    std::string exit_branch_sign;
    std::string exit_toward_sign;
    std::string exit_name_sign;

    // Determine if driving side matches relative direction
    if (((maneuver.GetType() == ManeuverType::kTypeExitRight) && maneuver.GetDriveOnRight()) ||
        ((maneuver.GetType() == ManeuverType::kTypeExitLeft) && !maneuver.GetDriveOnRight())) {
        phrase_id = 15;
    }

    // TODO: modify later
#if 0
    if (maneuver.HasExitNumberSign()) {
        phrase_id += 1;
        // Assign number sign
        exit_number_sign = maneuver.signs().GetExitNumberString();
    }
    if (maneuver.HasExitBranchSign()) {
        phrase_id += 2;
        // Assign branch sign
        exit_branch_sign =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count);
    }
    if (maneuver.HasExitTowardSign()) {
        phrase_id += 4;
        // Assign toward sign
        exit_toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count);
    }
    if (maneuver.HasExitNameSign() && !maneuver.HasExitNumberSign()) {
        phrase_id += 8;
        // Assign name sign
        exit_name_sign =
            maneuver.signs().GetExitNameString(element_max_count, limit_by_consecutive_count);
    }
#endif

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->exit_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag,
                        FormRelativeTwoDirection(maneuver.GetType(),
                                                dictionary_->exit_subset.relative_directions));
    boost::replace_all(instruction, kNumberSignTag, exit_number_sign);
    boost::replace_all(instruction, kBranchSignTag, exit_branch_sign);
    boost::replace_all(instruction, kTowardSignTag, exit_toward_sign);
    boost::replace_all(instruction, kNameSignTag, exit_name_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertExitInstruction(Maneuver& maneuver,
                                                             bool limit_by_consecutive_count,
                                                             uint32_t element_max_count,
                                                             const std::string& delim) {
    // "0": "Take the exit on the <RELATIVE_DIRECTION>.",
    // "1": "Take exit <NUMBER_SIGN> on the <RELATIVE_DIRECTION>.",
    // "2": "Take the <BRANCH_SIGN> exit on the <RELATIVE_DIRECTION>.",
    // "4": "Take the exit on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "8": "Take the <NAME_SIGN> exit on the <RELATIVE_DIRECTION>.",
    // "15": "Take the exit.",
    // "16": "Take exit <NUMBER_SIGN>.",
    // "17": "Take the <BRANCH_SIGN> exit.",
    // "19": "Take the exit toward <TOWARD_SIGN>.",
    // "23": "Take the <NAME_SIGN> exit.",

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string exit_number_sign;
    std::string exit_branch_sign;
    std::string exit_toward_sign;
    std::string exit_name_sign;

    // Determine if driving side matches relative direction
    if (((maneuver.GetType() == ManeuverType::kTypeExitRight) && maneuver.GetDriveOnRight()) ||
        ((maneuver.GetType() == ManeuverType::kTypeExitLeft) && !maneuver.GetDriveOnRight())) {
        phrase_id = 15;
    }

    // TODO: modify later
#if 0
    if (maneuver.HasExitNumberSign()) {
        phrase_id += 1;
        // Assign number sign
        exit_number_sign =
            maneuver.signs().GetExitNumberString(0, false, delim, maneuver.verbal_formatter(),
                                                &markup_formatter_);
    } else if (maneuver.HasExitBranchSign()) {
        phrase_id += 2;
        // Assign branch sign
        exit_branch_sign =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    } else if (maneuver.HasExitTowardSign()) {
        phrase_id += 4;
        // Assign toward sign
        exit_toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    } else if (maneuver.HasExitNameSign()) {
        phrase_id += 8;
        // Assign name sign
        exit_name_sign =
            maneuver.signs().GetExitNameString(element_max_count, limit_by_consecutive_count, delim,
                                            maneuver.verbal_formatter(), &markup_formatter_);
    }
#endif

    return FormVerbalExitInstruction(phrase_id,
                                    FormRelativeTwoDirection(maneuver.GetType(),
                                                                dictionary_->exit_verbal_subset
                                                                    .relative_directions),
                                    exit_number_sign, exit_branch_sign, exit_toward_sign,
                                    exit_name_sign);
}

std::string NarrativeBuilder::FormVerbalExitInstruction(Maneuver& maneuver,
                                                        bool limit_by_consecutive_count,
                                                        uint32_t element_max_count,
                                                        const std::string& delim) {
    // "0": "Take the exit on the <RELATIVE_DIRECTION>.",
    // "1": "Take exit <NUMBER_SIGN> on the <RELATIVE_DIRECTION>.",
    // "2": "Take the <BRANCH_SIGN> exit on the <RELATIVE_DIRECTION>.",
    // "3": "Take exit <NUMBER_SIGN> on the <RELATIVE_DIRECTION> onto <BRANCH_SIGN>.",
    // "4": "Take the exit on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "5": "Take exit <NUMBER_SIGN> on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "6": "Take the <BRANCH_SIGN> exit on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "7": "Take exit <NUMBER_SIGN> on the <RELATIVE_DIRECTION> onto <BRANCH_SIGN> toward
    // <TOWARD_SIGN>.", "8": "Take the <NAME_SIGN> exit on the <RELATIVE_DIRECTION>.", "10": "Take the
    // <NAME_SIGN> exit on the <RELATIVE_DIRECTION> onto <BRANCH_SIGN>.", "12": "Take the <NAME_SIGN>
    // exit on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "14": "Take the <NAME_SIGN> exit on
    // the <RELATIVE_DIRECTION> onto <BRANCH_SIGN> toward <TOWARD_SIGN>."
    // "15": "Take the exit.",
    // "16": "Take exit <NUMBER_SIGN>.",
    // "17": "Take the <BRANCH_SIGN> exit.",
    // "18": "Take exit <NUMBER_SIGN> onto <BRANCH_SIGN>.",
    // "19": "Take the exit toward <TOWARD_SIGN>.",
    // "20": "Take exit <NUMBER_SIGN> toward <TOWARD_SIGN>.",
    // "21": "Take the <BRANCH_SIGN> exit toward <TOWARD_SIGN>.",
    // "22": "Take exit <NUMBER_SIGN> onto <BRANCH_SIGN> toward <TOWARD_SIGN>.",
    // "23": "Take the <NAME_SIGN> exit.",
    // "25": "Take the <NAME_SIGN> exit onto <BRANCH_SIGN>.",
    // "27": "Take the <NAME_SIGN> exit toward <TOWARD_SIGN>.",
    // "29": "Take the <NAME_SIGN> exit onto <BRANCH_SIGN> toward <TOWARD_SIGN>."

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string exit_number_sign;
    std::string exit_branch_sign;
    std::string exit_toward_sign;
    std::string exit_name_sign;

    // Determine if driving side matches relative direction
    if (((maneuver.GetType() == ManeuverType::kTypeExitRight) && maneuver.GetDriveOnRight()) ||
        ((maneuver.GetType() == ManeuverType::kTypeExitLeft) && !maneuver.GetDriveOnRight())) {
        phrase_id = 15;
    }

#if 0
    if (maneuver.HasExitNumberSign()) {
        phrase_id += 1;
        // Assign number sign
        exit_number_sign =
            maneuver.signs().GetExitNumberString(0, false, delim, maneuver.verbal_formatter(),
                                                &markup_formatter_);
    }
    if (maneuver.HasExitBranchSign()) {
        phrase_id += 2;
        // Assign branch sign
        exit_branch_sign =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    }
    if (maneuver.HasExitTowardSign()) {
        phrase_id += 4;
        // Assign toward sign
        exit_toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    }
    if (maneuver.HasExitNameSign() && !maneuver.HasExitNumberSign()) {
        phrase_id += 8;
        // Assign name sign
        exit_name_sign =
            maneuver.signs().GetExitNameString(element_max_count, limit_by_consecutive_count, delim,
                                            maneuver.verbal_formatter(), &markup_formatter_);
    }
#endif

    return FormVerbalExitInstruction(phrase_id,
                                    FormRelativeTwoDirection(maneuver.GetType(),
                                                                dictionary_->exit_verbal_subset
                                                                    .relative_directions),
                                    exit_number_sign, exit_branch_sign, exit_toward_sign,
                                    exit_name_sign);
}

std::string NarrativeBuilder::FormVerbalExitInstruction(uint8_t phrase_id,
                                                        const std::string& relative_dir,
                                                        const std::string& exit_number_sign,
                                                        const std::string& exit_branch_sign,
                                                        const std::string& exit_toward_sign,
                                                        const std::string& exit_name_sign) {

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->exit_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag, relative_dir);
    boost::replace_all(instruction, kNumberSignTag, exit_number_sign);
    boost::replace_all(instruction, kBranchSignTag, exit_branch_sign);
    boost::replace_all(instruction, kTowardSignTag, exit_toward_sign);
    boost::replace_all(instruction, kNameSignTag, exit_name_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }
    return instruction;
}

std::string NarrativeBuilder::FormKeepToStayOnInstruction(Maneuver& maneuver,
                                                          bool limit_by_consecutive_count,
                                                          uint32_t element_max_count) {

    // "0": "Keep <RELATIVE_DIRECTION> to stay on <STREET_NAMES>.",
    // "1": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> to stay on <STREET_NAMES>.",
    // "2": "Keep <RELATIVE_DIRECTION> to stay on <STREET_NAMES> toward <TOWARD_SIGN>.",
    // "3": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> to stay on <STREET_NAMES> toward
    //      <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names =
        FormStreetNames(maneuver.GetStreetNames(), element_max_count);

    // If they exist, process guide toward signs
    // else use exit toward signs
    std::string toward_sign;
    // TODO: modify later
#if 0
    if (maneuver.HasGuideTowardSign()) {
        // Assign guide sign
        toward_sign =
            maneuver.signs().GetGuideTowardString(element_max_count, limit_by_consecutive_count);
    } else if (maneuver.HasExitTowardSign()) {
        toward_sign = maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count);
    }
#endif

    // Determine which phrase to use
    std::string exit_number_sign;
    uint8_t phrase_id = 0;
    // TODO: modify later
#if 0
    if (maneuver.HasExitNumberSign()) {
        phrase_id += 1;
        // Assign number sign
        exit_number_sign = maneuver.signs().GetExitNumberString();
    }
    if (!toward_sign.empty()) {
        phrase_id += 2;
    }
#endif

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->keep_to_stay_on_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag,
                        FormRelativeThreeDirection(maneuver.GetType(), dictionary_->keep_to_stay_on_subset
                                                                        .relative_directions));
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kNumberSignTag, exit_number_sign);
    boost::replace_all(instruction, kTowardSignTag, toward_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertKeepToStayOnInstruction(Maneuver& maneuver,
                                                                     uint32_t element_max_count,
                                                                     const std::string& delim) {

    // "0": "Keep <RELATIVE_DIRECTION> to stay on <STREET_NAMES>.",

    // Assign the street names
    std::string street_names =
        FormStreetNames(maneuver.GetStreetNames(), element_max_count, delim, 
            maneuver.GetVerbalFormatter());

    return FormVerbalKeepToStayOnInstruction(
        0,
        FormRelativeThreeDirection(maneuver.GetType(),
                                    dictionary_->keep_to_stay_on_verbal_subset.relative_directions),
        street_names);
}

std::string NarrativeBuilder::FormVerbalKeepToStayOnInstruction(Maneuver& maneuver,
                                                                bool limit_by_consecutive_count,
                                                                uint32_t element_max_count,
                                                                const std::string& delim) {

    // "0": "Keep <RELATIVE_DIRECTION> to stay on <STREET_NAMES>.",
    // "1": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> to stay on <STREET_NAMES>.",
    // "2": "Keep <RELATIVE_DIRECTION> to stay on <STREET_NAMES> toward <TOWARD_SIGN>.",
    // "3": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> to stay on <STREET_NAMES> toward
    // <TOWARD_SIGN>."

    // Assign the street names
    std::string street_names =
        FormStreetNames(maneuver.GetStreetNames(),
                        element_max_count, delim, maneuver.GetVerbalFormatter());

    // If they exist, process guide toward signs
    // else use exit toward signs
    std::string toward_sign;
    // TODO: modify later
#if 0
    if (maneuver.HasGuideTowardSign()) {
        // Assign guide sign
        toward_sign =
            maneuver.signs().GetGuideTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    } else if (maneuver.HasExitTowardSign()) {
        toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    }
#endif

    // Determine which phrase to use
    std::string exit_number_sign;
    uint8_t phrase_id = 0;
#if 0
    if (maneuver.HasExitNumberSign()) {
        phrase_id += 1;
        // Assign number sign
        exit_number_sign =
            maneuver.signs().GetExitNumberString(0, false, delim, maneuver.verbal_formatter(),
                                                &markup_formatter_);
    }
    if (!toward_sign.empty()) {
        phrase_id += 2;
    }
#endif

  return FormVerbalKeepToStayOnInstruction(
      phrase_id,
      FormRelativeThreeDirection(maneuver.GetType(),
                                 dictionary_->keep_to_stay_on_verbal_subset.relative_directions),
      street_names, exit_number_sign, toward_sign);
}

std::string NarrativeBuilder::FormVerbalKeepToStayOnInstruction(uint8_t phrase_id,
                                                                const std::string& relative_dir,
                                                                const std::string& street_names,
                                                                const std::string& exit_number_sign,
                                                                const std::string& toward_sign) {

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->keep_to_stay_on_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag, relative_dir);
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kNumberSignTag, exit_number_sign);
    boost::replace_all(instruction, kTowardSignTag, toward_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}


std::string NarrativeBuilder::FormMergeInstruction(Maneuver& maneuver,
                                                   bool limit_by_consecutive_count,
                                                   uint32_t element_max_count) {
    // "0": "Merge."
    // "1": "Merge <RELATIVE_DIRECTION>."
    // "2": "Merge onto <STREET_NAMES>."
    // "3": "Merge <RELATIVE_DIRECTION> onto <STREET_NAMES>."
    // "4": "Merge toward <TOWARD_SIGN>."
    // "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names = FormStreetNames(maneuver.GetStreetNames());

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string guide_sign;

    if (!street_names.empty()) {
        // Street names take priority over toward phrase
        phrase_id = 2;
    } // TODO: modify later
    /*else if (maneuver.HasGuideSign()) {
        // Use toward phrase if street names is empty
        phrase_id = 4;
        // Assign guide sign
        guide_sign = maneuver.signs().GetGuideString(element_max_count, limit_by_consecutive_count);
    } */

    // Check for merge relative direction
    std::string relative_direction;
    if ((maneuver.GetType() == ManeuverType::kTypeMergeToLeft) ||
        (maneuver.GetType() == ManeuverType::kTypeMergeToRight)) {
        phrase_id += 1;
        relative_direction =
            FormRelativeTwoDirection(maneuver.GetType(), dictionary_->merge_subset.relative_directions);
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->merge_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag, relative_direction);
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }
    return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertMergeInstruction(Maneuver& maneuver,
                                                              bool limit_by_consecutive_count,
                                                              uint32_t element_max_count,
                                                              const std::string& delim) {
  // "0": "Merge."
  // "1": "Merge <RELATIVE_DIRECTION>."
  // "2": "Merge onto <STREET_NAMES>."
  // "3": "Merge <RELATIVE_DIRECTION> onto <STREET_NAMES>."
  // "4": "Merge toward <TOWARD_SIGN>."
  // "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."

  return FormVerbalMergeInstruction(maneuver, limit_by_consecutive_count, element_max_count, delim);
}

std::string NarrativeBuilder::FormVerbalMergeInstruction(Maneuver& maneuver,
                                                         bool limit_by_consecutive_count,
                                                         uint32_t element_max_count,
                                                         const std::string& delim) {
    // "0": "Merge."
    // "1": "Merge <RELATIVE_DIRECTION>."
    // "2": "Merge onto <STREET_NAMES>."
    // "3": "Merge <RELATIVE_DIRECTION> onto <STREET_NAMES>."
    // "4": "Merge toward <TOWARD_SIGN>."
    // "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names =
        FormStreetNames(maneuver.GetStreetNames(),
                         element_max_count, delim, maneuver.GetVerbalFormatter());

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string guide_sign;

    if (!street_names.empty()) {
        // Street names take priority over toward phrase
        phrase_id = 2;
    } 
    // TODO: modify later
    /* else if (maneuver.HasGuideSign()) {
        // Use toward phrase if street names is empty
        phrase_id = 4;
        // Assign guide sign
        guide_sign = maneuver.signs().GetGuideString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
    } */

    // Check for merge relative direction
    std::string relative_direction;
    if ((maneuver.GetType() == ManeuverType::kTypeMergeToLeft) ||
        (maneuver.GetType() == ManeuverType::kTypeMergeToRight)) {
        phrase_id += 1;
        relative_direction =
            FormRelativeTwoDirection(maneuver.GetType(),
                                    dictionary_->merge_verbal_subset.relative_directions);
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->merge_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag, relative_direction);
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string
NarrativeBuilder::FormVerbalSuccinctMergeTransitionInstruction(Maneuver& maneuver,
                                                               bool limit_by_consecutive_count,
                                                               uint32_t element_max_count,
                                                               const std::string& delim) {
    // "0": "Merge."
    // "1": "Merge <RELATIVE_DIRECTION>."
    // "4": "Merge toward <TOWARD_SIGN>."
    // "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string guide_sign;

#if 0
    if (maneuver.HasGuideSign()) {
        // Use toward phrase if street names is empty
        phrase_id = 4;
        // Assign guide sign
        guide_sign = maneuver.signs().GetGuideString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
    }
#endif

    // Check for merge relative direction
    std::string relative_direction;
    if ((maneuver.GetType() == ManeuverType::kTypeMergeToLeft) ||
        (maneuver.GetType() == ManeuverType::kTypeMergeToRight)) {
        phrase_id += 1;
        relative_direction =
            FormRelativeTwoDirection(maneuver.GetType(),
                                    dictionary_->merge_verbal_subset.relative_directions);
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->merge_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag, relative_direction);
    boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormContinueInstruction(Maneuver& maneuver,
                                                      bool limit_by_consecutive_count,
                                                      uint32_t element_max_count) {
    // "0": "Continue.",
    // "1": "Continue on <STREET_NAMES>."
    // "2": "Continue at <JUNCTION_NAME>."
    // "3": "Continue toward <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names = FormStreetNames(maneuver.GetStreetNames());
    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string junction_name;
    std::string guide_sign;

#if 0
    if (maneuver.HasGuideSign()) {
        // Set the toward phrase - it takes priority over street names and junction name
        phrase_id = 3;
        // Assign guide sign
        guide_sign = maneuver.signs().GetGuideString(element_max_count, limit_by_consecutive_count);
    } else if (maneuver.HasJunctionNameSign()) {
        // Set the junction phrase - it takes priority over street names
        phrase_id = 2;
        // Assign guide sign
        junction_name =
            maneuver.signs().GetJunctionNameString(element_max_count, limit_by_consecutive_count);
    } else if (!street_names.empty()) {
        phrase_id = 1;
    }
#else
    if (!street_names.empty()) {
        phrase_id = 1;
    }
#endif

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->continue_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kJunctionNameTag, junction_name);
    boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertContinueInstruction(Maneuver& maneuver,
                                                                 bool limit_by_consecutive_count,
                                                                 uint32_t element_max_count,
                                                                 const std::string& delim) {
    // "0": "Continue.",
    // "1": "Continue on <STREET_NAMES>."
    // "2": "Continue at <JUNCTION_NAME>."
    // "3": "Continue toward <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names =
        FormStreetNames(maneuver.GetStreetNames(),
                      element_max_count, delim, maneuver.GetVerbalFormatter());

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string junction_name;
    std::string guide_sign;
    // TODO: modify later
#if 0
    if (maneuver.HasGuideSign()) {
        // Set the toward phrase - it takes priority over street names and junction name
        phrase_id = 3;
        // Assign guide sign
        guide_sign = maneuver.signs().GetGuideString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
    } else if (maneuver.HasJunctionNameSign()) {
        // Set the junction phrase - it takes priority over street names
        phrase_id = 2;
        // Assign guide sign
        junction_name =
            maneuver.signs().GetJunctionNameString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    } else if (!street_names.empty()) {
        phrase_id = 1;
    }
#else
    if (!street_names.empty()) {
        phrase_id = 1;
    }
#endif

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->continue_verbal_alert_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kJunctionNameTag, junction_name);
    boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormVerbalContinueInstruction(VerbalTextUpdator& updator,
                                                            Maneuver& maneuver,
                                                            bool limit_by_consecutive_count,
                                                            uint32_t element_max_count,
                                                            const std::string& delim) {
    // "0": "Continue.",
    // "1": "Continue for <LENGTH>.",
    // "2": "Continue on <STREET_NAMES>.",
    // "3": "Continue on <STREET_NAMES> for <LENGTH>."
    // "4": "Continue at <JUNCTION_NAME>."
    // "5": "Continue at <JUNCTION_NAME> for <LENGTH>."
    // "6": "Continue toward <TOWARD_SIGN>."
    // "7": "Continue toward <TOWARD_SIGN> for <LENGTH>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names = 
            FormStreetNames(maneuver.GetStreetNames(),
                        element_max_count, delim, maneuver.GetVerbalFormatter());

    // Determine base phrase
    uint8_t phrase_id = 0;
    std::string junction_name;
    std::string guide_sign;

    // TODO: modify later
#if 0
    if (maneuver.HasGuideSign()) {
        // Set the toward phrase - it takes priority over street names and junction name
        phrase_id = 6;
        // Assign guide sign
        guide_sign = maneuver.signs().GetGuideString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
    } else if (maneuver.HasJunctionNameSign()) {
        // Set the junction phrase - it takes priority over street names
        phrase_id = 4;
        // Assign guide sign
        junction_name =
            maneuver.signs().GetJunctionNameString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    } else if (!street_names.empty()) {
        phrase_id = 2;
    }
#else
    if (!street_names.empty()) {
        phrase_id = 2;
    }
#endif

    if (maneuver.GetIncludeVerbalPreTransitionLength()) {
        // Increment phrase id for length
        phrase_id += 1;
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->continue_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
#if 0
    boost::replace_all(instruction, kLengthTag,
                        length_builder_.FormLength(maneuver, dictionary_->continue_verbal_subset.metric_lengths,
                                    dictionary_->continue_verbal_subset.us_customary_lengths));
#else
    DistanceUnits dist_unit = options_.distance_unit;
    NarrativeDictionaryPtr dictionary = dictionary_;
    updator = [dist_unit, dictionary](const Maneuver *maneuver, 
                                      const std::string& instruction) {
        LengthBuilder length_builder(dist_unit, dictionary);
        std::string result = instruction;
        boost::replace_all(result, kLengthTag,
                        length_builder.FormLength(*maneuver, dictionary->continue_verbal_subset.metric_lengths,
                                    dictionary->continue_verbal_subset.us_customary_lengths));
        return result;
    };
#endif

    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kJunctionNameTag, junction_name);
    boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }
    return instruction;
}


std::string NarrativeBuilder::FormKeepInstruction(Maneuver& maneuver,
                                                  bool limit_by_consecutive_count,
                                                  uint32_t element_max_count) {
    // "0": "Keep <RELATIVE_DIRECTION> at the fork.",
    // "1": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN>.",
    // "2": "Keep <RELATIVE_DIRECTION> to take <STREET_NAMES>.",
    // "3": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> onto <STREET_NAMES>.",
    // "4": "Keep <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "5": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> toward <TOWARD_SIGN>.",
    // "6": "Keep <RELATIVE_DIRECTION> to take <STREET_NAMES> toward <TOWARD_SIGN>.",
    // "7": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> onto <STREET_NAMES> toward
    // <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    std::string street_names;
    std::string exit_number_sign;
    std::string toward_sign;

    // TODO: modify later
#if 0
    // If they exist, process guide signs
    if (maneuver.HasGuideSign()) {
        if (maneuver.HasGuideBranchSign()) {
        street_names =
            maneuver.signs().GetGuideBranchString(element_max_count, limit_by_consecutive_count);
        }
        if (maneuver.HasGuideTowardSign()) {
        // Assign guide sign
        toward_sign =
            maneuver.signs().GetGuideTowardString(element_max_count, limit_by_consecutive_count);
        }
    } else {
        // For ramps with branch sign info - we use the sign info to match what users are seeing
        if (maneuver.ramp() && maneuver.HasExitBranchSign()) {
        street_names =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count);
        } else {
        street_names =
            FormStreetNames(maneuver, maneuver.street_names(),
                            &dictionary_->keep_subset.empty_street_name_labels, true, element_max_count);

        // If street names string is empty and the maneuver has sign branch info
        // then assign the sign branch name to the street names string
        if (street_names.empty() && maneuver.HasExitBranchSign()) {
            street_names =
                maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count);
        }
        }

        // If it exists, process the exit toward sign
        if (maneuver.HasExitTowardSign()) {
        // Assign toward sign
        toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count);
        }
    }
#else
    street_names = FormStreetNames(maneuver.GetStreetNames(), element_max_count);
#endif

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    // TODO: modify later
#if 0
    if (maneuver.HasExitNumberSign()) {
        phrase_id += 1;
        // Assign number sign
        exit_number_sign = maneuver.signs().GetExitNumberString();
    }
#endif

    if (!street_names.empty()) {
        phrase_id += 2;
    }

    if (!toward_sign.empty()) {
        phrase_id += 4;
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->keep_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag,
                        FormRelativeThreeDirection(maneuver.GetType(),
                                                    dictionary_->keep_subset.relative_directions));
    boost::replace_all(instruction, kNumberSignTag, exit_number_sign);
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kTowardSignTag, toward_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

  return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertKeepInstruction(Maneuver& maneuver,
                                                             bool limit_by_consecutive_count,
                                                             uint32_t element_max_count,
                                                             const std::string& delim) {

    // "0": "Keep <RELATIVE_DIRECTION> at the fork.",
    // "1": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN>.",
    // "2": "Keep <RELATIVE_DIRECTION> to take <STREET_NAMES>.",
    // "4": "Keep <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",

    std::string street_names;
    std::string exit_number_sign;
    std::string toward_sign;

#if 0
    // If they exist, process guide signs
    if (maneuver.HasGuideSign()) {
        if (maneuver.HasGuideBranchSign()) {
        street_names =
            maneuver.signs().GetGuideBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
        }
        if (maneuver.HasGuideTowardSign()) {
        // Assign guide sign
        toward_sign =
            maneuver.signs().GetGuideTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
        }
    } else {
        // For ramps with branch sign info - we use the sign info to match what users are seeing
        if (maneuver.ramp() && maneuver.HasExitBranchSign()) {
        street_names =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
        } else {
        // Assign the street names
        street_names = FormStreetNames(maneuver, maneuver.street_names(),
                                        &dictionary_->keep_verbal_subset.empty_street_name_labels, true,
                                        element_max_count, delim, maneuver.verbal_formatter());

        // If street names string is empty and the maneuver has sign branch info
        // then assign the sign branch name to the street names string
        if (street_names.empty() && maneuver.HasExitBranchSign()) {
            street_names =
                maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
        }
        }

        // If it exists, process exit toward sign
        if (maneuver.HasExitTowardSign()) {
        // Assign toward sign
        toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
        }
    }
#else
    // Assign the street names
    street_names = FormStreetNames(maneuver.GetStreetNames(),
                                    element_max_count, delim, maneuver.GetVerbalFormatter());
#endif

    // Determine which phrase to use
    uint8_t phrase_id = 0;
#if 0
    if (maneuver.HasExitNumberSign()) {
        phrase_id += 1;
        // Assign number sign
        exit_number_sign =
            maneuver.signs().GetExitNumberString(0, false, delim, maneuver.verbal_formatter(),
                                                &markup_formatter_);
    } else if (!street_names.empty()) {
        phrase_id += 2;
    } else if (!toward_sign.empty()) {
        phrase_id += 4;
    }
#else
    if (!street_names.empty()) {
        phrase_id += 2;
    }
#endif

    return FormVerbalKeepInstruction(phrase_id,
                                    FormRelativeThreeDirection(maneuver.GetType(),
                                                                dictionary_->keep_verbal_subset
                                                                    .relative_directions),
                                    street_names, exit_number_sign, toward_sign);
}

std::string NarrativeBuilder::FormVerbalKeepInstruction(Maneuver& maneuver,
                                                        bool limit_by_consecutive_count,
                                                        uint32_t element_max_count,
                                                        const std::string& delim) {

    // "0": "Keep <RELATIVE_DIRECTION> at the fork.",
    // "1": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN>.",
    // "2": "Keep <RELATIVE_DIRECTION> to take <STREET_NAMES>.",
    // "3": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> onto <STREET_NAMES>.",
    // "4": "Keep <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.",
    // "5": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> toward <TOWARD_SIGN>.",
    // "6": "Keep <RELATIVE_DIRECTION> to take <STREET_NAMES> toward <TOWARD_SIGN>.",
    // "7": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> onto <STREET_NAMES> toward
    // <TOWARD_SIGN>."

    std::string exit_number_sign;
    std::string toward_sign;
    std::string street_names;

#if 0
    // If they exist, process guide signs
    if (maneuver.HasGuideSign()) {
        if (maneuver.HasGuideBranchSign()) {
        street_names =
            maneuver.signs().GetGuideBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
        }
        if (maneuver.HasGuideTowardSign()) {
        // Assign guide sign
        toward_sign =
            maneuver.signs().GetGuideTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
        }
    } else {
        // For ramps with branch sign info - we use the sign info to match what users are seeing
        if (maneuver.ramp() && maneuver.HasExitBranchSign()) {
        street_names =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
        } else {
        // Assign the street names
        street_names = FormStreetNames(maneuver, maneuver.street_names(),
                                        &dictionary_->keep_verbal_subset.empty_street_name_labels, true,
                                        element_max_count, delim, maneuver.verbal_formatter());

        // If street names string is empty and the maneuver has sign branch info
        // then assign the sign branch name to the street names string
        if (street_names.empty() && maneuver.HasExitBranchSign()) {
            street_names =
                maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
        }
        }

        // If it exists, process exit toward sign
        if (maneuver.HasExitTowardSign()) {
        // Assign toward sign
        toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
        }
    }
#else
    street_names = FormStreetNames(maneuver.GetStreetNames(),
                                    element_max_count, delim, maneuver.GetVerbalFormatter());

#endif

    // Determine which phrase to use
    uint8_t phrase_id = 0;
#if 0
    if (maneuver.HasExitNumberSign()) {
        phrase_id += 1;
        // Assign number sign
        exit_number_sign =
            maneuver.signs().GetExitNumberString(0, false, delim, maneuver.verbal_formatter(),
                                                &markup_formatter_);
    }
#endif

    if (!street_names.empty()) {
        phrase_id += 2;
    }
    if (!toward_sign.empty()) {
        phrase_id += 4;
    }

    return FormVerbalKeepInstruction(phrase_id,
                                    FormRelativeThreeDirection(maneuver.GetType(),
                                                                dictionary_->keep_verbal_subset
                                                                    .relative_directions),
                                    street_names, exit_number_sign, toward_sign);
}

std::string NarrativeBuilder::FormVerbalKeepInstruction(uint8_t phrase_id,
                                                        const std::string& relative_dir,
                                                        const std::string& street_names,
                                                        const std::string& exit_number_sign,
                                                        const std::string& toward_sign) {

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->keep_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag, relative_dir);
    boost::replace_all(instruction, kNumberSignTag, exit_number_sign);
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kTowardSignTag, toward_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}


void NarrativeBuilder::UpdateObviousManeuverStreetNames(const Maneuver& maneuver,
                                                        std::string& begin_street_names,
                                                        std::string& street_names) {
    if (maneuver.GetContainObviousManeuver() && !begin_street_names.empty()) {
        street_names = begin_street_names;
        begin_street_names.clear();
    }
}

std::string NarrativeBuilder::FormDestinationInstruction(Maneuver& maneuver) {
    // "0": "You have arrived at your destination."
    // "1": "You have arrived at <DESTINATION>."
    // "2": "Your destination is on the <RELATIVE_DIRECTION>."
    // "3": "<DESTINATION> is on the <RELATIVE_DIRECTION>."

    uint8_t phrase_id = 0;
    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Determine if location (name or street) exists
    std::string destination;
    
    EnhancePathQueryPtr query = path_data_manager_->GetEnhancePathQuery();

    if (query != nullptr && query->GetDestination() != nullptr) {
        const auto& dest = query->GetDestination()->name;
        if (!dest.empty()) {
            destination = dest;
            phrase_id += 1;
        }
    }

    // Check for side of street relative direction
    std::string relative_direction;
    if (maneuver.GetType() == ManeuverType::kTypeDestinationLeft) {
        phrase_id += 2;
        relative_direction = dictionary_->destination_subset.relative_directions.at(0);
    } else if (maneuver.GetType() == ManeuverType::kTypeDestinationRight) {
        phrase_id += 2;
        relative_direction = dictionary_->destination_subset.relative_directions.at(1);
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->destination_subset.phrases.at(std::to_string(phrase_id));

    if (phrase_id > 0) {
        // Replace phrase tags with values
        boost::replace_all(instruction, kRelativeDirectionTag, relative_direction);
        boost::replace_all(instruction, kDestinationTag, destination);
    }

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }
    return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertDestinationInstruction(Maneuver& maneuver) {
    // "0": "You will arrive at your destination.",
    // "1": "You will arrive at <DESTINATION>.",
    // "2": "Your destination will be on the <RELATIVE_DIRECTION>.",
    // "3": "<DESTINATION> will be on the <RELATIVE_DIRECTION>."

    uint8_t phrase_id = 0;
    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Determine if destination (name or street) exists
    std::string destination;
    EnhancePathQueryPtr query = path_data_manager_->GetEnhancePathQuery();

    if (query != nullptr && query->GetDestination() != nullptr) {
        const auto& dest = query->GetDestination()->name;
        if (!dest.empty()) {
            destination = dest;
            phrase_id += 1;
        }
    }

    // Check for side of street relative direction
    std::string relative_direction;
    if (maneuver.GetType() == ManeuverType::kTypeDestinationLeft) {
        phrase_id += 2;
        relative_direction = dictionary_->destination_subset.relative_directions.at(0);
    } else if (maneuver.GetType() == ManeuverType::kTypeDestinationRight) {
        phrase_id += 2;
        relative_direction = dictionary_->destination_subset.relative_directions.at(1);
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->destination_verbal_alert_subset.phrases.at(std::to_string(phrase_id));

    if (phrase_id > 0) {
        // Replace phrase tags with values
        boost::replace_all(instruction, kRelativeDirectionTag, relative_direction);
        boost::replace_all(instruction, kDestinationTag, destination);
    }

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;

}

std::string NarrativeBuilder::FormVerbalDestinationInstruction(Maneuver& maneuver) {
    // "0": "You have arrived at your destination.",
    // "1": "You have arrived at <DESTINATION>.",
    // "2": "Your destination is on the <RELATIVE_DIRECTION>.",
    // "3": "<DESTINATION> is on the <RELATIVE_DIRECTION>."

    uint8_t phrase_id = 0;
    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Determine if destination (name or street) exists
    std::string destination;
    EnhancePathQueryPtr query = path_data_manager_->GetEnhancePathQuery();
    if (query != nullptr && query->GetDestination() != nullptr) {
        const auto& dest = query->GetDestination()->name;
        if (!dest.empty()) {
            destination = dest;
            phrase_id += 1;
        }
    }

    // Check for side of street relative direction
    std::string relative_direction;
    if (maneuver.GetType() == ManeuverType::kTypeDestinationLeft) {
        phrase_id += 2;
        relative_direction = dictionary_->destination_subset.relative_directions.at(0);
    } else if (maneuver.GetType() == ManeuverType::kTypeDestinationRight) {
        phrase_id += 2;
        relative_direction = dictionary_->destination_subset.relative_directions.at(1);
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->destination_verbal_subset.phrases.at(std::to_string(phrase_id));

    if (phrase_id > 0) {
        // Replace phrase tags with values
        boost::replace_all(instruction, kRelativeDirectionTag, relative_direction);
        boost::replace_all(instruction, kDestinationTag, destination);
    }

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormTurnInstruction(Maneuver& maneuver,
                                                  bool limit_by_consecutive_count,
                                                  uint32_t element_max_count) {
    // "0": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION>.",
    // "1": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> onto <STREET_NAMES>.",
    // "2": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> onto <BEGIN_STREET_NAMES>. Continue on
    // <STREET_NAMES>.",
    // "3": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> to stay on <STREET_NAMES>."
    // "4": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>."
    // "5": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."

    const TurnSubset* subset = nullptr;
    switch (maneuver.GetType()) {
    case ManeuverType::kTypeSlightLeft:
    case ManeuverType::kTypeSlightRight:
        subset = &dictionary_->bear_subset;
        break;

    case ManeuverType::kTypeLeft:
    case ManeuverType::kTypeRight:
        subset = &dictionary_->turn_subset;
        break;

    case ManeuverType::kTypeSharpLeft:
    case ManeuverType::kTypeSharpRight:
        subset = &dictionary_->sharp_subset;
        break;

    default:
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("unexpect type ...");
        break;
    }

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names = FormStreetNames(maneuver.GetStreetNames());

    // Assign the begin street names
    std::string begin_street_names = FormStreetNames(maneuver.GetBeginStreetNames());

    // Update street names for maneuvers that contain obvious maneuvers
    UpdateObviousManeuverStreetNames(maneuver, begin_street_names, street_names);

    // Determine which phrase to use
    uint8_t phrase_id = 0;

    // TODO: 忽略junction name && sign post

    if (maneuver.GetToStayOn()) {
        phrase_id = 3;
    } else if (!begin_street_names.empty()) {
        phrase_id = 2;
    } else if (!street_names.empty()) {
        phrase_id = 1;
    }

    // Set instruction to the determined tagged phrase
    instruction = subset->phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag,
                        FormRelativeTwoDirection(maneuver.GetType(), subset->relative_directions));
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kBeginStreetNamesTag, begin_street_names);
    // boost::replace_all(instruction, kJunctionNameTag, junction_name);
    // boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertTurnInstruction(Maneuver& maneuver,
                                                             bool limit_by_consecutive_count,
                                                             uint32_t element_max_count,
                                                             const std::string& delim) {
  // "0": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION>.",
  // "1": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> onto <STREET_NAMES>.",
  // "2": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> onto <BEGIN_STREET_NAMES>.",
  // "3": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> to stay on <STREET_NAMES>."
  // "4": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>."
  // "5": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."

  return FormVerbalTurnInstruction(maneuver, limit_by_consecutive_count, element_max_count, delim);
}

std::string NarrativeBuilder::FormVerbalTurnInstruction(Maneuver& maneuver,
                                                        bool limit_by_consecutive_count,
                                                        uint32_t element_max_count,
                                                        const std::string& delim) {
    // "0": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION>.",
    // "1": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> onto <STREET_NAMES>.",
    // "2": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> onto <BEGIN_STREET_NAMES>.",
    // "3": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> to stay on <STREET_NAMES>."
    // "4": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>."
    // "5": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."

    const TurnSubset* subset = nullptr;
    switch (maneuver.GetType()) {
    case ManeuverType::kTypeSlightLeft:
    case ManeuverType::kTypeSlightRight:
        subset = &dictionary_->bear_verbal_subset;
        break;
    
    case ManeuverType::kTypeLeft:
    case ManeuverType::kTypeRight:
        subset = &dictionary_->turn_verbal_subset;
        break;

    case ManeuverType::kTypeSharpLeft:
    case ManeuverType::kTypeSharpRight:
        subset = &dictionary_->sharp_verbal_subset;
        break;

    default:
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("unexpect type ...");
        break;
    }

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names =
        FormStreetNames(maneuver.GetStreetNames(), element_max_count, delim, maneuver.GetVerbalFormatter());

    // Assign the begin street names
    std::string begin_street_names =
        FormStreetNames(maneuver.GetBeginStreetNames(),element_max_count, delim, maneuver.GetVerbalFormatter());

    // Update street names for maneuvers that contain obvious maneuvers
    UpdateObviousManeuverStreetNames(maneuver, begin_street_names, street_names);

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    // TODO: 暂时未处理路口和方向路牌

    if (!street_names.empty()) {
        phrase_id = 1;
    }

    if (!begin_street_names.empty()) {
        phrase_id = 2;
    }

    if (maneuver.GetToStayOn()) {
        phrase_id = 3;
    }

    // Set instruction to the determined tagged phrase
    instruction = subset->phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag,
                        FormRelativeTwoDirection(maneuver.GetType(), subset->relative_directions));
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kBeginStreetNamesTag, begin_street_names);
    // boost::replace_all(instruction, kJunctionNameTag, junction_name);
    // boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string 
NarrativeBuilder::FormVerbalSuccinctTurnTransitionInstruction(Maneuver& maneuver,
                                                              bool limit_by_consecutive_count,
                                                              uint32_t element_max_count,
                                                              const std::string& delim) {

    // "0": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION>."
    // "4": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>."
    // "5": "Turn/Bear/Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."

    const TurnSubset* subset = nullptr;
    switch (maneuver.GetType()) {
    case ManeuverType::kTypeSlightLeft:
    case ManeuverType::kTypeSlightRight:
        subset = &dictionary_->bear_verbal_subset;
        break;

    case ManeuverType::kTypeLeft:
    case ManeuverType::kTypeRight:
        subset = &dictionary_->turn_verbal_subset;
    break;

    case ManeuverType::kTypeSharpLeft:
    case ManeuverType::kTypeSharpRight:
        subset = &dictionary_->sharp_verbal_subset;
        break;

    default:
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("unexpect maneuver type ..");
        return "";
    }

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);
    uint8_t phrase_id = 0;
    
    // TODO: 忽略junction view && sign post

    // Set instruction to the determined tagged phrase
    instruction = subset->phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag,
                        FormRelativeTwoDirection(maneuver.GetType(), subset->relative_directions));
    // boost::replace_all(instruction, kJunctionNameTag, junction_name);
    // boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormUturnInstruction(Maneuver& maneuver,
                                                   bool limit_by_consecutive_count,
                                                   uint32_t element_max_count) {
    // "0": "Make a <RELATIVE_DIRECTION> U-turn.",
    // "1": "Make a <RELATIVE_DIRECTION> U-turn onto <STREET_NAMES>.",
    // "2": "Make a <RELATIVE_DIRECTION> U-turn to stay on <STREET_NAMES>.",
    // "3": "Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES>.",
    // "4": "Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES> onto <STREET_NAMES>.",
    // "5": "Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES> to stay on <STREET_NAMES>."
    // "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>."
    // "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names = FormStreetNames(maneuver.GetStreetNames());

    // Assign the cross street names
    std::string cross_street_names = FormStreetNames(maneuver.GetCrossStreetNames());

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string junction_name;
    std::string guide_sign;

    // TODO: 暂不处理SignPost && JunctionView
    if (!street_names.empty()) {
        phrase_id += 1;
        if (maneuver.GetToStayOn()) {
            phrase_id += 1;
        }
    }

    if (!cross_street_names.empty()) {
        phrase_id += 3;
    }

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->uturn_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag,
                        FormRelativeTwoDirection(maneuver.GetType(),
                                                dictionary_->uturn_subset.relative_directions));
    boost::replace_all(instruction, kStreetNamesTag, street_names);
    boost::replace_all(instruction, kCrossStreetNamesTag, cross_street_names);
    boost::replace_all(instruction, kJunctionNameTag, junction_name);
    boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string
NarrativeBuilder::FormVerbalSuccinctUturnTransitionInstruction(Maneuver& maneuver,
                                                               bool limit_by_consecutive_count,
                                                               uint32_t element_max_count,
                                                               const std::string& delim) {
    // "0": "Make a <RELATIVE_DIRECTION> U-turn."
    // "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>."
    // "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);
    uint8_t phrase_id = 0;

    // TODO: 暂不处理junctionview && signpost
#if 0
    std::string junction_name;
    std::string guide_sign;

    if (maneuver.HasGuideSign()) {
        // Set the toward phrase - it takes priority over street names and junction name
        phrase_id = 7;
        // Assign guide sign
        guide_sign = maneuver.signs().GetGuideString(element_max_count, limit_by_consecutive_count, delim,
                                                    maneuver.verbal_formatter(), &markup_formatter_);
    } else if (maneuver.HasJunctionNameSign()) {
        // Set the junction phrase - it takes priority over street names
        phrase_id = 6;
        // Assign guide sign
        junction_name =
            maneuver.signs().GetJunctionNameString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    }
#endif

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->uturn_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kRelativeDirectionTag,
                        FormRelativeTwoDirection(maneuver.GetType(),
                                                dictionary_->uturn_verbal_subset.relative_directions));
    // boost::replace_all(instruction, kJunctionNameTag, junction_name);
    // boost::replace_all(instruction, kTowardSignTag, guide_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }
    return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertUturnInstruction(Maneuver& maneuver,
                                                              bool limit_by_consecutive_count,
                                                              uint32_t element_max_count,
                                                              const std::string& delim) {
    // "0": "Make a <RELATIVE_DIRECTION> U-turn.",
    // "1": "Make a <RELATIVE_DIRECTION> U-turn onto <STREET_NAMES>.",
    // "2": "Make a <RELATIVE_DIRECTION> U-turn to stay on <STREET_NAMES>.",
    // "3": "Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES>."
    // "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>."
    // "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Assign the street names
    std::string street_names =
        FormStreetNames(maneuver.GetStreetNames(), 
                            element_max_count, delim, maneuver.GetVerbalFormatter());

    // Assign the cross street names
    std::string cross_street_names =
        FormStreetNames(maneuver.GetCrossStreetNames(),
                        element_max_count, delim, maneuver.GetVerbalFormatter());

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string junction_name;
    std::string guide_sign;

    if (!street_names.empty()) {
        phrase_id = 1;
        if (maneuver.GetToStayOn()) {
            phrase_id = 2;
        }
    }
    
    if (!cross_street_names.empty()) {
        phrase_id = 3;
    }

    return FormVerbalUturnInstruction(phrase_id,
                                    FormRelativeTwoDirection(maneuver.GetType(),
                                                             dictionary_->uturn_verbal_subset
                                                                 .relative_directions),
                                    street_names, cross_street_names, junction_name, guide_sign);
}


std::string NarrativeBuilder::FormVerbalUturnInstruction(Maneuver& maneuver,
                                                         bool limit_by_consecutive_count,
                                                         uint32_t element_max_count,
                                                         const std::string& delim) {
    // "0": "Make a <RELATIVE_DIRECTION> U-turn.",
    // "1": "Make a <RELATIVE_DIRECTION> U-turn onto <STREET_NAMES>.",
    // "2": "Make a <RELATIVE_DIRECTION> U-turn to stay on <STREET_NAMES>.",
    // "3": "Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES>.",
    // "4": "Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES> onto <STREET_NAMES>.",
    // "5": "Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES> to stay on <STREET_NAMES>."
    // "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>."
    // "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."

    // Assign the street names
    std::string street_names =
        FormStreetNames(maneuver.GetStreetNames(),
                        element_max_count, delim, maneuver.GetVerbalFormatter());

    // Assign the cross street names
    std::string cross_street_names =
        FormStreetNames(maneuver.GetCrossStreetNames(),
                        element_max_count, delim, maneuver.GetVerbalFormatter());

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string junction_name;
    std::string guide_sign;

    if (!street_names.empty()) {
        phrase_id += 1;
        if (maneuver.GetToStayOn()) {
            phrase_id += 1;
        }
    }

    if (!cross_street_names.empty()) {
        phrase_id += 3;
    }

    return FormVerbalUturnInstruction(phrase_id,
                                    FormRelativeTwoDirection(maneuver.GetType(),
                                                             dictionary_->uturn_verbal_subset
                                                                 .relative_directions),
                                    street_names, cross_street_names, junction_name, guide_sign);
}

std::string NarrativeBuilder::FormVerbalUturnInstruction(uint8_t phrase_id,
                                                         const std::string& relative_dir,
                                                         const std::string& street_names,
                                                         const std::string& cross_street_names,
                                                         const std::string& junction_name,
                                                         const std::string& guide_sign) {

  std::string instruction;
  instruction.reserve(kInstructionInitialCapacity);

  // Set instruction to the determined tagged phrase
  instruction = dictionary_->uturn_verbal_subset.phrases.at(std::to_string(phrase_id));

  // Replace phrase tags with values
  boost::replace_all(instruction, kRelativeDirectionTag, relative_dir);
  boost::replace_all(instruction, kStreetNamesTag, street_names);
  boost::replace_all(instruction, kCrossStreetNamesTag, cross_street_names);
  // boost::replace_all(instruction, kJunctionNameTag, junction_name);
  // boost::replace_all(instruction, kTowardSignTag, guide_sign);

  // If enabled, form articulated prepositions
  if (articulated_preposition_enabled_) {
    FormArticulatedPrepositions(instruction);
  }

  return instruction;
}

std::string NarrativeBuilder::FormRampStraightInstruction(Maneuver& maneuver,
                                                          bool limit_by_consecutive_count,
                                                          uint32_t element_max_count) {
    // "0": "Stay straight to take the ramp.",
    // "1": "Stay straight to take the <BRANCH_SIGN> ramp.",
    // "2": "Stay straight to take the ramp toward <TOWARD_SIGN>.",
    // "3": "Stay straight to take the <BRANCH_SIGN> ramp toward <TOWARD_SIGN>.",
    // "4": "Stay straight to take the <NAME_SIGN> ramp."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string exit_branch_sign;
    std::string exit_toward_sign;
    std::string exit_name_sign;

    // TODO: 针对SignPost等此处需要细化
#if 0
    if (maneuver.HasExitBranchSign()) {
        phrase_id += 1;
        // Assign branch sign
        exit_branch_sign =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count);
    }
    if (maneuver.HasExitTowardSign()) {
        phrase_id += 2;
        // Assign toward sign
        exit_toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count);
    }
    if (maneuver.HasExitNameSign() && !maneuver.HasExitBranchSign() && !maneuver.HasExitTowardSign()) {
        phrase_id += 4;
        // Assign name sign
        exit_name_sign =
            maneuver.signs().GetExitNameString(element_max_count, limit_by_consecutive_count);
    }
#endif

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->ramp_straight_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kBranchSignTag, exit_branch_sign);
    boost::replace_all(instruction, kTowardSignTag, exit_toward_sign);
    boost::replace_all(instruction, kNameSignTag, exit_name_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}

std::string NarrativeBuilder::FormVerbalAlertRampStraightInstruction(Maneuver& maneuver,
                                                                     bool limit_by_consecutive_count,
                                                                     uint32_t element_max_count,
                                                                     const std::string& delim) {
    // "0": "Stay straight to take the ramp.",
    // "1": "Stay straight to take the <BRANCH_SIGN> ramp.",
    // "2": "Stay straight to take the ramp toward <TOWARD_SIGN>.",
    // "4": "Stay straight to take the <NAME_SIGN> ramp."

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string exit_branch_sign;
    std::string exit_toward_sign;
    std::string exit_name_sign;

    // TODO: 待优化
#if 0
    if (maneuver.HasExitBranchSign()) {
        phrase_id = 1;
        exit_branch_sign =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    } else if (maneuver.HasExitTowardSign()) {
        phrase_id = 2;
        // Assign toward sign
        exit_toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    } else if (maneuver.HasExitNameSign()) {
        phrase_id = 4;
        // Assign name sign
        exit_name_sign =
            maneuver.signs().GetExitNameString(element_max_count, limit_by_consecutive_count, delim,
                                            maneuver.verbal_formatter(), &markup_formatter_);
    }
#endif

    return FormVerbalRampStraightInstruction(phrase_id, exit_branch_sign, exit_toward_sign,
                                            exit_name_sign);
}

std::string NarrativeBuilder::FormVerbalRampStraightInstruction(Maneuver& maneuver,
                                                                bool limit_by_consecutive_count,
                                                                uint32_t element_max_count,
                                                                const std::string& delim) {
    // "0": "Stay straight to take the ramp.",
    // "1": "Stay straight to take the <BRANCH_SIGN> ramp.",
    // "2": "Stay straight to take the ramp toward <TOWARD_SIGN>.",
    // "3": "Stay straight to take the <BRANCH_SIGN> ramp toward <TOWARD_SIGN>.",
    // "4": "Stay straight to take the <NAME_SIGN> ramp."

    // Determine which phrase to use
    uint8_t phrase_id = 0;
    std::string exit_branch_sign;
    std::string exit_toward_sign;
    std::string exit_name_sign;
#if 0
    if (maneuver.HasExitBranchSign()) {
        phrase_id += 1;
        // Assign branch sign
        exit_branch_sign =
            maneuver.signs().GetExitBranchString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    }
    if (maneuver.HasExitTowardSign()) {
        phrase_id += 2;
        // Assign toward sign
        exit_toward_sign =
            maneuver.signs().GetExitTowardString(element_max_count, limit_by_consecutive_count, delim,
                                                maneuver.verbal_formatter(), &markup_formatter_);
    }
    if (maneuver.HasExitNameSign() && !maneuver.HasExitBranchSign() && !maneuver.HasExitTowardSign()) {
        phrase_id += 4;
        // Assign name sign
        exit_name_sign =
            maneuver.signs().GetExitNameString(element_max_count, limit_by_consecutive_count, delim,
                                            maneuver.verbal_formatter(), &markup_formatter_);
    }
#endif

    return FormVerbalRampStraightInstruction(phrase_id, exit_branch_sign, exit_toward_sign,
                                            exit_name_sign);
}

std::string NarrativeBuilder::FormVerbalRampStraightInstruction(uint8_t phrase_id,
                                                                const std::string& exit_branch_sign,
                                                                const std::string& exit_toward_sign,
                                                                const std::string& exit_name_sign) {

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Set instruction to the determined tagged phrase
    instruction = dictionary_->ramp_straight_verbal_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kBranchSignTag, exit_branch_sign);
    boost::replace_all(instruction, kTowardSignTag, exit_toward_sign);
    boost::replace_all(instruction, kNameSignTag, exit_name_sign);

    // If enabled, form articulated prepositions
    if (articulated_preposition_enabled_) {
        FormArticulatedPrepositions(instruction);
    }

    return instruction;
}


std::string 
NarrativeBuilder::FormRelativeTwoDirection(ManeuverType type,
                                          const std::vector<std::string>& relative_directions) {
    switch (type) {
        case ManeuverType::kTypeLeft:
        case ManeuverType::kTypeSharpLeft:
        case ManeuverType::kTypeSlightLeft:
        case ManeuverType::kTypeLeftUTurn:
        case ManeuverType::kTypeLeftToRamp:
        case ManeuverType::kTypeExitLeft:
        case ManeuverType::kTypeMergeToLeft:
        case ManeuverType::kTypeDestinationLeft: {
            return relative_directions.at(0); // "left"
        }

        case ManeuverType::kTypeRight:
        case ManeuverType::kTypeSharpRight:
        case ManeuverType::kTypeSlightRight:
        case ManeuverType::kTypeRightUTurn:
        case ManeuverType::kTypeRightToRamp:
        case ManeuverType::kTypeExitRight:
        case ManeuverType::kTypeMergeToRight:
        case ManeuverType::kTypeDestinationRight: {
            return relative_directions.at(1); // "right"
        }
        
        default: {
            GUIDE_ASSERT(false);
            GUIDE_LOG_ERROR("unknown  maneuver type .");
            return "";
        }
    }
}

std::string
NarrativeBuilder::FormRelativeThreeDirection(ManeuverType type,
                                             const std::vector<std::string>& relative_directions) {
  switch (type) {
    case ManeuverType::kTypeStayLeft: {
        return relative_directions.at(0); // "left"
    }

    case ManeuverType::kTypeStayStraight: {
        return relative_directions.at(1); // "straight"
    }

    case ManeuverType::kTypeStayRight: {
        return relative_directions.at(2); // "right"
    }

    default: {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("unknown  maneuver type .");
        return "";
    }
  }
}



std::string NarrativeBuilder::FormStreetNames(const std::shared_ptr<StreetNames>& street_names,
                                              uint32_t max_count,
                                              const std::string& delim,
                                              const VerbalTextFormatter* verbal_formatter) {
    std::string street_names_string;
    uint32_t count = 0;
    
    GUIDE_ASSERT(street_names != nullptr);
    if (street_names == nullptr) {
        GUIDE_LOG_ERROR("FormStreetNames street_names is nullptr .");
        return street_names_string;
    }

    for (const auto& street_name : *street_names) {
        // If supplied, limit by max count
        if ((max_count > 0) && (count == max_count)) {
        break;
        }
        // If the street_names_string is not empty then add the delimiter
        if (!street_names_string.empty()) {
        street_names_string += delim;
        }

        // Append next name to string
        street_names_string += (verbal_formatter)
                                ? verbal_formatter->Format(street_name->Value())
                                : street_name->Value();
        ++count;
    }
    return street_names_string;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
