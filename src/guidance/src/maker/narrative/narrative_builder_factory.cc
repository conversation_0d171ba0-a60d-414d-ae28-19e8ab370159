#include "guidance/src/maker/narrative/narrative_builder_factory.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {


std::unique_ptr<NarrativeBuilder> 
NarrativeBuilderFactory::Create(GuidanceOptions *options, PathDataManagerPtr path_manager, NarrativeDictionaryPtr dictionary) {
    if (options == nullptr) {
        GUIDE_LOG_ERROR("options_ is nullptr .");
        return nullptr;
    }

    if (path_manager == nullptr) {
        GUIDE_LOG_ERROR("path_manager is nullptr .");
        return nullptr;
    }

    if (dictionary == nullptr) {
        GUIDE_LOG_ERROR("dictionary is nullptr .");
        return nullptr;
    }

    return std::make_unique<NarrativeBuilder>(options, path_manager, dictionary);
}

}  // namespace guide
}  // namespace aurora
/* EOF */
