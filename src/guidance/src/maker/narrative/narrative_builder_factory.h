#ifndef MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_NARRATIVE_BUILDER_FACTORY_H
#define MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_NARRATIVE_BUILDER_FACTORY_H

#include <string>
#include <memory>
#include "guidance/src/data/path_data_manager.h"
#include "guidance/src/maker/narrative/narrative_builder.h"
#include "guidance/src/maker/narrative/narrative_dictionary.h"

namespace aurora {
namespace guide {

class NarrativeBuilderFactory {
public:
    NarrativeBuilderFactory() = delete;

    static std::unique_ptr<NarrativeBuilder> Create(GuidanceOptions *options, PathDataManagerPtr path_manager, NarrativeDictionaryPtr dictionary);
};

}  // namespace guide
}  // namespace aurora
#endif // MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_NARRATIVE_BUILDER_FACTORY_H
/* EOF */
