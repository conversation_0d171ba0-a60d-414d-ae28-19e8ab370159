file(GLOB HEADERS ./include/*.h)
file(GLO<PERSON> SOURCES ./src/*.cc)

add_library(aurora_location SHARED ${HEADERS} ${SOURCES})

# add base as dependency
target_link_libraries(aurora_location
        PUBLIC
        aurora_base
        yaml-cpp::yaml-cpp
)

add_library(aurora_location_static STATIC ${HEADERS} ${SOURCES})

target_link_libraries(aurora_location_static
        PUBLIC
        aurora_base
        yaml-cpp::yaml-cpp
        
)



target_include_directories(aurora_location_static PUBLIC ${CMAKE_BINARY_DIR}/_deps/yaml-cpp-src/include  ${RAPIDJSON_SOURCE_DIR}/include ${Boost_INCLUDE_DIRS})
target_include_directories(aurora_location PRIVATE ${CMAKE_BINARY_DIR}/_deps/yaml-cpp-src/include ${RAPIDJSON_SOURCE_DIR}/include ${Boost_INCLUDE_DIRS})

# add -Wall to the location library
target_compile_options(aurora_location PRIVATE -Wall -Wno-unused-variable -Wno-unused-parameter -Wextra -Wno-pedantic -Wno-class-memaccess -Werror)
target_compile_options(aurora_location_static PRIVATE -Wall -Wno-unused-variable -Wno-unused-parameter -Wextra -Wno-pedantic -Wno-class-memaccess -Werror)

# target_include_directories(aurora_location PRIVATE  ${RAPIDJSON_SOURCE_DIR}/include)
# target_include_directories(aurora_location_static PRIVATE  ${RAPIDJSON_SOURCE_DIR}/include)

# Add tests if enabled
if(ENABLE_GTEST)
    add_subdirectory(tests)
endif()
