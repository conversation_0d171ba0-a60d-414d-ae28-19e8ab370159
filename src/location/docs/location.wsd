@startuml Location Module Class Diagram

package "Location Module" {


    interface GraphReader {
        +GetInEdgeIds(NodeId) : std::vector<DirectedEdgeId>
        +GetOutEdgeIds(NodeId) : std::vector<DirectedEdgeId>
        +GetEdgeInfos(AABB2) : std::vector<EdgeInfo>
        +GetEdgeInfo(EdgeId) : EdgeInfo
    }

    class ReachableEdgeInfo {
        -edge_id:DirectedEdgeId
        -distance:double
        -turn_angle: double
        -from:DirectedEdgeId
        -offset: double
        -intermediates: std::vector<DirectedEdgeId>
    }

    
    struct TraverseParams {
        -from_edge: DirectedEdgeId
        -from_offset:double
        -target_mbr:AABB2
        -max_distance:double
        -max_turn: double
    }

    class GraphTraverse
    {
        -reader_: GraphReader
        +DistanceFirstSearch(TraverseParams): std::vector<ReachableEdgeInfo>
    }

    class EnhanceRouteManager {
        +SetRoutePath(PathInfoPtr)
        +IsPointOnPath(DirectedEdgeId, Offset) : bool
    }

    interface MatchScorer {
        + CalcDistanceScore(distance, radius)

        + CalcHeadingScore(car_heading, map_road_heading)
        + CalcTranferScore(car_speed, map_road_speed)
        + CalcMovementScore(car_gps_move, map_move)
        + SoftmaxScores(scores: std::vector<float>): std::vector<float>
    }

    interface TraceManager {
        +AddMatchResult(MatchResult)
        +StartTrace(trace_file_path)
        +StopTrace()
    }

    interface PolicyManager {
        +SetScenario()
        +GetScenario()
        +GetCurrentPolicy()
    }

    struct SimulatorConfig {
        -speed_multiples: double
        -position_noise_stddev: double
        -heading_noise_stddev: double
    }

    interface MapMatchingListener {
        +OnMapMatchingResult(result:MatchResult)
    }
    

    interface SimulatorController {
        +Start()
        +Stop()
        +Pause()
        +Resume()
        +SetConfig(SimulatorConfig)
        +GetConfig():SimulatorConfig
        +SetRoutePath(PathInfoPtr route_path) = 0;
        +SaveRoute(route_file_path: string) : bool
        +LoadRoute(route_file_path: string) : bool
        +IsFinished():bool
    }

    interface PostProcessor {
        +Process(MatchResult) : MatchResult
    }

    interface PreProcessor {
        +Process(GnssPosInfo) : GnssPosInfo
    }

    interface MapMatchingEngine {
        +Initialize(MatchEngineConfig) : bool
        +Uninitialize() : bool

        +MatchGPS(GnssPosInfo, bool) : MatchResult
        +MatchPosition(PointLL, float) : MatchCandidate
        +SwitchParallelRoad()
    
        +GetSimulatorController() : std::shared_ptr<SimulatorController>
        +GetEnhanceRouteManager() : std::shared_ptr<EnhanceRouteManager>
        +GetStateContainer(): std::shared_ptr<StateContainer>
        +GetPolicyManager() : std::shared_ptr<PolicyManager>
        

        +GetPreprocessor() : std::shared_ptr<PreProcessor>
        +SetPreprocessor(pre_processor: std::shared_ptr<PreProcessor>);
        +GetPostprocessor() : std::shared_ptr<PostProcessor>
        +SetPostprocessor(post_processor: std::shared_ptr<PostProcessor>);
    }



    class MapMatchingEngineFactory {
        +GetInstance() : MapMatchingEngineFactory&
        +CreateEngine(string) : std::shared_ptr<MapMatchingEngine>
    }

    struct StateInfo {
        -edge_id: DirectedEdgeId
        -offset: double
        -heading: double
        -speed: double
        -score: double
        -匹配相关信息...
    }

    class FrameInfo {
        -pos_info:GnssPosInfo;
        -states:std::vector<StateInfo>;
        // 按照edge states 分别存放
        +FindStates(edge_id: DirectedEdgeId, offset: double) : std::vector<StateInfo>
        +FindState(edge_id: DirectedEdgeId, segment_index: int) : StateInfo
        +GetState(id:StateID): StateInfo
    }

    

    interface StateContainer {
        +BuildCandidateStates(GnssPosInfo) 
        +GetFrameInfo(frame_index: int) : FrameInfo
        +Reset()
    }

    interface InternalDataDumper {
        +DumpStates(path)
        +DumpEdges(path)
    }

    class ViterbiSearch {
        -state_container_: StateContainer&
        -ProcessOneFrame()
    }

    interface ILocation {
        +MatchPosition()
        +UpdateGnssInfo()
        +SetRoutePath()
        +SwitchParallelRoad()

        +GetSimulatorController()

        +AddDRListener()
        +RemoveDRListener()
        +AddMapMatchingListener()
        +RemoveMapMatchingListener()

    }

    ' MapMatchingEngine "1" --> "1" GraphTraverse : manages
    ' MapMatchingEngine "1" -- "1" StateContainer : uses
    MapMatchingEngine "1" --> "1" PreProcessor : preprocesses
    MapMatchingEngine "1" -right-> "1" EnhanceRouteManager : set_route_path
    MapMatchingEngine "1" --> "1" PostProcessor : postprocesses
    MapMatchingEngine "1" --> "1" ViterbiSearch : uses
    ViterbiSearch "1" --> "1" StateContainer : access
    ViterbiSearch "1" -right-> "1" MatchScorer : uses
    GraphReader "1" <-up- "1" GraphTraverse : uses

    ' MapMatchingEngine "1" --> "1" GraphReader : queries
    MapMatchingEngine "1" --> "1" SimulatorController : uses
    MapMatchingEngine "1" -left-> "1" TraceManager : manages
    SimulatorController "1" --> "1" SimulatorConfig : uses
    MapMatchingEngineFactory "1" --> "1" MapMatchingEngine : creates
    StateContainer "1" --> "n" FrameInfo : manages
    FrameInfo "1" -up-> "n" StateInfo : contains
    GraphTraverse "1" --> "1" TraverseParams : uses
    GraphTraverse "1" --> "1" ReachableEdgeInfo : provides
    ' GraphReader "1" --> "1" EdgeQuery : queries

    ILocation "1" -right-> "1" MapMatchingListener : notify
    ILocation "1" --> "1" MapMatchingEngine: uses
    MapMatchingEngine "1" --> InternalDataDumper : uses
    StateContainer "1" --> "1" GraphReader : access
    ViterbiSearch "1" --> "1" GraphTraverse : access
    ViterbiSearch  "1" -up-> "1" EnhanceRouteManager : access
    ViterbiSearch "1" --> "1" PolicyManager:use

}

@enduml
