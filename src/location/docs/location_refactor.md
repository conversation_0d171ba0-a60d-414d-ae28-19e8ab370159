# 匹配重构基本方案

## 重构后类图

### 核心组件概述
Location Module 包含以下主要组件：

- **MapMatchingEngine**: 核心匹配引擎，协调各组件工作
- **GraphReader**: 图数据读取接口  
- **GraphTraverse**: 图遍历算法实现
- **EnhanceRouteManager**: 增强路径管理接口
- **StateContainer**: 状态管理组件
- **ViterbiSearch**: Viterbi搜索算法
- **MatchScorer**: 匹配评分组件  
- **PreProcessor/PostProcessor**: 前后处理组件
- **SimulatorController**: 模拟器控制接口
- **TraceManager**: 跟踪管理接口
- **PolicyManager**: 策略管理接口
- **InternalDataDumper**: 内部数据导出接口

### 接口与类详细说明

#### 核心接口
- **GraphReader**: 图数据访问
  - `GetInEdgeIds()`: 获取入边ID列表
  - `GetOutEdgeIds()`: 获取出边ID列表  
  - `GetEdgeInfos()`: 区域查询边信息
  - `GetEdgeInfo()`: 获取单边详细信息

- **GraphTraverse**: 图遍历算法
  - `DistanceFirstSearch()`: 距离优先搜索

- **EnhanceRouteManager**: 路径增强
  - `SetRoutePath()`: 设置路径
  - `IsPointOnPath()`: 判断点是否在路径上

- **MatchScorer**: 评分计算
  - `CalcDistanceScore()`: 距离评分
  - `CalcHeadingScore()`: 航向评分
  - `CalcTransferScore()`: 转移评分
  - `CalcMovementScore()`: 移动评分
  - `SoftmaxScores()`: 分数归一化

#### 数据结构
- **ReachableEdgeInfo**: 可达边信息
  - `edge_id`: 边ID
  - `distance`: 距离
  - `turn_angle`: 转向角
  - `from`: 来源边
  - `offset`: 偏移量
  - `intermediates`: 中间边列表

- **TraverseParams**: 遍历参数
  - `from_edge`: 起始边
  - `from_offset`: 起始偏移  
  - `target_mbr`: 目标区域
  - `max_distance`: 最大距离
  - `max_turn`: 最大转向角

- **StateInfo**: 状态信息
  - `edge_id`: 边ID
  - `offset`: 偏移量
  - `heading`: 航向
  - `speed`: 速度
  - `score`: 分数

- **FrameInfo**: 帧信息
  - `pos_info`: 位置信息
  - `states`: 状态列表
  - `FindStates()`: 查找状态
  - `FindState()`: 查找单个状态
  - `GetState()`: 获取状态

#### 核心类
- **MapMatchingEngine**: 主引擎
  - `MatchGPS()`: GPS匹配
  - `MatchPosition()`: 位置匹配
  - `SwitchParallelRoad()`: 切换平行道路
  - 管理各组件生命周期

- **ViterbiSearch**: Viterbi算法
  - `BuildForwardLinks()`: 构建前向链接
  - `UpdateScores()`: 更新分数
  - `PruneStates()`: 状态剪枝

- **StateContainer**: 状态管理
  - `BuildCandidateStates()`: 构建候选状态
  - `GetFrameInfo()`: 获取帧信息
  - `Reset()`: 重置状态

- **MapMatchingEngineFactory**: 工厂类
  - `GetInstance()`: 获取单例
  - `CreateEngine()`: 创建引擎实例

### 组件关系

- MapMatchingEngine 协调各组件工作流
- GraphTraverse 依赖 GraphReader 进行图查询
- ViterbiSearch 使用 StateContainer 管理状态
- MatchScorer 为 ViterbiSearch 提供评分
- EnhanceRouteManager 提供路径增强功能
- TraceManager 记录匹配过程数据
- PolicyManager 管理匹配策略

### 数据流
1. 输入处理: PreProcessor → MapMatchingEngine
2. 候选生成: GraphReader → StateContainer
3. 状态传播: GraphTraverse → ViterbiSearch
4. 评分计算: MatchScorer → ViterbiSearch 
5. 结果处理: PostProcessor → MapMatchingListener
6. 数据记录: InternalDataDumper
7. 输出: MapMatchingEngine → ILocation

### 扩展点
- 可通过实现接口扩展：
  - GraphReader: 支持不同图数据源
  - MatchScorer: 自定义评分算法
  - PreProcessor/PostProcessor: 自定义处理逻辑
  - TraceManager: 自定义跟踪记录
  - PolicyManager: 自定义策略
