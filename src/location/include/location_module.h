#ifndef MAP_SRC_LOCATION_INCLUDE_LOCATION_INTERFACE_H
#define MAP_SRC_LOCATION_INCLUDE_LOCATION_INTERFACE_H

#include "location_def.h"
#include "module.h"
#include "path_module.h"

namespace aurora {
namespace loc {

/**
 * @class DRListener
 * @brief Interface for receiving Dead Reckoning (DR) location updates
 *
 * This interface provides callbacks for receiving high-frequency (10Hz)
 * dead reckoning position updates when registered with ILocation.
 */
class DRListener {
 public:
  virtual ~DRListener() = default;

  /**
   * @brief Callback for receiving DR location results
   * @param result The GNSS position information containing location data
   */
  virtual void OnLocationResult(const GnssPosInfo& result) = 0;
};

/**
 * @class MapMatchingListener
 * @brief Interface for receiving map matching results
 *
 * Provides callbacks for receiving map-aligned position updates.
 */
class MapMatchingListener {
 public:
  virtual ~MapMatchingListener() = default;

  /**
   * @brief Callback for receiving map matching results
   * @param result The matching result containing map-aligned position
   */
  virtual void OnMapMatchingResult(const MatchResult& result) = 0;
};

class MatchPostProcessor {
  public:
  virtual ~MatchPostProcessor() = default;
  virtual bool Process(MatchResult& result) = 0;
};

class MatchPreProcessor {
  public:
  virtual ~MatchPreProcessor() = default;
  virtual bool Process(GnssPosInfo& GnssPosInfo) = 0;
};


class TraceManager {
  public:
  virtual ~TraceManager() = default;
  virtual bool StartRecordTrace(const std::string& trace_path) = 0;
  virtual bool StopRecordTrace() = 0;
  virtual bool ReplayTrace(const std::string& trace_path) = 0;
};


/// @brief Shared pointer to a DRListener
using DRListenerPtr = std::shared_ptr<DRListener>;

/// @brief Shared pointer to a MapMatchingListener
using MapMatchingListenerPtr = std::shared_ptr<MapMatchingListener>;

/// @brief Shared pointer to PathInfo
using PathInfoPtr = std::shared_ptr<aurora::path::PathInfo>;

class ILocation;

/// @brief Base class for simulator controllers
class SimulatorController {
 public:
  struct Config {
    double position_noise_mean = 0.0;    //
    double position_noise_stddev = 0.0;  //

    double position_noise_period_mean = 0.0;  // 米, 采样周期
    double position_noise_period_stddev = 0.0;  // seconds, 采样周期


    double heading_noise_mean = 0.0;     //
    double heading_noise_stddev = 10;    //

    double speed_mean = 20.0;  // meter/second, 建议值，路口、低道路等级自动调整
    double speed_stddev = 2;   //
    double sample_interval = 1; // seconds, 采样间隔
    double play_interval = 0.1;  // seconds, 播放间隔
  };

  virtual ~SimulatorController() = default;



  virtual int32_t SetRoutePath(PathInfoPtr route_path) = 0;
  // Start, Stop, Pause, Resume methods for the simulator
  virtual int32_t Start() = 0;
  virtual int32_t Stop() = 0;
  virtual int32_t Pause() = 0;
  virtual int32_t Resume() = 0;


  virtual int32_t SetConfig(const Config& config) = 0;
  /// @brief Get the current configuration of the simulator
  virtual Config GetConfig() const = 0;

  virtual int32_t LoadTrackData(const std::string& track_data_dir) {
    return 0;
  }

  virtual bool IsFinished() const {
    return false;  // Default implementation returns false
  }
};

/**
 * @class ILocation
 * @brief Main location service interface providing positioning and map matching
 *
 * This interface provides methods for:
 * - Updating GNSS position information
 * - Managing navigation routes
 * - Registering/unregistering listeners for position updates
 */
class AURORA_EXPORT ILocation : public IInterface {
 public:
  virtual ~ILocation() {}

  virtual MatchCandidate MatchPosition(const aurora::PointLL& point,
                                       float radius) = 0;
  /**
   * @brief Update GNSS position information
   * @param pos The GNSS position data to update
   * @return Error code (0 for success)
   */
  virtual int32_t UpdateGnssInfo(const GnssPosInfo& pos) = 0;

  /**
   * @brief Set the navigation route path
   * @param route_path The path to follow (nullptr to stop navigation)
   * @return Error code (0 for success)
   */
  virtual int32_t SetRoutePath(PathInfoPtr route_path) = 0;

  /**
   * @brief Switch parallel road matching mode
   * @param type The parallel road type to switch to
   * @return Error code (0 for success)
   */
  virtual int32_t SwitchParallelRoad(ParallelRoadType type) = 0;

  /**
   * @brief Register a DR listener for position updates
   * @param callback The listener to register
   * @return Error code (0 for success)
   * @note The listener will receive updates at 10Hz
   */
  virtual int32_t AddDRListener(DRListenerPtr callback) = 0;

  /**
   * @brief Unregister a DR listener
   * @param callback The listener to unregister
   * @return Error code (0 for success)
   */
  virtual int32_t RemoveDRListener(DRListenerPtr callback) = 0;

  /**
   * @brief Register a map matching listener
   * @param callback The listener to register
   * @return Error code (0 for success)
   * @note The listener will receive updates at 10Hz
   */
  virtual int32_t AddMapMatchingListener(MapMatchingListenerPtr callback) = 0;

  /**
   * @brief Unregister a map matching listener
   * @param callback The listener to unregister
   * @return Error code (0 for success)
   */
  virtual int32_t RemoveMapMatchingListener(
      MapMatchingListenerPtr callback) = 0;

  /**
   * @brief Get simulator controller
   * @return std::shared_ptr<SimulatorController>
   */
  virtual std::shared_ptr<SimulatorController> GetSimulatorController() {
    return nullptr;
  }

  // 最新一次的定位位置
  virtual const MatchResult& GetLastMatchResult() const = 0;

  // 保存LastMatchResult
  virtual bool SaveLastMatchResult(const std::string& path) const {
    return true;
  }

  // 保存LastMatchResult
  virtual bool LoadLastMatchResult(const std::string& path) {
    return true;
  }

};

// forward declaration
class LocationModuleImpl;

/**
 * @class LocationModule
 * @brief A module that handles location functionalities.
 *
 * LocationModule is a derived class from Module that provides location-related
 * services. It follows the standard module lifecycle: Prepare, Init, Start,
 * Stop, UnInit. Implementation details are hidden via the pimpl idiom through
 * LocationModuleImpl.
 */
class LocationModule : public Module {
 public:
  LocationModule();
  virtual ~LocationModule();

  virtual int32_t Prepare(const std::string& config);
  virtual int32_t Init(const InterfaceFinder& finder);
  virtual int32_t Start();
  virtual int32_t Stop();
  virtual int32_t UnInit();

  virtual ModuleInitStatus IsInit() const;
  virtual std::shared_ptr<IInterface> GetInterface() const;

 protected:
 private:
  std::shared_ptr<LocationModuleImpl> impl_;
  ModuleInitStatus status_;
};

}  // namespace loc
}  // namespace aurora
#endif  // MAP_SRC_LOCATION_INCLUDE_LOCATION_INTERFACE_H
/* EOF */
