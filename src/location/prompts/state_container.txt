创建 StateContainer接口和实现类 StateContainerImplOld， 包含一下功能

void CreateCandicateState
1. 根据定位通过 GraphReader 类预加载有向边、 节点信息
2. 将有向边打断，最大为2m 的片段， 对每个片段state 编码
3. StateID 信息里编码了一下信息 
struct RoadPiece {
    TileID tile_id;
    LinkId link_id;
    int32_t segment_index:16;
    int16_t total_piece_in_seg;
    int16_t piece_idx_in_seg;
    PointLL pos;
    float heading;
};

State 编码如下：

struct State {
    int id;
    RoadPiece peice;
    int64_t timestamp;
};


