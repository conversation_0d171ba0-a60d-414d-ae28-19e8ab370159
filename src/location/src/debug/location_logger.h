#ifndef MAP_SRC_LOCATION_SRC_LOCATION_DEBUG_LOCATION_LOGGER_H_
#define MAP_SRC_LOCATION_SRC_LOCATION_DEBUG_LOCATION_LOGGER_H_
#include "base/include/logger.h"
#include "location_build_config.h"

#if LOCATION_LOG_LEVEL <= LOG_LEVEL_TRACE && ENABLE_LOCATION_LOG
#define LOCATION_TRACE_LOG(...) LOG_TRACE_TAG(LOCATION_LOG_TAG, __VA_ARGS__)
#else
#define LOCATION_TRACE_LOG(...) 
#endif


#if LOCATION_LOG_LEVEL <= LOG_LEVEL_DEBUG && ENABLE_LOCATION_LOG
#define LOCATION_DEBUG_LOG(...) LOG_DEBUG_TAG(LOCATION_LOG_TAG, __VA_ARGS__)
#else
#define LOCATION_DEBUG_LOG(...) 
#endif


#if LOCATION_LOG_LEVEL <= LOG_LEVEL_INFO && ENABLE_LOCATION_LOG
#define LOCATION_INFO_LOG(...) LOG_INFO_TAG(LOCATION_LOG_TAG, __VA_ARGS__)
#else
#define LOCATION_INFO_LOG(...) 
#endif


#if LOCATION_LOG_LEVEL <= LOG_LEVEL_WARN  && ENABLE_LOCATION_LOG
#define LOCATION_WARN_LOG(...) LOG_WARN_TAG(LOCATION_LOG_TAG, __VA_ARGS__)
#else
#define LOCATION_WARN_LOG(...) 
#endif


#if LOCATION_LOG_LEVEL <= LOG_LEVEL_ERROR  && ENABLE_LOCATION_LOG
#define LOCATION_ERROR_LOG(...) LOG_ERROR_TAG(LOCATION_LOG_TAG, __VA_ARGS__)
#else
#define LOCATION_ERROR_LOG(...) 
#endif


#if LOCATION_LOG_LEVEL <= LOG_LEVEL_FATAL  && ENABLE_LOCATION_LOG
#define LOCATION_FATAL_LOG(...) LOG_FATAL_TAG(LOCATION_LOG_TAG, __VA_ARGS__)
#else
#define LOCATION_FATAL_LOG(...) 
#endif

#endif // MAP_SRC_LOCATION_SRC_LOCATION_DEBUG_LOCATION_LOGGER_H_
