#ifndef LOCATION_DEBUG_RANDOM_GENERATOR_H
#define LOCATION_DEBUG_RANDOM_GENERATOR_H

#include <random>
#include <cmath>
#include "base/include/pointll.h"

namespace aurora {
namespace loc {
namespace debug {

class SineWaveGenerator {
public:
    SineWaveGenerator(double mean_period, double period_stddev,
                     double mean_scale, double scale_stddev)
        : period_dist_(mean_period, period_stddev),
          scale_dist_(mean_scale, scale_stddev),
          gen_(std::random_device{}()) {
        generate_new_parameters();
    }

    double next_sample() {
        if (current_sample_ >= current_period_) {
            current_sample_ = std::fmod(current_sample_ / current_period_, 1.0);

            generate_new_parameters();
            current_sample_ = current_sample_ * current_period_;
        }

        double value = current_scale_ * 
                      std::sin(2 * M_PI * current_sample_ / current_period_);
        current_sample_ += 1.0;
        return value;
    }

    void AddNoise(PointLL& point) {
        double x_noise = point.y() * kMetersPerDegreeLat;
        x_noise = std::fmod(x_noise, current_period_) / current_period_ * 2 * M_PI;
        x_noise = cos(x_noise) / kMetersPerDegreeLat * current_scale_;
         
        double y_noise = point.x() * DistanceApproximator<PointLL>::LngScalePerLat(point.y()) * kMetersPerDegreeLat;
        y_noise = std::fmod(y_noise, current_period_) / current_period_ * 2 * M_PI;
        y_noise = sin(y_noise) /( DistanceApproximator<PointLL>::LngScalePerLat(point.y()) * kMetersPerDegreeLat) * current_scale_;

        point.set_x(point.x() +  x_noise );
        point.set_y(point.y() +  y_noise);
    }

    void SetScale(double mean_scale, double scale_stddev) {
        scale_dist_ = std::normal_distribution<double>(mean_scale, scale_stddev);
        generate_new_parameters();
    }
    void SetPeriod(double mean_period, double period_stddev) {
        period_dist_ = std::normal_distribution<double>(mean_period, period_stddev);
        generate_new_parameters();
    }

private:
    void generate_new_parameters() {
        current_period_ = 200 + period_dist_(gen_); //period_dist_(gen_);
        current_scale_ = scale_dist_.stddev();
    }

    std::normal_distribution<double> period_dist_;
    std::normal_distribution<double> scale_dist_;
    std::mt19937 gen_;

    double current_period_{0};
    double current_scale_{0};
    double current_sample_{0};
};

} // namespace debug
} // namespace location
} // namespace aurora

#endif // LOCATION_DEBUG_RANDOM_GENERATOR_H
