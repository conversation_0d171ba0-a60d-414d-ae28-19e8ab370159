
#include "enhance_data_info.h"

#define LOCATION_LOG_TAG "Loc::Data"
#include  "debug/location_logger.h"

namespace aurora {
namespace loc {

EdgeInfo::EdgeInfo(TileInfo* tile_info, uint32_t feature_id) {
  this->tile_info_ptr = tile_info;
  edge_id = aurora::parser::RouteEdgeID(tile_info->tile_id_, feature_id);
  auto augment_edge = tile_info->reader_.GetAugmentEdgeByID(edge_id.feature_id);
  auto topol_edge = tile_info->reader_.GetTopolEdgeByID(edge_id.feature_id);

  if (augment_edge == nullptr && topol_edge == nullptr) {
    LOCATION_WARN_LOG("Edge not found in tile: {}.{}", edge_id.tile_id.value,
                  edge_id.feature_id);
    return;
  }

  this->augment_edge = augment_edge;
  this->topol_edge = topol_edge;

  if (augment_edge != nullptr) {
    auto& edge_shape = augment_edge->GetGeoPoints();
    double total_len = 0;
    for (size_t i = 1; i < augment_edge->GetGeoPoints().size(); ++i) {
      double heading = aurora::GeoPoint<double>(edge_shape[i - 1])
                           .Heading(aurora::GeoPoint<double>(edge_shape[i]));
      this->headings.push_back(heading);
    

      double len = std::sqrt(DistanceApproximator(aurora::PointLL(edge_shape[i - 1]))
                        .DistanceSquared(aurora::PointLL(edge_shape[i])));
      this->lens.push_back(len);
      total_len += len;
    }
    if(headings.size() >= 2) {
        total_turn = std::fmod(headings.front() - headings.back()  + 720., 360.) ;
        if(total_turn > 180.) { 
          total_turn = 360 - total_turn;
        }
    } else {
        total_turn = 0;
    }
    this->total_len = total_len;
  }
}
}
}
