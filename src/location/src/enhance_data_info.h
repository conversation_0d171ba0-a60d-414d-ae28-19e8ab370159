#ifndef MAP_SRC_LOCATION_SRC_ENHANCE_DATA_INFO_H
#define MAP_SRC_LOCATION_SRC_ENHANCE_DATA_INFO_H

#include <unordered_map>

#include "data_provider/include/route_data/feature/augment_edge.h"
#include "data_provider/include/route_data/route_tile_reader.h"
#include "data_provider/include/route_data/route_data_def.h"

#include "location/include/location_def.h"

namespace aurora {
namespace loc {

using aurora::parser::RouteNodeID;
struct TileInfo;
struct EdgeInfo {
  private:
  EdgeInfo();
  public:
  aurora::parser::RouteEdgeID edge_id;
  TileInfo* tile_info_ptr = nullptr;
  aurora::parser::AugmentEdge* augment_edge = nullptr;
  aurora::parser::TopolEdge* topol_edge = nullptr;

  std::vector<double> lens;
  std::vector<double> headings;
  double total_len;
  double total_turn;

  EdgeInfo(TileInfo* tile_info, uint32_t feature_id);

  bool SupportForward() const {
    if (topol_edge) {
      return (topol_edge->GetBaseInfo()->direction & 0x01) == 0x01;
    }
    return true;  // 默认双向
  }

  bool SupportBackward() const {
    if (topol_edge) {
      return (topol_edge->GetBaseInfo()->direction & 0x02) == 0x02;
    }
    return true;  // 默认双向
  }

  RouteNodeID GetStartNodeID() const {
    if (topol_edge) {
      return RouteNodeID(edge_id.tile_id,
                         topol_edge->GetBaseInfo()->start_node_id);
    }
    return RouteNodeID(0, 0);  // 返回一个无效的节点ID
  }

  RouteNodeID GetEndNodeID() const {
    if (topol_edge) {
      return RouteNodeID(edge_id.tile_id,
                         topol_edge->GetBaseInfo()->end_node_id);
    }
    return RouteNodeID(0, 0);  // 返回一个无效的节点ID
  }

  AABB2<PointLL> GetMbr() { return augment_edge->GetMbr(); }
};

typedef std::shared_ptr<EdgeInfo> EdgeInfoPtr;

class TileInfo {
 private:
  aurora::parser::RouteTileID tile_id_;
  aurora::parser::RouteTileReader reader_;
  aurora::parser::RouteTilePackagePtr tile_ptr_ = nullptr;
  std::unordered_map<uint32_t, EdgeInfoPtr> edge_info_map_;

 public:
 TileInfo(const aurora::parser::RouteTileID& tid, const aurora::parser::RouteTilePackagePtr& tptr) : tile_id_(tid), tile_ptr_(tptr){
  reader_.SetTarget(tptr);

 }

  void SetRouteTile(aurora::parser::RouteTilePackagePtr tile_ptr) {
    this->tile_ptr_ = tile_ptr;
    reader_.SetTarget(tile_ptr);
  }

  bool HasTile() const { return tile_ptr_ != nullptr; }
  std::unordered_map<uint32_t, EdgeInfoPtr>& GetEdgeInfoMap() { return edge_info_map_; }
  const std::unordered_map<uint32_t, EdgeInfoPtr>& GetEdgeInfoMap() const { return edge_info_map_; }

  EdgeInfoPtr GetEdgeInfo(uint32_t feature_id) {
    if(edge_info_map_.count(feature_id) != 0) {
      return edge_info_map_.at(feature_id);
    }
    if(feature_id >= reader_.GetAugmentEdges().size()) {
      return nullptr;
    }
    auto edge_info = std::make_shared<EdgeInfo>(this, feature_id);
    edge_info_map_[feature_id] = edge_info;
    return edge_info;
  }

  EdgeInfoPtr GetEdgeInfo(const aurora::parser::RouteEdgeID& edge_id) {
    if(tile_id_ != edge_id.tile_id) {
      return nullptr;
    }
    auto feature_id = edge_id.feature_id;
    if(edge_info_map_.count(feature_id) != 0) {
      return edge_info_map_.at(feature_id);
    }
    if(feature_id >= reader_.GetAugmentEdges().size()) {
      return nullptr;
    }
    auto edge_info = std::make_shared<EdgeInfo>(this, feature_id);
    edge_info_map_[feature_id] = edge_info;
    return edge_info;
    
  }

  friend class GraphReaderImpl;
  friend class EdgeInfo;
};
typedef std::shared_ptr<TileInfo> TileInfoPtr;

}  // namespace loc
}  // namespace aurora

#endif  // MAP_SRC_LOCATION_SRC_ENHANCE_DATA_INFO_H
