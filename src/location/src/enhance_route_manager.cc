#include "enhance_route_manager.h"

#include <fstream>
#define LOCATION_LOG_TAG "Loc::RM"
#include "debug/location_logger.h"

namespace aurora {
namespace loc {
static const double kBoundaryTolerance = 10.0;
bool EnhanceRouteManager::SetRoutePath(
    std::shared_ptr<aurora::path::PathInfo> path_info) {
  path_link_indexs_.clear();
  if (!path_info) {
    LOCATION_ERROR_LOG("Route path is null, so clear current route path");
    path_id_ = kInvalidPathID;
    return true;
  }

  path_id_ = path_info->path_id;

  for (size_t i = 0; i < path_info->links.size(); i++) {
    auto& link = path_info->links[i];
    // route_path->links[0]->
    aurora::parser::RouteEdgeID edge_id(link->tile_id, link->id);

    path_link_indexs_.emplace(DirectedEdgeId(edge_id, link->forward),
                              i);  // Initialize with 0 offset

    LOCATION_DEBUG_LOG("Set route edge: {}.{} with direction {}",
                       edge_id.tile_id.value, edge_id.feature_id,
                       (int)link->forward);
  }
  total_link_count_ = path_info->links.size();

  start_offset_ = path_info->sections[0].start_offset;
  end_offset_ = path_info->sections[0].end_offset;

  LOCATION_DEBUG_LOG("start_offset_: {} end_offset_:  {}", start_offset_,
                     end_offset_);
  path_info_ = path_info;
  return true;
}

std::shared_ptr<aurora::path::PathInfo> EnhanceRouteManager::GetRoutePath() {
  return path_info_;
}

bool EnhanceRouteManager::IsPointOnPath(const DirectedEdgeId& directed_edge_id,
                                        double offset) {
  bool is_on_path = path_link_indexs_.count(directed_edge_id) > 0;
  if (is_on_path) {
    if (path_link_indexs_[directed_edge_id] == 0) {
      if (offset < start_offset_ - kBoundaryTolerance) {
        return false;
      }
    } else if (path_link_indexs_[directed_edge_id] ==
               int(total_link_count_ - 1)) {
      if (offset > end_offset_ + kBoundaryTolerance) {
        return false;
      }
    }
  }
  return is_on_path;
}

bool EnhanceRouteManager::GetLinkInfoOnRoute(const DirectedEdgeId& edge_id,
                                             double offset, uint64_t& path_id,
                                             int& path_link_idx) {
  bool is_on_path = path_link_indexs_.count(edge_id) > 0;
  if (is_on_path) {
    if (path_link_indexs_[edge_id] == 0) {
      if (offset < start_offset_ - kBoundaryTolerance) {
        return false;
      }
    } else if (path_link_indexs_[edge_id] == int(total_link_count_ - 1)) {
      if (offset > end_offset_ + kBoundaryTolerance) {
        return false;
      }
    }
    path_link_idx = path_link_indexs_[edge_id];
    path_id = path_id_;
  }
  return is_on_path;
}

std::shared_ptr<aurora::path::PathInfo> EnhanceRouteManager::LoadRoutePathFromFile(const std::string& file_path) {
  std::shared_ptr<aurora::path::PathInfo> path_info =
      std::make_shared<aurora::path::PathInfo>();
  path_info->path_id = 0;  // Default path ID
  path_info->length = 0.0;
  path_info->travel_time = 0.0;
  path_info->traffic_light_num = 0;

  // Load path info from file
  std::ifstream file(file_path);
  if (!file.is_open()) {
    LOCATION_ERROR_LOG("Failed to open path info file: {}", file_path);
    return nullptr;
  }

  std::string line;
  while (std::getline(file, line)) {
    if (line.empty()) continue;

    if (line.find("Path ID: ") == 0) {
      try {
        path_info->path_id = std::stoull(line.substr(9));
      } catch (const std::exception& e) {
        LOCATION_ERROR_LOG("Invalid path ID format: {}", line.substr(9));
        path_info.reset();
        return path_info;
      }
    } else if (line.find("Length: ") == 0) {
      path_info->length = std::stod(line.substr(8));
    } else if (line.find("StartOffset: ") == 0) {
      aurora::path::Section section;
      section.start_offset = std::stod(line.substr(13));
      path_info->sections.push_back(section);
    } else if (line.find("EndOffset: ") == 0) {
      if (!path_info->sections.empty()) {
        path_info->sections.back().end_offset = std::stod(line.substr(11));
      }
    } else if (line == "Links:") {
      // Parse links section
      while (std::getline(file, line) && !line.empty()) {
        std::istringstream iss(line);
        auto link = std::make_shared<aurora::path::EdgeInfo>();
        bool forward;
        iss >> link->tile_id >> link->id >> forward;
        link->forward = forward;
        path_info->links.push_back(link);
      }
    }  // if
  }
  return path_info;
}

bool EnhanceRouteManager::SaveRoutePathToFile(
    const std::shared_ptr<aurora::path::PathInfo>& path_info,
    const std::string& file_path) {
  if (!path_info) return false;

  std::ofstream path_file(file_path);
  if (!path_file.is_open()) {
    LOCATION_ERROR_LOG("Failed to open {} for writing", file_path);
    return false;
  }

  path_file << "Path ID: " << path_info->path_id << "\n"
            << "Length: " << path_info->length << "\n"
            << "StartOffset: " << path_info->sections[0].start_offset << "\n"
            << "EndOffset: " << path_info->sections[0].end_offset << "\n"
            << "Links:\n";

  for (const auto& link : path_info->links) {
    path_file << link->tile_id << " " << link->id << " " << link->forward
              << "\n";
  }

  return true;
}

}  // namespace loc
}  // namespace aurora
