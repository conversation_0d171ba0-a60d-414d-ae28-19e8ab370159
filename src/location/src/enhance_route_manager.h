#if !defined(MAP_SRC_LOCATION_SRC_ENHANCE_ROUTE_MANAGER_H)
#define MAP_SRC_LOCATION_SRC_ENHANCE_ROUTE_MANAGER_H
#include "location_module.h"
#include "location_internal_def.h"

namespace aurora {
namespace loc {

#include <unordered_map>
#include "location_def.h"

class EnhanceRouteManager {
private:
    std::unordered_map<DirectedEdgeId, int> path_link_indexs_;
    uint64_t path_id_;

    double start_offset_;
    double end_offset_;
    size_t total_link_count_;
    std::shared_ptr<aurora::path::PathInfo> path_info_;
public:
    bool SetRoutePath(std::shared_ptr<aurora::path::PathInfo> path);
    std::shared_ptr<aurora::path::PathInfo> GetRoutePath();
    bool IsPointOnPath(const DirectedEdgeId& directed_edge_id, double offset);
    bool GetLinkInfoOnRoute(const DirectedEdgeId& edge_id, double offset, uint64_t& path_id, int& path_link_idx);
    static  std::shared_ptr<aurora::path::PathInfo> LoadRoutePathFromFile(const std::string& file_path);
    static bool SaveRoutePathToFile(
        const std::shared_ptr<aurora::path::PathInfo>& path_info,
        const std::string& file_path);
};

} // namespace loc
} // namespace aurora

#endif // MAP_SRC_LOCATION_SRC_ENHANCE_ROUTE_MANAGER_H
