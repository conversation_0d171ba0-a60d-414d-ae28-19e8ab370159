#ifndef MAP_SRC_LOCATION_SRC_FRAME_INFO_H
#define MAP_SRC_LOCATION_SRC_FRAME_INFO_H

#include <unordered_map>
#include <cstdint>
#include "location/src/state_info.h"

namespace aurora {
namespace loc {
struct FrameInfo {
  FrameInfo(int32_t frame_id, const GnssPosInfo& gnss_data)
      : frame_id(frame_id), gnss_data(gnss_data) {}
  FrameInfo() : frame_id(0) {}
  int32_t frame_id;
  // GNSS data
  GnssPosInfo gnss_data;
  std::unordered_map<StateId, StateInfo> state_map;

  const std::vector<const StateInfo*> FindStates(
      DirectedEdgeId edge_id) {
    std::vector<const StateInfo*> states;
    for (const auto& state_pair : state_map) {
      const auto& state_info = state_pair.second;
      if (state_info.edge_id == edge_id) {
        states.push_back(&state_info);
      }
    }
    return states;
  }
};
typedef std::shared_ptr<FrameInfo> FrameInfoPtr;

}  // namespace loc
}  // namespace aurora

#endif  // MAP_SRC_LOCATION_SRC_FRAME_INFO_H
