#include "geojson_writer.h"
#include "pointll.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/prettywriter.h"
#include <fstream>

namespace aurora {
namespace loc {

static void ConvertProperties(const std::unordered_map<std::string, boost::any>& from_props, rapidjson::Value& to_props, rapidjson::MemoryPoolAllocator<rapidjson::CrtAllocator>& allocator) {
    for (const auto& [key, val] : from_props) {
        rapidjson::Value keyValue(key.c_str(), allocator);
        if (val.type() == typeid(char)) {
            to_props.AddMember(keyValue, boost::any_cast<char>(val), allocator);
        } else if (val.type() == typeid(short)) {
            to_props.AddMember(keyValue, boost::any_cast<short>(val), allocator);
        } else if (val.type() == typeid(long)) {
            to_props.AddMember(keyValue, boost::any_cast<long>(val), allocator);
        // } else if (val.type() == typeid(const char*)) {
        //     to_props.AddMember(keyValue, std::string(boost::any_cast<const char*>(val)), allocator);
        } else if (val.type() == typeid(bool)) {
            to_props.AddMember(keyValue, boost::any_cast<bool>(val), allocator);
        } else if (val.type() == typeid(int)) {
            to_props.AddMember(keyValue, boost::any_cast<int>(val), allocator);
        } else if (val.type() == typeid(double)) {
            to_props.AddMember(keyValue, boost::any_cast<double>(val), allocator);
        } else if (val.type() == typeid(float)) {
            to_props.AddMember(keyValue, boost::any_cast<float>(val), allocator);
        } else if (val.type() == typeid(int8_t)) {
            to_props.AddMember(keyValue, boost::any_cast<int8_t>(val), allocator);
        } else if (val.type() == typeid(uint8_t)) {
            to_props.AddMember(keyValue, boost::any_cast<uint8_t>(val), allocator);
        } else if (val.type() == typeid(int16_t)) {
            to_props.AddMember(keyValue, boost::any_cast<int16_t>(val), allocator);
        } else if (val.type() == typeid(uint16_t)) {
            to_props.AddMember(keyValue, boost::any_cast<uint16_t>(val), allocator);
        } else if (val.type() == typeid(int32_t)) {
            to_props.AddMember(keyValue, boost::any_cast<int32_t>(val), allocator);
        } else if (val.type() == typeid(uint32_t)) {
            to_props.AddMember(keyValue, boost::any_cast<uint32_t>(val), allocator);
        } else if (val.type() == typeid(int64_t)) {
            to_props.AddMember(keyValue, boost::any_cast<int64_t>(val), allocator);
        } else if (val.type() == typeid(uint64_t)) {
            to_props.AddMember(keyValue, boost::any_cast<uint64_t>(val), allocator);
        } else if (val.type() == typeid(std::string)) {
            to_props.AddMember(keyValue, 
                          rapidjson::Value(boost::any_cast<std::string>(val).c_str(), allocator),
                          allocator);
        }
    }
}

GeoJsonWriter::GeoJsonWriter(const std::string& path) : path_(path) {
    doc_.SetObject();
    auto& allocator = doc_.GetAllocator();
    rapidjson::Value features(rapidjson::kArrayType);
    doc_.AddMember("type", "FeatureCollection", allocator);
    doc_.AddMember("features", features, allocator);
}

GeoJsonWriter::~GeoJsonWriter() {
    if(!reset_) {
        rapidjson::StringBuffer buffer;
        rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
        doc_.Accept(writer);

        std::ofstream out(path_);
        out << buffer.GetString();
        out.close();
    }
}

void GeoJsonWriter::AppendLineString(const std::vector<PointLL>& line_string, 
                                   const std::unordered_map<std::string, boost::any>& properties) {
    auto& allocator = doc_.GetAllocator();
    rapidjson::Value feature(rapidjson::kObjectType);
    feature.AddMember("type", "Feature", allocator);

    // Add geometry
    rapidjson::Value geometry(rapidjson::kObjectType);
    geometry.AddMember("type", "LineString", allocator);
    
    rapidjson::Value coordinates(rapidjson::kArrayType);
    for (const auto& point : line_string) {
        rapidjson::Value coord(rapidjson::kArrayType);
        coord.PushBack(point.lng(), allocator);
        coord.PushBack(point.lat(), allocator);
        coordinates.PushBack(coord, allocator);
    }
    geometry.AddMember("coordinates", coordinates, allocator);
    feature.AddMember("geometry", geometry, allocator);

    // Add properties
    rapidjson::Value props(rapidjson::kObjectType);
    ConvertProperties(properties, props, allocator);
    feature.AddMember("properties", props, allocator);

    doc_["features"].PushBack(feature, allocator);
}

void GeoJsonWriter::AppendLineString(const std::vector<PointXY<double>>& line_string, 
    const std::unordered_map<std::string, boost::any>& properties) {
auto& allocator = doc_.GetAllocator();
rapidjson::Value feature(rapidjson::kObjectType);
feature.AddMember("type", "Feature", allocator);

// Add geometry
rapidjson::Value geometry(rapidjson::kObjectType);
geometry.AddMember("type", "LineString", allocator);

rapidjson::Value coordinates(rapidjson::kArrayType);
for (const auto& point : line_string) {
rapidjson::Value coord(rapidjson::kArrayType);
coord.PushBack(point.x(), allocator);
coord.PushBack(point.y(), allocator);
coordinates.PushBack(coord, allocator);
}
geometry.AddMember("coordinates", coordinates, allocator);
feature.AddMember("geometry", geometry, allocator);

// Add properties
rapidjson::Value props(rapidjson::kObjectType);
ConvertProperties(properties, props, allocator);
feature.AddMember("properties", props, allocator);

doc_["features"].PushBack(feature, allocator);
}

void GeoJsonWriter::AppendPoint(const PointLL& pnt, 
                              const std::unordered_map<std::string, boost::any>& properties) {
    auto& allocator = doc_.GetAllocator();
    rapidjson::Value feature(rapidjson::kObjectType);
    feature.AddMember("type", "Feature", allocator);

    // Add geometry
    rapidjson::Value geometry(rapidjson::kObjectType);
    geometry.AddMember("type", "Point", allocator);
    
    rapidjson::Value coordinates(rapidjson::kArrayType);
    coordinates.PushBack(pnt.lng(), allocator);
    coordinates.PushBack(pnt.lat(), allocator);
    geometry.AddMember("coordinates", coordinates, allocator);
    feature.AddMember("geometry", geometry, allocator);

    // Add properties
    rapidjson::Value props(rapidjson::kObjectType);
    ConvertProperties(properties, props, allocator);
    feature.AddMember("properties", props, allocator);

    doc_["features"].PushBack(feature, allocator);
}

}  // namespace loc
}  // namespace aurora
