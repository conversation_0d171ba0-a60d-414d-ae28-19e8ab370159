#ifndef MAP_SRC_LOCATION_SRC_GEOJSON_WRITER_H
#define MAP_SRC_LOCATION_SRC_GEOJSON_WRITER_H
#include "enhance_data_info.h"
#include "rapidjson/document.h"
#include "boost/any.hpp"
#include <unordered_map>

namespace aurora {
namespace loc {
class GeoJsonWriter {
  private:
    std::string path_;
    rapidjson::Document doc_;
    bool reset_ = false;
 public:
    typedef typename std::unordered_map<std::string, boost::any> PropertiesType;
    GeoJsonWriter(const std::string& path);
    ~GeoJsonWriter();
    void AppendLineString(const std::vector<PointLL>& line_string, const PropertiesType& properties);
    void AppendLineString(const std::vector<PointXY<double>>& line_string, const PropertiesType& properties);
    void AppendPoint(const PointLL& pnt, const PropertiesType& properties);
    void Reset() {reset_ = true; }
};
}  // namespace loc
}  // namespace aurora

#endif  // MAP_SRC_LOCATION_SRC_GEOJSON_WRITER_H
