#ifndef MAP_SRC_LOCATION_INCLUDE_GRAPH_READER_H
#define MAP_SRC_LOCATION_INCLUDE_GRAPH_READER_H

#include <memory>
#include <vector>

#include "base/include/pointll.h"
#include "data_provider/include/data_provider.h"
#include "data_provider/include/route_data/route_data_def.h"
#include "data_provider/include/route_data/route_tile_reader.h"
#include "location/include/location_def.h"
#include "location/include/location_module.h"
#include "location/src/location_internal_def.h"
#include "enhance_route_manager.h"

#include "enhance_data_info.h"

namespace aurora {
namespace loc {
using aurora::parser::RouteNodeID;

class GraphReader {
public:

  virtual ~GraphReader() = default;
  virtual std::vector<DirectedEdgeId> GetInEdgeIds(const aurora::parser::RouteNodeID& node_id) = 0;
  virtual std::vector<DirectedEdgeId> GetOutEdgeIds(const aurora::parser::RouteNodeID& node_id) = 0;
  virtual std::vector<EdgeInfoPtr> GetEdgeInfos(const aurora::AABB2<aurora::PointLL>& mbr) = 0;
  virtual EdgeInfoPtr GetEdgeInfo(const aurora::parser::RouteEdgeID& edge_id) = 0;

};

}  // namespace loc
}  // namespace aurora

#endif  // MAP_SRC_LOCATION_INCLUDE_GRAPH_READER_H
