#include "location/src/graph_reader_impl.h"
#include "base/include/util.h"

#define LOCATION_LOG_TAG "Loc::GR"
#include "location/src/debug/location_logger.h"

namespace aurora {
namespace loc {

GraphReaderImpl::GraphReaderImpl(const std::string& data_dir) 
    : provider_() {
  if (!provider_.InitRouteParser(data_dir.c_str())) {
    LOCATION_ERROR_LOG("Failed to initialize route parser with data directory: {}", data_dir);
  }
}

std::vector<DirectedEdgeId> GraphReaderImpl::GetInEdgeIds(const aurora::parser::RouteNodeID& node_id) {
  auto tile_id = node_id.tile_id;
  auto& tile_info = tile_cache_map_[tile_id];
  if (!tile_info) {
    tile_info = std::make_shared<TileInfo>(tile_id, provider_.GetRouteTileByID(tile_id));
    if (!tile_info->HasTile()) {
      LOCATION_ERROR_LOG("Tile not found for node: {}.{}", tile_id.value, node_id.feature_id);
      return {};
    }
  }
  
  std::vector<aurora::parser::RouteEdgeID> edge_ids;
  std::vector<bool> is_forward_vec;
  tile_info->reader_.GetInEdgeID(node_id, edge_ids, is_forward_vec);

  std::vector<DirectedEdgeId> result;
  for (size_t i = 0; i < edge_ids.size(); ++i) {
    DirectedEdgeId directed_edge;
    directed_edge.edge_id = edge_ids[i];
    directed_edge.is_forward = is_forward_vec[i];
    result.push_back(directed_edge);
  }
  return result;
}

std::vector<DirectedEdgeId> GraphReaderImpl::GetOutEdgeIds(const aurora::parser::RouteNodeID& node_id) {
  auto tile_id = node_id.tile_id;
  auto& tile_info = tile_cache_map_[tile_id];
  if (!tile_info) {
    tile_info = std::make_shared<TileInfo>(tile_id, provider_.GetRouteTileByID(tile_id));
    if (!tile_info->HasTile()) {
      LOCATION_ERROR_LOG("Tile not found for node: {}.{}", tile_id.value, node_id.feature_id);
      return {};
    }
  }
  
  std::vector<aurora::parser::RouteEdgeID> edge_ids;
  std::vector<bool> is_forward_vec;
  tile_info->reader_.GetOutEdgeID(node_id, edge_ids, is_forward_vec);

  std::vector<DirectedEdgeId> result;
  for (size_t i = 0; i < edge_ids.size(); ++i) {
    DirectedEdgeId directed_edge;
    directed_edge.edge_id = edge_ids[i];
    directed_edge.is_forward = is_forward_vec[i];
    result.push_back(directed_edge);
  }
  return result;
}

std::vector<EdgeInfoPtr> GraphReaderImpl::GetEdgeInfos(const aurora::AABB2<aurora::PointLL>& mbr) {
  std::vector<EdgeInfoPtr> edge_infos;
  std::vector<aurora::parser::RouteTileID> tile_ids;
  provider_.GetRouteTileIDsByMBR(0, mbr, tile_ids);

  for (const auto& tile_id : tile_ids) {
    auto& tile_info = tile_cache_map_[tile_id];
    if (!tile_info) {
      tile_info = std::make_shared<TileInfo>(tile_id, provider_.GetRouteTileByID(tile_id));
      if (!tile_info->HasTile()) {
        LOCATION_ERROR_LOG("Tile not found: {}", tile_id.value);
        continue;
      }
    }
    
    std::vector<uint32_t> edge_ids = tile_info->reader_.GetAugmentEdgesByMbr(mbr);
    for (const auto& feature_id : edge_ids) {
      aurora::parser::RouteEdgeID edge_id(tile_id, feature_id);
      EdgeInfoPtr edge_info = GetEdgeInfo(edge_id);
      if (edge_info) {
        edge_infos.push_back(edge_info);
      } else {
        LOCATION_ERROR_LOG("Edge not found in tile: {}.{}", tile_id.value, feature_id);
      }
    }
  }
  return edge_infos;
}

EdgeInfoPtr GraphReaderImpl::GetEdgeInfo(const aurora::parser::RouteEdgeID& edge_id) {
  auto tile_id = edge_id.tile_id;
  auto& tile_info = tile_cache_map_[tile_id];
  if (tile_info == nullptr) {
    tile_info = std::make_shared<TileInfo>(tile_id, provider_.GetRouteTileByID(tile_id));
    if (!tile_info->HasTile()) {
      LOCATION_ERROR_LOG("Tile not found: {}", tile_id.value);
      tile_info = nullptr;
      return nullptr;
    }
  }
  
  EdgeInfoPtr edge_info = tile_info->GetEdgeInfo(edge_id.feature_id);
  if (!edge_info) {
    LOCATION_ERROR_LOG("Edge not found in tile: {}.{}", tile_id.value, edge_id.feature_id);
  }
  return edge_info;
}

}  // namespace loc
}  // namespace aurora
