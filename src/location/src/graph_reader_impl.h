#ifndef MAP_SRC_LOCATION_INCLUDE_GRAPH_READER_IMPL_H
#define MAP_SRC_LOCATION_INCLUDE_GRAPH_READER_IMPL_H

#include "graph_reader.h"
#include "location_internal_def.h"
#include "enhance_route_manager.h"
#include "location/include/location_module.h"
#include "data_provider/include/data_provider.h"
#include "data_provider/include/route_data/route_data_def.h"
#include "data_provider/include/route_data/route_tile_reader.h"

namespace aurora {
namespace loc {

class GraphReaderImpl : public GraphReader {
public:
  GraphReaderImpl(const std::string& data_dir);
  virtual ~GraphReaderImpl() = default;

  std::vector<DirectedEdgeId> GetInEdgeIds(const aurora::parser::RouteNodeID& node_id) override;
  std::vector<DirectedEdgeId> GetOutEdgeIds(const aurora::parser::RouteNodeID& node_id) override;
  std::vector<EdgeInfoPtr> GetEdgeInfos(const aurora::AABB2<aurora::PointLL>& mbr) override;
  EdgeInfoPtr GetEdgeInfo(const aurora::parser::RouteEdgeID& edge_id) override;
private:

  aurora::parser::DataProvider provider_;
  std::unordered_map<aurora::parser::RouteTileID, TileInfoPtr> tile_cache_map_;
};

}  // namespace loc
}  // namespace aurora

#endif  // MAP_SRC_LOCATION_INCLUDE_GRAPH_READER_IMPL_H
