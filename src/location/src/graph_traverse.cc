#include "graph_traverse.h"
#include "graph_reader.h"

#include <queue>
#include <unordered_map>
#include <functional>
#define LOCATION_LOG_TAG "Loc::GT"
#include "location/src/debug/location_logger.h"
namespace aurora {
namespace loc {

struct EdgeTraverseState {
    EdgeTraverseState(double dist, double tn, double pre_outheading, const aurora::loc::DirectedEdgeId& prev, const aurora::loc::DirectedEdgeId& edge_id) : distance(dist), turn(tn),pre_out_heading(pre_outheading), prev_edge_id(prev), edge_id(edge_id) {
    }

    double distance; // 距离累积
    double turn; // 转向累积
    double pre_out_heading;
    aurora::loc::DirectedEdgeId prev_edge_id;
    aurora::loc::DirectedEdgeId edge_id;

    bool operator < (const EdgeTraverseState& other) const {
        return distance > other.distance;
    }
};


GraphTraverse::GraphTraverse(GraphReader& reader) : reader_(reader) {}

std::vector<ReachableEdgeInfo> GraphTraverse::DistanceFirstTraverse(const TraverseParams& params) {
    std::vector<ReachableEdgeInfo> result;
    std::unordered_map<DirectedEdgeId, DirectedEdgeId> visited;
    std::priority_queue<EdgeTraverseState, std::vector<EdgeTraverseState>> queue;

    // Start with initial edge
    queue.emplace(-params.from_offset, 0.0, 0.0, params.from_edge, params.from_edge);
    // visited.insert({params.from_edge, params.from_edge});

    while (!queue.empty()) {
        auto [current_dist, cur_turn, pre_out_heading, prev_edge_id, current_edge] = queue.top();
        queue.pop();

        // here
        if (visited.count(current_edge)) {
            continue;
        } else {
            visited.insert({current_edge, prev_edge_id});
        }
        

        if (current_dist > params.max_distance) {
            LOCATION_DEBUG_LOG("continue because current_dist = {} > {}", current_dist , params.max_distance);
            continue;
        }

        if (cur_turn > params.max_turn) {
            LOCATION_DEBUG_LOG("continue because cur_turn = {} > {}", cur_turn , params.max_turn);
            continue;
        }
        


        // Add to results
        auto edge_info = reader_.GetEdgeInfo(current_edge.edge_id);
        double out_heading =  current_edge.is_forward? edge_info->headings.back() : std::fmod(edge_info->headings.front() + 180.0, 360.0);
        if(visited.size() > 1) {
            double in_heading = current_edge.is_forward ? edge_info->headings.front() : std::fmod(edge_info->headings.back() + 180.0, 360.0);
            double turn_angle = std::abs(pre_out_heading - in_heading);
            if (turn_angle > 180.0) {
                turn_angle = 360.0 - turn_angle;
            }
            cur_turn += turn_angle;
            cur_turn += edge_info->total_turn;
        }

        if (cur_turn > params.max_turn) {
            LOCATION_DEBUG_LOG("continue because cur_turn = {} > {}", cur_turn , params.max_turn);
            continue;
        }
        LOCATION_DEBUG_LOG("process current_dist = {}  {} {}.{}", current_dist , cur_turn, current_edge.edge_id.feature_id, current_edge.is_forward);

        // if in target MBR, add to results
        if (params.target_mbr.Intersects(edge_info->GetMbr())) {
            ReachableEdgeInfo info;
            info.edge_id = current_edge;
            info.distance = current_dist;
            info.from_edge = params.from_edge;
            info.from_offset = params.from_offset;
            info.turn_angle = cur_turn;
            DirectedEdgeId im =  current_edge;
            
            while(visited[im] != im) {
                im = visited[im];
                info.intermediates.push_back(im);
            }
            // info.intermediates = {current_edge};
            result.push_back(info);
        }

        aurora::parser::RouteNodeID node_id = current_edge.is_forward ? edge_info->GetEndNodeID() : edge_info->GetStartNodeID();
        std::vector<DirectedEdgeId> out_edges = reader_.GetOutEdgeIds(node_id);

        for (const auto& next_edge : out_edges) {
            

            queue.emplace(edge_info->total_len + current_dist, cur_turn, out_heading, current_edge, next_edge);
        }
    }

    return result;
}

}  // namespace loc
}  // namespace aurora
