#ifndef MAP_SRC_LOCATION_INCLUDE_GRAPH_TRAVERSE_H
#define MAP_SRC_LOCATION_INCLUDE_GRAPH_TRAVERSE_H
#include "location_internal_def.h"
namespace aurora {
namespace loc {
class GraphReader;

struct ReachableEdgeInfo {
    DirectedEdgeId edge_id;
    double distance;
    double turn_angle;
    DirectedEdgeId from_edge;
    double from_offset;
    std::vector<DirectedEdgeId> intermediates;
};

struct TraverseParams {
    DirectedEdgeId from_edge;
    double from_offset;
    AABB2<PointLL> target_mbr;
    double max_distance;
    double max_turn;
};

class GraphTraverse {
 private:
    GraphReader& reader_;
 public:
    GraphTraverse(GraphReader& reader);
    std::vector<ReachableEdgeInfo> DistanceFirstTraverse(const TraverseParams& params);

};
};  // namespace loc
};  // namespace aurora

#endif  // MAP_SRC_LOCATION_INCLUDE_GRAPH_TRAVERSE_H
