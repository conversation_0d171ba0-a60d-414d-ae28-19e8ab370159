#ifndef LOCATION_SRC_INTERNAL_DATA_DUMPER_H
#define LOCATION_SRC_INTERNAL_DATA_DUMPER_H

#include <string>
namespace aurora {
namespace loc {
class InternalDataDumper {
 public:
  virtual ~InternalDataDumper() = default;

  virtual void DumpStates(const std::string& internal_state_dir) = 0;
  virtual void DumpEdges(const std::string& accessed_edges_file) = 0;
};
}  // namespace loc
}  // namespace aurora
#endif  // LOCATION_SRC_INTERNAL_DATA_DUMPER_H
