#include "internal_data_dumper_impl.h"

#include "geojson_writer.h"

#define LOCATION_LOG_TAG "Loc::Dump"

#include "location/src/debug/location_logger.h"
#include <unordered_set>
#include <fstream>

namespace aurora {
namespace loc {
#define UPDATE_PROPS(name) props[#name] = state_info.name

void InternalDataDumperImpl::DumpStates(const std::string& internal_state_dir) {

    auto& c = engine_.GetStateContainer();
    int frame_cnt = c.GetCurrentFrameId();
    FrameInfo* prev_frame_info = nullptr;
    std::string state_info_fname_overall = internal_state_dir + "/000.geojson";
    GeoJsonWriter overall_writer(state_info_fname_overall);
    std::vector<PointLL> gps_track;
    GeoJsonWriter break_writer(internal_state_dir + "/000_breaks.geojson");


    DumpEdges(internal_state_dir + "/0000_edges.geojson");

    std::array<std::unique_ptr<GeoJsonWriter>, 3> top_n = {
        std::make_unique<GeoJsonWriter>(internal_state_dir + "/0000_top1.geojson"),
        std::make_unique<GeoJsonWriter>(internal_state_dir + "/0000_top2.geojson"),
        std::make_unique<GeoJsonWriter>(internal_state_dir + "/0000_top3.geojson")

    };

    int break_cnt = 0;
    StateId pre_state_id = 0;
    for(int i=1; i<=frame_cnt; i++) {
        FrameInfo* info = c.GetFrameInfo(i);
        std::string zero_padding = i >= 100? "" : (i >= 10 ? "0" : "00");
        if(info) {
           std::string state_info_fname = internal_state_dir + "/" + zero_padding + std::to_string(i) + ".geojson";
           GeoJsonWriter state_writer(state_info_fname);
           gps_track.push_back(info->gnss_data.lnglat);
           {
                // write origin gps pos
                GeoJsonWriter::PropertiesType props;
                props["frame_id"] = i;
                props["speed"] = info->gnss_data.speed;
                props["heading"] = info->gnss_data.heading;
                props["mode"] = info->gnss_data.mode;
                props["timestamp"] = info->gnss_data.timestamp;
                state_writer.AppendPoint(info->gnss_data.lnglat, props);
                overall_writer.AppendPoint(info->gnss_data.lnglat, props);
           }
           
           // 优先队列保存最大的10个状态
        //    std::vector<std::pair<StateId, const StateInfo*>> sorted_states;

           for(const auto& state_pair : info->state_map) {
                auto& state_info = state_pair.second;

                // if(state_info.total_score < 0.01) {
                //     continue;  // Skip low score states
                // }
                // sorted_states.emplace_back(state_info.id, &state_info);
                
                GeoJsonWriter::PropertiesType props;
                UPDATE_PROPS(id);
                UPDATE_PROPS(history_link_len);
                props["frame_id"] = i;
                props["tile_id"] = (state_info.edge_id.edge_id.tile_id.value);
                props["edge_id"] = (state_info.edge_id.edge_id.feature_id);

                UPDATE_PROPS(project_angle);
                UPDATE_PROPS(edge_offset);
                UPDATE_PROPS(order);
                UPDATE_PROPS(heading);
                UPDATE_PROPS(heading_score);
                UPDATE_PROPS(distance_score);
                UPDATE_PROPS(distance);
                UPDATE_PROPS(route_score);
                UPDATE_PROPS(forward_score);
                UPDATE_PROPS(prev_total_score);
                UPDATE_PROPS(parent_id);
                UPDATE_PROPS(path_link_index);
                UPDATE_PROPS(path_id);
                UPDATE_PROPS(total_score);

                state_writer.AppendPoint(state_info.proj_pos, props);
                for(auto& link: state_info.forward_links) {
                    GeoJsonWriter::PropertiesType link_props;
                    props["frame_id"] = i;
                    link_props["forward_score"] = link.forward_score;
                    link_props["from_id"] = link.from_id;
                    link_props["prev_total_score"] = link.prev_total_score;
                    link_props["to_id"] = state_info.id;
                    link_props["history_link_len"] = link.history_link_len;
                    link_props["movement_vector_score"] = link.movement_vector_score;
                    
                    link_props["road_distance"] = link.road_distance;
                    state_writer.AppendLineString(std::vector<PointLL>({prev_frame_info->state_map.at(link.from_id).proj_pos, state_info.proj_pos}), link_props);
                }

                if(state_info.order == 0) {
                    if(pre_state_id != state_info.parent_id) {
                        break_cnt++;
                        break_writer.AppendLineString(std::vector<PointLL>({info->gnss_data.lnglat, state_info.proj_pos}), props);
                    }
                    pre_state_id = state_info.id;
                }

                if(size_t(state_info.order) < top_n.size() && size_t(state_info.order) >= 0 ) {

                    GeoJsonWriter::PropertiesType props_with_forward_link = props;
                    // props["parent_id"] = topn_state_info->parent_id;
                    if(state_info.forward_links.size() > 0 && state_info.best_link_idx < state_info.forward_links.size()) {
                        auto& best_link = state_info.forward_links[state_info.best_link_idx];
                        props["parent_id"] = best_link.from_id;
                        props["best_link_idx"] = state_info.best_link_idx;
                        props["best_forward_score"] = best_link.forward_score;
                        props["movement_vector_score"] = best_link.movement_vector_score;
                    }
        
                   

                    top_n[state_info.order]->AppendLineString(std::vector<PointLL>({info->gnss_data.lnglat, state_info.proj_pos}), props);

                }
           }
           if(info->state_map.size() > 0) {
                
                
           } else {
             pre_state_id = -1;
             LOCATION_ERROR_LOG("InternalDataDumper No states found in frame {}", i);
           }
           
        }
        prev_frame_info = info;
    }
    overall_writer.AppendLineString(gps_track, GeoJsonWriter::PropertiesType());

    std::ofstream statistics_file(internal_state_dir + "/statistics.txt");

    statistics_file << "Total frames: " << frame_cnt << "\n";
    statistics_file << "Break count: " << break_cnt << "\n";
    if(break_cnt == 0) {
        break_writer.Reset();
    }

    std::ofstream gpx_trace_file(internal_state_dir + "/gps_trace.gpx");



    if(gpx_trace_file.is_open()) {
        gpx_trace_file << std::fixed << std::setprecision(7); // Set precision for GPS coordinates
        gpx_trace_file << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        gpx_trace_file << "<gpx version=\"1.1\" creator=\"Aurora Location Module\">\n";
        gpx_trace_file << "<trk>\n";
        gpx_trace_file << "<name>GPS Track</name>\n";
        gpx_trace_file << "<trkseg>\n";
        for(const auto& pnt : gps_track) {
            gpx_trace_file << "<trkpt lat=\"" << pnt.lat() << "\" lon=\"" << pnt.lng() << "\">\n";
            gpx_trace_file << "</trkpt>\n";
        }

        gpx_trace_file << "</trkseg>\n";
        gpx_trace_file << "</trk>\n";
        gpx_trace_file << "</gpx>\n";
    }

}
void InternalDataDumperImpl::DumpEdges(const std::string& accessed_edges_file) {
    auto& c = engine_.GetStateContainer();
    std::unordered_set<aurora::parser::RouteEdgeID> edge_set;
    for(int i=1; i<=c.GetCurrentFrameId(); i++) {
        FrameInfo* info = c.GetFrameInfo(i);
        if(info) {
            for(const auto& state_pair : info->state_map) {
                const auto& state_info = state_pair.second;
                edge_set.insert(state_info.edge_id.edge_id);
            }
        }
    }

    GeoJsonWriter writer(accessed_edges_file);
    for(const auto& edge_id : edge_set) {
        GeoJsonWriter::PropertiesType props;
        props["tile_id"] = edge_id.tile_id.value;
        props["edge_id"] = edge_id.feature_id;
        EdgeInfoPtr edge_info = engine_.GetGraphReader().GetEdgeInfo(edge_id);
        writer.AppendLineString(edge_info->augment_edge->GetGeoPoints(), props);
    }
}

}  // namespace loc
}  // namespace aurora