#ifndef LOCATION_SRC_INTERNAL_DATA_DUMPER_IMPL_H
#define LOCATION_SRC_INTERNAL_DATA_DUMPER_IMPL_H

#include <string>
#include "internal_data_dumper.h"
#include "map_matching_engine.h"
namespace aurora {
namespace loc {
class InternalDataDumperImpl : public InternalDataDumper {
    MapMatchingEngine& engine_;
 public:
  InternalDataDumperImpl(MapMatchingEngine& engine): engine_(engine) {}
  virtual ~InternalDataDumperImpl() = default;

  void DumpStates(const std::string& internal_state_dir) override;
  void DumpEdges(const std::string& accessed_edges_file) override;
};
}  // namespace loc
}  // namespace aurora
#endif  // LOCATION_SRC_INTERNAL_DATA_DUMPER_IMPL_H
