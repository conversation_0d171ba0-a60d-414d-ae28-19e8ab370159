#ifndef MAP_SRC_LOCATION_SRC_LOCATION_INTERNAL_DEF_H
#define MAP_SRC_LOCATION_SRC_LOCATION_INTERNAL_DEF_H

#include <unordered_map>
#include <cinttypes>
#include "data_provider/include/route_data/feature/augment_edge.h"
#include "data_provider/include/route_data/route_tile_reader.h"
#include "location/include/location_def.h"

namespace aurora {
namespace loc {
typedef uint64_t StateId;



class EdgeDirectionType {
  private:
    uint8_t direction_ = 0;  // 0: unknown, 1: forward, 2: backward, 3: both
  public:
  EdgeDirectionType() : direction_(0) {}
  EdgeDirectionType(bool forward, bool backward) {
    if(forward) {
      direction_ = 0x01;
    }
    if(backward) {
      direction_ |= 0x02;
    }
  }
  void SetForward(bool is_forward) {
    if (is_forward) {
      direction_ |= 0x01;  // Set forward bit
    } else {
      direction_ &= ~0x01;  // Clear forward bit
    }
  }
  void SetBackward(bool is_backward) {
    if (is_backward) {
      direction_ |= 0x02;  // Set backward bit
    } else {
      direction_ &= ~0x02;  // Clear backward bit
    }
  }

  bool IsForward() const { return (direction_ & 0x01) != 0; }
  bool IsBackward() const { return (direction_ & 0x02) != 0; } 

  static const EdgeDirectionType kUnknown;
  static const EdgeDirectionType kForward;
  static const EdgeDirectionType kBackward;
  static const EdgeDirectionType kBoth;

};

struct DirectedEdgeId {
  DirectedEdgeId() : edge_id(0, 0), is_forward(true) {}
  // operators
  bool operator==(const DirectedEdgeId& other) const {
    return edge_id == other.edge_id && is_forward == other.is_forward;
  }
  bool operator!=(const DirectedEdgeId& other) const { return !(*this == other); }

  DirectedEdgeId(aurora::parser::RouteEdgeID edge_id_, bool is_forward_)
      : edge_id(edge_id_), is_forward(is_forward_) {}
  aurora::parser::RouteEdgeID edge_id;  // The base edge ID
  bool is_forward;
};


const uint64_t kInvalidPathID = -1;  // Invalid path ID

}  // namespace loc
}  // namespace aurora

namespace std {
template <>
struct hash<aurora::loc::DirectedEdgeId> {
  size_t operator()(const aurora::loc::DirectedEdgeId& direct_edge) const {
    return std::hash<aurora::parser::RouteEdgeID>()(direct_edge.edge_id) ^
           (direct_edge.is_forward ? 0x8F23845238465212 : 0x48F325DD20213846);
  }
};
};  // namespace std

#endif  // MAP_SRC_LOCATION_SRC_LOCATION_INTERNAL_DEF_H
/* EOF */
