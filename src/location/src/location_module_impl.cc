#include "location/src/location_module_impl.h"

#include "base/include/errorcode.h"
#include "location/src/match_policy.h"

#include "location/src/simulator_controller_impl.h"
#define LOCATION_LOG_TAG "Loc::Module"
#include "location/src/debug/location_logger.h"

namespace aurora {
namespace loc {

LocationModuleImpl::LocationModuleImpl() {
}

LocationModuleImpl::~LocationModuleImpl() {
  LOCATION_DEBUG_LOG("LocationModuleImpl destructor called");
  map_matching_engine_old_->SetLocationApi(nullptr);
}

int32_t LocationModuleImpl::Prepare(const std::string &config) {
  map_matching_engine_old_ =
  MapMatchingEngineFactory::GetInstance().CreateEngine(config);
      map_matching_engine_old_->SetLocationApi(this);
  return ErrorCode::kErrorCodeOk;
}

int32_t LocationModuleImpl::Init(const InterfaceFinder &finder) {
  // 可以在这里使用InterfaceFinder获取其他模块提供的接口
  return ErrorCode::kErrorCodeOk;
}

int32_t LocationModuleImpl::Start() {
  // 启动相关功能
  return ErrorCode::kErrorCodeOk;
}

int32_t LocationModuleImpl::Stop() {
  // 停止相关功能
  return ErrorCode::kErrorCodeOk;
}

int32_t LocationModuleImpl::UnInit() {
  // 清理资源
  return ErrorCode::kErrorCodeOk;
}

ModuleId LocationModuleImpl::GetModuleId() const {
  return ModuleId::kModuleIdLocation;
}

int32_t LocationModuleImpl::UpdateGnssInfo(const GnssPosInfo &pos) {
  // TODO: Implement GNSS position update handling
  bool is_success = true;
  MatchResult result = map_matching_engine_old_->MatchGPS(pos, is_success);

  LOCATION_INFO_LOG("MatchResult.Org: timestamp={}, lnglat=({}, {}), speed={}, heading={} Result: lnglat=({}, {})",
           result.origin_pos.timestamp, result.origin_pos.lnglat.lng(),
           result.origin_pos.lnglat.lat(), result.origin_pos.speed,
           result.origin_pos.heading, 
           result.road_match_info.proj_pos.lng(),
           result.road_match_info.proj_pos.lat()
          );
  if(is_success ) {
    last_match_result_ = result;
    for (auto &listener : map_matching_listeners_) {
      if (listener) {
        listener->OnMapMatchingResult(result);
      }
    }
  } else {
    LOCATION_WARN_LOG("Map matching failed for position: timestamp={}, lnglat=({}, {})",
                  pos.timestamp, pos.lnglat.lng(), pos.lnglat.lat());
  }
  return ErrorCode::kErrorCodeOk;
}

MatchCandidate LocationModuleImpl::MatchPosition(const aurora::PointLL &point,
                                                 float radius) {
  return map_matching_engine_old_->MatchPosition(point, radius);
}

int32_t LocationModuleImpl::SetRoutePath(PathInfoPtr route_path) {
  // TODO: Implement route path setting
  map_matching_engine_old_->GetEnhanceRouteManager().SetRoutePath(route_path);
  return ErrorCode::kErrorCodeOk;
}

int32_t LocationModuleImpl::SwitchParallelRoad(ParallelRoadType type) {
  // TODO: Implement parallel road switching
  return ErrorCode::kErrorCodeOk;
}

int32_t LocationModuleImpl::AddDRListener(const DRListenerPtr callback) {
  if (callback == nullptr) {
    return ErrorCode::kErrorCodeParamNullPointer;
  }
  // Register the listener
  dr_listeners_.insert(callback);
  return 0;  // Return registration ID
}

int32_t LocationModuleImpl::RemoveDRListener(const DRListenerPtr callback) {
  if (callback == nullptr) {
    return ErrorCode::kErrorCodeParamNullPointer;
  }
  auto it = dr_listeners_.find(callback);
  if (it != dr_listeners_.end()) {
    dr_listeners_.erase(it);
  } else {
    return ErrorCode::kErrorCodeInvalidParam;  // Listener not found
  }
  return ErrorCode::kErrorCodeOk;
}

int32_t LocationModuleImpl::AddMapMatchingListener(
    const MapMatchingListenerPtr callback) {
  if (callback == nullptr) {
    return ErrorCode::kErrorCodeParamNullPointer;
  }
  // Register the listener
  map_matching_listeners_.insert(callback);
  return ErrorCode::kErrorCodeOk;
  ;  // Return registration ID
}

int32_t LocationModuleImpl::RemoveMapMatchingListener(
    const MapMatchingListenerPtr callback) {
  if (callback == nullptr) {
    return ErrorCode::kErrorCodeParamNullPointer;
  }
  auto it = map_matching_listeners_.find(callback);
  if (it != map_matching_listeners_.end()) {
    map_matching_listeners_.erase(it);
  } else {
    return ErrorCode::kErrorCodeInvalidParam;  // Listener not found
  }
  return ErrorCode::kErrorCodeOk;
}

std::shared_ptr<SimulatorController> LocationModuleImpl::GetSimulatorController() {
  simulator_controller_ =  map_matching_engine_old_->GetSimulatorController();
  // auto wr = weak_from_this();
  // simulator_controller_->BindToInterface(this);
  return simulator_controller_;
}

const MatchResult& LocationModuleImpl::GetLastMatchResult() const {
  return last_match_result_;
}

const EdgeDirectionType EdgeDirectionType::kBackward(false, true);
const EdgeDirectionType EdgeDirectionType::kBoth(true, true);
const EdgeDirectionType EdgeDirectionType::kForward(true, false);
const EdgeDirectionType EdgeDirectionType::kUnknown(false, false);


}  // namespace loc
}  // namespace aurora
/* EOF */
