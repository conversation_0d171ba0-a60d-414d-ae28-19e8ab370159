#ifndef MAP_SRC_LOCATION_SRC_LOCATION_MODULE_IMPL_H
#define MAP_SRC_LOCATION_SRC_LOCATION_MODULE_IMPL_H

#include <unordered_set>

#include "base/include/module.h"
#include "location/include/location_module.h"
#include "location/src/graph_reader.h"
#include "location/src/map_matching_engine.h"
#include "location/src/match_policy.h"

namespace aurora {
namespace loc {

class LocationModuleImpl : public ILocation {
 public:
  LocationModuleImpl();
  ~LocationModuleImpl();

  int32_t Prepare(const std::string &config);
  int32_t Init(const InterfaceFinder &finder);
  int32_t Start();
  int32_t Stop();
  int32_t UnInit();

  ModuleId GetModuleId() const override;

  // ILocation interface implementations
  int32_t UpdateGnssInfo(const GnssPosInfo &pos) override;
  MatchCandidate MatchPosition(const aurora::PointLL &point,
                               float radius) override;

  int32_t SetRoutePath(PathInfoPtr route_path) override;
  int32_t SwitchParallelRoad(ParallelRoadType type) override;
  int32_t AddDRListener(const DRListenerPtr callback) override;
  int32_t RemoveDRListener(const DRListenerPtr callback) override;
  int32_t AddMapMatchingListener(
      const MapMatchingListenerPtr callback) override;
  int32_t RemoveMapMatchingListener(
      const MapMatchingListenerPtr callback) override;

  std::shared_ptr<SimulatorController> GetSimulatorController() override;
  const MatchResult& GetLastMatchResult() const override;

  std::shared_ptr<MapMatchingEngine> GetMapMatchingEngine() {
    return map_matching_engine_old_;
  }

 private:
  std::shared_ptr<MapMatchingEngine> map_matching_engine_old_;
  std::unordered_set<DRListenerPtr> dr_listeners_;
  std::unordered_set<MapMatchingListenerPtr> map_matching_listeners_;
  std::shared_ptr<SimulatorController> simulator_controller_;
  MatchResult last_match_result_;

};  // class LocationModuleImpl

}  // namespace loc
}  // namespace aurora
#endif  // MAP_SRC_LOCATION_SRC_LOCATION_MODULE_IMPL_H
/* EOF */
