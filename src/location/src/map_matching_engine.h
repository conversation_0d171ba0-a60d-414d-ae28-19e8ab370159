#ifndef MAP_SRC_LOCATION_SRC_MAP_MATCHING_ENGINE_H
#define MAP_SRC_LOCATION_SRC_MAP_MATCHING_ENGINE_H

#include <memory>
#include <string>
#include <vector>

#include "location/include/location_def.h"
#include "location/include/location_module.h"
#include "location/src/match_policy.h"
#include "state_container.h"
#include "graph_reader.h"
#include "location/src/internal_data_dumper.h"
namespace aurora::loc {


struct MatchEngineConfig {
  // add matching Engine Config
  std::string data_dir;
  std::string config_dir;
};



/**
 *  @brief Post processor interface for processing match results
 */
class MatchingPostProcessor {
  public:
    virtual ~MatchingPostProcessor() = default;
  
    /**
    * @brief Process the match result
    * @param match_result The match result to process
    * @return Processed match result
    */
    virtual MatchResult Process(const MatchResult& match_result)  {
      return match_result;
    }
};

/**
 * @brief Pre processor interface for processing GNSS position information
 */
class MatchingPreProcessor {
  public:
    virtual ~MatchingPreProcessor() = default;

    /**
     * @brief Process the GNSS position information before matching
     * @param gps The GNSS position information to process
     * @return Processed GNSS position information
     */
    virtual GnssPosInfo Process(const GnssPosInfo& gps) {
      return gps;
    }
};
/**
 * @brief Interface for map matching engine implementations
 */
class MapMatchingEngine {
 public:

 
  virtual ~MapMatchingEngine() = default;

    
  virtual bool Initialize(const MatchEngineConfig& ) = 0;  
  virtual void Uninitialize() = 0;



  /**
   * @brief Match a single point to the map
   * @param gps The geographic point to match, with GNSS position information
   * and speed、heading etc.
   * @return The match result
   */
  virtual MatchResult MatchGPS(const GnssPosInfo& gps, bool& is_success) = 0;

  /**
   * @brief Match a single point to the map
   * @param point The geographic point to match
   * @param radius The search radius around the point
   * @return The match result
   */
  virtual MatchCandidate MatchPosition(const aurora::PointLL& point,
                                       float radius) = 0;

  virtual std::shared_ptr<SimulatorController> GetSimulatorController() = 0;

  virtual int32_t SwitchParallelRoad(ParallelRoadType type) = 0;
  virtual EnhanceRouteManager& GetEnhanceRouteManager() = 0;
  virtual StateContainer& GetStateContainer() = 0;
  virtual PolicyManager& GetPolicyManager()= 0;

  virtual GraphReader& GetGraphReader() = 0;

  virtual void SetLocationApi(ILocation* location_api) = 0;
  virtual ILocation* GetLocationApi() = 0;

  virtual std::shared_ptr<MatchingPreProcessor> GetPreprocessor() = 0;
  virtual void SetPreprocessor(std::shared_ptr<MatchingPreProcessor> pre_processor) = 0;
  virtual std::shared_ptr<MatchingPostProcessor> GetPostprocessor() = 0; 
  virtual void SetPostprocessor(std::shared_ptr<MatchingPostProcessor> post_processor) = 0; 

  virtual InternalDataDumper& GetInternalDataDumper() = 0;
};

/**
 * @brief Factory class for creating MapMatchingEngine instances
 */
class MapMatchingEngineFactory {
 public:
  /**
   * @brief Get the singleton instance of the factory
   * @return Reference to the factory instance
   */
  static MapMatchingEngineFactory& GetInstance();

  /**
   * @brief Create a map matching engine based on configuration
   * @param configPath Path to the configuration file
   * @return Unique pointer to the created engine
   */
  std::shared_ptr<MapMatchingEngine> CreateEngine(
      const std::string& configPath);

 private:
  MapMatchingEngineFactory() = default;
  ~MapMatchingEngineFactory() = default;

  // Delete copy constructor and assignment operator
  MapMatchingEngineFactory(const MapMatchingEngineFactory&) = delete;
  MapMatchingEngineFactory& operator=(const MapMatchingEngineFactory&) = delete;
};

}  // namespace aurora::loc

#endif  // MAP_SRC_LOCATION_SRC_MAP_MATCHING_ENGINE_H