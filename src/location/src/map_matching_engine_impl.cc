#include "map_matching_engine.h"

#include "graph_reader.h"
#include "location/include/location_def.h"
#include "match_policy.h"
#include "graph_reader_impl.h"
#include "state_container_impl.h"

#include "simulator_controller_impl.h"
#include "viterbi_search.h"
#include "match_scorer_impl.h"
#include "match_scorer.h"

#include "internal_data_dumper_impl.h"
#include <list>

#define LOCATION_LOG_TAG "Loc::Engine"
#include "location/src/debug/location_logger.h"


namespace aurora::loc {

// class PostProcessImpl : public MatchingPostProcessor {
//  private:
//   std::list<MatchResult> history_;

//  public:
//  MatchResult Process(MatchResult& result) override {
//   MatchResult ret = result;
//     if(history_.size() > 100) {
//       history_.pop_front();  // Keep only the last 100 results
//     }

//     // if preview and cur reroute is all true and at same edge, and path id is decrease, just ignore it

//     if(history_.size() < 2) {
//       return ret;  // Not enough history to compare
//     }

//     auto& prev = history_.back();

//     if(!prev.reroute && !result.reroute) {
//       if(prev.road_match_info.path_id == result.road_match_info.path_id) {
//           if(prev.road_match_info.path_link_idx == result.road_match_info.path_link_idx &&
//             prev.road_match_info.offset > result.road_match_info.offset) {
//             // If both are not rerouting and on the same edge, ignore the new result
//             return ret;
//           } else if(prev.road_match_info.path_link_idx > result.road_match_info.path_link_idx) {
//             // If the path ID has decreased, ignore the new result
//             return ret;
//           }
//       }
//       return true;
//     }
//     history_.push_back(result);
//     return true;
//   }
// };

class MapMatchingEngineHmmImpl : public MapMatchingEngine {
 public:
  ~MapMatchingEngineHmmImpl() {
    location_api_ = nullptr;

    simulate_controller_->Stop();
    simulate_controller_->Detach();
    simulate_controller_ = nullptr;
  }

  int32_t SwitchParallelRoad(ParallelRoadType type) override {
    return kErrorCodeNotSupport;
  }

  InternalDataDumper& GetInternalDataDumper() override {
    return dumper_;
  }


  MapMatchingEngineHmmImpl(const std::string& configPath) : dumper_(*this) {
    MatchEngineConfig config;
    config.config_dir = configPath;
    config.data_dir = configPath;
    Initialize(config);
  }



  MatchResult MatchGPS(const GnssPosInfo& input, bool& success) override {
    GnssPosInfo  gps;
    if (pre_processor_) {
      gps = pre_processor_->Process(input);
    } else {
      gps = input;
    }

    if(gps.speed == 0) {
      success = true;
      return prev_result_;
    }

    if(gps.speed == 0 || gps.lnglat.Distance(prev_result_.origin_pos.lnglat) <  2. ) {
      success = true;
      // prev_result_.car_pos_info.heading
      return prev_result_;
    }
    
    // GraphReader& reader, 
    // StateContainer& state_container,
    // MatchScorer& scorer, 
    // PolicyManager& policy_manager,
    // EnhanceRouteManager& route_manager
    MatchScorer match_scorer;
    ViterbiSearch vs(*graph_reader_, *state_container_.get(), match_scorer, match_policy_manager_, route_manager_);

    bool ret = vs.ProcessOneFrame(gps);
    if(!ret) {
      success = false;
    }
    auto res = GetBestMatchCandidate(success);
    res.car_pos_info.pitch = res.origin_pos.pitch;

    prev_result_ = res;
    if (!success) {
      res.on_road = false;
      res.reroute = true;  // Indicate that rerouting is needed
      res.road_match_info.proj_pos = res.origin_pos.lnglat;
      res.car_pos_info.speed = res.origin_pos.speed;
      success = true;
      return res;
    } else {
      res.on_road = true;
      res.origin_pos = gps;

      res.timestamp = gps.timestamp;
      if(post_processor_) {
        return post_processor_->Process(res);
      }
      return res;
    }
  }

  MatchCandidate MatchPosition(const aurora::PointLL& point,
                               float radius) override {
    MatchCandidate candidate;
    candidate.proj_pos = point;
    candidate.tile_id = 0;      // Default tile ID, will be set later
    candidate.link_id = 0;      // Default link ID, will be set later
    candidate.dir = 0;          // Default direction, will be set later
    candidate.offset = 0.0;     // Default offset, will be set later
    candidate.confidence = -1;  // Default confidence, will be set later
    double best_distance = std::numeric_limits<double>::max();

    //TODO: static match
    (void)best_distance;

    return candidate;
  }

  bool Initialize(const MatchEngineConfig& config) override {
    // TODO: Perform initialization
    config_ = config;
    graph_reader_.reset(new GraphReaderImpl(config.data_dir));

    state_container_ = std::make_unique<StateContainerImpl>(*graph_reader_);

    simulate_controller_ = std::make_shared<SimulatorControllerImpl>(config.config_dir , *this);

    return true;
  }

  void Uninitialize() override {

  }

  GraphReader& GetGraphReader() override {
    return *graph_reader_;
  }


  std::shared_ptr<SimulatorController> GetSimulatorController() override {
    return simulate_controller_;
  }

  EnhanceRouteManager& GetEnhanceRouteManager()  override {
    return route_manager_;
  };

  StateContainer& GetStateContainer() {
    return *state_container_;
  }

  PolicyManager& GetPolicyManager() {
    return match_policy_manager_;
  }

  void SetLocationApi(ILocation* location_api) override { 
    location_api_ = location_api;
  }
  ILocation* GetLocationApi() override { 
    return location_api_;
  }

  std::shared_ptr<MatchingPreProcessor> GetPreprocessor() override { 
    return pre_processor_;
  }
  void SetPreprocessor(std::shared_ptr<MatchingPreProcessor> pre_processor) override {
    pre_processor_ = pre_processor;
   }
  std::shared_ptr<MatchingPostProcessor> GetPostprocessor() override {
    return post_processor_;
   } 
  void SetPostprocessor(std::shared_ptr<MatchingPostProcessor> post_processor) override {
    post_processor_ = post_processor;
   } 
 private:
  // Add any private members needed for the implementation
  std::string config_path_;
  MatchEngineConfig config_;
  std::unique_ptr<GraphReader> graph_reader_;
  std::unique_ptr<StateContainerImpl> state_container_;
  PolicyManager match_policy_manager_;
  std::shared_ptr<SimulatorControllerImpl> simulate_controller_;
  EnhanceRouteManager route_manager_;
  ILocation* location_api_;
  MatchResult prev_result_;
  std::shared_ptr<MatchingPostProcessor> post_processor_;
  std::shared_ptr<MatchingPreProcessor> pre_processor_;

  InternalDataDumperImpl dumper_;

  MatchResult GetBestMatchCandidate(bool& success) const {
    MatchResult candidate;
    auto frame_id = state_container_->GetCurrentFrameId();
    auto frame_info = state_container_->GetFrameInfo(frame_id);
    if (frame_info == nullptr) {
      // Not found frame_info
      success = false;
      LOCATION_ERROR_LOG("GetBestMatchCandidate: frame_info == null when  frame_id = {}", frame_id);
    } else {
      success = true;
      candidate.on_road = false;
      candidate.origin_pos = frame_info->gnss_data;
      candidate.timestamp = frame_info->gnss_data.timestamp;

      // Get Best state based on total score
      auto& states = frame_info->state_map;
      auto best_state_it = std::max_element(
          states.begin(), states.end(), [](const auto& a, const auto& b) {
            return a.second.total_score < b.second.total_score;
          });

      if (best_state_it == states.end()) {
        success = false;
        LOCATION_INFO_LOG("GetBestMatchCandidate: best_state_it == states.end when  frame_id = {}", frame_id);
        return candidate;  // No valid state found
      }

      auto best_state_on_route = std::max_element(
          states.begin(), states.end(),
          [](const auto& a, const auto& b) {
            return a.second.route_score*20. + a.second.total_score < b.second.route_score*20. + b.second.total_score;
          });
      

      if (best_state_on_route != states.end()) {
        candidate.reroute = false;
        if(best_state_it != best_state_on_route) {
          LOCATION_INFO_LOG("Best state is not on route: "
                   "frame_id={}, best_state_id={}, best_state_on_route_id={} , {}:{}",
                   frame_id, best_state_it->first, best_state_on_route->first, best_state_it->second.total_score, best_state_on_route->second.total_score);
        }
        best_state_it = best_state_on_route;  // Use the best state on route
      } else {
        candidate.reroute = true;  // No valid route found, set reroute flag
      }
      // get prev of best state and return 

      
      auto best_state = best_state_it->second;

      candidate.road_match_info.link_id = best_state.edge_id.edge_id.feature_id;
      candidate.road_match_info.tile_id = best_state.edge_id.edge_id.tile_id.value;
      candidate.road_match_info.proj_pos = best_state.proj_pos;
      candidate.road_match_info.offset = best_state.edge_offset;
      candidate.road_match_info.dir = best_state.edge_id.is_forward ? 0 : 1;
      candidate.road_match_info.path_id = best_state.path_id;
      candidate.road_match_info.path_link_idx = best_state.path_link_index;
      candidate.road_match_info.confidence = best_state.total_score;
      candidate.road_match_info.topo_edge = *(graph_reader_->GetEdgeInfo(best_state.edge_id.edge_id)->topol_edge->GetBaseInfo());

      candidate.car_pos_info.speed = frame_info->gnss_data.speed;
      // candidate.car_pos_info.heading = frame_info->gnss_data.heading;
      candidate.car_pos_info.heading = best_state.heading;

      candidate.car_pos_info.pitch = frame_info->gnss_data.pitch;

      candidate.car_pos_info.accel =
          0.0;  // TODO: Calculate acceleration if needed
      candidate.car_pos_info.steering_angle =
          0.0;  // TODO: Calculate steering angle if needed
      candidate.on_road = true;
      if(best_state.route_score > 0.0) {
        candidate.reroute = false;
      } else {
        candidate.reroute = true;
      }

      LOCATION_DEBUG_LOG("Best match candidate: frame_id={}, link_id={}, tile_id={}, "
               "offset={}, reroute={}, score={}, dir={}",
               frame_id, candidate.road_match_info.link_id,
               candidate.road_match_info.tile_id,
               candidate.road_match_info.offset,
               candidate.reroute ? "true" : "false",
               best_state.total_score,
               candidate.road_match_info.dir
              );

      candidate.origin_pos = frame_info->gnss_data;
    }

    return candidate;
  }

};

MapMatchingEngineFactory& MapMatchingEngineFactory::GetInstance() {
  static MapMatchingEngineFactory instance;
  return instance;
}

std::shared_ptr<MapMatchingEngine> MapMatchingEngineFactory::CreateEngine(
    const std::string& configPath) {
  // TODO: Read configuration and create appropriate engine implementation
  return std::make_shared<MapMatchingEngineHmmImpl>(configPath);
}

}  // namespace aurora::loc
