#include "location/src/match_policy.h"

namespace aurora {
namespace loc {

PolicyManager::PolicyManager() {
  // Initialize default weights
  current_weights_ = {
      1.5,  // distance_weight
      0.4,  // forward_weight
      0.95,  // history_factor
      0.0,  // movement_distance_weight
      1.0,  // heading
      0.0,
      1.0   //movement_vector_weight
  };

  current_weights_.best_forward_score = 0.1;

  // Define policy scenarios and their weights
  scenario_policies_ = {
      {Scenario::kDefault, {1.0, 1.0, 0, 0, 0, 0,0}},  // Default weights
      {Scenario::kParallelRoad,
        {1.0, 1.0, 0, 0, 0, 0,0}},                     // Favor heading and scenario
      {Scenario::kTunnel, {1.0, 1.0, 0, 0, 0, 0,0}},  // Favor distance and scenario
      {Scenario::kParking, {1.0, 1.0, 0, 0, 0, 0,0}},  // Favor route adherence
      {Scenario::kSmallAngle,
        {1.0, 1.0, 0, 0, 0, 0,0}},  // Favor heading for small angles
      {Scenario::kUTurn,
        {1.0, 1.0, 0, 0, 0, 0,0}}  // Strongly favor route for U-turns
  };
}

const Policy& PolicyManager::GetCurrentPolicy() const {
  return current_weights_;
}

void PolicyManager::SetScenario(Scenario scenario) {
  auto it = scenario_policies_.find(scenario);
  if (it != scenario_policies_.end()) {
    current_weights_ = it->second;
  }
}
}  // namespace loc
}  // namespace aurora
