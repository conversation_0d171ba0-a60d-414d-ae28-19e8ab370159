#ifndef MAP_SRC_LOCATION_SRC_MATCH_POLICY_H
#define MAP_SRC_LOCATION_SRC_MATCH_POLICY_H

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

namespace aurora {
namespace loc {

/**
 * @brief Available location matching scenarios
 */
enum class Scenario {
  kDefault,       // Default scenario
  kParallelRoad,  // Parallel road scenario
  kTunnel,        // Tunnel scenario
  kParking,       // Parking scenario
  kSmallAngle,    // 小夹角场景
  kUTurn          // U-Turn场景
};

/**
 * @brief Policy weights configuration for different scenarios
 */
struct Policy {
  Policy(float distance_weight = 1.0, 
        float forward_weight = 1.0, 
        float history_factor = 0.95, 
        float movement_distance_weight = 0.0,
        float heading_weight = 0.0,
        float route_weight = 0.0, 
        float movement_vector_weight = 0.0,
        float projection_angle_weight = 0.0)
      : distance_weight(distance_weight),
        forward_weight(forward_weight),
        history_factor(history_factor),
        movement_distance_weight(movement_distance_weight),
        heading_weight(heading_weight),
        route_weight(route_weight),
        movement_vector_weight(movement_vector_weight),
        projection_angle_weight(projection_angle_weight) { }
  float distance_weight;  // Weight for project distance matching
  float forward_weight;   // Weight for road forward matching
  float history_factor;   // history weights factor
  float movement_distance_weight;  // Weight for movement distance 
  float heading_weight;   // Weight for heading matching
  float route_weight;     // Weight for route adherence
  float movement_vector_weight;  // Weight for movement matching
  float projection_angle_weight;  // Weight for projection angle
  // 最优状态后续奖励
  float best_forward_score = 0.0; // Best forward score for the state
};

/**
 * @class Policy
 * @brief Manages policy weights for different location scenarios
 */
class PolicyManager {
 public:
  PolicyManager();
  ~PolicyManager() = default;

  /**
   * @brief Get weights for current active policy
   */
  const Policy& GetCurrentPolicy() const;

  /**
   * @brief Set active policy scenario
   * @param scenario Scenario name (parallel_road, tunnel, parking, etc.)
   */
  void SetScenario(Scenario scenario);

 private:
  // Current active weights
  Policy current_weights_;

  // Available policy scenarios and their weights
  std::unordered_map<Scenario, Policy> scenario_policies_;
};

}  // namespace loc
}  // namespace aurora

#endif  // MAP_SRC_LOCATION_SRC_MATCH_POLICY_H
