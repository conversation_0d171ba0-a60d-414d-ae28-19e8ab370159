#ifndef MAP_SRC_LOCATION_SRC_MATCH_SCORER_H
#define MAP_SRC_LOCATION_SRC_MATCH_SCORER_H

#include <vector>
#include "base/include/vector2.h"
#include <cmath>

namespace aurora {
namespace loc {

    
    
    class MatchScorer {
    private:
        
    struct FastScorer {
        FastScorer(double sigma, double max, double min) {
            this->sigma = sigma;
            this->max_value = max;
            this->min_value = min;

            alpha = std::log(1.0 / (std::sqrt(2.0 * M_PI) * sigma));
            beta = (-0.5 / sigma/sigma);

            this->min_score = logNormalDistribution(min);
            this->max_score = logNormalDistribution(max);
            alpha2 = 1. + (min_score - alpha)/ ( max_score - min_score);
            beta2 = -beta / ( max_score - min_score);
        }

        double operator()(double x) const {
            if (x < min_value) {
                x = min_value;
            } else if (x > max_value) {
                x = max_value;
            }
            // double logP = alpha + beta * x*x ;
            return alpha2 + beta2 * x * x; // Normalize to [0, 1]
        }
        
        double sigma;
        double max_value;
        double min_value;
        double min_score;
        double max_score;
        double alpha;
        double beta;
        double alpha2;
        double beta2;
        double logNormalDistribution(double x) const {
            return alpha + beta * x*x ;
        }
    };
    FastScorer distance_scorer_;
    FastScorer heaidng_scorer_;
    FastScorer movement_scorer_;

    public:
        MatchScorer();
        void SetDistanceScorerParams(double sigma, double max, double min) {
            distance_scorer_ = FastScorer(sigma, max, min);
        }

        void SetHeadingScorerParams(double sigma, double max, double min) {
            heaidng_scorer_ = FastScorer(sigma, max, min);
        }

        virtual ~MatchScorer() = default;
        virtual double CalcDistanceScore(double project_distance, double radius, double sigma = 40.);

        // virtual double CalcDistanceScore(double project_distance, double radius, double sigma = 40.);
        virtual double CalcHeadingScore(double road_heading, double car_heading) ;
        virtual double CalcTranferScore(double road_speed, double car_speed);
        virtual double CalcMovementScore(const Vector2d& map_move, const Vector2d gps_move);
        virtual double CalcTransitionScore(double road_forward_distance, double gps_distance);
        virtual double CalcProjectionAngleScore(double projection_angle) {
            // Default implementation can be overridden
            return normalDistribution(45., projection_angle - 90.0);
        }
        
        virtual std::vector<double> SoftmaxScores(const std::vector<double>& scores);

        static double normalDistribution(double sigma, double x) {
            return 1.0 / (sqrt(2.0 * M_PI) * sigma) * exp(-0.5 * pow(x / sigma, 2));
        }

    
        /**
         * Use this function instead of Math.log(normalDistribution(sigma, x)) to avoid an
         * arithmetic underflow for very small probabilities.
         */
        static double logNormalDistribution(double sigma, double x) {
            return std::log(1.0 / (std::sqrt(2.0 * M_PI) * sigma)) + (-0.5 * std::pow(x / sigma, 2.));
        }
    
        /**
         * @param beta =1/lambda with lambda being the standard exponential distribution rate parameter
         */
        double exponentialDistribution(double beta, double x) {
            return 1.0 / beta * exp(-x / beta);
        }
    
        /**
         * Use this function instead of Math.log(exponentialDistribution(beta, x)) to avoid an
         * arithmetic underflow for very small probabilities.
         *
         * @param beta =1/lambda with lambda being the standard exponential distribution rate parameter
         */
        static double logExponentialDistribution(double beta, double x) {
            return log(1.0 / beta) - (x / beta);
        }

        static double transitionLogProbability(double routeLength, double linearDistance) {
            // Transition metric taken from Newson & Krumm.
            double transitionMetric = std::abs(linearDistance - routeLength);
            return logExponentialDistribution(2.0, transitionMetric);
        }
        private:
           double GaussianLogProbility(double value, double sigma, double mu = 0.0) const;

           double Prob2Score(double prob, double alpha, double beta) const; 
    };
        
} // namespace loc
} // namespace aurora

#endif // MAP_SRC_LOCATION_SRC_MATCH_SCORER_H
 