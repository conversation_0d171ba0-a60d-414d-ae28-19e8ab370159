#include "match_scorer.h"
#include <cmath>
#include <numeric>
#include <algorithm>
#include "base/include/vector2.h"
#define LOCATION_LOG_TAG "Loc::Scorer"
#include "location/src/debug/location_logger.h"

namespace aurora {
namespace loc {

MatchScorer::MatchScorer() 
    : distance_scorer_(5.0, 10.0, 0), // sigma, max, min
        heaidng_scorer_(70.0, 90.0, 0),
        movement_scorer_(8,10,0) { // sigma, max, min
    LOCATION_DEBUG_LOG("MatchScorer initialized with distance scorer (sigma: {}, max: {}, min: {}) and heading scorer (sigma: {}, max: {}, min: {})",
        distance_scorer_.sigma, distance_scorer_.max_value, distance_scorer_.min_value,
        heaidng_scorer_.sigma, heaidng_scorer_.max_value, heaidng_scorer_.min_value);
}

double MatchScorer::CalcDistanceScore(double project_distance, double radius, double sigma) {
    return distance_scorer_(project_distance);
}

double MatchScorer::CalcHeadingScore(double road_heading, double car_heading) {
    double angle_diff = std::abs(std::fmod(road_heading - car_heading + 720, 360)) ;
    angle_diff = std::min(angle_diff, 360.0 - angle_diff);

    // double prob = GaussianLogProbility(angle_diff, 70.0); // 30 degrees sigma
    return heaidng_scorer_(angle_diff);
}

double MatchScorer::CalcTranferScore(double road_speed, double car_speed) {
    double prob = GaussianLogProbility(road_speed - car_speed, 2.0); // 2 m/s sigma
    return Prob2Score(prob, 0.3, 5);
}

double MatchScorer::CalcMovementScore(const Vector2d& map_move, const Vector2d gps_move) {
    // double distance = (map_move - gps_move).Norm();
    double distance = map_move.Norm() - gps_move.Norm();

    return movement_scorer_(distance);

    // double prob = GaussianLogProbility(distance, 2.0); // 1 meter sigma
    // return Prob2Score(prob, 0.3, 5);
}

double MatchScorer::CalcTransitionScore(double route_pass_distance, double gps_distance) {
    // Transition score is based on the ratio of drive distance to GPS distance
    if (gps_distance <= 0) {
        return 0.0; // Avoid division by zero
    }
    
    double ratio = gps_distance / route_pass_distance;
    // double prob = GaussianLogProbility(ratio, 0.1, 1.0); // 0.1 sigma, mean at 1.0
    return ratio * 2.0; // Simple linear scaling for now, can be adjusted
}


std::vector<double> MatchScorer::SoftmaxScores(const std::vector<double>& scores) {
    std::vector<double> exp_scores(scores.size());
    double max_score = *std::max_element(scores.begin(), scores.end());
    std::transform(scores.begin(), scores.end(), exp_scores.begin(),
        [max_score](double s) { return std::exp(s - max_score); });
    
    double sum = std::accumulate(exp_scores.begin(), exp_scores.end(), 0.000001);
    std::transform(exp_scores.begin(), exp_scores.end(), exp_scores.begin(),
        [sum](double s) { return s / sum; });
    
    return exp_scores;
}

double MatchScorer::GaussianLogProbility(double value, double sigma, double mu) const {
    const double x = value - mu;
    return std::log( 1.0 / (std::sqrt(2.0 * M_PI) * sigma)) +(-0.5 * std::pow(x / sigma, 2));
}

double MatchScorer::Prob2Score(double prob, double alpha, double beta) const {
   return std::min(std::max((beta + prob) * alpha, 0.), 1.);
}

} // namespace loc
} // namespace aurora
