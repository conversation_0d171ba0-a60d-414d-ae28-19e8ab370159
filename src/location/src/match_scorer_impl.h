// #ifndef MAP_SRC_LOCATION_SRC_MATCH_SCORER_IMPL_H
// #define MAP_SRC_LOCATION_SRC_MATCH_SCORER_IMPL_H

#include "match_scorer.h"

// namespace aurora {
// namespace loc
// {
//     class MatchScorerImpl : public MatchScorer {
//     public:
//         MatchScorerImpl() = default;
//         ~MatchScorerImpl() override = default;
//         double CalcDistanceScore(double project_distance, double radius) override;
//         double CalcHeadingScore(double road_heading, double car_heading) override;
//         double CalcTranferScore(double road_speed, double car_speed) override;
//         double CalcMovementScore(const Vector2d& map_move, const Vector2d gps_move) override;

//         double CalcTransitionScore(double route_pass_distance, double gps_distance)  override;

//         std::vector<double> SoftmaxScores(const std::vector<double>& scores) override;
//     private:
//         double GaussianLogProbility(double value, double sigma, double mu = 0.0) const;

//         double Prob2Score(double prob, double alpha, double beta) const; 
//     };
        
// } // namespace loc
// } // namespace aurora

// #endif // MAP_SRC_LOCATION_SRC_MATCH_SCORER_IMPL_H
