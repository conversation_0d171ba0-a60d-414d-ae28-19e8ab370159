
#include "location/src/simulator_controller_impl.h"

#include <fstream>
#include "location/src/location_module_impl.h"
#include "map_matching_engine.h"
#include "path/include/path_def.h"
#include "yaml-cpp/yaml.h"

#define LOCATION_LOG_TAG "Loc::SimCtrl"
#include "location/src/debug/location_logger.h"

namespace aurora {
namespace loc {

std::vector<GnssPosInfo> SimulatorControllerImpl::LoadGPSData(
    const std::string& path) {
  std::vector<GnssPosInfo> gps_data;

  if (!std::filesystem::exists(path)) {
    LOCATION_ERROR_LOG("GPS data file does not exist: {}", path);
    return gps_data;
  }

  std::ifstream file(path);
  if (!file.is_open()) {
    LOCATION_ERROR_LOG("Failed to open GPS data file: {}", path);
    return gps_data;
  }

  std::string line;
  std::getline(file, line);
  while (std::getline(file, line)) {
    std::istringstream iss(line);
    std::string token;
    std::vector<std::string> tokens;

    while (std::getline(iss, token, ',')) {
      tokens.push_back(token);
    }

    if (tokens.size() >= 5) {  // timestamp,lng,lat,speed,heading
      GnssPosInfo gps;
      gps.timestamp = std::stoull(tokens[0]);
      gps.lnglat = aurora::PointLL(std::stod(tokens[1]), std::stod(tokens[2]));
      gps.speed = std::stod(tokens[3]);
      gps.heading = std::stod(tokens[4]);
      gps.num_sats = tokens.size() > 5 ? std::stoi(tokens[5]) : 10;
      gps.num_used = tokens.size() > 6 ? std::stoi(tokens[6]) : 8;

      gps_data.push_back(gps);
    }
  }

  LOCATION_INFO_LOG("Loaded {} GPS points from {}", gps_data.size(),
               path);
  return gps_data;
}

void SimulatorControllerImpl::DumpGPSData(
    const std::string& path, const std::vector<GnssPosInfo>& data) {
  std::ofstream file(path);
  if (!file.is_open()) {
    LOCATION_ERROR_LOG("Failed to open GPS data file for writing: {}", path);
    return;
  }

  // Write CSV header
  file << "timestamp,lng,lat,speed,heading,num_sats,num_used\n";

  file << std::setprecision(6)
       << std::fixed;  // Set precision for floating-point numbers

  // Write data rows
  for (const auto& gps : data) {
    file << gps.timestamp << "," << gps.lnglat.lng() << "," << gps.lnglat.lat()
         << "," << gps.speed << "," << gps.heading << "," << gps.num_sats << ","
         << gps.num_used << "\n";
  }

  LOCATION_INFO_LOG("Saved {} GPS points to {}", data.size(), path);
}

SimulatorControllerImpl::SimulatorControllerImpl(const std::string& config_dir,
                                                 MapMatchingEngine& engine)
    : engine_(engine),
      graph_reader_(&engine.GetGraphReader()),
      pos_noise_x_generator_(12.0, 2, 8, 2),
      pos_noise_y_generator_(12.0, 2, 8, 2),
      heading_noise_generator_(12.0, 2, 8, 2),
      is_detach_(false) {
  // Load configuration defaults

  // load yaml config file
  std::string config_file_path = config_dir + "/simulator_config.yaml";
  if (std::filesystem::exists(config_file_path)) {
    std::ifstream config_file(config_file_path);
    if (!config_file.is_open()) {
      LOCATION_ERROR_LOG("Failed to open config file: {}",
                    config_file_path);
      return;
    }
    YAML::Node config_node = YAML::Load(config_file);
    config_.position_noise_mean =
        config_node["position_noise_mean"].as<double>(0.0);
    config_.position_noise_stddev =
        config_node["position_noise_stddev"].as<double>(1.0);
    config_.heading_noise_mean =
        config_node["heading_noise_mean"].as<double>(0.0);
    config_.heading_noise_stddev =
        config_node["heading_noise_stddev"].as<double>(0.0);
    // config_.speed_mean = config_node["speed_mean"].as<double>(20.0);
    // config_.speed_stddev = config_node["speed_stddev"].as<double>(2.0);
    // config_.sample_interval = config_node["sample_interval"].as<double>(1.0);
    config_.play_interval = config_node["play_interval"].as<double>(0.2);
    config_file.close();
    LOCATION_INFO_LOG("Loaded simulator config from {}",
                 config_file_path);
  } else {
    LOCATION_WARN_LOG(
                  "Config file {} does not exist, using default values",
                  config_file_path);
    config_.position_noise_mean = 0.0;
    config_.position_noise_stddev = 5.0;
    config_.heading_noise_mean = 0.0;
    config_.heading_noise_stddev = 2.0;
    // config_.speed_mean = 20.0; // meter/second
    // config_.speed_stddev = 2.0;
    config_.sample_interval = 1.0;  // seconds
    config_.play_interval = 0.1;    // seconds
  }
  UpdateRadomNoise();
}

SimulatorControllerImpl::~SimulatorControllerImpl() { Stop(); }
int32_t SimulatorControllerImpl::SetRoutePath(PathInfoPtr route_path) {
  // Implementation for setting the route path
  route_path_from_client_ = route_path;
  // UpdatePlanGpsPosInfo();
  return ErrorCode::kErrorCodeOk;
}
int32_t SimulatorControllerImpl::Start() {
  auto route_path_from_location_ =
      this->engine_.GetEnhanceRouteManager().GetRoutePath();

  LOCATION_INFO_LOG("Start Simulator");
  if(!is_replay_) {
    std::lock_guard<std::recursive_mutex> lock(mutex_);
    UpdatePlanGpsPosInfo();
  }

  gps_index_ = 0;
  if (!worker_thread_) {
    worker_thread_.reset(new aurora::LoopThread("SimulatorWorkerThread"));
  }
  worker_thread_->Start();
  is_pause_ = false;

  worker_thread_->PostDelayed(std::bind(&SimulatorControllerImpl::Tick, this),
                              100);

  return ErrorCode::kErrorCodeOk;
}
int32_t SimulatorControllerImpl::Stop() {
  LOCATION_INFO_LOG("Stop Simulator");

  if(is_detach_) {
    return ErrorCode::kErrorCodeMMEnd;
  }
  auto route_path_from_location_ =
      this->engine_.GetEnhanceRouteManager().GetRoutePath();

  if (worker_thread_) {
    worker_thread_->Stop();
  }
  gps_index_ = 0;

  if (is_replay_) {
    LOCATION_WARN_LOG("Simulator is replace mode, don't dump states");
    return ErrorCode::kErrorCodeOk;
  }
  // if "loc_debug/" is exists, dump states
  if (GetLocation()) {
    LocationModuleImpl* location_module =
        dynamic_cast<LocationModuleImpl*>(GetLocation());

    if (location_module) {
      if (std::filesystem::exists("location_debug/")) {
        // Create timestamped directory
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::system_clock::to_time_t(now);
        std::tm tm = *std::localtime(&timestamp);

        std::ostringstream dirName;
        dirName << "location_debug/" << std::put_time(&tm, "%m%d_%H%M%S/");

        std::filesystem::path dumpPath(dirName.str());

        // Create directory and log full path
        if (std::filesystem::create_directories(dumpPath)) {
          LOCATION_INFO_LOG("Dumping states to {}",
                       dumpPath.string());

          // dump planned GPS positions, path_info to file

          DumpGPSData(dumpPath.string() + "planned_gps.csv", planed_gps_);

          // dump route path info

          if (route_path_from_client_ || route_path_from_location_) {
            PathInfoPtr path_info = route_path_from_client_
                                        ? route_path_from_client_
                                        : route_path_from_location_;
            EnhanceRouteManager::SaveRoutePathToFile(
                path_info, dumpPath.string() + "route_path.txt");
          }

        } else {
          LOCATION_ERROR_LOG("Failed to create directory {}",
                        dumpPath.string());
        }
      }
      // location_module->ResetStates();
    }

    planed_gps_.clear();
  }
  return ErrorCode::kErrorCodeOk;
}
int32_t SimulatorControllerImpl::Pause() {
  LOCATION_INFO_LOG("Pause Simulator");

  // Implementation for pausing the simulator
  is_pause_ = true;
  return ErrorCode::kErrorCodeOk;
}
int32_t SimulatorControllerImpl::Resume() {
  LOCATION_INFO_LOG("Pause Simulator");

  // Implementation for resuming the simulator
  is_pause_ = false;
  return ErrorCode::kErrorCodeOk;
}
int32_t SimulatorControllerImpl::SetConfig(const Config& config) {
  LOCATION_INFO_LOG("SetConfig");

  config_ = config;
  UpdateRadomNoise();
  return ErrorCode::kErrorCodeOk;
}
SimulatorControllerImpl::Config SimulatorControllerImpl::GetConfig() const {
  return config_;
}

void SimulatorControllerImpl::Tick() {
  static int tick_cnt_ = 0;
  LOCATION_INFO_LOG("Simulate Tick: {}", ++tick_cnt_);

  if (!is_pause_) {
    std::lock_guard<std::recursive_mutex> lock(mutex_);

    if (GetLocation() == nullptr) {
      LOCATION_ERROR_LOG("Location interface is not available.");
      return;
    }
    if (gps_index_ > planed_gps_.size() || planed_gps_.size() == 0) {
      LOCATION_INFO_LOG("All planned GPS positions have been processed.");
      return;
    }
    if (gps_index_ == planed_gps_.size()) {
      GetLocation()->UpdateGnssInfo(planed_gps_[gps_index_ - 1]);
    } else {
      GetLocation()->UpdateGnssInfo(planed_gps_[gps_index_]);
    }
    gps_index_++;
  }
  int play_delay = this->config_.play_interval * 1000;
  play_delay = std::max(std::min(play_delay, 3000), 1);

  worker_thread_->PostDelayed(std::bind(&SimulatorControllerImpl::Tick, this),
                              play_delay);
}

void SimulatorControllerImpl::UpdateRadomNoise() {
  // Update the random noise parameters based on the configuration
  dist_pos_noise_ = std::normal_distribution<double>(
      config_.position_noise_mean, config_.position_noise_stddev);
  dist_heading_noise_ = std::normal_distribution<double>(
      config_.heading_noise_mean, config_.heading_noise_stddev);

  this->pos_noise_x_generator_.SetScale(config_.position_noise_mean,
                                        config_.position_noise_stddev);
  this->pos_noise_x_generator_.SetPeriod(config_.position_noise_period_mean,
                                         config_.position_noise_period_stddev);
  this->pos_noise_y_generator_.SetScale(config_.position_noise_mean,
                                        config_.position_noise_stddev);

  this->pos_noise_y_generator_.SetPeriod(config_.position_noise_period_mean,
                                         config_.position_noise_period_stddev);
  this->heading_noise_generator_.SetPeriod(config_.heading_noise_mean,
                                           config_.heading_noise_stddev);
  this->heading_noise_generator_.SetScale(config_.heading_noise_mean,
                                          config_.heading_noise_stddev);
  LOCATION_INFO_LOG("Updated random noise parameters: "
               "position_noise_mean={}, position_noise_stddev={}, "
               "heading_noise_mean={}, heading_noise_stddev={}",
               config_.position_noise_mean, config_.position_noise_stddev,
               config_.heading_noise_mean, config_.heading_noise_stddev);
}

bool SimulatorControllerImpl::IsFinished() const {
  if (gps_index_ >= planed_gps_.size()) {
    LOCATION_INFO_LOG("All planned GPS positions have been processed.");
    return true;
  }
  return false;
}

void SimulatorControllerImpl::UpdatePlanGpsPosInfo() {
  std::lock_guard lock(mutex_);
  auto route_path_from_location_ =
      this->engine_.GetEnhanceRouteManager().GetRoutePath();

  planed_gps_.clear();
  if (graph_reader_ == nullptr ||
      !(route_path_from_client_ || route_path_from_location_)) {
      LOCATION_ERROR_LOG(
                 "UpdatePlanGpsPosInfo failure: graph_reader_={}, "
                 "route_path_from_client_={}, route_path_from_location_ = {}",
                 (void*)graph_reader_, (void*)route_path_from_client_.get(),
                 (void*)route_path_from_location_.get());
    return;
  }

  PathInfoPtr path_info = route_path_from_client_;
  if (!path_info) {
    path_info = route_path_from_location_;
  }

  double cur_sample_offset = 0;

  double cur_segment_offset = 0.0;
  double start_offset = path_info->sections.front().start_offset;
  double end_offset = std::numeric_limits<double>::max();

  LOCATION_INFO_LOG("UpdatePlanGpsPosInfo: start_offset={}, end_offset={}, link_cnt = {}",
      start_offset, path_info->sections.front().end_offset,
      path_info->links.size());
  double next_car_offset = start_offset;  // 当前车的偏移量
  LOCATION_INFO_LOG("UpdatePlanGpsPosInfo: path_id={}, length={}, travel_time={} links {}",
      path_info->path_id, path_info->length, path_info->travel_time,
      path_info->links.size());
  double move_len_step = 0;  // 20ms
  for (auto link_it = path_info->links.begin();
       link_it != path_info->links.end(); ++link_it) {
    auto& link = *link_it;

    // link->forward
    // LOG_INFO("Processing link: tile_id={}, id={}, forward={}", link->tile_id,
    // link->id, (bool)link->forward); LOG_INFO("current segment offset: {},
    // next car offset: {}, cur sample offset: {} planed_gps_.size {}",
    //  cur_segment_offset, next_car_offset, cur_sample_offset,
    //  planed_gps_.size());
    double cur_speed = 0;
    auto edge_info = graph_reader_->GetEdgeInfo(
        aurora::parser::RouteEdgeID(link->tile_id, link->id));
    if (!edge_info) {
      return;
    }
    bool forward = link->forward;

    if (&link == &path_info->links.back()) {
      end_offset = cur_segment_offset + path_info->sections.back().end_offset;
    }
    LOCATION_INFO_LOG("UpdatePlanGpsPosInfo: start_offset={}, end_offset={}, "
                 "link.total_len = {}",
                 start_offset, end_offset, edge_info->total_len);
    if (forward) {
      cur_speed = edge_info->topol_edge->GetBaseInfo()->positive_speed_limit;
    } else {
      cur_speed = edge_info->topol_edge->GetBaseInfo()->negtive_speed_limit;
    }

    if (cur_speed < 5.0 / 3.6) {
      auto speed_grade = edge_info->topol_edge->GetBaseInfo()->speed_grade;
      switch (speed_grade) {
        case aurora::parser::kSpeedGrade0:
          cur_speed = 130.0 / 3.6;
          break;
        case aurora::parser::kSpeedGrade1:
          cur_speed = 120.0 / 3.6;
          break;
        case aurora::parser::kSpeedGrade2:
          cur_speed = 100.0 / 3.6;
          break;
        case aurora::parser::kSpeedGrade3:
          cur_speed = 80.0 / 3.6;
          break;
        case aurora::parser::kSpeedGrade4:
          cur_speed = 60.0 / 3.6;
          break;
        case aurora::parser::kSpeedGrade5:
          cur_speed = 40.0 / 3.6;
          break;
        case aurora::parser::kSpeedGrade6:
          cur_speed = 20.0 / 3.6;
          break;
        case aurora::parser::kSpeedGrade7:
          cur_speed = 10.0 / 3.6;
          break;
        default:
          cur_speed = 30.0 / 3.6;
          break;
      }
    }
    next_car_offset -= move_len_step;  // 减去上一个段的偏移量

    double passed = cur_segment_offset - next_car_offset;
    double prev_move_len_step = move_len_step;
    move_len_step = cur_speed * config_.sample_interval;

    move_len_step = std::max(move_len_step, 0.1);  // 最小步长为0.1m
    if (passed > 0.01) {
      double radio = passed / prev_move_len_step;

      next_car_offset = cur_segment_offset +
                        (1. - radio) * move_len_step;  // 增加当前段的偏移量
    } else {
      next_car_offset += move_len_step;  // 增加当前段的偏移量
    }

    LOCATION_INFO_LOG("Processing link: tile_id={}, id={}, forward={}, cur_speed={}",
                 link->tile_id, link->id, forward, cur_speed);

    auto geos = edge_info->augment_edge->GetGeoPoints();

    auto lens = edge_info->lens;
    auto headings = edge_info->headings;

    // print all lens here
    for (size_t i = 0; i < lens.size(); ++i) {
      LOCATION_INFO_LOG("Segment {} length: {}, heading: {}", i, lens[i],
                   headings[i]);
    }
    if (forward) {
      for (size_t i = 0; i < lens.size(); ++i) {
        if (lens[i] < 0.01) {
          cur_segment_offset += lens[i];
          LOCATION_INFO_LOG("Skip segment with length close to zero: {}",
                       lens[i]);
          continue;  // 跳过长度接近0的段
        }
        while (next_car_offset < cur_segment_offset + lens[i]) {
          // 计算当前点的偏移量
          double offset = next_car_offset - cur_segment_offset;
          if (offset < 0) {
            offset = 0;
          }

          if (offset > lens[i]) {
            offset = lens[i];
          }
          // 计算当前点的经纬度
          PointLL cur_geo =
              geos[i].PointAlongSegment(geos[i + 1], offset / lens[i]);

          // 计算当前点的朝向
          double cur_heading = headings[i];
          // 添加到计划列表

          GnssPosInfo gps_info;
          pos_noise_y_generator_.AddNoise(cur_geo);
          gps_info.lnglat = aurora::PointLL(cur_geo.x(), cur_geo.y());
          gps_info.timestamp = static_cast<uint64_t>(cur_sample_offset * 1000);
          gps_info.speed = cur_speed;
          gps_info.heading =
              std::fmod(cur_heading + dist_heading_noise_(generator_), 360.0);
          // gps_info.heading = std::fmod(cur_heading +
          // heading_noise_generator_.next_sample(), 360.0);
          gps_info.num_sats = 10;  // 假设卫星数量为10
          gps_info.num_used = 8;   // 假设使用的卫星数量为8

          planed_gps_.push_back(gps_info);
          cur_sample_offset++;

          if (std::abs(next_car_offset - end_offset) < 0.0001) {
            next_car_offset += edge_info->total_len +
                               1;  // 如果已经超过了结束偏移量，则停止处理
            break;
          }
          if (next_car_offset + move_len_step >= end_offset) {
            next_car_offset =
                end_offset;  // 如果已经超过了结束偏移量，则停止处理
          } else {
            next_car_offset += move_len_step;
          }
        }
        cur_segment_offset += lens[i];
      }
    } else {
      for (int i = lens.size() - 1; i >= 0; --i) {
        if (lens[i] < 0.01) {
          cur_segment_offset += lens[i];
          continue;  // 跳过长度接近0的段
        }

        while (next_car_offset < cur_segment_offset + lens[i]) {
          // 计算当前点的偏移量
          double offset = next_car_offset - cur_segment_offset;
          if (offset < 0) {
            offset = 0;
          }

          if (offset > lens[i]) {
            offset = lens[i];
          }
          // 计算当前点的经纬度
          aurora::PointLL cur_geo =
              geos[i + 1].PointAlongSegment(geos[i], offset / lens[i]);

          pos_noise_y_generator_.AddNoise(cur_geo);

          // 计算当前点的朝向
          // std::fmod(cur_heading + dist_heading_noise_(generator_), 360.0) ;
          double cur_heading =
              std::fmod(headings[i] + 180.0 + dist_heading_noise_(generator_),
                        360.0);  // 反向行驶时，朝向需要加180度
          // 添加到计划列表

          GnssPosInfo gps_info;

          // auto x_noise =
          //     dist_pos_noise_(generator_) /
          //     (DistanceApproximator<PointLL>::LngScalePerLat(cur_geo.y()) *
          //      kMetersPerDegreeLat);
          // auto y_noise = dist_pos_noise_(generator_) / kMetersPerDegreeLat;
          // cur_geo.set_x(cur_geo.x() + x_noise);
          // cur_geo.set_y(cur_geo.y() + y_noise);

          gps_info.lnglat = aurora::PointLL(cur_geo.x(), cur_geo.y());
          gps_info.timestamp = static_cast<uint64_t>(cur_sample_offset * 1000);
          gps_info.speed = cur_speed;
          gps_info.heading = cur_heading;
          gps_info.num_sats = 10;  // 假设卫星数量为10
          gps_info.num_used = 8;   // 假设使用的卫星数量为8
          planed_gps_.push_back(gps_info);
          cur_sample_offset++;
          if (std::abs(next_car_offset - end_offset) < 0.0001) {
            next_car_offset += edge_info->total_len +
                               1;  // 如果已经超过了结束偏移量，则停止处理
            break;
          }
          if (next_car_offset + move_len_step >= end_offset) {
            next_car_offset =
                end_offset;  // 如果已经超过了结束偏移量，则停止处理
          } else {
            next_car_offset += move_len_step;
          }
        }
        cur_segment_offset += lens[i];
      }
    }
  }

  LOCATION_INFO_LOG("UpdatePlanGpsPosInfo: planed_gps_.size={}",
               planed_gps_.size());

  for (auto& pos : planed_gps_) {
    LOCATION_DEBUG_LOG(
        "Planed GPS: timestamp={}, lnglat=({}, {}), speed={}, heading={}",
        pos.timestamp, pos.lnglat.lng(), pos.lnglat.lat(), pos.speed,
        pos.heading);
  }
}

int32_t SimulatorControllerImpl::LoadTrackData(
    const std::string& trace_data_dir) {
  planed_gps_.clear();

  if (!std::filesystem::exists(trace_data_dir)) {
    LOCATION_ERROR_LOG("Track data directory does not exist: {}",
                  trace_data_dir);
    return ErrorCode::kErrorCodeFileNotExist;
  }

  is_replay_ = true;

  // if trace_data_dir is a file, load it directly
  if (std::filesystem::is_regular_file(trace_data_dir)) {
    LOCATION_INFO_LOG("Loading track data from file: {}", trace_data_dir);
    planed_gps_ = LoadGPSData(trace_data_dir);
    if (planed_gps_.empty()) {
      LOCATION_ERROR_LOG("Failed to load GPS data from file: {}",
                    trace_data_dir);
      return ErrorCode::kErrorCodeFileNotExist;
    }
    return ErrorCode::kErrorCodeOk;
  }
  

  this->route_path_from_client_ = EnhanceRouteManager::LoadRoutePathFromFile(trace_data_dir + "/route_path.txt");


  planed_gps_ = LoadGPSData(trace_data_dir + "/planned_gps.csv");

  LOCATION_INFO_LOG("Loaded {} track points", planed_gps_.size());
  return ErrorCode::kErrorCodeOk;
}


}  // namespace loc
}  // namespace aurora
