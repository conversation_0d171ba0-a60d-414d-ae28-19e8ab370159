#if !defined(MAP_SRC_LOCATION_SRC_SIMULATOR_CONTROLLER_IMPL_H)
#define MAP_SRC_LOCATION_SRC_SIMULATOR_CONTROLLER_IMPL_H
#include "simulator_controller_impl.h"
#include "location/include/location_module.h"
#include "debug/random_generator.h"
#include "location/src/graph_reader.h"
#include "base/include/loopthread.h"
#include "location/src/map_matching_engine.h"
#include <mutex>
#include <random>

namespace aurora {
namespace loc {
class SimulatorControllerImpl : public SimulatorController {
 public:
  SimulatorControllerImpl(const std::string& config_dir, MapMatchingEngine& map_engine);
  ~SimulatorControllerImpl() override;

  int32_t SetRoutePath(PathInfoPtr route_path) override;

  // Start, Stop, Pause, Resume methods for the simulator
  int32_t Start() override;
  int32_t Stop() override;
  int32_t Pause() override;
  int32_t Resume() override;

  bool IsFinished() const override;

  int32_t SetConfig(const Config& config) override;
  /// @brief Get the current configuration of the simulator
  Config GetConfig() const override;

  int32_t LoadTrackData(const std::string& trace_data_dir) override;
  void Detach() {
    is_detach_ = true;
  }
 private:
  void Tick();

  void UpdatePlanGpsPosInfo();

  void UpdateRadomNoise();


  void DumpGPSData(const std::string& path, const std::vector<GnssPosInfo>& data);
  // void DumpPathInfo(const std::filesystem::path& dumpPath, PathInfoPtr path_info);
  std::vector<GnssPosInfo> LoadGPSData(const std::string& path);
  // void LoadPathInfoFromFile(const std::string& file_path);


  // 高斯噪音生成器


  Config config_;
  std::recursive_mutex mutex_;

  ILocation* GetLocation() {
    return engine_.GetLocationApi();
  }
  MapMatchingEngine& engine_;
  GraphReader* graph_reader_;

  PathInfoPtr route_path_from_client_;

  std::unique_ptr<aurora::LoopThread> worker_thread_;

  std::vector<GnssPosInfo> planed_gps_;
  std::default_random_engine generator_;
  std::normal_distribution<double> dist_pos_noise_;

  aurora::loc::debug::SineWaveGenerator pos_noise_x_generator_;
  aurora::loc::debug::SineWaveGenerator pos_noise_y_generator_;
  aurora::loc::debug::SineWaveGenerator heading_noise_generator_;
  std::normal_distribution<double> dist_heading_noise_;
  std::normal_distribution<double> dist_speed_noise_;
  std::normal_distribution<double> dist_pitch_noise_;
  

  size_t gps_index_ = 0;  ///< Current index in the planned GPS positions
  bool is_pause_ = false;
  bool is_replay_ = false;  ///< Whether the simulator has loaded the path data
  bool is_detach_;

};

}  // namespace loc
}  // namespace aurora

#endif // MAP_SRC_LOCATION_SRC_SIMULATOR_CONTROLLER_IMPL_H