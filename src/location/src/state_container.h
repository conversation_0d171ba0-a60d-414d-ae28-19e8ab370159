#ifndef MAP_SRC_LOCATION_SRC_STATE_CONTAINER_H
#define MAP_SRC_LOCATION_SRC_STATE_CONTAINER_H


#include "location_def.h"
#include "frame_info.h"
namespace aurora {
namespace loc {


/**
 * @brief Interface for managing states and their topology in map matching
 */
class StateContainer {
 public:
  struct Options {
    Options():radius(20) {}
    double radius;

  };
  virtual ~StateContainer() = default;

  virtual const Options& GetOptions() const = 0;

  virtual void SetOptions(const Options& options) = 0;


  // 加载候选状态， 返回frame id
  virtual int32_t BuildCandidateStates(const GnssPosInfo& pos_info) = 0;
  virtual FrameInfo* GetFrameInfo(int32_t frame_id) const = 0;
  virtual int32_t GetCurrentFrameId() const = 0;
  virtual void Reset() = 0;
};

}  // namespace loc
}  // namespace aurora
#endif  // MAP_SRC_LOCATION_SRC_STATE_CONTAINER_H
