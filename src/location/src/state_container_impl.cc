#include "location/src/state_container_impl.h"

#include <unistd.h>
#include <filesystem>

#include <algorithm>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <sstream>

#include "base/include/pointll.h"
#include "base/include/util.h"
#include "data_provider/include/route_data/route_data_def.h"

#define LOCATION_LOG_TAG "Loc::SC"
#include "location/src/debug/location_logger.h"

// #include "boost/json/src.hpp"
namespace aurora {
namespace loc {
StateContainerImpl::StateContainerImpl(GraphReader& reader)
    : graph_reader_(reader),
      current_frame_id_(0) { }

std::vector<StateContainerImpl::StateCandidate> StateContainerImpl::GetStateCandicatesOnEdge(const EdgeInfo& edge_info, const PointLL& point, double radius) {
    std::vector<StateContainerImpl::StateCandidate> ret;
    auto augment_edge = edge_info.augment_edge;
    auto& points = augment_edge->GetGeoPoints();
    DistanceApproximator distance_approx(point);
    double radius_sqr = radius * radius;
    double offset = 0;
    StateContainerImpl::StateCandidate cand;

    int forward_lane_count = edge_info.topol_edge->GetBaseInfo()->forward_lane_count;
    int backward_lane_count = edge_info.topol_edge->GetBaseInfo()->backward_lane_count;



    for (size_t i = 1; i < points.size(); ++i) {
      // Build Line Segment and calculate distance
      offset += edge_info.lens[i - 1];
      double included_angle = 0;
      PointLL proj_point = point.ProjectWithAngle(points[i - 1], points[i], included_angle);
      double distance_sqr = distance_approx.DistanceSquared(proj_point);

      if (distance_sqr < 0) {
        distance_sqr = 0.0001;  // Skip if projection is invalid
      }
      if (distance_sqr > radius_sqr) {
         continue;  // Skip if distance is greater than radius
      }
      cand.project_angle = included_angle;
      cand.project_point = proj_point;
      cand.project_distance = std::sqrt(distance_sqr);
      cand.edge_offset =  offset - std::sqrt(DistanceApproximator(aurora::PointLL(points[i])).DistanceSquared(proj_point));
      cand.edge_heading = edge_info.headings[i - 1];
      cand.segment_index = i - 1;
      cand.is_forward = true;
      if(edge_info.SupportForward()) {
        // if(forward_lane_count > 0) {
        cand.project_distance = cand.project_distance - forward_lane_count * 1.0;  // Adjust for forward lane offset
        // }
        ret.push_back(cand);
      }
      if(edge_info.SupportBackward()) {
        cand.edge_offset = edge_info.total_len - cand.edge_offset;
        cand.edge_heading = std::fmod(cand.edge_heading  + 180., 360.);
        cand.segment_index = edge_info.lens.size() - i - 1;
        cand.is_forward = false;
        cand.project_distance = cand.project_distance - backward_lane_count * 1.0;  // Adjust for forward lane offset

        ret.push_back(cand);
      }
    }
    return ret;
}


int32_t StateContainerImpl::BuildCandidateStates(const GnssPosInfo& pos_info) {
  // 1. Get the tile IDs
  int state_seq = 0;
  ++current_frame_id_;
  frame_map_[current_frame_id_] = std::make_shared<FrameInfo>(current_frame_id_, pos_info);
  auto& frame_info = *frame_map_[current_frame_id_];
  auto mbr = aurora::ExpandMeters(pos_info.lnglat, options_.radius);
  auto edges = graph_reader_.GetEdgeInfos(mbr);

  for(auto& edge_ptr : edges) {
    EdgeInfo& edge_info = *edge_ptr;
    auto cands = GetStateCandicatesOnEdge(edge_info, pos_info.lnglat, options_.radius);
    for(auto& cand: cands) {
        StateId state_id = ((uint64_t)frame_info.frame_id << 32) | state_seq++;
        StateInfo& state_info = frame_info.state_map[state_id];
        state_info.id = state_id;
        state_info.edge_id = DirectedEdgeId(edge_info.edge_id,cand.is_forward);
        state_info.segment_idx = cand.segment_index;
        state_info.project_angle =  cand.project_angle;
        state_info.proj_pos = cand.project_point;
        state_info.distance = std::max(0.,cand.project_distance);
        state_info.edge_offset = cand.edge_offset;
        state_info.heading = cand.edge_heading;
    } //  for(auto& cand: cands) 
  }  // for(auto& edge_ptr : edges) {
  return current_frame_id_;
}

FrameInfo* StateContainerImpl::GetFrameInfo(int32_t frame_id) const {
    if(frame_map_.count(frame_id) == 0) {
        return nullptr;
    }
    return frame_map_.at(frame_id).get();
}

int32_t StateContainerImpl::GetCurrentFrameId() const  {
    return current_frame_id_;
}


void StateContainerImpl::Reset() {
    frame_map_.clear();
    current_frame_id_ = 0;
}

const StateContainer::Options& StateContainerImpl::GetOptions() const {
    return options_;
}
void StateContainerImpl::SetOptions(const Options& options) {
    options_ = options;
}



}  // namespace loc
}  // namespace aurora
