#ifndef STATE_CONTAINER_IMPL_H
#define STATE_CONTAINER_IMPL_H

#include "location_def.h"
#include "frame_info.h"
#include "graph_reader.h"
#include "match_policy.h"
#include <unordered_map>
#include "state_container.h"
#include <memory>

namespace aurora {
namespace loc {

class StateContainerImpl : public StateContainer {
public:
  explicit StateContainerImpl(GraphReader& reader);
  virtual ~StateContainerImpl() = default;

  // Implement StateContainer interface
  const Options& GetOptions() const override;
  void SetOptions(const Options& options) override;

  int32_t BuildCandidateStates(const GnssPosInfo& pos_info) override;
  FrameInfo* GetFrameInfo(int32_t frame_id) const override;
  int32_t GetCurrentFrameId() const override;
  void Reset() override;

private:
  struct StateCandidate {
    size_t segment_index;
    PointLL project_point;
    double edge_offset;
    double project_distance;
    double project_angle;
    double edge_heading;
    bool is_forward;
  };

  std::vector<StateCandidate> GetStateCandicatesOnEdge(const EdgeInfo& edge_info, const PointLL& point, double radius);

  GraphReader& graph_reader_;
  std::unordered_map<int32_t, std::shared_ptr<FrameInfo>> frame_map_;
  int32_t current_frame_id_;
  Options options_;
};

}  // namespace loc
}  // namespace aurora

#endif  // STATE_CONTAINER_IMPL_H
