#ifndef MAP_SRC_LOCATION_SRC_STATE_INFO_H
#define MAP_SRC_LOCATION_SRC_STATE_INFO_H

#include <vector>
#include <cstdint>
#include "data_provider/include/route_data/feature/augment_edge.h"
#include "location/include/location_def.h"
#include "location_internal_def.h"
namespace aurora {
namespace loc {

typedef uint64_t StateId;

struct ForwardLink {
  StateId from_id;
  double forward_score;
  double prev_total_score;
  int history_link_len;  // The length of the history link
  double road_distance;
  double movement_vector_score;
  ForwardLink(StateId from_id_, double forward_score_,
              double prev_total_score_, int history_link_len_, double road_distance, double movement_vector_score = 0)
      : from_id(from_id_),
        forward_score(forward_score_),
        prev_total_score(prev_total_score_),
        history_link_len(history_link_len_), 
        road_distance(road_distance),
        movement_vector_score(movement_vector_score) {}

};

struct StateInfo {
  StateId id;
  DirectedEdgeId edge_id;
  int16_t segment_idx;
  double project_angle;

  aurora::PointLL proj_pos;
  double distance;  // 距离
  double heading;

  double edge_offset;  // 距离edge start 点的偏移量

  double distance_score;
  double heading_score;
  double route_score;
  double forward_score;     // 前向代价
  double prev_total_score;  // 历史代价权重
  StateId parent_id;
  double total_score;  // 总体代价， 当前和历史代价一起计算， 经过代价权重调整,
                       // 保留的topn 需要进行正则化
                       
  int32_t path_link_index;  // The index of the path link in the route path
  uint64_t path_id;
  std::vector<ForwardLink> forward_links;
  int history_link_len;
  size_t best_link_idx;
  int order; // The order of the state in the frame, used for debugging
};

}  // namespace loc
}  // namespace aurora

#endif  // MAP_SRC_LOCATION_SRC_STATE_INFO_H
