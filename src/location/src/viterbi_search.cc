#include "viterbi_search.h"
#include "state_container.h"
#include "enhance_data_info.h"
#include "enhance_route_manager.h"
#include "graph_reader.h"
#include "base/include/util.h"
#include "graph_traverse.h"
#include "match_policy.h"

#include <cmath>
#include <algorithm>
#include <functional>
#include "match_scorer.h"

#define LOCATION_LOG_TAG "Loc::VB"
#include "location/src/debug/location_logger.h"

namespace aurora {
namespace loc {

ViterbiSearch::ViterbiSearch(GraphReader& reader,
                            StateContainer& state_container,
                            MatchScorer& scorer,
                            PolicyManager& policy_manager,
                            EnhanceRouteManager& route_manager) noexcept
    : reader_(reader),
      state_container_(state_container),
      scorer_(scorer),
      policy_manager_(policy_manager),
      route_manager_(route_manager) {
}

aurora::Vector2d ViterbiSearch::GetMoveVector(
    const aurora::PointLL& start, const aurora::PointLL& end) {
  double move_heading = start.Heading(end);
  double move_distance = end.Distance(start);
  return aurora::Vector2d(
      sin(move_heading / 180.0 * M_PI) * move_distance,
      cos(move_heading / 180.0 * M_PI) * move_distance);
}

void ViterbiSearch::BuildForwardLinks(FrameInfo& prev_frame, FrameInfo& cur_frame, double radius) {
  auto& policy = policy_manager_.GetCurrentPolicy();
  if (cur_frame.frame_id < 2) {
    return;
  }

  double average_speed =
      cur_frame.gnss_data.speed + prev_frame.gnss_data.speed;

  GraphTraverse tr(reader_);

  // Compute GPS movement vector
  aurora::Vector2d gps_move_vector = GetMoveVector(
      prev_frame.gnss_data.lnglat, cur_frame.gnss_data.lnglat);

  // Calculate MBR for target point

  TraverseParams params;
  params.target_mbr = aurora::ExpandMeters(cur_frame.gnss_data.lnglat, radius);;
  params.max_distance = average_speed + 20;
  params.max_turn = 360;
  
  for (const auto& pre_state_pair : prev_frame.state_map) {
    const auto& pre_state_info = pre_state_pair.second;
    params.from_edge = pre_state_pair.second.edge_id;
    params.from_offset = pre_state_pair.second.edge_offset;
    auto reached_edges = tr.DistanceFirstTraverse(params);
    for(auto& re : reached_edges) {
      auto sids = cur_frame.FindStates(re.edge_id);
      bool is_same_edge  = (re.edge_id == pre_state_info.edge_id);

      for(auto& sinfo : sids) {
        // double passed_distance = sinfo->edge_offset + re.distance;
        // if (passed_distance > params.max_distance) {
        //   continue;
        // }
        // const_cast<StateInfo*>(sinfo)->prev_total_score = 100;
        double road_move_distance = 0;
        if(is_same_edge) {
          if(sinfo->edge_offset < pre_state_info.edge_offset - 2.0) {
            continue;  // Skip if this state is behind the previous state on the same edge
          }
          road_move_distance = sinfo->edge_offset - pre_state_info.edge_offset;
        } else {
          road_move_distance =  sinfo->edge_offset + re.distance;
        }

        // Calculate movement vector and score
        aurora::Vector2d state_move_vector = GetMoveVector(
          pre_state_info.proj_pos, sinfo->proj_pos);

        // double forward_cost = scorer_.CalcMovementScore(gps_move_vector, state_move_vector);

        double movement_vector_score = scorer_.CalcMovementScore(gps_move_vector, state_move_vector);
        double road_move_score = scorer_.CalcTranferScore(road_move_distance, average_speed /2 ); // (* time_delta);

        double transfer_cost = -scorer_.transitionLogProbability(gps_move_vector.Norm(), road_move_distance) ;
        double total_prev_score = pre_state_info.total_score *  policy.history_factor  - transfer_cost * policy.movement_distance_weight + movement_vector_score * policy.movement_vector_weight;
        if(pre_state_info.order == 0) {
          total_prev_score += policy.best_forward_score;
        }
        const_cast<StateInfo*>(sinfo)->forward_links.emplace_back(
           pre_state_info.id, total_prev_score , pre_state_info.total_score, pre_state_info.history_link_len, road_move_distance, movement_vector_score);
      }

    }
  }
}

void ViterbiSearch::MergeCostAndPrune(FrameInfo& frame) {
  auto& policy = policy_manager_.GetCurrentPolicy();
  int total_states = frame.state_map.size();
  (void)total_states; // maybe use in future logging
  double heading = frame.gnss_data.heading;
  for (auto& state_pair : frame.state_map) {
    auto& state_info = state_pair.second;
    state_info.distance_score = scorer_.CalcDistanceScore(state_info.distance, state_container_.GetOptions().radius);
    state_info.heading_score = scorer_.CalcHeadingScore(state_info.heading, heading);
    uint64_t path_id;
    int32_t path_link_index;
    if(route_manager_.GetLinkInfoOnRoute(state_info.edge_id, state_info.edge_offset, path_id, path_link_index)) {
      state_info.path_id = path_id;
      state_info.path_link_index = path_link_index;
      state_info.route_score = 2;
    }

    state_info.total_score = state_info.distance_score * policy.distance_weight +
      state_info.heading_score * policy.heading_weight +
      state_info.route_score * policy.route_weight;


    if (!state_info.forward_links.empty()) {
      const ForwardLink* best_link = &state_info.forward_links[0];
      size_t best_link_index = 0;
      for (const auto& link : state_info.forward_links) {
        if (best_link->forward_score * policy.forward_weight + best_link->prev_total_score * policy.history_factor < link.forward_score * policy.forward_weight + link.prev_total_score * policy.history_factor) {
          best_link_index = &link - &state_info.forward_links[0];
          best_link = &link;
        }
      }
      state_info.best_link_idx = best_link_index;


      state_info.total_score += best_link->forward_score ;// + state_info.prev_total_score * policy.history_factor; 
      // state_info.history_link_len * policy.history_factor;
      state_info.parent_id = best_link->from_id;
      state_info.forward_score = best_link->forward_score;
      state_info.history_link_len = best_link->history_link_len + 1;
      state_info.prev_total_score = best_link->prev_total_score;
    }
  }

  // Prune states (keep top 30)
  std::vector<std::pair<StateId, StateInfo*>> state_ptrs;
  for (auto& pair : frame.state_map) {
    state_ptrs.emplace_back(pair.first, &pair.second);
  }

  std::sort(state_ptrs.begin(), state_ptrs.end(),
            [](const auto& a, const auto& b) {
              return a.second->total_score > b.second->total_score;
            });

  std::unordered_set<DirectedEdgeId> edge_set;
  std::unordered_map<StateId, StateInfo> pruned_map;

  // Keep top states per edge
  for (auto& state_ptr : state_ptrs) {
    auto& state_info = *state_ptr.second;
    auto edge_id = state_info.edge_id;
    if (edge_set.count(edge_id) == 0) {
      edge_set.insert(edge_id);
      pruned_map[state_ptr.first] = state_info;
    }
    if (edge_set.size() >= 30) {
      break;
    }
  }

  // Keep states that are on route regardless of score
  int order = 0;

  for (auto& state_ptr : state_ptrs) {
    auto& state_info = *state_ptr.second;
    if(pruned_map.find(state_ptr.first) == pruned_map.end()) {
      if ((state_info.route_score > 0.0 || pruned_map.size() < 30)) {
        state_info.order = order++;
        pruned_map[state_ptr.first] = state_info;
      }
    } else {
      pruned_map[state_ptr.first].order = order++;
      // state_info.order = order++;
    }
  }

  frame.state_map = std::move(pruned_map);
  
}

bool ViterbiSearch::ProcessOneFrame(const GnssPosInfo& input) noexcept {
  // Build candidate states using state container
  int32_t frame_id = state_container_.BuildCandidateStates(input);
  FrameInfo* cur_frame_info = state_container_.GetFrameInfo(frame_id);
  if (!cur_frame_info) {
    LOCATION_ERROR_LOG("Failed to get frame info for frame {}", frame_id);
    return false;
  }

  FrameInfo* prev_frame_info = state_container_.GetFrameInfo(frame_id - 1);
  if (prev_frame_info) {
    // Use a default radius of 50 meters since StateContainer doesn't have GetRadius()
    BuildForwardLinks(*prev_frame_info, *cur_frame_info, state_container_.GetOptions().radius);
  }

  // Calculate scores and prune states
  MergeCostAndPrune(*cur_frame_info);
  return true;
}
} // namespace loc
} // namespace aurora
