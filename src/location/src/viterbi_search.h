#ifndef MAP_SRC_LOCATION_SRC_VITERBI_SEARCH_H_
#define MAP_SRC_LOCATION_SRC_VITERBI_SEARCH_H_
#include "frame_info.h"
#include "base/include/vector2.h"
namespace aurora {
namespace loc {
/* Forward declarations */
class GraphReader;
class GnssPosInfo;
class StateContainer;
class MatchScorer;
class PolicyManager;
class EnhanceRouteManager;
/*End Forward declarations*/

/// Implements Viterbi algorithm for path matching in map localization
/// Manages dependencies required for path matching and scoring
class ViterbiSearch {
 private:
  GraphReader& reader_;
  StateContainer& state_container_;
  MatchScorer& scorer_;
  PolicyManager& policy_manager_;
  EnhanceRouteManager& route_manager_;

 public:
  ~ViterbiSearch() = default;
  
  /// Constructs a ViterbiSearch with required dependencies
  /// @param reader Provides graph data access
  /// @param state_container Manages search states
  /// @param scorer Scores candidate matches
  /// @param policy_manager Applies matching policies
  /// @param route_manager Handles enhanced route data
  explicit ViterbiSearch(GraphReader& reader, 
                        StateContainer& state_container,
                        MatchScorer& scorer, 
                        PolicyManager& policy_manager,
                        EnhanceRouteManager& route_manager) noexcept;

  /// Processes one frame of GNSS input data
  /// @param input GNSS position information to match
  /// @return true if matching succeeded, false otherwise
  bool ProcessOneFrame(const GnssPosInfo& input) noexcept;

  void Rescore(StateContainer& state_container) {
    // This function is not implemented in the original code, but can be used to rescore states
    // based on updated policies or scoring methods.
    // LOCATION_ERROR_LOG("Rescore function is not implemented yet.");

  }

private:
  /// Calculates movement vector between two points
  aurora::Vector2d GetMoveVector(
      const aurora::PointLL& start, const aurora::PointLL& end);

  /// Builds forward links between frames
  void BuildForwardLinks(FrameInfo& prev_frame, FrameInfo& cur_frame, double radius);

  /// Merges costs and prunes states
  void MergeCostAndPrune(FrameInfo& frame);
  
  // Deleted copy/move operations since we hold references
  ViterbiSearch(const ViterbiSearch&) = delete;
  ViterbiSearch& operator=(const ViterbiSearch&) = delete;
  ViterbiSearch(ViterbiSearch&&) = delete;
  ViterbiSearch& operator=(ViterbiSearch&&) = delete;
};
}  // namespace loc
}  // namespace aurora

#endif  // MAP_SRC_LOCATION_SRC_VITERBI_SEARCH_H_
