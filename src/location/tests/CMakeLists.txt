# Add gtest executable
# add_executable(emission_cost_model_test emission_cost_model_test.cc)

# # Link necessary libraries
# target_link_libraries(emission_cost_model_test
#     PRIVATE
#     aurora_location_static
#     aurora_base
#     gtest
#     gtest_main
# )

# # Add include directories
# target_include_directories(emission_cost_model_test
#     PRIVATE
#     ${CMAKE_CURRENT_SOURCE_DIR}/../include
#     ${CMAKE_CURRENT_SOURCE_DIR}/../../include
# )

# # Add test to CTest
# add_test(NAME emission_cost_model_test
#     COMMAND emission_cost_model_test)

# add data_provider_test
add_executable(data_provider_test data_provider_test.cc)
# Link necessary libraries
target_link_libraries(data_provider_test
    PRIVATE
    aurora_location_static
    aurora_base
    data_provider
    gtest
    gtest_main
)
# Add include directories
target_include_directories(data_provider_test
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
)
# Add test to CTest
add_test(NAME data_provider_test
    COMMAND data_provider_test)


# add state_container_impl_test
add_executable(state_container_impl_test state_container_impl_test.cc)
# Link necessary libraries
target_link_libraries(state_container_impl_test
    PRIVATE
    aurora_location_static
    aurora_base
    data_provider
    gtest
    gtest_main
)
# Add include directories
target_include_directories(state_container_impl_test
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    ${RAPIDJSON_SOURCE_DIR}/include
)
# Add test to CTest
add_test(NAME state_container_impl_test
    COMMAND state_container_impl_test)
#end state_container_impl_test


add_executable(location_api_test location_api_test.cc task_q_test.cc)
# Link necessary libraries
target_link_libraries(location_api_test
    PRIVATE
    aurora_location_static
    aurora_base
    data_provider
    aurora_path
    gtest
    gtest_main
)
# Add include directories
target_include_directories(location_api_test
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
)

add_executable(location_test_replay location_test_replay.cc)
# Link necessary libraries
target_link_libraries(location_test_replay
    PRIVATE
    aurora_location_static
    aurora_base
    data_provider
    aurora_path
)


add_executable(location_replay location_replay.cc)
# Link necessary libraries
target_link_libraries(location_replay
    PRIVATE
    aurora_location_static
    aurora_base
    data_provider
    aurora_path
)
# Add include directories
target_include_directories(location_replay
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
)

add_executable(graph_box_dump graph_box_dump.cc)
# Link necessary libraries
target_link_libraries(graph_box_dump
    PRIVATE
    aurora_location_static
    aurora_base
    data_provider
    aurora_path
)
# Add include directories
target_include_directories(graph_box_dump
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    ${RAPIDJSON_SOURCE_DIR}/include
)


# add graph_reader_impl_test
add_executable(graph_reader_test graph_reader_impl_test.cc graph_traverse_test.cc)
# Link necessary libraries
target_link_libraries(graph_reader_test
    PRIVATE
    aurora_location_static
    aurora_base
    data_provider
    gtest
    gtest_main
)
# Add include directories
target_include_directories(graph_reader_test
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../third_party/googletest-1.16.x/googlemock/include/
)
# Add test to CTest
add_test(NAME graph_reader_test
    COMMAND graph_reader_test)


    # add graph_reader_impl_test
add_executable(utils_test map_scorer_test.cc)
# Link necessary libraries
target_link_libraries(utils_test
    PRIVATE
    aurora_location_static
    aurora_base
    data_provider
    gtest
    gtest_main
)
# Add include directories
target_include_directories(utils_test
PRIVATE
${CMAKE_CURRENT_SOURCE_DIR}/../include
${CMAKE_CURRENT_SOURCE_DIR}/../../include
${CMAKE_CURRENT_SOURCE_DIR}/../../../third_party/googletest-1.16.x/googlemock/include/
)
# Add test to CTest
add_test(NAME utils_test
    COMMAND utils_test)
