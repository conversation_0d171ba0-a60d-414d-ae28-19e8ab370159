#include "base/include/logger.h"
#include "base/include/pointll.h"
#include "data_provider/include/route_data/route_data_def.h"
#include "data_provider/include/route_data/feature/augment_edge.h"
#include "location/src/graph_reader_impl.h"
#include "location/src/geojson_writer.h"
#include "base/include/util.h"
#include <fstream>
#include <memory>
#include <unordered_map>

namespace {
constexpr double kCoordinateEpsilon = 1e-13;

void ValidateNodePosition(
    const std::unordered_map<aurora::parser::RouteNodeID, aurora::PointXY<double>>& nodes,
    const aurora::parser::RouteNodeID& node_id,
    const aurora::PointXY<double>& point,
    const std::string& node_type) {
    
    if (nodes.count(node_id) == 0) return;
    
    const auto& prev_node = nodes.at(node_id);
    if (prev_node.Distance(point) > kCoordinateEpsilon) {
        LOG_ERROR("{} node {} has coordinate mismatch: ({}, {}) vs ({}, {})", 
                 node_type, node_id.feature_id, 
                 prev_node.x(), prev_node.y(),
                 point.x(), point.y());
    }
}

void AddEdgeFeature(
    aurora::loc::GeoJsonWriter& writer,
    const aurora::loc::EdgeInfoPtr& edge) {
    
    // Convert points to PointLL
    std::vector<aurora::PointLL> points;
    for (const auto& point : edge->augment_edge->GetGeoPoints()) {
        points.emplace_back(point.x(), point.y());
    }

    // Create properties map
    std::unordered_map<std::string, boost::any> properties;
    properties["tile_id"] = edge->edge_id.tile_id.value;
    properties["feature_id"] = edge->edge_id.feature_id;
    properties["start_node_id"] = edge->GetStartNodeID().feature_id;
    properties["end_node_id"] = edge->GetEndNodeID().feature_id;
    properties["total_length"] = edge->total_len;
    properties["start_heading"] = edge->headings.front();
    properties["end_heading"] = edge->headings.back();
    properties["total_turn"] = edge->total_turn;
    properties["direction"] = edge->topol_edge->GetBaseInfo()->direction;
    writer.AppendLineString(points, properties);
}

void AddNodeFeature(
    aurora::loc::GeoJsonWriter& writer,
    const aurora::parser::RouteNodeID& node_id,
    const aurora::PointXY<double>& point) {
    
    // Create properties map
    std::unordered_map<std::string, boost::any> properties;
    properties["id"] = node_id.feature_id;
    properties["tile_id"] = node_id.tile_id.value;

    writer.AppendPoint(aurora::PointLL(point.x(), point.y()), properties);
}
} // namespace

int main(int argc, char** argv) {
    if (argc < 4) {
        LOG_ERROR("Usage: {} <lat> <lng> <expand_meters>", argv[0]);
        LOG_ERROR("Example: {} 31.032298 121.714590 300", argv[0]);
        return 1;
    }

    // Validate and parse inputs
    double lat, lng, expand_meters;
    try {
        lat = std::stod(argv[1]);
        lng = std::stod(argv[2]);
        expand_meters = std::stod(argv[3]);
        
        if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
            LOG_ERROR("Invalid coordinates: lat {} lng {}", lat, lng);
            return 1;
        }
        if (expand_meters <= 0) {
            LOG_ERROR("Expand meters must be positive");
            return 1;
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Invalid input: {}", e.what());
        return 1;
    }

    // Initialize graph reader
    std::unique_ptr<aurora::loc::GraphReaderImpl> reader = 
        std::make_unique<aurora::loc::GraphReaderImpl>("distribution/data/route/");

    // Create bounding box
    aurora::PointLL center{lng, lat};
    aurora::AABB2<aurora::PointLL> bounding_box =
        aurora::ExpandMeters(center, expand_meters);

    // Get edges in bounding box
    auto edges = reader->GetEdgeInfos(bounding_box);
    LOG_INFO("Found {} edges in bounding box", edges.size());

    // Initialize GeoJSON writer
    aurora::loc::GeoJsonWriter writer("edges.geojson");
    std::unordered_map<aurora::parser::RouteNodeID, aurora::PointXY<double>> nodes;

    // Process edges
    for (const auto& edge : edges) {
        AddEdgeFeature(writer, edge);
        
        // Track nodes and validate consistency
        const auto& start_point = edge->augment_edge->GetGeoPoints().front();
        const auto& end_point = edge->augment_edge->GetGeoPoints().back();
        
        ValidateNodePosition(nodes, edge->GetStartNodeID(), start_point, "Start");
        ValidateNodePosition(nodes, edge->GetEndNodeID(), end_point, "End");
        
        if (!nodes.count(edge->GetStartNodeID())) {
            nodes.emplace(edge->GetStartNodeID(), start_point);
        }
        if (!nodes.count(edge->GetEndNodeID())) {
            nodes.emplace(edge->GetEndNodeID(), end_point);
        }
    }

    // Add nodes to features
    for (const auto& [node_id, point] : nodes) {
        AddNodeFeature(writer, node_id, point);
    }

    LOG_INFO("Successfully wrote {} edges and {} nodes to edges.geojson", 
            edges.size(), nodes.size());
    return 0;
}
