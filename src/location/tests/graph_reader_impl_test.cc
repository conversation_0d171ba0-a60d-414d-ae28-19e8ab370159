#include "gtest/gtest.h"
#include "location/src/graph_reader_impl.h"
#include "base/include/pointll.h"
#include "data_provider/include/route_data/route_data_def.h"
#include "base/include/logger.h"

using namespace aurora::loc;

class GraphReaderImplTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize with test data directory
        reader_ = std::make_unique<GraphReaderImpl>("distribution/data/route/");
    }

    std::unique_ptr<GraphReaderImpl> reader_;
};

TEST_F(GraphReaderImplTest, GetEdgeInfoValid) {
    aurora::parser::RouteEdgeID edge_id{aurora::parser::RouteTileID(25606977271161844L), 3155};
    auto edge_info = reader_->GetEdgeInfo(edge_id);
    
    ASSERT_NE(edge_info, nullptr);
    EXPECT_EQ(edge_info->edge_id.tile_id.value, 25606977271161844L);
    EXPECT_EQ(edge_info->edge_id.feature_id, 3155);
    EXPECT_GT(edge_info->total_len, 0);
}

TEST_F(GraphReaderImplTest, GetEdgeInfoInvalid) {
    aurora::parser::RouteEdgeID invalid_edge_id{aurora::parser::RouteTileID(9999), 9999};
    auto edge_info = reader_->GetEdgeInfo(invalid_edge_id);
    EXPECT_EQ(edge_info, nullptr);
}

TEST_F(GraphReaderImplTest, GetInOutEdgeIds) {
    aurora::parser::RouteNodeID node_id{aurora::parser::RouteTileID(25606977271161844L), 300};
    
    auto in_edges = reader_->GetInEdgeIds(node_id);
    auto out_edges = reader_->GetOutEdgeIds(node_id);
    
    EXPECT_FALSE(in_edges.empty());
    EXPECT_FALSE(out_edges.empty());
    
    // Verify that in-edges point to this node
    for (const auto& edge : in_edges) {
        auto info = reader_->GetEdgeInfo(edge.edge_id);
        ASSERT_NE(info, nullptr);
        if (edge.is_forward) {
            EXPECT_EQ(info->topol_edge->GetBaseInfo()->end_node_id, node_id.feature_id);
        } else {
            EXPECT_EQ(info->topol_edge->GetBaseInfo()->start_node_id, node_id.feature_id);
        }
        LOG_INFO("IN Edge: {}->{}", info->edge_id.feature_id, node_id.feature_id);
    }
    
    // Verify that out-edges start from this node
    for (const auto& edge : out_edges) {
        auto info = reader_->GetEdgeInfo(edge.edge_id);
        ASSERT_NE(info, nullptr);
        if (edge.is_forward) {
            EXPECT_EQ(info->topol_edge->GetBaseInfo()->start_node_id, node_id.feature_id);
        } else {
            EXPECT_EQ(info->topol_edge->GetBaseInfo()->end_node_id, node_id.feature_id);
        }
        LOG_INFO("Out Edge: {}<-{}", info->edge_id.feature_id, node_id.feature_id);
    }
}

TEST_F(GraphReaderImplTest, GetEdgeInfosInMBR) {

    double lng = 121.31604686379432;
    double lat = 31.189158148023324;

    aurora::PointLL min_point(lng - 0.2, lat - 0.10);
    aurora::PointLL max_point(lng + 0.1, lat + 0.1);

    // min_point(lng - 0.2, lat - 0.1, lng + 0.1, lat + 0.1);
    aurora::AABB2<aurora::PointLL> mbr(min_point, max_point);
    
    auto edges = reader_->GetEdgeInfos(mbr);
    EXPECT_FALSE(edges.empty());
    LOG_INFO("Fetch Edges Cnt {}", edges.size());

    for (const auto& edge : edges) {
        ASSERT_NE(edge, nullptr);
        const auto& points = edge->augment_edge->GetGeoPoints();
        bool in_mbr = false;
        for (const auto& point : points) {
            if (mbr.Contains(point)) {
                in_mbr = true;
                break;
            }
        }
        EXPECT_TRUE(in_mbr) << "Edge should be within MBR";

        //Print Edge 
        // LOG_INFO("Fetch Edge {}.{}", edge->edge_id.tile_id.value, edge->edge_id.feature_id);
    }
}

TEST_F(GraphReaderImplTest, TileCaching) {
    // aurora::parser::RouteTileID tile_id = 25606977271161844;
    aurora::parser::RouteEdgeID edge_id{aurora::parser::RouteTileID(25606977271161844L), 3155};
    
    // First access should load tile
    auto edge_info1 = reader_->GetEdgeInfo(edge_id);
    ASSERT_NE(edge_info1, nullptr);
    
    // Second access should use cached tile
    auto edge_info2 = reader_->GetEdgeInfo(edge_id);
    ASSERT_NE(edge_info2, nullptr);
    
    // Should be the same object (shared pointer)
    EXPECT_EQ(edge_info1, edge_info2);
}
