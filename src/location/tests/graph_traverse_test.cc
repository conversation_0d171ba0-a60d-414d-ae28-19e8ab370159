#include "location/src/graph_traverse.h"
#include "base/include/logger.h"
#include "location/src/graph_reader.h"
#include "location/src/graph_reader_impl.h"

#include "base/include/pointll.h"
#include "location/src/location_internal_def.h"
#include "data_provider/include/route_data/route_data_def.h"
#include <gtest/gtest.h>
#include <vector>
#include <memory>
#include "base/include/util.h"
#include "location/src/debug/location_logger.h"

using namespace aurora::loc;
using namespace testing;
using namespace aurora;

namespace {

class GraphTraverseTest : public ::testing::Test {
 protected:
  void SetUp() override {
    reader_ = std::make_unique<GraphReaderImpl>("distribution/data/route/");
    traverse_ = std::make_unique<GraphTraverse>(*reader_);
  }

  std::unique_ptr<GraphReader> reader_;
  std::unique_ptr<GraphTraverse> traverse_;
};

TEST_F(GraphTraverseTest, EmptyResultWhenNoReachableEdges) {
  TraverseParams params;
  params.from_edge = DirectedEdgeId(aurora::parser::RouteEdgeID(25554217892887280L, 8249), true);
  params.from_offset = 85.082130;


  params.target_mbr = aurora::ExpandMeters(aurora::PointLL{121.71535, 31.03375}, 100);//  aurora::AABB2<aurora::PointLL>{aurora::PointLL{121.21285401619197, 31.14488999222928}, aurora::PointLL{1, 1}};
  params.max_distance = 600.0;
  params.max_turn = 350.0;
  auto start = std::chrono::high_resolution_clock::now();
  auto result = traverse_->DistanceFirstTraverse(params);
  auto time_cost = std::chrono::high_resolution_clock::now() - start;
  LOG_INFO("Time Cost {} ns", time_cost.count());

  for(auto& r: result) {
    LOG_INFO("Edge ID: {}.{}, Distance: {}, Turn Angle: {}, From Edge: {}.{}., From Offset: {}",
             r.edge_id.edge_id.tile_id.value,  r.edge_id.edge_id.feature_id, r.distance, r.turn_angle, r.from_edge.edge_id.tile_id.value,  r.from_edge.edge_id.feature_id, r.from_offset);
    for (const auto& intermediate : r.intermediates) {
      LOG_INFO("Intermediate Edge ID: {}.{} {}", intermediate.edge_id.tile_id.value, intermediate.edge_id.feature_id, intermediate.is_forward ? "forward" : "backward");
    }
  }
  EXPECT_FALSE(result.empty());
}

// TEST_F(GraphTraverseTest, FindsSingleReachableEdge) {
//   TraverseParams params;
//   params.from_edge = DirectedEdgeId(aurora::parser::RouteEdgeID(1, 0), true);
//   params.from_offset = 10.0;
//   params.target_mbr = aurora::AABB2<aurora::PointLL>{aurora::PointLL{0, 0}, aurora::PointLL{1, 1}};
//   params.max_distance = 100.0;
//   params.max_turn = 45.0;

//   // Set up mock responses
//   EXPECT_CALL(*reader_, GetOutEdgeIds).WillOnce([](const aurora::parser::RouteNodeID&) {
//     return std::vector<DirectedEdgeId>{DirectedEdgeId(aurora::parser::RouteEdgeID(2, 0), true)};
//   });
  
//   EXPECT_CALL(*reader_, GetEdgeInfo).WillOnce([](const aurora::parser::RouteEdgeID& id) {
//     if (id == aurora::parser::RouteEdgeID(2, 0)) {
//       auto edge_info = std::make_shared<EdgeInfo>();
//       edge_info->total_len = 50;
//       edge_info->start_node = aurora::parser::RouteNodeID(100, 0);
//       edge_info->end_node = aurora::parser::RouteNodeID(101, 0);
//       return edge_info;
//     }
//     return nullptr;
//   });

//   auto result = traverse_->DistanceFirstTraverse(params);
//   ASSERT_EQ(result.size(), 1);
//   EXPECT_EQ(result[0].edge_id.edge_id, 2);
//   EXPECT_DOUBLE_EQ(result[0].distance, 50.0);
// }

// TEST_F(GraphTraverseTest, FiltersByMaxDistance) {
//   TraverseParams params;
//   params.from_edge = DirectedEdgeId(aurora::parser::RouteEdgeID(1, 0), true);
//   params.from_offset = 10.0;
//   params.target_mbr = AABB2<PointLL>{PointLL{0, 0}, PointLL{1, 1}};
//   params.max_distance = 50.0;  // Shorter max distance
//   params.max_turn = 45.0;

//   // Set up mock responses
//   EXPECT_CALL(*reader_, GetOutEdgeIds).WillOnce([](const aurora::parser::RouteNodeID&) {
//     return std::vector<DirectedEdgeId>{
//       DirectedEdgeId(aurora::parser::RouteEdgeID(2, 0), true),
//       DirectedEdgeId(aurora::parser::RouteEdgeID(3, 0), true)
//     };
//   });
  
//   EXPECT_CALL(*reader_, GetEdgeInfo)
//     .WillOnce([](const aurora::parser::RouteEdgeID& id) {
//       if (id == aurora::parser::RouteEdgeID(2, 0)) {
//         auto edge_info = std::make_shared<EdgeInfo>();
//         edge_info->length = 40.0;
//         return edge_info;
//       }
//       return nullptr;
//     })
//     .WillOnce([](const aurora::parser::RouteEdgeID& id) {
//       if (id == aurora::parser::RouteEdgeID(3, 0)) {
//         auto edge_info = std::make_shared<EdgeInfo>();
//         edge_info->length = 60.0;  // Exceeds max distance
//         return edge_info;
//       }
//       return nullptr;
//     });

//   auto result = traverse_->DistanceFirstTraverse(params);
//   ASSERT_EQ(result.size(), 1);
//   EXPECT_EQ(result[0].edge_id.edge_id, 2);
// }

}  // namespace

double EastAntiClockToNorthClockwideFunc(uint32_t east)  {
  double v = 360. - (double)(east) * 360.0 / 256.0 /65536.0 ;
  v = fmod (v + 360.0 + 90., 360.0);
  return v;
}

TEST_F(GraphTraverseTest, EastAntiClockToNorthClockwide) {
  EXPECT_NEAR(EastAntiClockToNorthClockwideFunc(0), 90., 1e-6);
  EXPECT_NEAR(EastAntiClockToNorthClockwideFunc(65536 * 256.0 / 360 * 90), 0., 1e-6);
  // EXPECT_NEAR(EastAntiClockToNorthClockwideFunc

}
