
#include <gtest/gtest.h>

#include <fstream>
#include <iomanip>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

// #include "boost/json/src.hpp"

#include "base/include/logger.h"
#include "base/include/util.h"
#include "data_provider/include/data_provider.h"
#include "data_provider/include/route_data/route_data_def.h"
#include "data_provider/include/route_data/route_tile_reader.h"
#include "location/include/location_def.h"
#include "location/include/location_module.h"
#include "location/src/location_module_impl.h"

#include "location/src/map_matching_engine.h"
#include "location/src/state_container.h"
#include "location/src/state_container_impl.h"
#include "location/src/geojson_writer.h"

#include "path/include/path_module.h"

using namespace aurora::parser;

// 31.032298,121.714590
const double c_lng = 121.714590;  // 121.31604686379432;
const double c_lat = 31.032298;   // 31.189158148023324;

class MapMatchingEngineTest : public ::testing::Test {
 protected:
  void SetUp() override {
    // graph_reader_.InitRouteParser("distribution/data/route");
    // provider_.InitRouteParser("distribution/data/route");
    map_match_engine_ =
        aurora::loc::MapMatchingEngineFactory::GetInstance().CreateEngine(
            "distribution/data/route");
  }
  std::shared_ptr<aurora::loc::MapMatchingEngine> map_match_engine_;
};

TEST_F(MapMatchingEngineTest, MatchGPSTest) {
  aurora::loc::GnssPosInfo gps_data;
  gps_data.lnglat = aurora::PointLL(c_lng, c_lat);
  gps_data.timestamp = **********;  // Example timestamp
  gps_data.speed = 10.0;            // Example speed
  gps_data.heading = 90.0;          // Example heading
  bool is_success = true;
  auto result = map_match_engine_->MatchGPS(gps_data, is_success);
  LOG_INFO("Match Result: {}.{} ({},{}) speed {}",
           result.road_match_info.tile_id, result.road_match_info.link_id,
           result.road_match_info.proj_pos.lat(),
           result.road_match_info.proj_pos.lng(), result.car_pos_info.speed);
}

TEST_F(MapMatchingEngineTest, MatchPositionTest) {
  aurora::PointLL point(c_lng, c_lat);
  float radius = 50.0;  // Example radius

  auto result = map_match_engine_->MatchPosition(point, radius);

  LOG_INFO("Match Position Result: {}.{} ({},{})", result.tile_id,
           result.link_id, result.proj_pos.lng(), result.proj_pos.lat());
}

class MatchListner : public aurora::loc::MapMatchingListener {
 public:
  MatchListner() = default;
  ~MatchListner() override = default;

  void OnMapMatchingResult(const aurora::loc::MatchResult& result) override {
    LOG_INFO("Match Result callback: {}.{} ({},{}), heading： {} confidence {} path_link_idx: {} path_id: {} reroute: {} onroad: {}",
             result.road_match_info.tile_id,
             result.road_match_info.link_id,
             result.road_match_info.proj_pos.lng(),
             result.road_match_info.proj_pos.lat(),
             result.car_pos_info.heading,
             result.road_match_info.confidence, 
             result.road_match_info.path_link_idx,
             result.road_match_info.path_id,
             result.reroute ? "true" : "false",
             result.on_road ? "true" : "false"
            );
    LOG_INFO("Match Result Origin: {},{} speed {}",
              result.origin_pos.lnglat.lng(),
              result.origin_pos.lnglat.lat(),
              result.origin_pos.speed);
  }
};



auto module_finder = [](aurora::ModuleId id) {
  return nullptr; //std::dynamic_pointer_cast<aurora::Interface>(path_module);
};

class LocationApiTest : public ::testing::Test {
 protected:
  void SetUp() override {

    location_module_.Prepare("distribution/data/route");
    location_module_.Init(module_finder);
    auto intf = location_module_.GetInterface();
    if (intf->GetModuleId() != aurora::ModuleId::kModuleIdLocation) {
      LOG_ERROR("Failed to get location module interface");
      return;
    }

    location_api_ = std::dynamic_pointer_cast<aurora::loc::ILocation>(intf);

    std::shared_ptr<MatchListner> listener = std::make_shared<MatchListner>();
    location_api_->AddMapMatchingListener(listener);
    ASSERT_TRUE(location_api_ != nullptr)
        << "Failed to get location API interface";
  }

  aurora::loc::LocationModule location_module_;
  std::shared_ptr<aurora::loc::ILocation> location_api_;
};



class MapMatcherResultGeojsonWriter : public aurora::loc::MapMatchingListener {
  private:
   std::vector<aurora::loc::MatchResult> results_;
   aurora::loc::GeoJsonWriter writer_;
  public:
  MapMatcherResultGeojsonWriter(const std::string& output_path) : writer_(output_path) {
    
  }

  ~MapMatcherResultGeojsonWriter() override {
    
  }
 
  void OnMapMatchingResult(const aurora::loc::MatchResult& result) override {
    std::unordered_map<std::string, boost::any> props;
    writer_.AppendPoint(result.road_match_info.proj_pos, props);
  }
 };


class LocationApiTestWithPath : public LocationApiTest {
  public:

    class PathListenerImpl: public aurora::path::PathListener {
      public:
  
    void OnPathResult(const aurora::path::PathQueryPtr& query,
                      const aurora::path::PathResultPtr& result) override {
      // Get 
      if(result->paths.empty()) {
        LOG_ERROR("Received empty path result");
      } else {
        result_ = std::make_shared<aurora::path::PathInfo>(result->paths[0]); 
      }                 
      path_received_ = true;
      cv_.notify_all();
    }



    bool WaitForPath() {
      std::unique_lock<std::mutex> lock(mtx_);
      return cv_.wait_for(lock, std::chrono::seconds(5), [this]() { return path_received_; });
    }

    std::shared_ptr<aurora::path::PathInfo> GetResult() {
      std::unique_lock<std::mutex> lock(mtx_);
      return result_;
    }

    private:
      bool path_received_ = false;
      std::mutex mtx_;
      std::condition_variable cv_;
      std::shared_ptr<aurora::path::PathInfo> result_;
    };
 protected:
  void SetUp() override {
    LocationApiTest::SetUp();
    path_module_ = std::make_shared<aurora::path::PathModule>();
    path_module_->Prepare("src/path/config/path.yaml");
    path_module_->SetParams({{"data_dir", "distribution/data/route"}});

    path_module_->Init(module_finder);
    path_module_->Start();
    path_api_ = std::dynamic_pointer_cast<aurora::path::PathInterface>(path_module_->GetInterface());
    path_api_->AddPathListener(path_listener_);

  }
  std::shared_ptr<aurora::path::PathModule>  path_module_;
  std::shared_ptr<aurora::path::PathInterface> path_api_;

  // std::shared_ptr<aurora::loc::ILocation> location_api_;


  std::shared_ptr<PathListenerImpl> path_listener_ = std::make_shared<PathListenerImpl>();
};

TEST_F(LocationApiTestWithPath, RequestPathTest) {
  auto path_query = std::make_shared<aurora::path::PathQuery>();
  // Shanghai to Beijing, fill to path_query
  auto start = std::make_shared<aurora::path::PathLandmark>();
  start->valid = true;
  // 31.14435,121.20995
  start->pt = aurora::PointLL(121.20995, 31.14435); // Shanghai coordinates
  start->name = "Start Point";
  path_query->path_points.push_back(start);

  auto end = std::make_shared<aurora::path::PathLandmark>();
  end->valid = true;
  // 31.07754,121.28914
  end->pt = aurora::PointLL(121.28914, 31.07754); // Beijing coordinates
  end->name = "End Point";
  path_query->path_points.push_back(end);

  path_query->strategy = aurora::path::PathStrategy::kTimeFirst;
  
  std::string uuid = path_api_->RequestPath(path_query);
  LOG_INFO("Requested path with UUID: {}", uuid);

  // Wait for the path result
  EXPECT_TRUE(path_listener_->WaitForPath()) << "Failed to receive path result within timeout";
  LOG_INFO("WaitForPath  return");

  auto result = path_listener_->GetResult();
  ASSERT_TRUE(result != nullptr) << "Path result is null";
  LOG_INFO("PATH result   {}", result->path_id);

  auto simulator = location_api_->GetSimulatorController();
  ASSERT_TRUE(simulator != nullptr) << "Failed to get simulator controller";
  LOG_INFO("Get simulator");


  auto c = simulator->GetConfig();
  
  c.sample_interval = 1;
  c.play_interval = 0.1;

  c.heading_noise_mean = 0;
  c.heading_noise_stddev = 0;
  c.position_noise_mean = 0;
  c.position_noise_stddev = 2;

  // c.speed_times = 2.0;
  

  simulator->SetConfig(c);


  // simulator->SetRoutePath(result);
  location_api_->SetRoutePath(result);
  LOG_INFO("SetRoutePath ok");

  std::shared_ptr<MapMatcherResultGeojsonWriter> result_writer_ = std::make_shared<MapMatcherResultGeojsonWriter>("match_result.geojson");
  simulator->Start();

  location_api_->AddMapMatchingListener(result_writer_);


  // Sleep for 500ms to wait exit
  LOG_INFO("Sleep 180 Start");
  std::this_thread::sleep_for(std::chrono::milliseconds(50000));
  LOG_INFO("Sleep 180 End");

  simulator->Stop();

  
  LOG_INFO("simulator Stop End");
  location_api_->RemoveMapMatchingListener(result_writer_);

  aurora::loc::LocationModuleImpl* location_impl_ = dynamic_cast<aurora::loc::LocationModuleImpl*>(location_api_.get());

  location_impl_->GetMapMatchingEngine()->GetInternalDataDumper().DumpStates("./test/location/");

  std::this_thread::sleep_for(std::chrono::milliseconds(180));
  LOG_INFO("Sleep 180 End");

}



TEST_F(LocationApiTest, UpdateGnssInfoTest) {
  aurora::loc::GnssPosInfo gps_data;
  gps_data.lnglat = aurora::PointLL(c_lng, c_lat);
  gps_data.timestamp = 12;  // Example timestamp
  gps_data.speed = 10.0;    // Example speed
  gps_data.heading = 90.0;  // Example heading

  int32_t result = location_api_->UpdateGnssInfo(gps_data);
  EXPECT_EQ(result, aurora::ErrorCode::kErrorCodeOk)
      << "Failed to update GNSS info";
}


TEST_F(LocationApiTest, SimulateTest) {
  auto simulator = location_api_->GetSimulatorController();
  ASSERT_TRUE(simulator != nullptr) << "Failed to get simulator controller";

  // Simulate a GNSS position update
  aurora::loc::GnssPosInfo gps_data;
  gps_data.lnglat = aurora::PointLL(c_lng, c_lat);
  gps_data.timestamp = **********;  // Example timestamp
  gps_data.speed = 10.0;            // Example speed
  gps_data.heading = 90.0;          // Example heading

  int32_t result = location_api_->UpdateGnssInfo(gps_data);
  EXPECT_EQ(result, aurora::ErrorCode::kErrorCodeOk)
      << "Failed to update GNSS info in simulation";

  simulator->Start();

  // Sleep for 500ms to wait exit
  LOG_INFO("Sleep 180 Start");
  std::this_thread::sleep_for(std::chrono::milliseconds(2000));
  LOG_INFO("Sleep 180 End");

  simulator->Stop();
  LOG_INFO("simulator Stop End");

  std::this_thread::sleep_for(std::chrono::milliseconds(180));
  LOG_INFO("Sleep 180 End");


}


TEST_F(LocationApiTest, AngleTest) {
  {
    aurora::PointLL a(0,0);
    aurora::PointLL b(1,0);
    aurora::PointLL c(0.5,0.5);
    double angle;
    auto d = c.ProjectWithAngle(a,b, angle);
    LOG_ERROR("1. Angle = {}", angle);

    EXPECT_NEAR(angle, 90.0, 0.001);
  }
  {
    aurora::PointLL a(0,0);
    aurora::PointLL b(1,0);
    aurora::PointLL c(10.5,0);
    double angle;
    auto d = c.ProjectWithAngle(a,b, angle);

    EXPECT_NEAR(angle, 0.0, 0.001);
    LOG_ERROR("2. Angle = {}", angle);
  }

  {
    aurora::PointLL a(0,0);
    aurora::PointLL b(1,0);
    aurora::PointLL c(1.5,0.5);
    double angle;
    auto d = c.ProjectWithAngle(a,b, angle);

    // EXPECT_NEAR(angle, 90.0, 0.001);
    EXPECT_NEAR(angle, 45, 0.001);
    LOG_ERROR("3. Angle = {}", angle);
  }

  {
    aurora::PointLL a(0,0);
    aurora::PointLL b(1,0);
    aurora::PointLL c(-1,1);
    double angle;
    auto d = c.ProjectWithAngle(a,b, angle);

    // EXPECT_NEAR(angle, 90.0, 0.001);
    LOG_ERROR("4. Angle = {}", angle);
  }
  {
    aurora::PointLL a(0,0);
    aurora::PointLL b(1,0);
    aurora::PointLL c(1, 0);
    double angle;
    auto d = c.ProjectWithAngle(a,b, angle);

    // EXPECT_NEAR(angle, 90.0, 0.001);
    LOG_ERROR("5. Angle = {}", angle);
  }

  {
    aurora::PointLL a(0,0);
    aurora::PointLL b(1,0);
    aurora::PointLL c(1, 0.01);
    double angle;
    auto d = c.ProjectWithAngle(a,b, angle);

    // EXPECT_NEAR(angle, 90.0, 0.001);
    LOG_ERROR("6. Angle = {}", angle);
  }

}
