
#include <gtest/gtest.h>

#include <fstream>
#include <iomanip>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include "base/include/logger.h"
#include "base/include/util.h"
#include "data_provider/include/data_provider.h"
#include "data_provider/include/route_data/route_data_def.h"
#include "data_provider/include/route_data/route_tile_reader.h"
#include "location/include/location_def.h"
#include "location/include/location_module.h"
#include "location/src/map_matching_engine.h"
#include "location/src/location_module_impl.h"

#include "location/src/state_container.h"
#include "location/src/state_container_impl.h"
#include "location/src/enhance_route_manager.h"

#include "path/include/path_module.h"

using namespace aurora;
using namespace aurora::loc;

EnhanceRouteManager route_manager;

class MatchListner : public aurora::loc::MapMatchingListener {
    public:
     MatchListner() = default;
     ~MatchListner() override = default;

     aurora::loc::MatchResult last_result_;
   
     void OnMapMatchingResult(const aurora::loc::MatchResult& result) override {

        
       LOG_INFO("Match Result Origin: {},{} speed {}",
        result.origin_pos.lnglat.lng(),
        result.origin_pos.lnglat.lat(),
        result.origin_pos.speed);

       LOG_INFO("Match Result callback: {}.{} ({},{}), heading： {} confidence {} path_link_idx: {} path_id: {} reroute: {} onroad: {} offset: {} ",
                result.road_match_info.tile_id,
                result.road_match_info.link_id,
                result.road_match_info.proj_pos.lng(),
                result.road_match_info.proj_pos.lat(),
                result.car_pos_info.heading,
                result.road_match_info.confidence, 
                result.road_match_info.path_link_idx,
                result.road_match_info.path_id,
                result.reroute ? "true" : "false",
                result.on_road ? "true" : "false",
                result.road_match_info.offset
               );
        if(last_result_.reroute || result.reroute) {
            LOG_ERROR("Same link but offset backward, skip this result ");

        }

        if(last_result_.road_match_info.path_link_idx == result.road_match_info.path_link_idx && 
            last_result_.road_match_info.offset > result.road_match_info.offset
        ) {

            LOG_INFO("Last Result callback: {}.{} ({},{}), heading： {} confidence {} path_link_idx: {} path_id: {} reroute: {} onroad: {} offset: {} ",
                last_result_.road_match_info.tile_id,
                last_result_.road_match_info.link_id,
                last_result_.road_match_info.proj_pos.lng(),
                last_result_.road_match_info.proj_pos.lat(),
                last_result_.car_pos_info.heading,
                last_result_.road_match_info.confidence, 
                last_result_.road_match_info.path_link_idx,
                last_result_.road_match_info.path_id,
                last_result_.reroute ? "true" : "false",
                last_result_.on_road ? "true" : "false",
                last_result_.road_match_info.offset
               );
          LOG_ERROR("Same link but offset backward, skip this result ");
        //   return;
        }

        last_result_ = result;
     }
};


int main(int argc, char** argv) {
    aurora::loc::LocationModule location_module_;

    auto module_finder = [](aurora::ModuleId id) {
        return nullptr; //std::dynamic_pointer_cast<aurora::Interface>(path_module);
      };
    
    location_module_.Prepare("distribution/data/route");
    location_module_.Init(module_finder);
    auto intf = location_module_.GetInterface();
    if (intf->GetModuleId() != aurora::ModuleId::kModuleIdLocation) {
      LOG_ERROR("Failed to get location module interface");
      return 0;
    }

    auto location_api_ = std::dynamic_pointer_cast<aurora::loc::ILocation>(intf);

    std::shared_ptr<MatchListner> listener = std::make_shared<MatchListner>();
    location_api_->AddMapMatchingListener(listener);

    auto sim_cont = location_api_->GetSimulatorController();

    auto sim_config = sim_cont->GetConfig();
    sim_config.play_interval = 0.0001;
    sim_cont->SetConfig(sim_config);
    std::string argv1(argv[1]);
    sim_cont->LoadTrackData(argv1);


    sim_cont->Start();

    // Wait for the simulation to finish
    while(sim_cont->IsFinished() == false) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    sim_cont->Stop();


    auto location_impl =
        std::dynamic_pointer_cast<aurora::loc::LocationModuleImpl>(location_module_.GetInterface());
    if (location_impl) {
      auto map_engine = location_impl->GetMapMatchingEngine();
      map_engine->GetInternalDataDumper().DumpStates("location_debug/internal_states/");
    }

    location_api_->RemoveMapMatchingListener(listener);
    LOG_INFO("Simulation finished, exiting...");
    // Clean up and exit

    return 0;
    // check first argument is 
}