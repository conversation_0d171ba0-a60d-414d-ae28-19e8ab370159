
#include <gtest/gtest.h>

#include <fstream>
#include <iomanip>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include "base/include/logger.h"
#include "base/include/util.h"
#include "data_provider/include/data_provider.h"
#include "data_provider/include/route_data/route_data_def.h"
#include "data_provider/include/route_data/route_tile_reader.h"
#include "location/include/location_def.h"
#include "location/include/location_module.h"
#include "location/src/map_matching_engine.h"
#include "location/src/location_module_impl.h"

#include "location/src/state_container.h"
#include "location/src/state_container_impl.h"
#include "location/src/enhance_route_manager.h"

#include "path/include/path_module.h"
#include <cstdint>

enum SignalType {
    SIGNAL_GPS = 0, 
    SIGNAL_DEMO, 
    SIGNAL_MANUAL,
    SIGNAL_GNSS,
    SIGNAL_UNKNOWN  
};

typedef uint64_t uint64;
typedef uint32_t uint32;
typedef int32_t int32;
typedef int64_t int64;
typedef uint16_t uint16;
typedef int16_t int16;
typedef float float32;

#define INVALID_INT                 (0x7FFFFFFF) 
struct GpsPos {
    int32 gps_lon;	// lon 1/65536 minute
    int32 gps_lat;	// lat 1/65536 minute

    GpsPos() :gps_lon(INVALID_INT), gps_lat(INVALID_INT)
    {}

    GpsPos(uint32 _gps_lon, uint32 _gps_lat) :gps_lon(_gps_lon), gps_lat(_gps_lat)
    {}

    GpsPos(const GpsPos& rhs)
    {
        gps_lon = rhs.gps_lon;
        gps_lat = rhs.gps_lat;
    }

    bool operator == (const GpsPos& rhs)
    {
        return gps_lon == rhs.gps_lon && gps_lat == rhs.gps_lat;
    }

    bool operator != (const GpsPos& rhs)
    {
        return !(*this == rhs);
    }

    GpsPos& operator = (const GpsPos& rhs)
    {
        if (this != &rhs)
        {
            gps_lon = rhs.gps_lon;
            gps_lat = rhs.gps_lat;
        }

        return *this;
    }

    bool IsInValid()
    {
        return (gps_lon == INVALID_INT || gps_lat == INVALID_INT);
    }

};

struct GpsLocInfo {
    uint64 gps_id; // ms
    SignalType signal_type;
    GpsPos gps_pos;
    GpsPos gps_real_pos;
    float32 speed;	// m/s
    uint32 gps_dir;	// east is zero, anticlockwise is positive direction, 360/256/65536 DEG
    float32 pitch; // DEG
    int16 pitch_flag; // -1-unknown, 0-flat, 1-upslope, 2-downslope, 4-under garage
    int16 hdop; // m
    bool fix_ok; // gps signal valid flag
    bool high_precision_flag; // high precision flag
    int64 time; // s
    int16 milli_secend; // ms

    GpsLocInfo() :gps_id(0), signal_type(SIGNAL_UNKNOWN), speed(0.0), gps_dir(0), pitch(0.0), pitch_flag(-1), hdop(0),
        fix_ok(false), high_precision_flag(false), time(0), milli_secend(0)
    {}

    GpsLocInfo(const GpsLocInfo& rhs)
    {
        gps_id = rhs.gps_id;
        signal_type = rhs.signal_type;
        gps_pos = rhs.gps_pos;
        gps_real_pos = rhs.gps_real_pos;
        speed = rhs.speed;
        gps_dir = rhs.gps_dir;
        pitch = rhs.pitch;
        pitch_flag = rhs.pitch_flag;
        hdop = rhs.hdop;
        fix_ok = rhs.fix_ok;
        high_precision_flag = rhs.high_precision_flag;
        time = rhs.time;
        milli_secend = rhs.milli_secend;
    }

    GpsLocInfo& operator = (const GpsLocInfo& rhs)
    {
        if (this != &rhs)
        {
            gps_id = rhs.gps_id;
            signal_type = rhs.signal_type;
            gps_pos = rhs.gps_pos;
            gps_real_pos = rhs.gps_real_pos;
            speed = rhs.speed;
            gps_dir = rhs.gps_dir;
            pitch = rhs.pitch;
            pitch_flag = rhs.pitch_flag;
            hdop = rhs.hdop;
            fix_ok = rhs.fix_ok;
            high_precision_flag = rhs.high_precision_flag;
            time = rhs.time;
            milli_secend = rhs.milli_secend;
        }

        return *this;
    }


    bool IsValid()
    {
        return (SIGNAL_UNKNOWN != signal_type);
    }

};

using namespace aurora;
using namespace aurora::loc;

EnhanceRouteManager route_manager;

class MatchListner : public aurora::loc::MapMatchingListener {
    public:
     MatchListner() = default;
     ~MatchListner() override = default;

     aurora::loc::MatchResult last_result_;
   
     void OnMapMatchingResult(const aurora::loc::MatchResult& result) override {

        
       LOG_INFO("Match Result Origin: {},{} speed {}",
        result.origin_pos.lnglat.lng(),
        result.origin_pos.lnglat.lat(),
        result.origin_pos.speed);

       LOG_INFO("Match Result callback: {}.{} ({},{}), heading： {} confidence {} path_link_idx: {} path_id: {} reroute: {} onroad: {} offset: {} ",
                result.road_match_info.tile_id,
                result.road_match_info.link_id,
                result.road_match_info.proj_pos.lng(),
                result.road_match_info.proj_pos.lat(),
                result.car_pos_info.heading,
                result.road_match_info.confidence, 
                result.road_match_info.path_link_idx,
                result.road_match_info.path_id,
                result.reroute ? "true" : "false",
                result.on_road ? "true" : "false",
                result.road_match_info.offset
               );
        if(last_result_.reroute || result.reroute) {
            LOG_ERROR("Same link but offset backward, skip this result ");

        }

        if(last_result_.road_match_info.path_link_idx == result.road_match_info.path_link_idx && 
            last_result_.road_match_info.offset > result.road_match_info.offset
        ) {

            LOG_INFO("Last Result callback: {}.{} ({},{}), heading： {} confidence {} path_link_idx: {} path_id: {} reroute: {} onroad: {} offset: {} ",
                last_result_.road_match_info.tile_id,
                last_result_.road_match_info.link_id,
                last_result_.road_match_info.proj_pos.lng(),
                last_result_.road_match_info.proj_pos.lat(),
                last_result_.car_pos_info.heading,
                last_result_.road_match_info.confidence, 
                last_result_.road_match_info.path_link_idx,
                last_result_.road_match_info.path_id,
                last_result_.reroute ? "true" : "false",
                last_result_.on_road ? "true" : "false",
                last_result_.road_match_info.offset
               );
          LOG_ERROR("Same link but offset backward, skip this result ");
        //   return;
        }

        last_result_ = result;
     }
};

GnssPosInfo ConvertTestGps(const GpsLocInfo& loc_info) {
    GnssPosInfo pos_info;
    pos_info.timestamp = loc_info.gps_id;
    pos_info.mode = GnssMode::kThreeD;
    pos_info.lnglat.set_lng( (double)(loc_info.gps_pos.gps_lon) / 65536.0 / 60.);
    pos_info.lnglat.set_lat( (double)(loc_info.gps_pos.gps_lat) / 65536.0 / 60.);
    pos_info.speed = loc_info.speed;
    pos_info.heading = 360. - (double)(loc_info.gps_dir) / 256.0 /65536.0 * 360.0;
    pos_info.heading = fmod (pos_info.heading + 360.0 + 90., 360.0); // convert to 0-360, 0 is true north
    pos_info.pitch = loc_info.pitch;
    pos_info.altitude = 0.0; // Altitude is not provided in GpsLocInfo
    pos_info.num_sats = 10; // Assuming 10 satellites visible
    pos_info.num_used = 8; // Assuming 8 satellites used in position fix
    return pos_info;
}



int main(int argc, char* argv[]) {
    // Example usage of the structures
    GpsLocInfo gps_info;

    aurora::loc::LocationModule location_module_;
    std::string output_dir = "location_debug/test_states/";
    std::string gps_data_file;

    if(argc < 2) {
        LOG_ERROR("Usage: {} <gps_data_file> [output_dir]", argv[0]);
        LOG_ERROR("Example: {} map_engine/test/api_test/location/data/loc_trace/test_normal_1_test_20180307161436.dat ./test_normal_1_test_20180307161436_results/ ", argv[0]);

        LOG_ERROR("Convert Usage: {} -c <gps_data_file> <output_trace.csv>", argv[0]);


        // map_engine/test/api_test/location/data/loc_trace/test_normal_1_test_20180307161436.dat
        return 1;
    }

    if(std::string(argv[1]) == "-c" ) {
        if(argc < 4) {
            LOG_ERROR("Convert Usage: {} -c <gps_data_file> <output_trace.csv>", argv[0]);
            return -1;
        }
        gps_data_file = argv[2];

        // std::string output_trace_file = argv[3];
        std::ofstream output_trace_file(argv[3]);
        GpsLocInfo test_gps_loc_info;
        int i = 0;
        FILE* fp = fopen(gps_data_file.c_str(), "rb");
        if (fp == nullptr) {
            return 0;
        }
        output_trace_file << std::fixed << std::setprecision(6)
                          << "timestamp,lng,lat,speed,heading,num_sats,num_used\n";
        while (fread(&test_gps_loc_info, sizeof(test_gps_loc_info), 1, fp)) {
            if(test_gps_loc_info.IsValid()) {
                GnssPosInfo gps = ConvertTestGps(test_gps_loc_info);

                // LOG_INFO("POS[{}]: time:{} mode:{} ll: ({}, {}) speed: {} heading: {} pitch: {} ",i,  gps_pos_info.timestamp,
                //     int(gps_pos_info.mode),
                //     gps_pos_info.lnglat.x(),
                //     gps_pos_info.lnglat.y(),
                //     gps_pos_info.speed ,
                //     gps_pos_info.heading ,
                //     gps_pos_info.pitch);
                output_trace_file << gps.timestamp << "," << gps.lnglat.lng() << "," << gps.lnglat.lat()
                         << "," << gps.speed << "," << gps.heading << "," << gps.num_sats << ","
                         << gps.num_used <<  "," <<
                         test_gps_loc_info.milli_secend <<  "\n";
                          "\n";

            
            }
        }
    

        return 0;

    }
    gps_data_file = argv[1];


    if(argc >= 3) {
        output_dir = argv[2];
    }

    std::filesystem::create_directories(output_dir);

    auto module_finder = [](aurora::ModuleId id) {
        return nullptr; //std::dynamic_pointer_cast<aurora::Interface>(path_module);
      };
    
    location_module_.Prepare("distribution/data/china/route");
    location_module_.Init(module_finder);
    auto intf = location_module_.GetInterface();
    if (intf->GetModuleId() != aurora::ModuleId::kModuleIdLocation) {
      LOG_ERROR("Failed to get location module interface");
      return 0;
    }

    auto location_api_ = std::dynamic_pointer_cast<aurora::loc::ILocation>(intf);

    std::shared_ptr<MatchListner> listener = std::make_shared<MatchListner>();
    location_api_->AddMapMatchingListener(listener);

    FILE* fp = fopen(gps_data_file.c_str(), "rb");
    if (fp == nullptr) {
        return 0;
    }
	
    GpsLocInfo test_gps_loc_info;
    int i = 0;
    while (fread(&test_gps_loc_info, sizeof(test_gps_loc_info), 1, fp)) {
        if(test_gps_loc_info.IsValid()) {
            GnssPosInfo gps_pos_info = ConvertTestGps(test_gps_loc_info);
            LOG_INFO("POS[{}]: time:{} mode:{} ll: ({}, {}) speed: {} heading: {} pitch: {} ",i,  gps_pos_info.timestamp,
                int(gps_pos_info.mode),
                gps_pos_info.lnglat.x(),
                gps_pos_info.lnglat.y(),
                gps_pos_info.speed ,
                gps_pos_info.heading ,
                gps_pos_info.pitch);
            location_api_->UpdateGnssInfo(gps_pos_info);
        }
    }
    fclose(fp);

    // do while 

    auto location_impl =
        std::dynamic_pointer_cast<aurora::loc::LocationModuleImpl>(location_module_.GetInterface());
    if (location_impl) {
      auto map_engine = location_impl->GetMapMatchingEngine();
      map_engine->GetInternalDataDumper().DumpStates(output_dir);
    }

    location_api_->RemoveMapMatchingListener(listener);
    LOG_INFO("Simulation finished, exiting...");

    

    return 0;
}