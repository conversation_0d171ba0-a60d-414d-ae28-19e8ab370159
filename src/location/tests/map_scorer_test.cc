#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "location/src/match_scorer_impl.h"

#include <numeric>

using namespace aurora::loc;
using ::testing::DoubleNear;

class MatchScorerImplTest : public ::testing::Test {
protected:
    MatchScorer scorer;
    
};

TEST_F(MatchScorerImplTest, CalcDistanceScore) {

    EXPECT_THAT(scorer.CalcDistanceScore(0.5, 60.0), DoubleNear(1.0, 1e-2));

    // Test with distance within radius
    EXPECT_THAT(scorer.CalcDistanceScore(5.0, 60.0), DoubleNear(0.993055555, 1e-4));
    // Test with distance equal to radius
    EXPECT_THAT(scorer.CalcDistanceScore(30.0, 60.0), DoubleNear(0.5, 0.01));
    // Test with distance beyond radius
    EXPECT_THAT(scorer.CalcDistanceScore(60.0, 60.0), Double<PERSON>ear(0, 1e-5));
}

TEST_F(MatchScorerImplTest, CalcHeadingScore) {
    // Test small angle difference
    EXPECT_THAT(scorer.CalcHeadingScore(35.0, 35.0), DoubleNear(-0.013888, 1e-5));
    // Test large angle difference
    EXPECT_THAT(scorer.CalcHeadingScore(5.0, 355.0), DoubleNear(-0.5, 1e-5));
    // Test wraparound case
    EXPECT_THAT(scorer.CalcHeadingScore(25.0, 35.0), DoubleNear(-0.013888, 1e-5));
}

TEST_F(MatchScorerImplTest, CalcTranferScore) {
    // Test matching speeds
    EXPECT_THAT(scorer.CalcTranferScore(10.0, 10.0), DoubleNear(-0.5, 1e-5));
    // Test small speed difference
    EXPECT_THAT(scorer.CalcTranferScore(10.0, 11.0), DoubleNear(-0.555571, 1e-5));
    // Test large speed difference
    EXPECT_THAT(scorer.CalcTranferScore(10.0, 15.0), DoubleNear(-1.111571, 1e-5));
}

TEST_F(MatchScorerImplTest, CalcMovementScore) {
    aurora::Vector2d map_move(1.0, 1.0);
    aurora::Vector2d gps_move(1.1, 1.1);
    // Test small movement difference
    EXPECT_THAT(scorer.CalcMovementScore(map_move, gps_move), DoubleNear(-0.555571, 1e-5));
    // Test larger movement difference
    aurora::Vector2d gps_move2(2.0, 2.0);
    EXPECT_THAT(scorer.CalcMovementScore(map_move, gps_move2), DoubleNear(-1.111571, 1e-5));
}

TEST_F(MatchScorerImplTest, SoftmaxScores) {
    std::vector<double> scores = {1.0, 2.0, 3.0};
    auto results = scorer.SoftmaxScores(scores);
    // Should sum to 1.0
    EXPECT_THAT(std::accumulate(results.begin(), results.end(), 0.0), DoubleNear(1.0, 1e-5));
    // Check individual values
    EXPECT_THAT(results[0], DoubleNear(0.090030, 1e-5));
    EXPECT_THAT(results[1], DoubleNear(0.244728, 1e-5));
    EXPECT_THAT(results[2], DoubleNear(0.665240, 1e-5));
}
