#include "gtest/gtest.h"
#include "location/src/state_container_impl.h"
#include "location/src/graph_reader.h"
#include "location/src/graph_reader_impl.h"

#include "base/include/aabb2.h"
#include "base/include/logger.h"

using namespace aurora::loc;



class StateContainerImplTest : public ::testing::Test {
protected:
    void SetUp() override {
        reader_ = std::make_unique<GraphReaderImpl>("distribution/data/route/");
        container_ = std::make_unique<StateContainerImpl>(*reader_);
    }

    void TearDown() override {
        container_->Reset();
    }


    std::unique_ptr<GraphReaderImpl> reader_;
    std::unique_ptr<StateContainerImpl> container_;
};

TEST_F(StateContainerImplTest, InitialState) {
    EXPECT_EQ(container_->GetFrameInfo(0), nullptr);
    EXPECT_EQ(container_->GetOptions().radius, 80.0);
}

TEST_F(StateContainerImplTest, SetAndGetOptions) {
    StateContainer::Options options;
    options.radius = 50.0;
    container_->SetOptions(options);
    
    const auto& retrieved_options = container_->GetOptions();
    EXPECT_EQ(retrieved_options.radius, 50.0);
}

TEST_F(StateContainerImplTest, BuildCandidateStatesWithNoEdges) {
    GnssPosInfo pos_info;
    // 31.087952207027154	121.59017144848544
    // 31.087993289657973	121.59043622367369
    pos_info.lnglat = aurora::PointLL(121.59017144848544, 31.087952207027154);
    StateContainer::Options opts;
    opts.radius = 50;
    container_->SetOptions(opts);
    int32_t frame_id = container_->BuildCandidateStates(pos_info);
    EXPECT_GT(frame_id, 0);
    
    FrameInfo* frame_info = container_->GetFrameInfo(frame_id);
    ASSERT_NE(frame_info, nullptr);
    EXPECT_FALSE(frame_info->state_map.empty());

    for(auto& state_pair: frame_info->state_map) {
        LOG_INFO("StateID: {} ",  state_pair.first);

        LOG_INFO("StateInfo: {}.{} dist: {} heading: {} ",  state_pair.second.edge_id.edge_id.tile_id.value, state_pair.second.edge_id.edge_id.feature_id, state_pair.second.distance, state_pair.second.heading);
        EXPECT_EQ(state_pair.first, state_pair.second.id);

    }
}

TEST_F(StateContainerImplTest, GetFrameInfoForInvalidId) {
    EXPECT_EQ(container_->GetFrameInfo(999), nullptr);
}

TEST_F(StateContainerImplTest, ResetClearsState) {
    GnssPosInfo pos_info;
    pos_info.lnglat = aurora::PointLL(121.59017144848544, 31.087952207027154);
    
    int32_t frame_id = container_->BuildCandidateStates(pos_info);
    EXPECT_GT(frame_id, 0);
    
    container_->Reset();
    EXPECT_EQ(container_->GetFrameInfo(frame_id), nullptr);
    EXPECT_EQ(container_->GetFrameInfo(0), nullptr);
}
