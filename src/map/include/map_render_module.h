/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_service.h
 * @brief Declaration file of class IMapRenderService.
 * @attention used for C/C++ only.
 */

 #ifndef INCLUDE_MAP_RENDER_SERVICE_H_
 #define INCLUDE_MAP_RENDER_SERVICE_H_
 
 #include <cstdint>
 #include <memory>
 #include <string>
 #include <vector>
 #include "point2.h"

 namespace aurora {

 class MapRenderService;

    struct Point64 {
        double x_{0.0};
        double y_{0.0};
    };

    struct AuroraMapConfig {
      std::string data_path_;
      std::string config_path_; // 目前主题配置在外面new出来传递，这个配置暂时用不到
      std::string default_fontPath_;
      std::string default_fontName_;
      std::string default_mark_path_;

      AuroraMapConfig() {}
      AuroraMapConfig(const std::string& data_path, const std::string& config_path, 
        const std::string& default_fontPath, const std::string& default_fontName,
        const std::string& default_mark_path)
      : data_path_(data_path),
        config_path_(config_path),
        default_fontPath_(default_fontPath),
        default_fontName_(default_fontName),
        default_mark_path_(default_mark_path)
      {}

      AuroraMapConfig(const AuroraMapConfig& config)
      : data_path_(config.data_path_),
        config_path_(config.config_path_),
        default_fontPath_(config.default_fontPath_),
        default_fontName_(config.default_fontName_),
        default_mark_path_(config.default_mark_path_)
      {}
    };

 /**
 * class breif description
 *
 * IMapRenderService
 * 
 */
 class IMapRenderService {

 public:        
   IMapRenderService();       
   ~IMapRenderService();
     
    /// @brief Initialize resources
    /// @param dataprovider Data provider
    /// @param themeconfig Theme configuration
    int32_t Init(const AuroraMapConfig& map_config, void* themeconfig);

    /// @brief Set screen size
    /// @param width Screen width
    /// @param height Screen height
    /// @return Returns true on 0, false on other
    int32_t SetScreenSize(uint32_t width, uint32_t height);
 
    /// @brief Destroy resources before rendering exits
    void Destory();
 
    /// @brief Set map center
    /// @param lon Longitude
    /// @param lat Latitude
    int32_t SetMapCenter(double lon, double lat, uint32_t animation_duration_ms = 0);
 
    /// @brief Get map center
    /// @return Map center coordinates
    Point64 GetMapCenter();
     
    /// @brief Set map scale
    /// @param scale Map scale
    /// @param animation_duration_ms Animation duration in milliseconds
    int32_t SetMapScale(double scale, uint32_t animation_duration_ms = 0);
 
    /// @brief Render map
    void RenderMap();
 
    /// @brief Set map rotation
    /// @param rot Rotation angle in degrees
    /// @param animation_duration_ms Animation duration in milliseconds
    int32_t SetMapRotation(float rot, uint32_t animation_duration_ms = 0);
 
    /// @brief Convert screen coordinates to world coordinates
    /// @param pt Screen coordinates
    /// @return World coordinates
    Point64 ScreenToMap(const Point64& pt);
 
    /// @brief Convert world coordinates to screen coordinates
    /// @param geo World coordinates
    /// @return Screen coordinates
    Point64 MapToScreen(const Point64& geo);

    /// @brief Move map
    /// @param delta_x X-axis offset
    /// @param delta_y Y-axis offset
    /// @param move_end Whether the move is the end
    /// @param animation_duration_ms Animation duration in milliseconds
    void MoveMap(double delta_x, double delta_y, bool move_end = false, uint32_t animation_duration_ms = 0);   

     float GetMapScale(); 


     void SetMapPitch(float pitch, uint32_t animation_duration_ms = 0);

     float GetMapPitch();

     //  默认flyto最少5s动画
     void FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms = 5000);

     void SetPath(uint32_t type, std::vector<Point2d>& path);

    void ClearPath(uint32_t type);

    // 返回 >= 0 MarkId, < 0 失败
    int32_t  SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name);

    // 更新对应markid图片，经纬度位置保持不变
    void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name);

    // 更新对应markid的位置和角度，图片和anchor保持不变
    void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree = 0.f );

    // mark_id < 0 清除所有mark_type
    void ClearMark(uint16_t mark_type, int32_t mark_id = -1);

 private:
   std::unique_ptr<MapRenderService> render_service_;
 };
     
 } //namespace 
 
 #endif // INCLUDE_MAP_RENDER_SERVICE_H_
 /* EOF */