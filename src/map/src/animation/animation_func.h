#ifndef MAP_RENDER_ANIMATION_ANIMATION_FUNC_H
#define MAP_RENDER_ANIMATION_ANIMATION_FUNC_H
#include <cstdint>
#include <functional>
#include <math.h>

#include "constants.h"

namespace aurora {
namespace func {
using AnimationFunc = std::function<double(double, double, double)>;

const AnimationFunc linear_func = [](double start, double end, double progress) -> double {
  return start + progress * (end - start);
};

const AnimationFunc damped_oscillation_func = [](double offset, double end,
                                                 double progress) -> double {
  double y = std::sin(4 * 2 * kPiD * progress) * std::pow(2, -progress * 3.25);
  return y * offset;
};

}  // namespace func

}  // namespace aurora
#endif  // MAP_RENDER_ANIMATION_ANIMATION_FUNC_H
