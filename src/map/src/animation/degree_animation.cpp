#include "degree_animation.h"

namespace aurora {

DegreeAnimation::DegreeAnimation(uint32_t durition_ms, float offset, float end, bool auto_start)
    : AnimationBase(kAnimationTypeDegree, Milliseconds(durition_ms), auto_start),
      offset_(offset),
      end_(end) {}

void DegreeAnimation::SetFunction(func::AnimationFunc func) { func_ = func; }

float DegreeAnimation::GetDegree(const TimePoint& current_time) {
  double progress = GetProcess(current_time);
  if (func_) {
    return func_(offset_, end_, progress);
  }
  return func::linear_func(offset_, end_, progress);
}

}  // namespace aurora
