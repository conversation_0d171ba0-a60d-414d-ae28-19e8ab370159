#ifndef MAP_RENDER_ANIMATION_DEGREE_ANIMATION_H
#define MAP_RENDER_ANIMATION_DEGREE_ANIMATION_H
#include <cstdint>

#include "animation_base.h"
#include "animation_func.h"

namespace aurora {

class DegreeAnimation : public AnimationBase {
public:
  DegreeAnimation(uint32_t durition_ms, float offset, float end, bool auto_start = true);

  void SetFunction(func::AnimationFunc func);

  float GetDegree(const TimePoint& current_time = Clock::now());

private:
  float offset_;
  float end_;
  func::AnimationFunc func_;
};

}  // namespace aurora
#endif  // MAP_RENDER_ANIMATION_DEGREE_ANIMATION_H
