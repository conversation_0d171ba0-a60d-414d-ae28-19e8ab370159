#include "collision/collision_mark.h"

namespace aurora {

CollisionMark::CollisionMark()
    : CollisionFeature(kMarkData), image_holder_(nullptr), rect_(), degree_(0.f), alpha_(1.f) {}

void CollisionMark::SetImageHolder(ImageHolderPtr image_holder) { image_holder_ = image_holder; }

ImageHolderPtr CollisionMark::GetImageHolder() { return image_holder_; }

void CollisionMark::SetSrcRect(MarkRect rect) { rect_ = rect; }

const MarkRect& CollisionMark::GetSrcRect() { return rect_; }

void CollisionMark::SetDegree(float degree) { degree_ = degree; }

void CollisionMark::SetDegreeAnimation(uint32_t durition_ms, float offset, float end) {
  degree_animation_.emplace(durition_ms, offset, end);
  degree_animation_->SetFunction(func::damped_oscillation_func);
  degree_ = end;
}

float CollisionMark::GetDegree() {
  if (degree_animation_) {
    float degree = degree_animation_->GetDegree();
    if (degree_animation_->NeedNextFrameDraw()) {
      DrawNextFrame();
    }
    return degree;
  }
  return degree_;
}

void CollisionMark::SetAlphaAnimation(uint32_t durition_ms, float s, float e) {
  alpha_animation_.emplace(durition_ms, s, e);
  alpha_ = e;
}

float CollisionMark::GetAlpha() {
  if (alpha_animation_) {
    float alpha = alpha_animation_->GetAlpha();
    if (alpha_animation_->NeedNextFrameDraw()) {
      DrawNextFrame();
    }
    return alpha;
  } else {
    return alpha_;
  }
}

void CollisionMark::SetSizeAnimation(uint32_t durition_ms, double sw, double sh, double ew,
                                     double eh) {
  size_animation_.emplace(durition_ms, sw, sh, ew, eh);
}

BoundingBox CollisionMark::GetDstRect() {
  if (size_animation_) {
    BoundingBox box = size_animation_->GetBox(GetAnchorType(), GetScreenX(), GetScreenY(),
                                              GetAnchorX(), GetAnchorY());
    if (size_animation_->NeedNextFrameDraw()) {
      DrawNextFrame();
    }
    return box;
  } else {
    return GetBox();
  }
}

void CollisionMark::RestartAnimation() {
  if (alpha_animation_) {
    alpha_animation_->Restart();
  }
  if (size_animation_) {
    size_animation_->Restart();
  }
  if (degree_animation_) {
    degree_animation_->Restart();
  }
}
}  // namespace aurora
