#ifndef MAP_DATA_COLLISION_COLLISION_MARK_H
#define MAP_DATA_COLLISION_COLLISION_MARK_H
#include <memory>

#include "alpha_animation.h"
#include "collision/collision_feature.h"
#include "collision/collision_text.h"
#include "mark/mark_def.h"
#include "size_animation.h"
#include "degree_animation.h"

namespace aurora {
class CollisionMark : public CollisionFeature {
public:
  CollisionMark();
  void SetImageHolder(ImageHolderPtr image_holder);
  ImageHolderPtr GetImageHolder();
  void SetSrcRect(MarkRect rect);
  const MarkRect& GetSrcRect();
  void SetDegree(float degree);
  void SetDegreeAnimation(uint32_t durition_ms, float offset, float end);
  float GetDegree();
  void SetAlphaAnimation(uint32_t durition_ms, float s, float e);
  float GetAlpha();
  void SetSizeAnimation(uint32_t durition_ms, double sw, double sh, double ew, double eh);
  BoundingBox GetDstRect();
  void RestartAnimation();

private:
  ImageHolderPtr image_holder_;
  MarkRect rect_;
  float degree_;
  float alpha_;
  optional<AlphaAnimation> alpha_animation_;
  optional<SizeAnimation> size_animation_;
  optional<DegreeAnimation> degree_animation_;
};
using CollisionMarkPtr = std::shared_ptr<CollisionMark>;
}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_MARK_H
