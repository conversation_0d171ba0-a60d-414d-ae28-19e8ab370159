/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file data_manager_interface.h
 * @brief Declaration file of class IMapRenderDataManager.
 * @attention used for C/C++ only.
 */

#ifndef MAP_SRC_DATA_RENDERDATAMANAGER_INTERFACE_H
#define MAP_SRC_DATA_RENDERDATAMANAGER_INTERFACE_H
#include <vector>
#include <memory>
#include "aabb2.h"

namespace aurora {
    class MapDataProvider;
    using MapDataProviderPtr = std::shared_ptr<MapDataProvider>;
    struct TileID;
    struct CacheTile;

    class MapRenderThemeIF;
    class MapRenderCameraIF;
    using CameraPtr = std::shared_ptr<MapRenderCameraIF>;

/**
* class breif description
*
* IMapRenderDataManager
*
*/
   
class IMapRenderDataManager {
public:
    IMapRenderDataManager() {};              
    virtual ~IMapRenderDataManager() {};   

    virtual bool Init() = 0;

    virtual void AddDataProvider(MapDataProviderPtr provider) = 0;

    virtual void SetRenderTheme(MapRenderThemeIF* theme) = 0;
    virtual MapRenderThemeIF* GetRenderTheme() const = 0;

    virtual void RemoveDataProvider() = 0;

    virtual void SetCacheStrategy() = 0;

    virtual void GetRenderTileData(CameraPtr camera, std::vector<TileID>& tile_ids, 
                                  std::vector<std::shared_ptr<CacheTile>>& cached_tile_datas,
                                  const AABB2<Point2d>& predict_boundingbox = {{0.0,0.0}, {0.0,0.0}}) = 0;

    virtual bool GetRenderTileDataById(const TileID& id, std::shared_ptr<CacheTile>& tile_data) = 0;

    
   /// @brief  将tile数据加入缓存中
   /// @param id 数据所在的tile id
   virtual void AddRenderTileCache(const TileID& id, const std::shared_ptr<CacheTile>& tile_data, bool add_to_active = true) noexcept = 0;

    virtual void UpdateProviderWithCamera(CameraPtr camera, uint32_t screen_width, uint32_t screen_height) = 0;

};
    
} //namespace 

#endif //MAP_SRC_DATA_RENDERDATAMANAGER_INTERFACE_H
/* EOF */
