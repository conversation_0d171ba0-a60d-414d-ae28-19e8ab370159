/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "map_render_data_manager.h"
#include "map_render_data_fetcher.h"
#include "map_render_backend.h"
// #include "render_line_layer.h"
#include "data_provider.h"
#include <fstream>
#include "display_data/display_tile_reader.h"
#include "display_data/display_data_def.h"
#include "render_layer_builder.h"
#include "map_data_provider.h"
#include "map_render_camera.h"
#include "scale_converter.h"
constexpr int kMaxTileThreadCount = 4;
constexpr int kMaxTileFetchThreshold = 500; // 4k屏一次可以请求:（4000/256 + 2） * （2000/256 + 2）个瓦片


static std::atomic<uint32_t> g_latest_taskid{0}; // 保存最新的 taskid

#if 1
namespace aurora {
    static std::unordered_map<TileID, int32_t, TileIDHashFunc> g_empty_tile_map;
    static std::recursive_mutex g_empty_tile_map_mutex;
    // namespace parser {
    class Treceiver : public MapTileDataReceiver {
    public:
        void OnTileData(uint32_t track_id, parser::DisplayTilePackagePtr data_ptr, uint32_t req_logic_level, const std::shared_ptr<RenderDataFetcher>&  data_cacher, CameraPtr camera) override;
        void OnAllTileData(uint32_t track_id, std::vector<parser::DisplayTilePackagePtr>& tiles, uint32_t req_logic_level, const std::shared_ptr<RenderDataFetcher>&  data_cacher, CameraPtr camera);
        void OnNullTiles(uint32_t track_id, std::vector<parser::DisplayTileID>& tiles, uint32_t req_logic_level);

        aurora::MapRenderThemeIF* theme_{nullptr};
    };

    void Treceiver::OnTileData(uint32_t track_id, parser::DisplayTilePackagePtr data_ptr, uint32_t req_logic_level, 
                                const std::shared_ptr<RenderDataFetcher>&  data_cacher, CameraPtr camera) {
        // if (track_id != g_latest_taskid.load()) {
        //     return; // 不是最新的taskid，不处理
        // }
        if (data_ptr != nullptr) {
            parser::DisplayTileReader reader;
            reader.SetTarget(data_ptr);
            // 一个数据层可能对应多个逻辑层
            auto data_level = GetDataLevel(reader.GetTileID().level);
            std::vector<int32_t> logic_levels = GetLogicLevels(data_level);
            if (logic_levels.empty()) {
                TileID cached_tile_id(reader.GetTileID().row, reader.GetTileID().col, req_logic_level);
                
                if (data_cacher && data_cacher->IsRenderTileDataCached(cached_tile_id)) { // 已经通知过且缓存过做好的数据了
                    printf(" 1tile %d %d %d already cached \n", cached_tile_id.x_, cached_tile_id.y_
                            , cached_tile_id.z_);
                    return;
                }
                aurora::RenderLayerBuilder layerbuilder;
                layerbuilder.BuildRenderLayer(reader, theme_, req_logic_level);
            }
            else  {
                #ifdef DEBUG
                printf("tick:%d tile %d %d %d data received\n",
                     std::chrono::duration_cast<std::chrono::milliseconds>(Clock::now().time_since_epoch()).count(),
                     reader.GetTileID().row, reader.GetTileID().col, reader.GetTileID().level);
                #endif
                for (auto logic_level : logic_levels) {
                    // 缓存request_logic和camera的趋势level层，如果趋势层和camera的level层一致，则最多缓存上下两层
                    auto trend_level = req_logic_level;
                    auto trend_more = req_logic_level;
                    if (camera) {
                        trend_level = camera->GetMapScale(Clock::now() + std::chrono::milliseconds(200));
                        trend_more = camera->GetMapScale(Clock::now() + std::chrono::milliseconds(1000));
                    }
                    bool up_down = false;
                    if (trend_level == logic_level) {
                        up_down = true;
                    }
                    if (logic_level == trend_level || logic_level == trend_more || logic_level == req_logic_level  || (up_down && (logic_level == trend_level + 1 || logic_level == trend_level - 1)))
                    {
                        // 缓存request_logic和camera的趋势level层，如果趋势层和camera的level层一致，则最多缓存上下两层
                        TileID cached_tile_id(reader.GetTileID().row, reader.GetTileID().col, logic_level);
                        if (data_cacher && data_cacher->IsRenderTileDataCached(cached_tile_id)) { // 已经通知过且缓存过做好的数据了
                            printf(" tile %d %d %d already cached \n", cached_tile_id.x_, cached_tile_id.y_
                                , cached_tile_id.z_);
                            continue;
                        }
                        aurora::RenderLayerBuilder layerbuilder;
                        layerbuilder.BuildRenderLayer(reader, theme_, logic_level); // 这样相同数据层次请求一次就可以了
                    }
                    
                }
            }
            
        }
    }

    void Treceiver::OnAllTileData(uint32_t track_id, std::vector<parser::DisplayTilePackagePtr>& tiles, uint32_t req_logic_level, 
                                const std::shared_ptr<RenderDataFetcher>&  data_cacher, CameraPtr camera) {
        if (track_id != g_latest_taskid.load()) {
            return; // 不是最新的taskid，不处理
        }
        // 所有tile数据都收到了，通知制作text
    }

    void Treceiver::OnNullTiles(uint32_t track_id, std::vector<parser::DisplayTileID>& tiles, uint32_t req_logic_level) {
        // 不存在的tile放到map里g_empty_tile_map
        for (auto& id : tiles) {
            TileID tile_id(id.row, id.col, req_logic_level);
            std::lock_guard<std::recursive_mutex> lock(g_empty_tile_map_mutex);
            g_empty_tile_map[tile_id] = 1;
        }
    }
    // } // namespace parser
} // namespace aurora
#endif 

namespace aurora {

    void ThreadPoolInit(size_t idx) {
        // 初始化线程池
        // 可以在这里执行一些线程初始化操作
        printf("ThreadPoolInit idx:%d\n", idx);
        // 设置线程名字
        char name[16];
        snprintf(name, sizeof(name), "maptilepool%d", idx);
        pthread_setname_np(pthread_self(), name);
    }

    MapRenderDataManager::MapRenderDataManager() 
        : thread_pool_(kMaxTileThreadCount, ThreadPoolInit) {
        data_fetcher_ = std::make_shared<RenderDataFetcher>();
    }

    MapRenderDataManager::~MapRenderDataManager() {
    }

    bool MapRenderDataManager::Init() {
        if (data_fetcher_ == nullptr) {
            data_fetcher_ = std::make_shared<RenderDataFetcher>();
        }
        if (data_fetcher_ && !data_fetcher_->Init()) {
            return false;
        }
        return true;
    }

    void MapRenderDataManager::AddDataProvider(MapDataProviderPtr provider) {
        data_provider_ = provider;
        if (data_provider_ == nullptr) {
            return;
        }
        MapTileDataReceiverPtr receiver = std::make_shared<Treceiver>();
        // temp code
        if (theme_ != nullptr) {
            ((Treceiver*)receiver.get())->theme_ = theme_;
        }
        data_provider_->AddDataReceiver(receiver);
    }

    void MapRenderDataManager::SetRenderTheme(MapRenderThemeIF* theme) {
        theme_ = theme;
    }

    void MapRenderDataManager::RemoveDataProvider() {
    }

    void MapRenderDataManager::SetCacheStrategy() {
    }

    void MapRenderDataManager::DisplayTileIdToTileID(const parser::DisplayTileID& display_id, TileID& id) {
        TileID convert_id(display_id.row, display_id.col, display_id.level);
        id = convert_id;
    }

    void MapRenderDataManager::TileIDToDisplayTileId(const TileID& id, parser::DisplayTileID& display_id) {
        display_id.row = id.x_;
        display_id.col = id.y_;
        display_id.level = id.z_;
    }

    /// @brief  将tile数据加入缓存中
    /// @param id 数据所在的tile id
    void MapRenderDataManager::AddRenderTileCache(const TileID& id, const std::shared_ptr<CacheTile>& tile_data, bool add_to_active) noexcept {
        if (data_fetcher_ && tile_data) {
            data_fetcher_->CacheRenderTileData(id, tile_data, add_to_active);
        }
        if (pre_req_tiles_.find(id) != pre_req_tiles_.end()) {
            pre_req_tiles_.erase(id);
        }
    }

    // 辅助函数：提取公共的 "获取未缓存tile并收集" 逻辑
    std::vector<TileID> MapRenderDataManager::FetchAndCollectNotCachedTiles(const std::vector<parser::DisplayTileID>& tiles, 
                                                            uint32_t target_level, 
                                                            std::vector<parser::DisplayTileID>& not_cached_tile_ids,
                                                            std::vector<std::shared_ptr<CacheTile>>& cached_tile_datas) {
        if (tiles.empty()) return std::vector<TileID>();
        
        std::vector<TileID> tile_ids;
        for (auto& tile : tiles) {
            TileID id;
            DisplayTileIdToTileID(tile, id);
            if (id.z_ != target_level) { // 调整为目标层级
                id.z_ = target_level;
            }
            // 对于已经返回了null的tile，不加到renderlist里
            std::lock_guard<std::recursive_mutex> lock(g_empty_tile_map_mutex);
            
            if (g_empty_tile_map.find(id) == g_empty_tile_map.end()) {
                // printf("fetch tile %d %d %d \n", id.x_, id.y_, id.z_);
                tile_ids.push_back(id);
            }
        }
        
        if (!tile_ids.empty()) {
            std::vector<TileID> not_cached_alt;
            data_fetcher_->FetchRenderTileData(tile_ids, not_cached_alt, cached_tile_datas);
            
            auto display_tile_level = target_level;
            if (!tiles.empty()) {
                display_tile_level = tiles.front().level;
            }
            for (auto& tile : not_cached_alt) {
                if (pre_req_tiles_.find(tile) == pre_req_tiles_.end() ) {
                    parser::DisplayTileID display_id;
                    TileIDToDisplayTileId(tile, display_id);
                    if (tile.z_ != display_tile_level) { 
                        display_id.level = display_tile_level;
                    }
                    not_cached_tile_ids.push_back(display_id);
                    #ifdef DEBUG
                    printf("should fetch not cached tile:%d %d %d\n", tile.x_, tile.y_, tile.z_);
                    #endif
                }
            }
           if (not_cached_tile_ids.empty() && cached_tile_datas.empty()) {
                not_cached_tile_ids = tiles;
           }
        }
        return tile_ids;
    }
    
    // 原 GetDestLevelNotCachedTileIds 简化后
    void MapRenderDataManager::GetDestLevelNotCachedTileIds(const uint32_t cur_logic_levle, const uint32_t dest_logic_levle, const AABB2<Point2d>& mbr, 
                                                            std::vector<parser::DisplayTileID>& not_cached_tile_ids) {
        if (data_provider_ == nullptr || cur_logic_levle == dest_logic_levle) {
            return;
        }
        
        Point2d center = mbr.Center();
        double height = mbr.Height();
        double width = mbr.Width();
        aurora::parser::DisplayTileIDSet tiles_dest;
        AABB2<Point2d> mbr_dest;
        
        // 计算目标 mbr（保持原有逻辑）
        if (cur_logic_levle > dest_logic_levle) {
            auto scale_zoom_out = std::pow(2, cur_logic_levle - dest_logic_levle);
            mbr_dest = {center.x() - width * 3 / 4.0 * scale_zoom_out, center.y() - height * 3 / 4.0 * scale_zoom_out, center.x() + width * 3 / 4.0 * scale_zoom_out, center.y() + height * 3 / 4.0 * scale_zoom_out};
        } else { 
            auto scale_zoom_in = std::pow(2, cur_logic_levle - dest_logic_levle);
            mbr_dest = {center.x() - width / 4 * scale_zoom_in, center.y() - height / 4 * scale_zoom_in, center.x() + width / 4 * scale_zoom_in, center.y() + height / 4 * scale_zoom_in};
        }
        
        #ifdef DEBUG
        printf("GetDestLevelNotCachedTileIds cur_logic_levle:%d dest_logic_levle:%d mbr_dest: %f, %f, %f, %f\n", 
            cur_logic_levle, dest_logic_levle,
               mbr_dest.minpt().x(), mbr_dest.minpt().y(), mbr_dest.maxpt().x(), mbr_dest.maxpt().y());
        #endif
        
        data_provider_->GetDisplayTileIDs(dest_logic_levle, mbr_dest, tiles_dest);
        std::vector<std::shared_ptr<CacheTile>> cached_tile_datas;
        FetchAndCollectNotCachedTiles(tiles_dest, dest_logic_levle, not_cached_tile_ids, cached_tile_datas); // 调用辅助函数
    }
    
    // 原 GetDestRollNotCachedTileIds 简化后
    void MapRenderDataManager::GetDestRollNotCachedTileIds(const double dest_logic_levle, const double dest_roll_angle, const AABB2<Point2d>& mbr, 
                                                            std::vector<parser::DisplayTileID>& not_cached_tile_ids ) {
        if (data_provider_ == nullptr) {
            return;
        }
        
        // 计算旋转后的 mbr（保持原有逻辑）, 旋转角度为 dest_roll_angle, 暂时暴力点直接算半径
        double radius = sqrt(mbr.Width() * mbr.Width() + mbr.Height() * mbr.Height()) / 2.0;
        double center_lon = mbr.Center().x();
        double center_lat = mbr.Center().y();
        AABB2<Point2d> new_mbr({center_lon - radius, center_lat - radius, center_lon + radius, center_lat + radius});
        
        aurora::parser::DisplayTileIDSet tiles_dest;
        data_provider_->GetDisplayTileIDs(dest_logic_levle, new_mbr, tiles_dest); 
        std::vector<std::shared_ptr<CacheTile>> cached_tile_datas;
        FetchAndCollectNotCachedTiles(tiles_dest, dest_logic_levle, not_cached_tile_ids, cached_tile_datas); // 调用辅助函数
    }

    void MapRenderDataManager::GetDestPitchNotCachedTileIds( const double dest_logic_levle, const double cur_pitch_angle, const double dest_pitch_angle,  AABB2<Point2d>& mbr, 
                                        std::vector<parser::DisplayTileID>& not_cached_tile_ids )
    {
        if (data_provider_ == nullptr) {
            return;
        }
        // pitch 角度越大，看的范围应该越广，将原有的mbr/cos(dest_pitch)
        double pitch_factor = cos((dest_pitch_angle - cur_pitch_angle)* M_PI / 180.0);
        double half_height = fabs(mbr.Height() / (2.0 *  (pitch_factor + 1e-6)) ); // 看的height要变大, 防止除0
        double half_width = mbr.Width() / 2.0; //宽度不会变
        double center_lon = mbr.Center().x();
        double center_lat = mbr.Center().y();
        AABB2<Point2d> new_mbr({center_lon - half_width, center_lat - half_height, center_lon + half_width, center_lat + half_height});
        mbr = new_mbr;
        aurora::parser::DisplayTileIDSet tiles_dest;
        data_provider_->GetDisplayTileIDs(dest_logic_levle, new_mbr, tiles_dest); // 
        printf(" new mbr: %f %f - %f %f\n", new_mbr.minpt().x(), new_mbr.minpt().y(), new_mbr.maxpt().x(), new_mbr.maxpt().y());
        // for(auto tile : tiles_dest) {
        //     printf("------------------GetDestPitchNotCachedTileIds %d %d %d \n", tile.row, tile.col, tile.level);
        // }
        std::vector<std::shared_ptr<CacheTile>> cached_tile_datas;
        FetchAndCollectNotCachedTiles(tiles_dest, dest_logic_levle, not_cached_tile_ids, cached_tile_datas); // 调用辅助函数

    }

    void MapRenderDataManager::GetUpDownLevelNotCachedTileIds(const uint32_t cur_logic_levle, const AABB2<Point2d>& mbr, 
                                                             std::vector<parser::DisplayTileID>& not_cached_tile_ids ) {
        if (data_provider_ == nullptr) {
            return;
        }
        Point2d center = mbr.Center();
        double height = mbr.Height();
        double width = mbr.Width();
        aurora::parser::DisplayTileIDSet tiles_up;
        aurora::parser::DisplayTileIDSet tiles_down;
        if (cur_logic_levle > MIN_VALID_TILE_LOGIC_LEVEL) {  //最小层级是1，不是0 
            // 小一级同样的中心点，请求的mbr应该是放大的, 放大的mbr是当前mbr的2倍
            AABB2<Point2d> mbr_up = {center.x() - width, center.y() - height, center.x() + width, center.y() + height};
            data_provider_->GetDisplayTileIDs(cur_logic_levle - 1, mbr_up, tiles_down);
        }
        if (cur_logic_levle < MAX_VALID_TILE_LOGIC_LEVEL) { // 最大层级是19，不是20
            // 大一级同样的中心点，请求的mbr应该是缩小的
            AABB2<Point2d> mbr_down = {center.x() - width / 4, center.y() - height / 4, center.x() + width / 4, center.y() + height / 4};
            data_provider_->GetDisplayTileIDs(cur_logic_levle + 1, mbr_down, tiles_up);
        }
        // 合并两个集合
        std::vector<TileID> tile_ids;
        for (auto& tile : tiles_up) {
            TileID id;
            DisplayTileIdToTileID(tile, id);
            if (id.z_ != cur_logic_levle + 1) { // 请求缓存数据的时候用实际的camera比例尺去请求
                id.z_ = cur_logic_levle + 1;
            }
            tile_ids.push_back(id);
        }
        if (!tile_ids.empty()) {
            std::vector<TileID> not_cached_tile_ids_alt;
            std::vector<std::shared_ptr<CacheTile>> cached_tile_datas;
            data_fetcher_->FetchRenderTileData(tile_ids, not_cached_tile_ids_alt, cached_tile_datas);
            #ifdef DEUBG
            printf("GetDisplayTileIDs not_cached_tile_ids_alt size %d\n", not_cached_tile_ids_alt.size());
            #endif
            // 未缓存的tileid，去请求
            auto display_tile_level = cur_logic_levle + 1;
            if (tiles_up.size() > 0) {
                display_tile_level = tiles_up.front().level;
            }
            for (auto tile : not_cached_tile_ids_alt) {
                parser::DisplayTileID display_id;
                TileIDToDisplayTileId(tile, display_id);
                // 请求缓存数据的时候用实际的camera比例尺去请求，请求数据的时候用DisplayTileID
                if (tile.z_ != display_tile_level) { 
                    display_id.level = display_tile_level;
                }
                #ifdef DEUBG
                printf("GetDisplayTileIDs request up tileid: %d, %d, %d display_id :%ld\n", tile.x_, tile.y_, tile.z_, display_id.value);
                #endif
                not_cached_tile_ids.push_back(display_id);
            }
        }
        tile_ids.clear();
        for (auto& tile : tiles_down) {
            TileID id;
            DisplayTileIdToTileID(tile, id);
            if (id.z_ != cur_logic_levle - 1) { // 请求缓存数据的时候用实际的camera比例尺去请求
                id.z_ = cur_logic_levle - 1;
            }
            tile_ids.push_back(id);
        }
        if (!tile_ids.empty()) {
            std::vector<TileID> not_cached_tile_ids_alt;
            std::vector<std::shared_ptr<CacheTile>> cached_tile_datas;
            data_fetcher_->FetchRenderTileData(tile_ids, not_cached_tile_ids_alt, cached_tile_datas);
            #ifdef DEUBG
            printf("GetDisplayTileIDs not_cached_tile_ids_alt size %d\n", not_cached_tile_ids_alt.size());
            #endif
            // 未缓存的tileid，去请求
            auto display_tile_level = cur_logic_levle - 1;
            if (tiles_down.size() > 0) {
                display_tile_level = tiles_down.front().level;
            }
            for (auto tile : not_cached_tile_ids_alt) {
                #ifdef DEUBG
                printf("request down tileid: %d, %d, %d\n", tile.x_, tile.y_, tile.z_);
                #endif
                parser::DisplayTileID display_id;
                TileIDToDisplayTileId(tile, display_id);
                // 请求缓存数据的时候用实际的camera比例尺去请求，请求数据的时候用DisplayTileID
                if (tile.z_ != display_tile_level) { 
                    display_id.level = display_tile_level;
                }
                not_cached_tile_ids.push_back(display_id);
            }
        }
    }

    void MapRenderDataManager::GetRenderTileData(CameraPtr camera, std::vector<TileID>& tile_ids,
                                                 std::vector<std::shared_ptr<CacheTile>>& cached_tile_datas ,
                                                 const AABB2<Point2d>& predict_boundingbox) {
        // 1. get tile data from cache
        auto now = Clock::now();
        auto cur_scale = camera->GetMapScale(now);
        int base_scale = static_cast<int>(std::floor(cur_scale));
        // if (cur_scale - base_scale > 0.5) {
        //     base_scale += 1;
        // }
        // int next_scale = base_scale + 1;
        auto cur_roll_angle = camera->GetMapRotation(now);
        auto cur_pitch_angle = camera->GetMapPitchAngle(now);
        auto cur_logic_levle = camera->GetMapScale(now + std::chrono::milliseconds(500)); // 动画状态下实际要跳转的比例尺
        if (data_fetcher_ && camera && data_provider_) {
            if (cur_scale == 0) {
                return;
            }
            tile_ids.clear();
            cached_tile_datas.clear();
            // 获取当前camera对应中心，size框选的tilelist
            // 1.1 获取当前camera对应中心，size扩1.5倍的tilelist
            uint32_t view_width = 0;
            uint32_t view_height = 0;
            camera->GetViewSize(view_width, view_height);
            uint32_t effective_view_height = camera->GetEffectiveHeight();
            double left_b_lon = 0.0;
            double left_b_lat = 0.0;
            double extra_offset_x = 200; // view_width /2.0有点过于大
            double extra_offset_y = 200; // view_height /2.0有点过于大
            double extra_offset_y_b = extra_offset_y;
            if (view_height > effective_view_height) {
                // 天空盒子生效时使用小偏移量（避免加载被天空覆盖的tile）
                extra_offset_x = 20.0;  // 天空盒子区域仅预加载20像素范围
                extra_offset_y = 20.0;
                extra_offset_y_b = 0.0f;
            } else if(fabs(cur_pitch_angle) > kEpsilon){ // 不带俯仰角的时候，加载周边+200pixel范围
                // 常规情况使用原动态偏移逻辑（保留之前的百分比+最小限制）
                extra_offset_x = std::max(view_width * 0.1, 50.0);
                // 当pitch为负（向下看）时，减少下方偏移量
                if (cur_pitch_angle < 0) {
                    extra_offset_y = std::max(effective_view_height * 0.05, 20.0); // 调整为更小的偏移比例（原0.1→0.05）
                } else {
                    extra_offset_y = std::max(effective_view_height * 0.1, 50.0);
                }
                extra_offset_y_b = 0.0f;
            }
            camera->Screen2World(0 -  extra_offset_x, 0 - extra_offset_y_b, left_b_lon, left_b_lat);
            double right_t_lon = 0.0;
            double right_t_lat = 0.0;
            
            auto ret = camera->Screen2World(view_width + extra_offset_x, effective_view_height + extra_offset_y, right_t_lon, right_t_lat);
            
            if(fabs(cur_roll_angle) > kEpsilon || fabs(cur_pitch_angle) > kEpsilon) {
                // 旋转角度不为0，需要根据旋转角度计算旋转后的mbr
                // 取4个顶点的坐标组合mbr
                double left_t_lon = 0.0;
                double left_t_lat = 0.0;
                camera->Screen2World(0 - extra_offset_x, effective_view_height + extra_offset_y, left_t_lon, left_t_lat);
                double right_b_lon = 0.0;
                double right_b_lat = 0.0;
                
                camera->Screen2World(view_width + extra_offset_x, 0 - extra_offset_y_b, right_b_lon, right_b_lat);
                #ifdef DEBUG
                printf("cur_pitch_angle: %f left_b %f %f, right_t %f %f, left_t %f %f, right_b %f %f ret:%d\n", 
                    cur_pitch_angle, left_b_lon, left_b_lat, right_t_lon, right_t_lat, left_t_lon, left_t_lat, right_b_lon, right_b_lat, ret);
                #endif
                // left_b_lon是4个lon里最小的lon
                auto min_left_b_lon = std::min({left_b_lon, right_t_lon, right_b_lon, left_t_lon});
                auto min_left_b_lat = std::min({left_b_lat, right_t_lat, right_b_lat, left_t_lat});
                auto max_right_t_lon = std::max({left_b_lon, right_t_lon, right_b_lon, left_t_lon});
                auto max_right_t_lat = std::max({left_b_lat, right_t_lat, right_b_lat, left_t_lat});
                left_b_lon = min_left_b_lon;
                left_b_lat = min_left_b_lat;
                right_t_lon = max_right_t_lon;
                right_t_lat = max_right_t_lat;
             
            }
            // 当前屏的先请求
            aurora::parser::GeoMbr mbr(left_b_lon, left_b_lat, right_t_lon, right_t_lat);
            aurora::parser::DisplayTileIDSet tiles;
            aurora::parser::DisplayTileIDSet current_screen_tiles;
            std::vector<parser::DisplayTileID> req_display_tile_ids;
            data_provider_->GetDisplayTileIDs(base_scale, mbr, tiles);
            auto current_screen_mbr = camera->GetMapRange();
            data_provider_->GetDisplayTileIDs(base_scale, current_screen_mbr, current_screen_tiles);
            if (tiles.size() == 0 || current_screen_tiles.empty()) {
                return;
            }
            
            tile_ids = FetchAndCollectNotCachedTiles(tiles, base_scale, req_display_tile_ids, cached_tile_datas);

            // 实际渲染的tile，不用加了缓存的tile
            if (current_screen_tiles != tiles) {
                std::vector<TileID> cur_tile_ids;
                for(auto cur_tile: current_screen_tiles) {
                    TileID cur_tile_id(cur_tile.row, cur_tile.col, base_scale);
                    cur_tile_ids.push_back(cur_tile_id);
                    #ifdef DEBUG
                    printf("cur_tile: %d %d %d\n", cur_tile.row, cur_tile.col, base_scale);
                    #endif
                }
                #ifdef DEBUG
                printf("============tile_ids.size: %d, cur_tile_ids.size: %d\n", tile_ids.size(), cur_tile_ids.size());
                #endif
                tile_ids = cur_tile_ids;
               
            }
            
            
            // auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
            //         std::chrono::system_clock::now().time_since_epoch()).count();
            //     uint32_t taskid = static_cast<uint32_t>(now);
            auto taskid = g_latest_taskid.load() + 1;
            g_latest_taskid.store(taskid);
             // 使用 thread_pool_ 替代 std::thread 来更新tileid的mbr到camera
            thread_pool_.submit_task([this, camera, req_display_tile_ids, taskid, base_scale]() {
                if (g_latest_taskid.load() != taskid) {
                    return; // 不是最新的taskid，不处理
                }
                data_provider_->RequestDisplayTilePackageByID(req_display_tile_ids, camera, taskid, base_scale, data_fetcher_);
            });
            // 预测的bbox请求
            if (predict_boundingbox.Width() > 0 && predict_boundingbox.Height() > 0) {
                req_display_tile_ids.clear();
                tiles.clear();
                data_provider_->GetDisplayTileIDs(base_scale, predict_boundingbox, tiles);
                FetchAndCollectNotCachedTiles(tiles, base_scale, req_display_tile_ids, cached_tile_datas);
                for (auto tile: req_display_tile_ids)
                {
                    pre_req_tiles_[TileID(tile.row, tile.col, base_scale)] = base_scale;
                    printf("predict_boundingbox tileid: %d %d %d\n", tile.row, tile.col, base_scale);
                }
                if (!req_display_tile_ids.empty()) {
                    // printf("==========cur_dest_pitch_angle: %f, req_display_tile_ids.size: %d\n", cur_dest_pitch_angle, req_display_tile_ids.size());
                    thread_pool_.submit_task([this, camera, req_display_tile_ids, taskid, base_scale]() {
                        data_provider_->RequestDisplayTilePackageByID(req_display_tile_ids, camera, taskid, base_scale, data_fetcher_);
                        
                    });
                }
            }
            // 加载小数点的下一层级数据
            // req_display_tile_ids.clear();
            // tiles.clear();
            // data_provider_->GetDisplayTileIDs(next_scale, mbr, tiles);
            // FetchAndCollectNotCachedTiles(tiles, next_scale, req_display_tile_ids, cached_tile_datas);
            // thread_pool_.submit_task([this, camera, req_display_tile_ids, taskid, next_scale]() {
                
            //     data_provider_->RequestDisplayTilePackageByID(req_display_tile_ids, camera, taskid, next_scale);
            // });

            // 比例尺动画，提前请求
            uint32_t trend_level = base_scale;
            if (cur_scale != cur_logic_levle) {
                req_display_tile_ids.clear();
                // 比例尺变小，取base_scale - 1， 比例尺变大取base_scale + 1
                trend_level = cur_scale > cur_logic_levle ? (base_scale - 1) : (base_scale + 1);
                trend_level = std::clamp(trend_level, camera->GetMinScale(), camera->GetMaxScale());
                GetDestLevelNotCachedTileIds(cur_scale, trend_level, mbr, req_display_tile_ids);
                // 缓存提前请求的tileid, 用来过滤多次请求
                for (auto tile: req_display_tile_ids)
                {
                    pre_req_tiles_[TileID(tile.row, tile.col, trend_level)] = trend_level;
                }
                if (!req_display_tile_ids.empty()) {
                    thread_pool_.submit_task([this, camera, req_display_tile_ids, taskid, trend_level]() {
                        data_provider_->RequestDisplayTilePackageByID(req_display_tile_ids, camera, taskid, trend_level, data_fetcher_);
                        
                    });
                }
            }
            // 旋转动画，目标mbr会变换，提前请求
            // 先看pitch变化， 再看旋转
            auto cur_dest_pitch_angle = camera->GetMapPitchAngle(now + std::chrono::seconds(1000));
            if (cur_pitch_angle != cur_dest_pitch_angle) {
                req_display_tile_ids.clear();
                GetDestPitchNotCachedTileIds(trend_level, cur_pitch_angle, cur_dest_pitch_angle, mbr, req_display_tile_ids);
                // 缓存提前请求的tileid, 用来过滤多次请求
                for (auto tile: req_display_tile_ids)
                {
                    pre_req_tiles_[TileID(tile.row, tile.col, trend_level)] = trend_level;
                }
                if (!req_display_tile_ids.empty()) {
                    // printf("==========cur_dest_pitch_angle: %f, req_display_tile_ids.size: %d\n", cur_dest_pitch_angle, req_display_tile_ids.size());
                    thread_pool_.submit_task([this, camera, req_display_tile_ids, taskid, trend_level]() {
                        data_provider_->RequestDisplayTilePackageByID(req_display_tile_ids, camera, taskid, trend_level, data_fetcher_);
                        
                    });
                }
            }
            auto cur_dest_roll_angle = camera->GetMapRotation(Clock::now() + std::chrono::seconds(1000));
            if (cur_roll_angle != cur_dest_roll_angle ) {
                req_display_tile_ids.clear();
                GetDestRollNotCachedTileIds(trend_level, cur_dest_roll_angle, mbr, req_display_tile_ids);
                // 缓存提前请求的tileid, 用来过滤多次请求
                for (auto tile: req_display_tile_ids)
                {
                    pre_req_tiles_[TileID(tile.row, tile.col, trend_level)] = trend_level;
                }
                if (!req_display_tile_ids.empty()) {
                    thread_pool_.submit_task([this, camera, req_display_tile_ids, taskid, trend_level]() {
                        data_provider_->RequestDisplayTilePackageByID(req_display_tile_ids, camera, taskid, trend_level, data_fetcher_);
                        
                    });
                }
            }


            // 优先加载当前层数据，预加载上下两层数据
            #if 0
            req_display_tile_ids.clear();
            GetUpDownLevelNotCachedTileIds(display_tile_level, mbr, req_display_tile_ids);
            thread_pool_.submit_task([this, camera, req_display_tile_ids, taskid]() {
                if (g_latest_taskid.load() != taskid) {
                    return; // 不是最新的taskid，不处理
                }
                data_provider_->RequestDisplayTilePackageByID(req_display_tile_ids, camera, taskid);
            });
            
          #endif
            
        }

#if 1 // test once  
        static int once1 = 0;
        if (once1 > 1)
        {
            return ;
        }
        once1++;
#endif 
        // if tile cache have no data
        // 2. request tile data
        // data_provider->getCurrentScreenTiles
        // to do ...
#if 0 // For_test
    static aurora::parser::DataProvider provider;
    bool ret = provider.InitDisplayParser("./display");
    double lon = 121.31604686379432;
    double lat = 31.189158148023324;
    aurora::parser::GeoMbr mbr(lon - 0.01, lat - 0.001, lon + 0.0000001, lat + 0.0000001);

        std::vector<std::future<void>> futures; // cache task future
        MapRenderThemeIF* theme = GetRenderTheme();
        static int cuntCallback = 0;
            // submit to thread pool
            futures.push_back(thread_pool_.submit_task([theme, mbr]() {
                std::cout << "  == submit task === " << std::endl;

                aurora::parser::DisplayTileIDSet tiles;
                static aurora::parser::Treceiver receiver;
                receiver.theme_ = theme;
                if (provider.GetDisplayTileIDByMbr(14, mbr, tiles)) {
                    provider.RequestDisplayTilePackageByID(tiles, receiver);
                }
            }));
        // }

        // check for all task done
        for (auto& future : futures) {
            future.get();
        }
    #endif
        if (data_provider_ == nullptr) {
            printf("data_provider_ is null\n");
            return;
        }
        // printf("data_provider_ start to request\n");
        // double lon = 121.31604686379432;
        // double lat = 31.189158148023324;
        // aurora::parser::GeoMbr mbr(lon - 0.01, lat - 0.001, lon + 0.0000001, lat + 0.0000001);
        // data_provider_->GetDisplayData(14, mbr);

    }

    bool MapRenderDataManager::GetRenderTileDataById(const TileID& id, std::shared_ptr<CacheTile>& tile_data)
    {
        if (data_fetcher_) {
            if (data_fetcher_->FetchRenderTileDataById(id, tile_data)) {
                if (pre_req_tiles_.find(id) != pre_req_tiles_.end()) {
                    pre_req_tiles_.erase(id);
                }
                return true;
            }
        }
        
        return false;
    }

     void MapRenderDataManager::UpdateProviderWithCamera(CameraPtr camera, uint32_t screen_width, uint32_t screen_height) 
     {
        if (data_provider_ == nullptr || camera == nullptr || screen_height == 0 || screen_width == 0) {
            printf("UpdateProviderWithCamera data_provider_:%p, camera:%p, screen_width:%d, screen_height:%d\n", data_provider_.get(), camera.get(), screen_width, screen_height);
            return;
        }
        printf("UpdateProviderWithCamera camera:%p\n", camera.get());
        // 1. 获取camera记录的中心点，屏幕对应的mbr
        int mapScale = camera->GetMapScale();
        if (mapScale == 0) {
            return;
        }
        double left_b_lon = 0.0;
        double left_b_lat = 0.0;
        camera->Screen2World(0, 0, left_b_lon, left_b_lat);
        printf("left_b_lon:%f, left_b_lat:%f\n", left_b_lon, left_b_lat);
        double right_t_lon = 0.0;
        double right_t_lat = 0.0;
        camera->Screen2World(screen_width, screen_height, right_t_lon, right_t_lat);
        printf("right_t_lon:%f, right_t_lat:%f\n", right_t_lon, right_t_lat);
        aurora::parser::GeoMbr mbr(left_b_lon, left_b_lat, right_t_lon, right_t_lat);
        aurora::parser::DisplayTileIDSet tiles;
        data_provider_->GetDisplayData(mapScale, mbr, tiles);



     }
} //namespace 
/* EOF */
