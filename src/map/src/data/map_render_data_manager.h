/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_data_manager.h
 * @brief Declaration file of class MapRenderDataManager.
 * @attention used for C/C++ only.
 */

#ifndef MAP_SRC_DATA_MAPRENDERDATAMANAGER_H
#define MAP_SRC_DATA_MAPRENDERDATAMANAGER_H

#include <memory>
#include "data_manager_interface.h"
#include "BS_thread_pool.hpp" 
#include "aabb2.h"
#include <unordered_map>
#include "util_tile_define.h"

namespace aurora {

class RenderDataFetcher;
 namespace parser 
 {
    class TileDataReceiver;
    struct DisplayTileID;
    typedef std::shared_ptr<TileDataReceiver> TileDataReceiverPtr;
 }

/**
* class breif description
*
* MapRenderDataManager
*
*/
   
class MapRenderDataManager: public IMapRenderDataManager {
public:
    MapRenderDataManager();              
    virtual ~MapRenderDataManager();   

    virtual bool Init();

    virtual void AddDataProvider(MapDataProviderPtr provider);

    virtual void SetRenderTheme(MapRenderThemeIF* theme);
    virtual MapRenderThemeIF* GetRenderTheme() const { return  theme_; }

    virtual void RemoveDataProvider();

    virtual void SetCacheStrategy();

    virtual void GetRenderTileData(CameraPtr camera,  std::vector<TileID>& tile_ids,
                                   std::vector<std::shared_ptr<CacheTile>>& cached_tile_datas,
                                   const AABB2<Point2d>& predict_boundingbox = {{0.0,0.0}, {0.0,0.0}} );

    virtual bool GetRenderTileDataById(const TileID& id, std::shared_ptr<CacheTile>& tile_data);

    
   /// @brief  将tile数据加入缓存中
   /// @param id 数据所在的tile id
   virtual void AddRenderTileCache(const TileID& id, const std::shared_ptr<CacheTile>& tile_data, bool add_to_active = true) noexcept;

    virtual void UpdateProviderWithCamera(CameraPtr camera, uint32_t screen_width, uint32_t screen_height);


private:
    void DisplayTileIdToTileID(const parser::DisplayTileID& display_id, TileID& id);

    void TileIDToDisplayTileId(const TileID& id, parser::DisplayTileID& display_id);

    void GetUpDownLevelNotCachedTileIds(const uint32_t cur_logic_levle, const AABB2<Point2d>& mbr, 
                                        std::vector<parser::DisplayTileID>& not_cached_tile_ids );

    void GetDestLevelNotCachedTileIds(const uint32_t cur_logic_levle, const uint32_t dest_logic_levle, const AABB2<Point2d>& mbr, 
                                        std::vector<parser::DisplayTileID>& not_cached_tile_ids );
    void GetDestRollNotCachedTileIds( const double dest_logic_levle, const double dest_roll_angle, const AABB2<Point2d>& mbr, 
                                        std::vector<parser::DisplayTileID>& not_cached_tile_ids );

    void GetDestPitchNotCachedTileIds( const double dest_logic_levle, const double cur_pitch_angle, const double dest_pitch_angle,  AABB2<Point2d>& mbr, 
                                        std::vector<parser::DisplayTileID>& not_cached_tile_ids );

    std::vector<TileID> FetchAndCollectNotCachedTiles(const std::vector<parser::DisplayTileID>& tiles, 
                                        uint32_t target_level, 
                                        std::vector<parser::DisplayTileID>& not_cached_tile_ids,  std::vector<std::shared_ptr<CacheTile>>& cached_tile_datas);
private:
    MapDataProviderPtr   data_provider_{nullptr};
    MapRenderThemeIF*  theme_{nullptr};

    // fetchar data from data cache
    std::shared_ptr<RenderDataFetcher>  data_fetcher_;

    // 缓存提前请求的tileid, 用来过滤多次请求 <id, req_level>
    std::unordered_map<TileID, int32_t, TileIDHashFunc> pre_req_tiles_;

    aurora::parser::TileDataReceiverPtr receiver_ {nullptr};
    
    // thread pool process tile data
    BS::thread_pool<> thread_pool_;

};
    
} //namespace 

#endif //MAPRENDER_DATAMANAGER_H_
/* EOF */
