#include "pin_mark/pin_mark_manager.h"

#include <cstdio>
#include <iostream>
#include <string>

#include "collision_area.h"
#include "text/skia/sk_text_holder.h"

namespace aurora {
PinMarkManager::PinMarkManager() : dirty_flag_(false) {}

int32_t PinMarkManager::Add(uint16_t layer, UserMarkInfoPtr mark, Point2d lnglat, float degree,
                            const char* str) {
  if (mark == nullptr) {
    return -1;
  }
  CollisionMarkPtr mark_ptr = std::make_shared<CollisionMark>();
  mark_ptr->SetImageHolder(mark->image_holder);
  mark_ptr->SetSrcRect({{0, 0}, {mark->image_holder->Width(), mark->image_holder->Height()}});
  mark_ptr->SetBoxWidth(mark->image_holder->Width());
  mark_ptr->SetBoxHeight(mark->image_holder->Height());
  mark_ptr->SetAnchorType(kAnchorFree);
  mark_ptr->SetAnchor(mark->anchor.x(), mark->anchor.y());
  mark_ptr->SetShow(kCollisionShow);
  // mark_ptr->SetAlphaAnimation(500, 0.0f, 1.0f);
  // mark_ptr->SetSizeAnimation(500, 0, 0, mark_ptr->GetBoxWidth(), mark_ptr->GetBoxHeight());
  mark_ptr->SetDegreeAnimation(500, 10.0f, 0.0f);

  float offset_x = 0;
  float offset_y = 0;
  uint32_t box_w = mark_ptr->GetBoxWidth();
  uint32_t box_h = mark_ptr->GetBoxHeight();
  CollisionTextPtr name_ptr = nullptr;
  if (str != nullptr) {
    name_ptr = std::make_shared<CollisionText>();

    // temp code
    int32_t font_size = 18;
    name_ptr->SetFont(0, 0xFFFFFF, font_size, 1, 1, 0);
    float gap = 4;
    // temp code end

    mark_ptr->SetShow(kCollisionShow);
    name_ptr->SetAnchorType(kAnchorMiddleTop);
    name_ptr->SetText(str);
    TextHolderPtr text_holder = std::make_shared<SkTextHolder>();
    BoundingBox box = text_holder->CreateTextBlob(str, "Arial Unicode.ttf", font_size, 8);
    name_ptr->SetTextHolder(text_holder);
    name_ptr->SetBoxWidth(box.Width());
    name_ptr->SetBoxHeight(box.Height());

    offset_y = gap + mark_ptr->GetBoxHeight() - mark_ptr->GetAnchorY();
#if 0
    switch (name_ptr->GetAnchorType()) {
      case kAnchorMiddleTop: {
        box_w = box_w > name_ptr->GetBoxWidth() ? box_w : name_ptr->GetBoxWidth();
        box_h = name_ptr->GetBoxHeight() + offset_y + mark_ptr->GetAnchorY();
      } break;
      case kAnchorLeftMiddle: {
        box_w = name_ptr->GetBoxWidth() + offset_x + mark_ptr->GetAnchorX();
        box_h = box_h > name_ptr->GetBoxHeight() ? box_h : name_ptr->GetBoxHeight();
      } break;
      case kAnchorRightMiddle: {
        box_w =
            name_ptr->GetBoxWidth() + offset_x + mark_ptr->GetBoxWidth() - mark_ptr->GetAnchorX();
        box_h = box_h > name_ptr->GetBoxHeight() ? box_h : name_ptr->GetBoxHeight();
      } break;
      default:
        break;
    }
#else
    box_w = box_w > name_ptr->GetBoxWidth() ? box_w : name_ptr->GetBoxWidth();
    box_h = name_ptr->GetBoxHeight() + gap + mark_ptr->GetBoxHeight();
#endif
  }
  CollisionPinMarkPtr ptr = std::make_shared<CollisionPinMark>(mark_ptr, name_ptr);
  ptr->SetLonLat(lnglat.x(), lnglat.y());
  ptr->SetBoxWidth(box_w);
  ptr->SetBoxHeight(box_h);
  ptr->SetCollisionLayer(layer);
  ptr->SetCollisionFlag(false);
  ptr->SetShow(kCollisionShow);
  ptr->SetNameOffset(offset_x, offset_y);

  std::lock_guard<std::mutex> lock(mark_mtx_);
  SetDirty();
  auto result = collisions_.emplace(layer, CollisionPinMarkSet());
  auto itr = result.first;
  int32_t id = itr->second.size();
  for (int32_t i = 0; i < id; ++i) {
    if (itr->second[i] == nullptr) {
      itr->second[i] = ptr;
      return i;
    }
  }
  itr->second.push_back(ptr);
  return id;
}

void PinMarkManager::Update(uint16_t layer, int32_t id, Point2d lnglat, float degree) {
  std::lock_guard<std::mutex> lock(mark_mtx_);
  auto ptr = GetPinMark(layer, id);
  if (ptr) {
    ptr->SetLonLat(lnglat.x(), lnglat.y());
    auto mark_ptr = ptr->GetMark();
    if (mark_ptr) {
      mark_ptr->SetDegree(degree);
      mark_ptr->RestartAnimation();
      SetDirty();
    }
  }
}

void PinMarkManager::Update(uint16_t layer, int32_t id, UserMarkInfoPtr mark) {
  if (mark == nullptr) {
    return;
  }
  std::lock_guard<std::mutex> lock(mark_mtx_);
  auto ptr = GetPinMark(layer, id);
  if (ptr) {
    auto mark_ptr = ptr->GetMark();
    if (mark_ptr) {
      mark_ptr->SetImageHolder(mark->image_holder);
      mark_ptr->SetAnchor(mark->anchor.x(), mark->anchor.y());
      SetDirty();
    }
  }
}

CollisionPinMarkSet PinMarkManager::GetPinMarks(uint16_t layer) {
  std::lock_guard<std::mutex> lock(mark_mtx_);
  auto itr = collisions_.find(layer);
  if (itr == collisions_.end()) {
    return CollisionPinMarkSet();
  }
  return itr->second;
}

std::map<uint16_t, CollisionPinMarkSet> PinMarkManager::GetAllPinMarks() {
  std::lock_guard<std::mutex> lock(mark_mtx_);
  return collisions_;
}

void PinMarkManager::Delete(uint16_t layer, int32_t id) {
  std::lock_guard<std::mutex> lock(mark_mtx_);
  auto itr = collisions_.find(layer);
  if (itr == collisions_.end()) {
    return;
  }
  auto& set = itr->second;
  if (id < 0) {
    set.clear();
  } else if (id < set.size()) {
    set[id] = nullptr;
  } else {
  }
  SetDirty();
}

void PinMarkManager::Clear(uint16_t layer) {
  std::lock_guard<std::mutex> lock(mark_mtx_);
  auto itr = collisions_.find(layer);
  if (itr == collisions_.end()) {
    return;
  }
  collisions_.erase(itr);
  SetDirty();
}

void PinMarkManager::ClearAll() {
  std::lock_guard<std::mutex> lock(mark_mtx_);
  collisions_.clear();
  SetDirty();
}

bool PinMarkManager::NeedDraw() {
  bool dirty = dirty_flag_.exchange(false, std::memory_order_acq_rel);

  for (auto itr = collisions_.begin(); itr != collisions_.end(); ++itr) {
    for (auto ptr : itr->second) {
      if (ptr && (ptr->IsShow() == kCollisionShow)) {
        dirty = ptr->NeedDraw() || dirty;
      }
    }
  }
  return dirty;
}

void PinMarkManager::SetDirty() { dirty_flag_.store(true, std::memory_order_release); }

CollisionPinMarkPtr PinMarkManager::GetPinMark(uint16_t layer, int32_t id) {
  auto itr = collisions_.find(layer);
  if (itr == collisions_.end()) {
    return nullptr;
  }
  if (id < 0 || id > itr->second.size()) {
    return nullptr;
  }
  return itr->second.at(id);
}
}  // namespace aurora
