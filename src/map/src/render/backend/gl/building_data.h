#pragma once
#include "util_matrix4.h"
#include "util_color.h"
 #include <GLES3/gl3.h>
#include <vector>
#include "render_geometry_type.h"

namespace aurora{
struct BuildingData {
    // 几何数据
    std::vector<float> side_vertices;     // 顶点数据（3位置+3法线+2纹理坐标）
    std::vector<float> top_vertices;     // 顶点数据（3位置+3法线+2纹理坐标）
    // std::vector<uint32_t> indices;   // 索引数据
    std::vector<uint32_t> top_indices;  // 顶面三角带索引（连续序列）
    std::vector<uint32_t> side_indices;  // 侧面三角带索引（连续序列）
    std::vector<float>    side_uvs;   // 纹理uv
    std::vector<float>    top_uvs;   // 纹理uv
    // TODO: 不单独渲染一个building，vao， vbo等暂时不生成， 后续可以remove
    GLuint vao = 0;                  // 顶点数组对象 ID（预先生成），
    GLuint vbo = 0;                  // 顶点缓冲对象 ID（预先生成）
    GLuint ebo = 0;                  // 索引缓冲对象 ID（预先生成）

    // 渲染属性
    Matrix4<float> model_matrix;     // 模型变换矩阵
    GLuint texture_id = 0;           // 纹理 ID
    Color diffuse_color;             // 漫反射颜色

    // 建筑特征
    int floor_count = 1;             // 层数高度
    std::vector<Point> base_polygon; // 底面多边形顶点（用于碰撞检测等）
    uint16_t category = 0;

    // 新增：析构函数释放 OpenGL 资源
    ~BuildingData() {
        // 释放 VAO（顶点数组对象）
        if (vao != 0) {
            glDeleteVertexArrays(1, &vao);
            vao = 0;
        }
        // 释放 VBO（顶点缓冲对象）
        if (vbo != 0) {
            glDeleteBuffers(1, &vbo);
            vbo = 0;
        }
        // 释放 EBO（索引缓冲对象）
        if (ebo != 0) {
            glDeleteBuffers(1, &ebo);
            ebo = 0;
        }
        // 释放纹理 ID
        if (texture_id != 0) {
            glDeleteTextures(1, &texture_id);
            texture_id = 0;
        }
    }
};


} // namespace


