/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file gpu_program_id.h
 * @brief Declaration file of gpu shader ID
 * @attention used for C/C++ only.
 */

#ifndef MAP_SRC_RENDER_BACKEND_GL_GPUPROGRAM_ID_H_
#define MAP_SRC_RENDER_BACKEND_GL_GPUPROGRAM_ID_H_

namespace aurora {
namespace glshader {

    static constexpr const char* kVertexDefault = R"(
    #version 300 es
    in vec2 position;
    in vec2 texCoord;
    out vec2 vTexCoord;
    uniform mat4 u_mvpMatrix;
    void main() {
        gl_Position = u_mvpMatrix*vec4(position, 0.0, 1.0);
        vTexCoord = texCoord;
    })";

    static constexpr const char* kFragmentDefault = R"(
    #version 300 es
    precision mediump float;
    in vec2 vTexCoord;
    uniform sampler2D texture;
    out vec4 fragColor;
    void main() {
        fragColor = texture2D(texture, vTexCoord);
    })";
    
    static constexpr const char* kVertexColor = R"(
    #version 300 es
    layout (location = 0) in vec4 vertex;
    out vec2 TexCoords;
    uniform mat4 u_mvpMatrix;
    void main() {
        gl_Position = u_mvpMatrix * vec4(vertex.xy, 0.0, 1.0);
        TexCoords = vertex.zw;
    })";

    static constexpr const char* kFragColor = R"(
    #version 300 es
    precision mediump float;
    in vec2 TexCoords;
    out vec4 fragColor;
    uniform sampler2D texture;
    uniform vec3 u_color;
    void main()
    {    
        vec4 sampled = vec4(1.0, 1.0, 1.0, texture2D(texture, TexCoords).r);
        fragColor = vec4(u_color, 1.0) * sampled;
    })";

    // 新增：3D 建筑带光照和纹理的着色器
    static const char* kBuildingVertexShader = R"(
    #version 300 es
    layout (location = 0) in vec3 aPosition;  // 顶点位置（3D 坐标）
    layout (location = 1) in vec3 aNormal;    // 顶点法线（用于光照计算）
    layout (location = 2) in vec2 aTexCoord;  // 纹理坐标（用于采样纹理）
    uniform mat4 u_mvp;                       // 模型-视图-投影矩阵
    uniform mat4 u_model;                     // 模型矩阵（仅用于法线变换）
    out vec3 vNormal;                         // 传递给片段着色器的变换后法线
    out vec2 vTexCoord;                       // 传递给片段着色器的纹理坐标
    void main() {
        gl_Position = u_mvp * vec4(aPosition, 1.0);
        vNormal = mat3(u_model) * aNormal;    // 法线变换（仅保留旋转/缩放）
        vTexCoord = aTexCoord;
    }
    )";

    static const char* kBuildingFragmentShader = R"(
      #version 300 es
    precision mediump float;
    in vec3 vNormal;                          // 顶点法线（已变换）
    in vec2 vTexCoord;                        // 纹理坐标
    out vec4 FragColor;
    uniform vec3 u_lightDir;                  // 光照方向（归一化）
    uniform vec3 u_ambient;                   // 环境光颜色
    uniform vec3 u_topColor;                  // 顶面颜色（如白色）
    uniform vec3 u_sideColor;                 // 侧面颜色（如灰色）
    uniform sampler2D u_texture;              // 纹理采样器（可选）
    uniform bool u_useTexture;                // 新增：是否使用纹理标记

    // === 新增：适配纯色/纹理的抗锯齿函数 ===
    vec3 antiAlias(vec3 color) {
        // 计算颜色在屏幕空间的导数（变化率）
        vec3 dx = dFdx(color);  // x方向颜色变化
        vec3 dy = dFdy(color);  // y方向颜色变化
        float edgeStrength = length(dx) + length(dy);  // 边缘强度（变化率越大越强）

        // 非边缘区域直接返回原颜色（阈值0.05可调整）
        if (edgeStrength < 0.05) return color;

        // 边缘区域：采样上下左右4邻域颜色（通过导数近似邻域位置）
        vec3 upColor = color + dy;    // 上方像素颜色（近似）
        vec3 downColor = color - dy;  // 下方像素颜色（近似）
        vec3 leftColor = color - dx;  // 左方像素颜色（近似）
        vec3 rightColor = color + dx; // 右方像素颜色（近似）

        // 加权混合邻域颜色（中心颜色占50%，邻域各占12.5%）
        return color * 0.5 + (upColor + downColor + leftColor + rightColor) * 0.125;
    }

    void main() {
        // 原有光照和颜色计算逻辑
          vec3 normal = normalize(vNormal);
        vec3 lightDir = normalize(u_lightDir);
        float diff = max(dot(normal, lightDir), 0.1);
        float isTop = step(0.5, normal.z);
        vec3 baseColor = mix(u_sideColor, u_topColor, isTop);
        
        // 根据是否使用纹理选择颜色来源
        vec3 finalColor;
        if (u_useTexture) {
            vec3 texColor = texture(u_texture, vTexCoord).rgb;
            finalColor = mix(baseColor, texColor, 0.2);  // 有纹理时叠加纹理
        } else {
            finalColor = baseColor;  // 无纹理时直接使用纯色
        }
        
        // 最终颜色 = 环境光 + 漫反射 * 最终颜色
        vec3 result = u_ambient +  finalColor;

        // 移除抗锯齿，直接输出颜色
        FragColor = vec4(result, 1.0);
    }
    )";

    // 新增：2D 投影着色器（用于经纬度坐标渲染）
    // 新增/修改 2D 顶点着色器（直接处理屏幕坐标）
    static constexpr const char* k2DVertexShader = R"(
        #version 300 es
        in vec2 aPosition;  // 输入屏幕坐标（x: 0→width, y: 0→height）
        uniform mat4 u_mvpMatrix;
        void main() {
            gl_Position = u_mvpMatrix*vec4(aPosition, 0.0, 1.0);
        }
    )";
    
    static constexpr const char* k2DFragmentShader = R"(
        #version 300 es
        precision mediump float;
        out vec4 fragColor;
        uniform vec3 u_color;  // 三角形颜色
        void main() {
            fragColor = vec4(u_color, 1.0);
        }
    )";
    
    enum GPUProgramID {
        PROGRAM_TILE = 0, 
        PROGRAM_OVERLAYER = 1,
        PROGRAM_BUILDING = 2,
        PROGRAM_2D_PROJECTION = 3,  // 新增：2D 投影着色器枚举
    
        PROGRAM_MAX
    };

}
                                                                                                                    
} //namespace 

#endif //MAP_SRC_RENDER_BACKEND_GL_GPUPROGRAM_ID_H_
/* EOF */