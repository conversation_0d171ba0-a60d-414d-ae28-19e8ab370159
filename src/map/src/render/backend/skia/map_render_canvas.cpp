/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "map_render_paint.h"
#include "map_render_canvas.h"
#include "include/core/SkPaint.h"
#include "include/core/SkTextBlob.h"
#include "include/core/SkPath.h"
#include "include/core/SkCanvas.h"
#include "map_render_skfontmanager.h"
#include "map_render_skfontface.h"
#include "map_render_poi_style.h"
#include "mark/mark_manager.h"
#include "mark/skia/sk_image_holder.h"
#include "text/skia/sk_text_holder.h"

namespace aurora {

    MapRenderCanvas::MapRenderCanvas()
    : render_canvas_(nullptr)
    {

    }

    MapRenderCanvas::~MapRenderCanvas()
    {

    }

    void MapRenderCanvas::Init(SkCanvas* canvas)
    {
        render_canvas_ = canvas;
    }

    void MapRenderCanvas::Clear(Color clr)
    {
        if (render_canvas_ != nullptr)
        {
            render_canvas_->clear(static_cast<SkColor>(clr.toDWORD()));
        }
    }

    void MapRenderCanvas::DrawPolyline(const std::vector<Point>& points, MapRenderPaint* paint)
    {
        DrawPath(points, false, paint);
    }

    void MapRenderCanvas::DrawPolygon(const std::vector<Point> & points, MapRenderPaint* paint)
    {
        DrawPath(points, true, paint);
    }

    void MapRenderCanvas::DrawPath(const std::vector<Point> & points, bool close, MapRenderPaint* paint)
    {
        if (points.empty() || render_canvas_ == nullptr)
        {
            return ;
        }

        SkPath path;
        path.moveTo(points.begin()->x, points.begin()->y);
        for (int i=1; i<points.size(); i++)
        {
            path.lineTo(points[i].x, points[i].y);
        }

        if (close)
        {
            path.close();
        }

        render_canvas_->drawPath(path, paint->getPaint());
    }

    void MapRenderCanvas::GetCenter(const Point& pt, float w, float h, AnchorType type, Point& center)
    {
        switch (type) {
          case kAnchorCenter: {
            center = pt;
          } break;
          case kAnchorLeftTop: {
            center.x = pt.x + w / 2;
            center.y = pt.y - h / 2;
          } break;
          case kAnchorLeftMiddle: {
            center.x = pt.x + w / 2;
            center.y = pt.y;
          } break;
          case kAnchorLeftBottom: {
            center.x = pt.x + w / 2;
            center.y = pt.y + h / 2;
          } break;
          case kAnchorMiddleBottom: {
            center.x = pt.x;
            center.y = pt.y + h / 2;
          } break;
          case kAnchorRightBottom: {
            center.x = pt.x - w / 2;
            center.y = pt.y + h / 2;
          } break;
          case kAnchorRightMiddle: {
            center.x = pt.x - w / 2;
            center.y = pt.y;
          } break;
          case kAnchorRightTop: {
            center.x = pt.x - w / 2;
            center.y = pt.y - h / 2;
          } break;
          case kAnchorMiddleTop: {
            center.x = pt.x;
            center.y = pt.y - h / 2;
          } break;
          default:
            break;
        }

    }

    void MapRenderCanvas::DrawText(const std::string& str, const Point& pt, float degree, const TextStyle& style, TextHolderPtr& text_holder)
    {
        if (text_holder == nullptr) {
            text_holder = std::make_shared<SkTextHolder>();
            text_holder->CreateTextBlob(str, "Arial Unicode.ttf", style.font_size_, 0);
        }

        SkTextHolder* holder = (SkTextHolder*)text_holder.get();
        sk_sp<SkTextBlob> blob = holder->GetTextBlob();
        Point center;
        if (blob) {
            GetCenter(pt, holder->GetWidth(), holder->GetHeight(), style.type_, center);

            render_canvas_->save();
            render_canvas_->translate(center.x, center.y);
            render_canvas_->scale(1, -1);
            render_canvas_->rotate(-degree);

            float x = -holder->GetWidth() * 0.5;
            float y = -holder->GetHeight() * 0.5f;
            SkPaint back_paint;
            back_paint.setAntiAlias(true);
            back_paint.setStyle(SkPaint::kStroke_Style);
            back_paint.setStrokeWidth(style.outline_);
            back_paint.setColor(style.back_color_.toDWORD());
            render_canvas_->drawTextBlob(blob, x, y, back_paint);

            SkPaint fore_Paint;
            fore_Paint.setAntiAlias(true);
            fore_Paint.setColor(style.fore_color_.toDWORD());
            render_canvas_->drawTextBlob(blob, x, y, fore_Paint);
            render_canvas_->restore();
        }
#if 0
        // draw text rect
        {
            SkRect box = blob->bounds();
            float w = ((SkTextHolder*)text_holder.get())->GetWidth();
            float h = ((SkTextHolder*)text_holder.get())->GetHeight();
            SkRect rect = SkRect::MakeXYWH(center.x - w / 2 , center.y - h / 2, w, h);
           
            SkPaint paint;
            paint.setColor(SK_ColorBLACK);
            paint.setStyle(SkPaint::kStroke_Style);
            paint.setStrokeWidth(1.0); 
            render_canvas_->drawRect(rect, paint);
            
            paint.setStyle(SkPaint::kFill_Style); 
            paint.setColor(SK_ColorBLUE);
            render_canvas_->drawCircle(pt.x, pt.y, 2.0, paint);
        }
#endif
    }

    void MapRenderCanvas::DrawImage()
    {

    }

    void MapRenderCanvas::DrawMark(ImageHolderPtr img, const AABB2<Point2d>& src, const AABB2<Point2d>& dst, float degree, float alpha) {
        if (render_canvas_ == nullptr || img == nullptr) {
            return;
        }
        const sk_sp<SkImage>& mark = ((SkImageHolder*)img.get())->GetImage();
        if (mark == nullptr) {
            return;
        }

        render_canvas_->save();
        render_canvas_->translate(dst.Center().x(), dst.Center().y());
        render_canvas_->scale(1, -1);
        render_canvas_->rotate(-degree);

        float half_width = dst.Width() / 2;
        float half_height = dst.Height() / 2;
        SkRect dst_rect = SkRect{-half_width, -half_height, half_width, half_height};
        SkRect src_rect = SkRect::MakeLTRB(src.minx(), src.miny(), src.maxx(), src.maxy());

        SkPaint paint;
        paint.setAntiAlias(true);
        paint.setAlpha(alpha * 255.f);
        SkSamplingOptions options;
        render_canvas_->drawImageRect(mark, src_rect, dst_rect, options, &paint, SkCanvas::kFast_SrcRectConstraint);

        render_canvas_->restore();
    }

    void MapRenderCanvas::DrawMark(ImageHolderPtr img, const AABB2<Point2d>& src, const AABB2<Point2d>& dst, float anchor_x, float anchor_y, float degree, float alpha) {
        if (render_canvas_ == nullptr || img == nullptr) {
            return;
        }
        const sk_sp<SkImage>& mark = ((SkImageHolder*)img.get())->GetImage();
        if (mark == nullptr) {
            return;
        }

        float dst_anch_x = dst.minx() + dst.Width() / src.Width() * anchor_x;
        float dst_anch_y = dst.miny() + dst.Height()/ src.Height() * anchor_y;

        render_canvas_->save();
        render_canvas_->translate(dst.Center().x(), dst.Center().y());
        render_canvas_->scale(1, -1);
        render_canvas_->translate(dst_anch_x - dst.Center().x(), dst_anch_y - dst.Center().y());
        render_canvas_->rotate(-degree);

        float dst_min_x = dst.minx() - dst_anch_x;
        float dst_min_y = dst.miny() - dst_anch_y;
        float dst_max_x = dst.maxx() - dst_anch_x;
        float dst_max_y = dst.maxy() - dst_anch_y;
        SkRect dst_rect = SkRect{dst_min_x, dst_min_y, dst_max_x, dst_max_y};
        SkRect src_rect = SkRect::MakeLTRB(src.minx(), src.miny(), src.maxx(), src.maxy());

        SkPaint paint;
        paint.setAntiAlias(true);
        paint.setAlpha(alpha * 255.f);
        SkSamplingOptions options;
        render_canvas_->drawImageRect(mark, src_rect, dst_rect, options, &paint, SkCanvas::kFast_SrcRectConstraint);

        render_canvas_->restore();
    }

} //namespace
