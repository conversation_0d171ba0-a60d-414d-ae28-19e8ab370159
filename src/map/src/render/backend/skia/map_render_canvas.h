/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_canvas.h
 * @brief Declaration file of class MapRenderCanvas.
 * @attention used for C/C++ only.
 */

#ifndef RENDER_BACKEND_MAP_RENDER_CANVAS_H_
#define RENDER_BACKEND_MAP_RENDER_CANVAS_H_

#include <vector>
#include "util_basic_type.h"
#include "util_vector2.h"
#include "util_color.h"
#include "aabb2.h"
#include "point2.h"
#include "text_def.h"
#include "mark/mark_def.h"

class SkCanvas;
class SkPaint;
class SkTextBlob;


namespace aurora {

class TextStyle;
class MapRenderPaint;

/**
* class breif description
*
* MapRenderCanvas
*
*/
class MapRenderCanvas {
public:
    MapRenderCanvas();              
    ~MapRenderCanvas();   

    void Init(SkCanvas* canvas);

    void Clear(Color clr);

    void DrawPolyline(const std::vector<Point>& points, MapRenderPaint* paint);

    void DrawPolygon(const std::vector<Point>& points, MapRenderPaint* paint);

    void DrawText(const std::string& str, const Point& pt, float degree, const TextStyle& style, TextHolderPtr& text_holder);

    void DrawImage();

    void DrawMark(ImageHolderPtr img, const AABB2<Point2d>& src, const AABB2<Point2d>& dst, float degree, float alpha);

    void DrawMark(ImageHolderPtr img, const AABB2<Point2d>& src, const AABB2<Point2d>& dst, float anchor_x, float anchor_y, float degree, float alpha);

    SkCanvas* GetCanvas() { return render_canvas_;}

private: 
    void GetCenter(const Point& pt, float w, float h, AnchorType type, Point& center);
    void DrawPath(const std::vector<Point> & points, bool close, MapRenderPaint* paint);

private:
    SkCanvas* render_canvas_;
};
    
} //namespace 

#endif //RENDER_BACKEND_MAP_RENDER_CANVAS_H_
/* EOF */
