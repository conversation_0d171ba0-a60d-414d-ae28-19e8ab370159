/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file i_map_render_manager.h
 * @brief Declaration file of class IMapRenderManager.
 * @attention used for C/C++ only.
 */

#ifndef MAP_SRC_RENDER_MAPRENDERMANAGER_INTERFACE_H_
#define MAP_SRC_RENDER_MAPRENDERMANAGER_INTERFACE_H_


#include "render_geometry_type.h"
#include "util_lonlat.h"
#include "util_vector2.h"
#include <vector>
#include "point2.h"


namespace aurora {
    
class MapDataProvider;
using MapDataProviderPtr = std::shared_ptr<MapDataProvider>;

class MapRenderThemeIF;

/**
* class breif description
*
* IMapRenderManager
*
*/
class IMapRenderManager
{
public:             
    virtual ~IMapRenderManager() = default;
    
    /**
     * Initialize resource
     */
    virtual bool Init(MapDataProviderPtr provider, MapRenderThemeIF* theme) = 0;

    /**
     * Destory resource
     */
    virtual void Destory() = 0;

    virtual void RenderMap() = 0;

    virtual void SetMapScale(float scale, const uint32_t animation_duration_ms = 0) = 0;

    virtual void SetMapCenter(double lon, double lat, const uint32_t animation_duration_ms = 0) = 0;

    virtual void SetMapRotation(float rot, const uint32_t animation_duration_ms = 0) = 0;

    virtual float GetMapScale() = 0;

    virtual Lonlat GetMapCenter() = 0;

    virtual float GetMapRotation() = 0;

    virtual Vector2<double> ScreenToMap(const Vector2<int32_t>& pt) = 0;

    virtual Vector2<int32_t> MapToScreen(const Vector2<double>& geo) = 0;


    virtual void SetMapThemeByLayerId() = 0;

    virtual void RenderEmbedGeoObj() = 0;

    virtual void SetScreenSize(int32_t width, int32_t height) = 0;

    // 注意：地图内部左下角为0,0点，所以如果窗口给的y是按左上角做为0，0点，delta_y应该取反传入
    virtual void MoveMap(double delta_x, double delta_y, const bool move_end = false, const uint32_t animation_duration = 0) = 0;  

    virtual void SetMapPitch(float pitch, const uint32_t animation_duration = 0) = 0;

    virtual float GetMapPitch() = 0;

    //  flyto 到指定经纬度和比例尺，默认最少5s动画
    virtual void FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms) = 0;

    virtual void SetPath(uint32_t type, std::vector<Point2d>& path) = 0;

    virtual void ClearPath(uint32_t type) = 0;

    virtual int32_t SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name) = 0;

    virtual void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name) = 0;

    virtual void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree = 0.f ) = 0;

    virtual void ClearMark(uint16_t mark_type, int32_t mark_id) = 0;

};
    
} //namespace 

#endif //MAP_SRC_RENDER_MAPRENDERMANAGER_INTERFACE_H_
/* EOF */