#include "building_layer.h"
#include "map_render_backend_2d.h"

namespace aurora {
  const GLuint INVALID_INDEX = 0xFFFFFFFF;
BuildingLayer::BuildingLayer() {}

void BuildingLayer::SetBackend(BackendPtr backend) { 
  backend_ = backend; 
}

void BuildingLayer::AddBuildings(const std::vector<BuildingData>& building_list) {
  building_list_.insert(building_list_.end(), building_list.begin(), building_list.end());
}

void BuildingLayer::ClearBuildings() { 
  building_list_.clear(); 
}

void BuildingLayer::Update(CameraPtr camera) {
  if (backend_ == nullptr) {
    return;
  }

  uint32_t w = 0;
  uint32_t h = 0;
  camera->GetViewSize(w, h);

  // 渲染建筑
  if (backend_ && camera) {
      // 新增：初始化光照参数（示例值，可根据需求调整）
      // Vector3<float> light_dir(0.5f, 0.5f, 1.0f);  // 光照方向（右上前方）
      // Vector3<float> ambient(0.3f, 0.3f, 0.3f);    // 环境光强度（灰色）
      // Matrix4<double> prject_view_matrix;
      // if (camera->GetOrthViewMatrix(prject_view_matrix)) {
      //     backend_->DrawBuildings(building_list_, light_dir, ambient, prject_view_matrix);
      // }
  }
}

bool BuildingLayer::GetBuildingRenderData(
    std::vector<float>& merged_side_vertices, 
    std::vector<uint32_t>& merged_side_indices, 
    std::vector<float>& merged_side_uvs,
    std::vector<float>& merged_top_vertices,
    std::vector<uint32_t>& merged_top_indices,
    std::vector<float>& merged_top_uvs
) {
    if (building_list_.empty()) {
        return false;
    }
    uint32_t vertex_offset = 0;  // 全局顶点偏移量（按顶点数量计算，每个顶点占8个float）
    uint32_t top_vertex_offset = 0;  // 全局顶点偏移量（按顶点数量计算，每个顶点占8个float）
    uint32_t index_offset = 0;   // 全局索引偏移量（按索引数量计算）
    uint32_t top_index_offset = 0;   // 全局索引偏移量（按索引数量计算）

    for (auto& building : building_list_) {
        // if (building.vertices.empty() || building.side_indices.empty() || building.top_indices.empty()) {
        // if (building.vertices.empty()  || building.top_indices.empty()) {
        //     continue;
        // }

        // 1. 合并顶点数据（顶点数量 = building.vertices.size() / 8）
        // size_t building_vertex_count = building.vertices.size() / 8;
        merged_side_vertices.insert(merged_side_vertices.end(), building.side_vertices.begin(), building.side_vertices.end());

        // 2. 合并侧面索引（作为独立三角带）
        size_t side_count = building.side_indices.size();
        for (auto idx : building.side_indices) {
            // 确保侧面索引不越界（当前建筑顶点数 = building.vertices.size() / 8）
            if (idx >= building.side_vertices.size() / 8 && idx != INVALID_INDEX) {
                printf("[BuildingLayer] 侧面索引越界：idx=%zu，顶点数=%zu\n", idx, building.side_vertices.size() / 8);
                continue;
            }
            // printf("[BuildingLayer] 侧面索引：idx=%zu，顶点数=%zu , indice:%d\n", idx, building.side_vertices.size() / 8, idx + vertex_offset);
            merged_side_indices.push_back(idx + vertex_offset);  // 顶点偏移量（初始为0）
        }
        merged_side_indices.push_back(INVALID_INDEX);
        // strip_counts.push_back(side_count);
        // strip_offsets.push_back(reinterpret_cast<GLvoid*>(index_offset * sizeof(uint32_t)));
        index_offset += side_count;  // 更新索引偏移量（侧面索引已合并）
        
        // 3. 合并顶面索引（作为独立三角带）
        size_t top_count = building.top_indices.size();
        merged_top_vertices.insert(merged_top_vertices.end(), building.top_vertices.begin(), building.top_vertices.end());
        for (auto idx : building.top_indices) {
            // 确保顶面索引不越界（当前建筑顶点数 = building.top_vertices.size() / 8）
            if (idx >= building.top_vertices.size() / 8 && idx != INVALID_INDEX) {
                printf("[BuildingLayer] 顶面索引越界：idx=%zu，顶点数=%zu\n", idx, building.top_vertices.size() / 8);
                continue;
            }
            // printf("[BuildingLayer] 顶面索引：idx=%zu，顶点数=%zu\n", idx, building.top_vertices.size() / 8);
            merged_top_indices.push_back(idx + top_vertex_offset);  // 顶点偏移量（初始为0）
        }
        merged_top_indices.push_back(INVALID_INDEX);
        // strip_counts.push_back(top_count);
        // strip_offsets.push_back(reinterpret_cast<GLvoid*>(index_offset * sizeof(uint32_t)));
        top_index_offset += top_count;  // 更新索引偏移量（顶面索引已合并）

        // 在合并完当前建筑的所有顶点后，立即更新vertex_offset ===
        // 计算当前建筑的顶点总数（侧面+顶面）
        size_t building_vertex_count = building.side_vertices.size() / 8 ;  // 每个顶点占8个float
        if (building.side_vertices.size() % 8 != 0) {
            printf("[BuildingLayer] 顶点数据长度异常（%zu），非8的倍数\n", building.side_vertices.size());
        }
          // 更新全局顶点偏移量（累加当前建筑的顶点数）
        size_t building_top_vertex_count = building.top_vertices.size() / 8;
        vertex_offset += building_vertex_count;
        top_vertex_offset += building_top_vertex_count;
        // merged_indices.push_back(0xFFFFFFFF);

        // 合并UV数据（保持原有逻辑）
        merged_side_uvs.insert(merged_side_uvs.end(), building.side_uvs.begin(), building.side_uvs.end());
        merged_top_uvs.insert(merged_top_uvs.end(), building.top_uvs.begin(), building.top_uvs.end());
    }

    return !merged_side_vertices.empty() && !merged_side_indices.empty() || !merged_top_vertices.empty() && !merged_top_indices.empty();
}

bool BuildingLayer::GetBuildingRenderData(
    std::vector<std::vector<float>>& merged_vertices, 
    std::vector<std::vector<uint32_t>>& merged_indices, 
    std::vector<std::vector<float>>& merged_uvs
) {
    if (building_list_.empty()) {
        return false;
    }
    std::vector<float> merged_side_vertices;
    std::vector<uint32_t> merged_side_indices;
    std::vector<float> merged_side_uvs;
    std::vector<float> merged_top_vertices;
    std::vector<uint32_t> merged_top_indices;
    std::vector<float> merged_top_uvs;
    bool ret = GetBuildingRenderData(merged_side_vertices, merged_side_indices, merged_side_uvs, merged_top_vertices, merged_top_indices, merged_top_uvs);
    if (ret) {
        merged_vertices.push_back(merged_side_vertices);
        merged_vertices.push_back(merged_top_vertices);
        merged_indices.push_back(merged_side_indices);
        merged_indices.push_back(merged_top_indices);
        merged_uvs.push_back(merged_side_uvs);
        merged_uvs.push_back(merged_top_uvs);
    }
    return ret;
  }

  
}  // namespace aurora
