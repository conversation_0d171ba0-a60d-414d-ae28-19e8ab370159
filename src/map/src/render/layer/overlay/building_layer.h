#ifndef MAP_RENDER_LAYER_OVERLAY_BUILDING_LAYER_H
#define MAP_RENDER_LAYER_OVERLAY_BUILDING_LAYER_H

#include <cstdint>

#include "map_render_camera_if.h"
#include "map_render_backend.h"

namespace aurora {

class BuildingLayer {
public:
  BuildingLayer();

  void SetBackend(BackendPtr backend_ptr);
  void AddBuildings(const std::vector<BuildingData>& building_list);
  void ClearBuildings();

  void Update(CameraPtr camera);

  virtual bool GetBuildingRenderData(
    std::vector<float>& merged_side_vertices, 
    std::vector<uint32_t>& merged_side_indices, 
    std::vector<float>& merged_side_uvs,
    std::vector<float>& merged_top_vertices,
    std::vector<uint32_t>& merged_top_indices,
    std::vector<float>& merged_top_uvs
);

virtual bool GetBuildingRenderData(
    std::vector<std::vector<float>>& merged_vertices, 
    std::vector<std::vector<uint32_t>>& merged_indices, 
    std::vector<std::vector<float>>& merged_uvs
);

private:
  BackendPtr backend_{nullptr};
  std::vector<BuildingData> building_list_;
};
}  // namespace aurora
#endif  // MAP_RENDER_LAYER_OVERLAY_BUILDING_LAYER_H
