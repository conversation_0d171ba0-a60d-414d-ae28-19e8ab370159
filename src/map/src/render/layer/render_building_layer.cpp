/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "render_building_layer.h"

#include "gl_texture2d.h"
#include "gpu_shader_cache.h"
#include "map_render_backend_gl.h"
#include "util_matrix4.h"
#include <GLES3/gl3.h>  // GLES 3.0+ 头文件
#include <GL/gl.h>



namespace aurora {

RenderBuildingLayer::RenderBuildingLayer() {}

RenderBuildingLayer::~RenderBuildingLayer() {
  if (backend_) {
    backend_->Destory();
  }
}


void RenderBuildingLayer::AddBuildings(const std::vector<BuildingData> & buildings) {
  building_layer_.AddBuildings(buildings);
}

void RenderBuildingLayer::ClearBuildings() { 
  building_layer_.ClearBuildings(); 
}

void RenderBuildingLayer::Update(CameraPtr camera) {
  Matrix4<double> mtx;
  camera->GetOrthViewMatrix(mtx);
  bool need_draw = false;
  if (::memcmp(mtx.getRawPointer(), camera_mtx_.getRawPointer(), sizeof(double) * 16)) {
    need_draw = true;
    camera_mtx_ = mtx;
  }
  uint32_t w = 0;
  uint32_t h = 0;
  camera->GetViewSize(w, h);
  if (w != width_ || h != height_) {
    Release();
    Init(w, h);
    need_draw = true;
  }
  if (backend_ == nullptr) {
    return;
  }



  if (need_draw) {
    // backend_->BeginDraw();
    building_layer_.Update(camera);
   
  }
}

void RenderBuildingLayer::Release() {
  vao_.Release();
  vbo_.Release();
  ebo_.Release();

  if (building_texture_) {
    building_texture_->Release();
    building_texture_ = nullptr;
  }
  if (default_texture_id_ != 0) {
    glDeleteTextures(1, &default_texture_id_);
  }
  if (backend_) {
    backend_->Destory();
    backend_ = nullptr;
  }

}

bool RenderBuildingLayer::Init(uint32_t w, uint32_t h) {
  width_ = w;
  height_ = h;
  
  backend_ = std::make_shared<RenderBackendGL>();
  backend_->Init(w, h, Color(0));  
  backend_->SetAntiAlias(true); // // 修改：初始化渲染后端时启用 4x 多重采样
  building_layer_.SetBackend(backend_);


  if (!vao_.GenBuffer()) {
    return false;
  }

  if (!vbo_.GenBuffer()) {
    return false;
  }

  if (!ebo_.GenBuffer()) {
    return false;
  }
  building_shader_ = GpuShaderCache::Instance().GetProgram(glshader::PROGRAM_BUILDING);
  if (building_shader_ == nullptr) {
      return false;
  }

  if (building_texture_ == nullptr) {
    // TODO: 根据配置的building纹理图片加载， 第一版用纯色
    //  building_texture_ = std::make_shared<GLTexture2D>();

  }
  if (default_texture_id_ == 0) {
    glGenTextures(1, &default_texture_id_);
    glBindTexture(GL_TEXTURE_2D, default_texture_id_);
    // 设置纹理参数（线性过滤、边缘不重复）
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    // 生成 1x1 白色纹理（所有像素为 RGBA(255,255,255,255)）
    const uint8_t white_pixel[4] = {255, 255, 255, 255};
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, 1, 1, 0, GL_RGBA, GL_UNSIGNED_BYTE, white_pixel);
    glBindTexture(GL_TEXTURE_2D, 0);  // 解绑

    // 新增：启用多重采样（部分环境需要显式开启）
    glEnable(GL_MULTISAMPLE);  // 关键抗锯齿设置
  }
  
  // 在初始化时配置 VAO 的顶点属性（替代 Render 中的重复设置）
  vao_.Bind();  // 绑定 VAO 以记录后续配置
  vbo_.Bind();  // 绑定 VBO 到当前 VAO
  ebo_.Bind();  // 绑定 EBO 到当前 VAO（VAO 会记录 EBO 绑定状态）

  // 设置顶点属性指针（这些配置会被 VAO 记录）
  glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)0);
  glEnableVertexAttribArray(0);
  glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)(3 * sizeof(float)));
  glEnableVertexAttribArray(1);
  glVertexAttribPointer(2, 2, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)(6 * sizeof(float)));
  glEnableVertexAttribArray(2);


  // building_shader_->UseProgram();

  // vao_.Bind();
  // vbo_.Bind();
  // vbo_.SetData(sizeof(kVertices), kVertices, BufferUsageType::USAGE_STATIC_DRAW);
  // ebo_.Bind();
  // ebo_.SetData(sizeof(kIndices), kIndices, BufferUsageType::USAGE_STATIC_DRAW);

  // vbo_.SetData(sizeof(kVertices), kVertices, BufferUsageType::USAGE_STATIC_DRAW);
  // ebo_.SetData(sizeof(kIndices), kIndices, BufferUsageType::USAGE_STATIC_DRAW);

  // GLint pos_attrib = building_shader_->GetAttributeLocation("position");
  // vao_.VertexAttribute(pos_attrib, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void*)0);

  // GLint tex_attrib = building_shader_->GetAttributeLocation("texCoord");
  // vao_.VertexAttribute(tex_attrib, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float),
  //                      (void*)(2 * sizeof(float)));

  return true;
}

void RenderBuildingLayer::Render(CameraPtr camera) {
    if (building_shader_ == nullptr) {
        return;
    }
  // if (building_texture_) {
  //   building_texture_->Bind();
  // }
  // else {
  //   glBindTexture(GL_TEXTURE_2D, default_texture_id_);        
  // }
  //  GLuint vao, vbo, ebo;
  //   glGenVertexArrays(1, &vao);
  //   glGenBuffers(1, &vbo);
  //   glGenBuffers(1, &ebo);
  //   glBindVertexArray(vao);

  // enable alpha blend
  glEnable(GL_LINE_SMOOTH);
  glHint(GL_LINE_SMOOTH_HINT, GL_NICEST);
  glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
  glEnable(GL_BLEND);
  
  // 新增：关闭背面剔除（避免旋转时三角条带被误判为背面）
  glDisable(GL_CULL_FACE);  // 关键修改

        // 绘制合并后的所有三角形（仅一次调用）
      // glDisable(GL_DEPTH_TEST);
       // 渲染前清除深度缓冲（确保深度值从0开始）
      glClear(GL_DEPTH_BUFFER_BIT);
      
      glDepthFunc(GL_LEQUAL);  // 使用默认深度比较（仅近平面覆盖远平面）
      // glDrawElements(GL_TRIANGLE_STRIP, merged_indices.size(), GL_UNSIGNED_INT, 0);
       // 一次性渲染所有建筑的三角带（关键优化）, 需要opengl版本支持，暂时先用多次渲染的方式
  //  for (size_t i = 0; i < strip_counts.size(); ++i) {
    glDisable(GL_CULL_FACE);  // 关键修改
  std::vector<std::vector<float>> merged_vertices;
  std::vector<std::vector<uint32_t>> merged_indices;
  std::vector<std::vector<float>> merged_uvs;
  // std::vector<float> merged_top_vertices;
  // std::vector<uint32_t> merged_top_indices;
  // std::vector<float> merged_top_uvs;
  // std::vector<GLsizei> strip_counts;
  // std::vector<GLvoid*> strip_offsets;
  building_layer_.GetBuildingRenderData(merged_vertices, merged_indices, merged_uvs);
  if (merged_vertices.size() == 0 || merged_indices.size() == 0 || merged_vertices.size() != merged_indices.size()) {
    return;
  }
   vao_.Bind();
   building_shader_->UseProgram();
   Matrix4<double> proj_view_matrix;
   Matrix4<float> mvp;
  if (camera->GetProjViewMatrix(proj_view_matrix)) {
      mvp = proj_view_matrix;
  }
  else {
    return;
  }
  // glEnable(GL_PRIMITIVE_RESTART);
  // glPrimitiveRestartIndex(INVALID_INDEX);  
  
    // glPrimitiveRestartIndex(GL_INVALID_INDEX);
  for (int i = 0; i < merged_vertices.size(); i++) {
    if (i == 0) { // 侧面渲染加深度测试，顶面不加深度测试，防止被旁边建筑的侧面影响
      glEnable(GL_DEPTH_TEST);
    }
    else {
      glDisable(GL_DEPTH_TEST);
    }
    
    vao_.Bind();
    vbo_.Bind();
    ebo_.Bind();
    // 填充顶点缓冲
    // 填充索引缓冲
    // glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, ebo);
    // glBufferData(GL_ELEMENT_ARRAY_BUFFER, merged_indices.size() * sizeof(uint32_t), merged_indices.data(), GL_STATIC_DRAW);
  // 动态更新 VBO 数据（如果数据频繁变化，使用 USAGE_DYNAMIC_DRAW）
    vbo_.SetData(merged_vertices[i].size() * sizeof(float), merged_vertices[i].data(), BufferUsageType::USAGE_DYNAMIC_DRAW);
    ebo_.SetData(merged_indices[i].size() * sizeof(uint32_t), merged_indices[i].data(), BufferUsageType::USAGE_DYNAMIC_DRAW);
    GLuint mvp_loc = building_shader_->GetUniformLocation("u_mvp");
      if (mvp_loc != -1) {
          glUniformMatrix4fv(mvp_loc, 1, GL_FALSE, mvp.getTranspose().getRawPointer());
      }

      // 新增：设置模型矩阵（假设为单位矩阵，实际可能需要从建筑数据中获取）
      GLuint model_loc = building_shader_->GetUniformLocation("u_model");
      if (model_loc != -1) {
          Matrix4<float> model_matrix;  // 初始化为单位矩阵
          glUniformMatrix4fv(model_loc, 1, GL_FALSE, model_matrix.getTranspose().getRawPointer());
      }

      // 统一设置光照参数（与DrawBuildings逻辑一致）
      // GLuint diffuse_loc = building_shader_->GetUniformLocation("u_diffuse");
      // if (diffuse_loc != -1) glUniform3f(diffuse_loc, 0.8f, 0.8f, 0.8f);  // 原漫反射色
      
      // 新增：设置环境光颜色（建议白色或浅灰色）
      GLuint ambient_loc = building_shader_->GetUniformLocation("u_ambient");
      if (ambient_loc != -1) glUniform3f(ambient_loc, 0.6f, 0.6f, 0.6f);  // 环境光（弱白色）
      
      // 新增：设置光照方向（例如从斜上方照射，归一化后的向量）
      GLuint lightDir_loc = building_shader_->GetUniformLocation("u_lightDir");
      if (lightDir_loc != -1) glUniform3f(lightDir_loc, 0.5f, 1.0f, 0.5f);  // 归一化后的方向（x,y,z）
      
      GLuint sideColor_loc = building_shader_->GetUniformLocation("u_sideColor");
      if (sideColor_loc != -1) glUniform3f(sideColor_loc, 0.2f, 0.2f, 0.2f);  // 灰色
      
      GLuint topColor_loc = building_shader_->GetUniformLocation("u_topColor");
      if (topColor_loc == -1) {
          printf("Warning: u_topColor not found in shader!\n");  // 日志提示
      } else {
          glUniform3f(topColor_loc, 0.9f, 0.9f, 0.9f);  // 白色
      }


      // 绑定白色纹理（统一使用预加载纹理）
      GLuint tex_loc = building_shader_->GetUniformLocation("u_texture");
      if (tex_loc != -1) {
          glUniform1i(tex_loc, 0);
          glActiveTexture(GL_TEXTURE0);
          if (building_texture_) {
              // glBindTexture(GL_TEXTURE_2D, building_texture_->GetTextureID());
          }
          else {
            // printf("Usae default_texture_id_:%d\n", default_texture_id_);
            glBindTexture(GL_TEXTURE_2D, default_texture_id_);
          }
      }
  // vbo_.Bind();
  // // 填充顶点缓冲
  // vbo_.SetData(merged_vertices[i].size()* sizeof(float), merged_vertices[i].data(), BufferUsageType::USAGE_STATIC_DRAW);
  // // 填充索引缓冲
  // ebo_.Bind();
  // ebo_.SetData( merged_indices[i].size()* sizeof(uint32_t), merged_indices[i].data(), BufferUsageType::USAGE_STATIC_DRAW);

  // glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)0);
  // glEnableVertexAttribArray(0);
  // glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)(3 * sizeof(float)));
  // glEnableVertexAttribArray(1);
  // glVertexAttribPointer(2, 2, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)(6 * sizeof(float)));
  // glEnableVertexAttribArray(2);
  
  
      

      // if (strip_counts[i] < 3) {
      //     printf("[RenderBuilding] 条带%d顶点数不足（%zu），跳过绘制\n", i, strip_counts[i]);
      //     continue;  // 跳过无效条带
      // }
      // printf("[RenderBuilding] 绘制三角带%d：索引数=%d，偏移=%p\n", 
      //       i, strip_counts[i], strip_offsets[i]);
      glDrawElements(
          GL_TRIANGLE_STRIP,
          merged_indices[i].size(),
          GL_UNSIGNED_INT,
          nullptr // VAO 已记录 EBO 绑定，无需再传数据指针
      );
      glBindVertexArray(0);
    // }
      }
      glDisable(GL_DEPTH_TEST);  // 可选：关闭深度测试（若后续无需深度比较）

       // 清理临时资源

      
      glUseProgram(0);
      
      glDisable(GL_BLEND);

    
  
}

}  // namespace aurora
