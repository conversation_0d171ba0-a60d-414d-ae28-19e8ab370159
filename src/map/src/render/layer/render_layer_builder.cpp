/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "render_layer_builder.h"
#include "render_line_layer.h"
#include "render_point_layer.h"
#include "render_area_layer.h"
#include "data_manager_interface.h"
#include "display_data/display_tile_reader.h"
#include "display_data/display_data_def.h"
#include "data_provider.h"
#include "scale_converter.h"
#include "util_basic_type.h"
#include <iostream>
#include <cmath>
#include "constants.h"

#include "collision/collision_tile.h"
#include "text/skia/sk_text_holder.h"
#include "building_data.h"
#include "gpc.h"

namespace aurora {

    const float kFloorHeight = 10.0f;

     // 辅助函数：将aurora::Point转换为gpc_vertex
      // 辅助函数：将aurora::Point转换为gpc_vertex
    inline gpc_vertex ToGpcVertex(const aurora::Point& pt) {
        return {pt.x, pt.y};
    }

    // 辅助函数：计算多边形绕向（逆时针为正）
    bool IsCounterClockwise(const std::vector<aurora::Point>& poly) {
        float area = 0.0f;
        for (size_t i = 0; i < poly.size(); ++i) {
            size_t j = (i + 1) % poly.size();
            area += (poly[i].x * poly[j].y - poly[j].x * poly[i].y);
        }
        return area > 0;  // 面积正表示逆时针
    }

    // 替换原耳剪法为GPC的gpc_polygon_to_tristrip实现
    std::vector<uint32_t> GpcTriangulatePolygon(const std::vector<aurora::Point>& polygon, std::vector<aurora::Point>& result_strip) {
        std::vector<uint32_t> indices;
        if (polygon.size() < 3) {
            printf("[Triangulate] 多边形顶点数不足（%zu），无法三角化\n", polygon.size());
            return indices;
        }

        // === 闭合性检查与去重（保留原有逻辑） ===
        bool is_closed = (polygon.front().x == polygon.back().x && polygon.front().y == polygon.back().y);
        std::vector<aurora::Point> closed_poly = polygon;
        if (!is_closed) closed_poly.push_back(polygon.front());

        std::vector<aurora::Point> unique_poly;
        for (size_t i = 0; i < closed_poly.size() - (is_closed ? 0 : 1); ++i) {
            if (i == 0 || !(closed_poly[i].x == closed_poly[i-1].x && closed_poly[i].y == closed_poly[i-1].y)) {
                unique_poly.push_back(closed_poly[i]);
            }
        }
        if (unique_poly.size() < 3) {
            printf("[Triangulate] 去重后顶点数不足（%zu），无法三角化\n", unique_poly.size());
            return indices;
        }

        // === 构建GPC输入多边形（外轮廓，无内孔） ===
        gpc_polygon gpc_input = {0};
        gpc_input.num_contours = 1;          // 单个外轮廓
        gpc_input.hole = (int*)calloc(1, sizeof(int));  // 外轮廓标记（0表示非孔）
        gpc_input.contour = (gpc_vertex_list*)calloc(1, sizeof(gpc_vertex_list));
        *gpc_input.hole = 0;
        // 确保外轮廓为逆时针顺序（GPC要求）
        if (!IsCounterClockwise(unique_poly)) {
            std::reverse(unique_poly.begin(), unique_poly.end());
        }

        // 填充顶点数据
        gpc_input.contour[0].num_vertices = unique_poly.size();
        gpc_input.contour[0].vertex = (gpc_vertex*)calloc(unique_poly.size(), sizeof(gpc_vertex));
        for (size_t i = 0; i < unique_poly.size(); ++i) {
            gpc_input.contour[0].vertex[i] = ToGpcVertex(unique_poly[i]);
        }

        // === 调用GPC生成三角带 ===
        gpc_tristrip gpc_strip;
        //  printf("[Triangulate] 输入多边形顶点数：%zu\n", polygon.size());
        gpc_polygon_to_tristrip(&gpc_input, &gpc_strip);  // 关键调用
        //  printf("[Triangulate] GPC生成三角带数量：%d\n", gpc_strip.num_strips);
         // === 插入前一个条带最后一个顶点和当前条带第一个顶点（避免连接） ===
        aurora::Point prev_last_vertex;  // 记录前一个条带的最后一个顶点
        bool has_prev = false;
        // bool prev_even = false;

        for (int strip_idx = 0; strip_idx < gpc_strip.num_strips; ++strip_idx) {
            //  printf("[Triangulate] 条带%d顶点数：%d\n", strip_idx, gpc_strip.strip[strip_idx].num_vertices);
            const gpc_vertex_list& strip = gpc_strip.strip[strip_idx];
            if (strip.num_vertices < 3) {
                printf("[Triangulate] 条带%zu顶点数不足（%d），跳过\n", strip_idx, strip.num_vertices);
                continue;
            }

            // 非第一个条带时插入过渡点
            if (has_prev) {
                // 插入前一个条带的最后一个顶点（重复一次）和当前条带的第一个顶点
                result_strip.push_back(prev_last_vertex);  // 前一个条带最后一个顶点（重复）
                // if (prev_even) { // 奇数个点的条带，插两个
                //     result_strip.push_back(prev_last_vertex);  // 前一个条带最后一个顶点（重复）
                //     indices.push_back(indices.size());  // 索引指向新插入的prev_last_vertex
                // }
                result_strip.push_back(aurora::Point(strip.vertex[0].x, strip.vertex[0].y));  // 当前条带第一个顶点
                indices.push_back(indices.size());  // 指向顶点A（前一个条带最后一个）
                indices.push_back(indices.size());  // 索引指向新插入的current_first_vertex
            }

            // 添加当前条带的所有顶点
            for (int i = 0; i < strip.num_vertices; ++i) {
                aurora::Point pt(strip.vertex[i].x, strip.vertex[i].y);
                result_strip.push_back(pt);
                indices.push_back(indices.size());  // 连续索引（顶点数递增）
            }

            // 记录当前条带的最后一个顶点，供下一个条带使用
            prev_last_vertex = aurora::Point(strip.vertex[strip.num_vertices - 1].x, 
                                            strip.vertex[strip.num_vertices - 1].y);
            has_prev = true;
            // prev_even = (strip.num_vertices % 2 == 0);
        }

        // === 清理GPC资源 ===
        gpc_free_tristrip(&gpc_strip);
        free(gpc_input.hole);
        free(gpc_input.contour[0].vertex);
        free(gpc_input.contour);

        // printf("[Triangulate] GPC三角化完成，生成三角形数：%zu\n", indices.size() / 3);
        return indices;
    }
    
    // 辅助函数：简单多边形三角化（耳剪法）
    std::vector<uint32_t> TriangulatePolygon(const std::vector<aurora::Point>& polygon) {
      std::vector<uint32_t> indices;
    if (polygon.size() < 3) {
        printf("[Triangulate] 多边形顶点数不足（%zu），无法三角化\n", polygon.size());
        return indices;
    }

    // === 闭合性检查与自动补全（保持原有逻辑） ===
    bool is_closed = (polygon.front().x == polygon.back().x && polygon.front().y == polygon.back().y);
    std::vector<aurora::Point> closed_poly = polygon;
    if (!is_closed) {
        closed_poly.push_back(polygon.front());
        printf("[Triangulate] 检测到非闭合多边形（原顶点数：%zu），自动补全闭合点，新顶点数：%zu\n", polygon.size(), closed_poly.size());
    } else {
        printf("[Triangulate] 多边形已闭合（顶点数：%zu）\n", closed_poly.size());
    }

    // === 去重并保留原始顺序（保持原有逻辑） ===
    std::vector<size_t> remaining;
    for (size_t i = 0; i < closed_poly.size() - (is_closed ? 0 : 1); ++i) {
        if (i == 0 || !(closed_poly[i].x == closed_poly[i-1].x && closed_poly[i].y == closed_poly[i-1].y)) {
            remaining.push_back(i);
        }
    }
    printf("[Triangulate] 处理后剩余顶点索引（去重后）：");
    for (size_t idx : remaining) printf("%zu ", idx);
    printf("\n");

    // === 关键调整：重新计算绕向符号（可能原绕向判断与实际顶点顺序相反） ===
    float area = 0.0f;
    for (size_t i = 0; i < closed_poly.size(); ++i) {
        size_t j = (i + 1) % closed_poly.size();
        area += (closed_poly[i].x * closed_poly[j].y - closed_poly[j].x * closed_poly[i].y);
    }
    bool is_clockwise = area < 0;
    // 反转凸角符号（根据日志中叉积为负但绕向为逆时针的矛盾，可能实际绕向与计算相反）
    float convex_sign = is_clockwise ? 1.0f : -1.0f;  // 调整符号！
    printf("[Triangulate] 原绕向判断：%s（面积=%.2f），调整后凸角符号：%.1f\n", 
           is_clockwise ? "顺时针" : "逆时针", area, convex_sign);

    size_t max_iterations = remaining.size() * remaining.size() * 100;
    size_t current_iter = 0;

    while (remaining.size() > 2 && current_iter++ < max_iterations) {
        bool found_ear = false;
        printf("\n[Iter %zu/%zu] 当前剩余顶点：", current_iter, max_iterations);
        for (size_t idx : remaining) printf("%zu ", idx);
        printf("\n");

        for (size_t i = 0; i < remaining.size(); ++i) {
            size_t a_idx = (i + remaining.size() - 1) % remaining.size();
            size_t b_idx = i;
            size_t c_idx = (i + 1) % remaining.size();
            size_t a = remaining[a_idx];
            size_t b = remaining[b_idx];
            size_t c = remaining[c_idx];
            const auto& pa = closed_poly[a];
            const auto& pb = closed_poly[b];
            const auto& pc = closed_poly[c];

            // === 调整后凸角判断：允许负叉积（根据新的convex_sign） ===
            float cross = (pb.x - pa.x) * (pc.y - pb.y) - (pb.y - pa.y) * (pc.x - pb.x);
            const float epsilon = 1e-6f;
            if (cross * convex_sign <= epsilon) {  // 符号已调整，允许负叉积作为凸角
                printf("[Ear Check] 顶点%zu-%zu-%zu 非凸角（cross=%.6f）\n", a, b, c, cross);
                continue;
            }

            // === 耳内顶点检查（保持原有逻辑） ===
            bool ear_has_inside_point = false;
            for (size_t k = 0; k < remaining.size(); ++k) {
                size_t p = remaining[k];
                if (p == a || p == b || p == c) continue;
                const auto& pt = closed_poly[p];

                float v0x = pc.x - pa.x, v0y = pc.y - pa.y;
                float v1x = pb.x - pa.x, v1y = pb.y - pa.y;
                float v2x = pt.x - pa.x, v2y = pt.y - pa.y;
                float dot00 = v0x * v0x + v0y * v0y;
                float dot01 = v0x * v1x + v0y * v1y;
                float dot02 = v0x * v2x + v0y * v2y;
                float dot11 = v1x * v1x + v1y * v1y;
                float dot12 = v1x * v2x + v1y * v2y;
                float invDenom = 1.0f / (dot00 * dot11 - dot01 * dot01);
                float u = (dot11 * dot02 - dot01 * dot12) * invDenom;
                float v = (dot00 * dot12 - dot01 * dot02) * invDenom;
                if (u >= -epsilon && v >= -epsilon && u + v <= 1 + epsilon) {
                    printf("[Ear Check] 顶点%zu-%zu-%zu 耳内包含顶点%zu（u=%.6f, v=%.6f）\n", a, b, c, p, u, v);
                    ear_has_inside_point = true;
                    break;
                }
            }
            if (ear_has_inside_point) continue;

            // 确认是有效耳，添加三角形并移除顶点b（保持原有逻辑）
            indices.push_back(a);
            indices.push_back(b);
            indices.push_back(c);
            remaining.erase(remaining.begin() + b_idx);
            found_ear = true;
            printf("[Ear Found] 耳顶点索引：a=%zu, b=%zu, c=%zu（剩余顶点数：%zu）\n", a, b, c, remaining.size());
            break;
        }

        if (!found_ear) {
            printf("[Triangulate] 无有效耳可剪，提前终止迭代\n");
            break;
        }
    }

    // === 后续日志输出（保持原有逻辑） ===
    if (current_iter >= max_iterations) {
        printf("[Triangulate] 达到最大迭代次数（%zu），强制终止\n", max_iterations);
    }
    printf("Triangle Index Count: %zu（Total Triangles: %zu）\n", indices.size(), indices.size() / 3);
    for (size_t i = 0; i < indices.size(); i += 3) {
        printf("Triangle %zu: %zu, %zu, %zu\n", i / 3, indices[i], indices[i + 1], indices[i + 2]);
    }
    printf("===== TriangulatePolygon Debug End =====\n\n");

    return indices;
    }

    RenderLayerBuilder::RenderLayerBuilder()
    {

    }

    void RenderLayerBuilder::BuildRenderLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme,  uint32_t request_logic_level)
    {
        auto data_logic_level = reader.GetTileID().level;
        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto scale = std::pow(2, (long)request_logic_level - (long)data_logic_level);
        BackendPtr backend = MapRenderBackend::CreateBackend(BackendAPIType::API_SKIA);
        float width = scale*kTileSize;
        float height = scale*kTileSize;
        bool ret = backend->Init(width, height, Color(0xFFEAF4F6));
        if (!ret) {
            std::cerr << "  RenderLayerBuilder::BuildRenderLayer backend init failed "  << std::endl;
            printf("  RenderLayerBuilder::BuildRenderLayer backend init failed %d %d %d reqlevel:%d \n", 
                reader.GetTileID().row, reader.GetTileID().col, reader.GetTileID().level, request_logic_level);
            return;
        }

        // 返回的数据和请求的level不一样，记录下来
        // TODO:针对这类特殊的错误，看如何处理
        if (GetDataLevel(request_logic_level) != GetDataLevel(reader.GetTileID().level))
        {
            std::cerr << "  Request Loigc level NOT get right data "  << std::endl;
        }
        // std::cout << "  tile_width : " << reader.GetTileWidth() << std::endl;
        // std::cout << "  tile_id col: " << reader.GetTileID().col << std::endl;
        // std::cout << "  tile_id row: " << reader.GetTileID().row << std::endl;
        // std::cout << "  tile_id level: " << reader.GetTileID().level << std::endl;
        // std::cout << "  request level: " << request_logic_level << std::endl;

        // draw background
        BuildAreaLayer(reader, theme, backend, request_logic_level);
        // draw poline
        BuildPolineLayer(reader, theme, backend, request_logic_level);
        // draw building
        std::vector<BuildingData> buildings;
        // TESTCODE
        // if (reader.GetTileID().row == 11026 && reader.GetTileID().col == 27457 &&  request_logic_level == 17)
        {
            buildings = BuildBuildingLayer(reader, theme, backend, request_logic_level);
        }
        // draw road
        BuildRoadLayer(reader, theme, backend, request_logic_level);
        // draw point
        //BuildPointLayer(reader, theme, backend, request_logic_level);

        CollisionTilePtr ptr = std::make_shared<CollisionTile>(width, height);
        ptr->CreateCollisions(request_logic_level, reader, theme);

        // 存数据的时候用请求的level
        TileID id(reader.GetTileID().row, reader.GetTileID().col , request_logic_level);
// #define TILE_DEBUG
#ifdef TILE_DEBUG
        DrawTileID(id, backend, scale);
#endif
        backend->EndDraw(id, *reader.GetMbr(), ptr, buildings);
    }

    void RenderLayerBuilder::BuildRoadLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        parser::EdgeFeature* edges = nullptr;
        uint32_t edge_count = reader.GetEdges(edges);
        RenderLineLayer linelayer;
        linelayer.Init(backend, theme);
        auto data_logic_level = reader.GetTileID().level;
        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto scale = std::pow(2, request_logic_level - data_logic_level);
        std::vector<aurora::Point> pointlist;

        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();

        // The road needs to be drawn twice
        // first - draw background
        for (uint32_t i = 0; i < edge_count; ++i) {
            aurora::Point pt;
            parser::EdgeFeature& edge = edges[i];

            if (!reader.IsShow(request_logic_level, reader.GetTileID(), edge.zoom_rank)) {
                    continue;
            }

            pointlist.clear();
            for (uint16_t i = 0; i < edge.point_size; ++i) {
                parser::TileXY& pos = edge.points[i];
                pt.x = pos.x() * kTileRtio;
                pt.y = pos.y() * kTileRtio;
                pointlist.push_back(pt);
            }
            linelayer.RenderBoundaries(edge.category, pointlist);
        }

        // second - draw fore
        for (uint32_t i = 0; i < edge_count; ++i) {
            aurora::Point pt;
            parser::EdgeFeature& edge = edges[i];

            if (!reader.IsShow(request_logic_level, reader.GetTileID(), edge.zoom_rank)) {
                    continue;
            }

            pointlist.clear();
            for (uint16_t i = 0; i < edge.point_size; ++i) {
                parser::TileXY& pos = edge.points[i];
                pt.x = pos.x() * kTileRtio;
                pt.y = pos.y() * kTileRtio;
                pointlist.push_back(pt);
            }
            linelayer.RenderRoad(edge.category, pointlist);
        }
    }

    void RenderLayerBuilder::BuildAreaLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        RenderAreaLayer polygonlayer;
        polygonlayer.Init(backend, theme);
        parser::PolygonFeature* polygons = nullptr;
        uint32_t polygon_count = reader.GetPolygons(polygons);
        std::vector<aurora::Point> pointlist;
        aurora::Point pt;
        auto data_logic_level = reader.GetTileID().level;
        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto scale = std::pow(2, request_logic_level - data_logic_level);

        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();

        for (uint32_t i = 0; i < polygon_count; ++i) {
            parser::PolygonFeature& polygon = polygons[i];
            // std::cout << "polygon: " << i << std::endl;
            // std::cout << "  x: " << pos.x() << std::endl;
            // std::cout << "  y: " << (int)polygon.zoom_rank << std::endl;
            // std::cout << "  uuid: " << polygon.uuid << std::endl;
            if (!reader.IsShow(request_logic_level, reader.GetTileID(), polygon.zoom_rank)) {
                    continue;
            }

            pointlist.clear();
            for (uint16_t i = 0; i < polygon.point_size; ++i) {
                parser::TileXY& pos = polygon.points[i];
                pt.x = pos.x() * kTileRtio;
                pt.y = pos.y() * kTileRtio;
                pointlist.push_back(pt);
            }
            polygonlayer.ImplRender(polygon.category, pointlist);
        }
    }

    void RenderLayerBuilder::BuildPointLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        parser::PointFeature* points = nullptr;
        uint32_t point_count = reader.GetPoints(points);
        aurora::Point pt;
        RenderPointLayer pointLayer;
        pointLayer.Init(backend, theme);

        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto data_logic_level = reader.GetTileID().level;
        auto scale = std::pow(2, request_logic_level - data_logic_level);
        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();

        for (uint32_t i = 0; i < point_count; ++i) {
            parser::PointFeature& p = points[i];
                //p.pos.x() << " " << p.pos.y() << ")";
            // std::cout << "|id: " << i;
            // std::cout << "|category: " << p.category;
            // std::cout << "|uuid: " << p.uuid;
            // std::cout << "|weight: " << p.weight;
            pt.x = p.pos.x() * kTileRtio;
            pt.y = p.pos.y() * kTileRtio;
            // printf("  TileXY.x : %d , TileXY.y : %d \n", p.pos.x(), p.pos.y());
            // printf("  pt.x : %f , pt.y : %f \n", pt.x, pt.y);

            if (!reader.IsShow(request_logic_level, reader.GetTileID(), p.zoom_rank)) {
                continue;
            }

            if (p.native_name_size > 0) {
                std::vector<char> name;
                name.resize(p.native_name_size + 1, 0);
                memcpy(&name[0], p.native_name, p.native_name_size);
                char* n = &name[0];
                printf("tile: %d, %d, %d, name: %s\n", (int)reader.GetTileID().level,
                       (int)reader.GetTileID().col, (int)reader.GetTileID().row, n);

                std::string str(n);
                pointLayer.RenderPOI(str, pt);
            }
        }
    }

    std::vector<BuildingData> RenderLayerBuilder::BuildBuildingLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        parser::BuildingFeature* buildings = nullptr;
        uint32_t building_count = reader.GetBuildings(buildings);
        RenderAreaLayer arealayer;
        arealayer.Init(backend, theme);
        aurora::Point pt;
        auto data_logic_level = reader.GetTileID().level;
        auto scale = std::pow(2, request_logic_level - data_logic_level);
        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();
    
        // 替换为 BuildingData 列表
        std::vector<BuildingData> building_list;
        double tile_min_x = reader.GetMbr()->minx();
        double tile_min_y = reader.GetMbr()->miny();
        double tile_max_x = reader.GetMbr()->maxx();
        double tile_max_y = reader.GetMbr()->maxy();
        double scale_x = (tile_max_x - tile_min_x) / reader.GetTileWidth();
        double scale_y = (tile_max_y - tile_min_y) / reader.GetTileWidth();
            
        for (uint32_t i = 0; i < building_count; ++i) {
            // if (building_list.size() > 0) {
            //     break;
            // }
            parser::BuildingFeature& building = buildings[i];
            if (!reader.IsShow(request_logic_level, reader.GetTileID(), building.zoom_rank)) {
                continue;
            }
            // 1. 转换 2D 底面多边形为 base_polygon（原有逻辑）
            std::vector<aurora::Point> base_polygon;
            for (uint16_t j = 0; j < building.point_size; ++j) {
                parser::TileXY& pos = building.points[j];
                pt.x =tile_min_x +  pos.x() * scale_x;
                pt.y =tile_min_y + pos.y() * scale_y;
                pt.x *= kDM5;
                pt.y *= kDM5;
            
                base_polygon.push_back(pt);
            }
    
            // 2. 对 base_polygon 进行三角化， 返回三角带
            std::vector<aurora::Point> result_strip;
            std::vector<uint32_t> base_tri_indices = GpcTriangulatePolygon(base_polygon, result_strip);
            if (base_tri_indices.empty()) {
                printf("----------------TriangulatePolygon failed\n");
                continue;  // 无效多边形，跳过
            }
            #ifdef DEBUG
             printf("\n=====%d %d %d  Building %d Triangulation Debug Info =====\n", reader.GetTileID().row, reader.GetTileID().col,reader.GetTileID().level, i);
            printf("Polygon Vertex Count: %zu\n", result_strip.size());
            for (size_t v = 0; v < result_strip.size(); ++v) {
                printf(" %zu,%.6f,%.6f\n", v, result_strip[v].x / kDM5, result_strip[v].y/kDM5);
            }
            printf("Triangle Index Count: %zu (Total Triangles: %zu)\n", 
                   base_tri_indices.size(), base_tri_indices.size() / 3);
            for (size_t t = 0; t < base_tri_indices.size(); t += 3) {
                printf("%zu, %u, %u, %u\n", 
                       t / 3, base_tri_indices[t], base_tri_indices[t+1], base_tri_indices[t+2]);
            }
            printf("===============================================\n\n");

            printf("-----------------TriangulatePolygon succeed base_tri_indices:%d\n", base_tri_indices.size());
            #endif
            // 3. 生成 3D 顶点和索引（基于三角化结果）
            // 3. 生成 3D 顶点和索引（基于三角化结果）
            
            float building_height = building.floor_count * kFloorHeight;
            std::vector<float> side_vertices;
            std::vector<float> top_vertices;
            std::vector<uint32_t> side_indices;
            std::vector<uint32_t> top_indices;
            Vector3<float> top_normal(0, 0, 1);  // 顶面法线向上

            // ---------------------------
            // 底面顶点（Z=0）和顶面顶点（Z=building_height）
            // ---------------------------
            std::vector<Vector3<float>> bottom_pts;  // 底面顶点（Z=0）
            std::vector<Vector3<float>> top_pts;     // 顶面顶点（Z=building_height）
            std::vector<Vector3<float>> top_strip_pts; // 三角化顶面
            for (const auto& pt : base_polygon) {
                bottom_pts.push_back({pt.x, pt.y, 0.0f});
                top_pts.push_back({pt.x, pt.y, building_height});
            }
            const float kDepthEpsilon = 0.1;  // 避免浮点精度误差的极小值
            for (const auto& pt : result_strip) {
                top_strip_pts.push_back({pt.x, pt.y, building_height + kDepthEpsilon});
            }

            #if 0 // 底面看不到，不需要画
            // ---------------------------
            // 生成底面三角形（基于三角化索引）
            // ---------------------------
            for (size_t j = 0; j < base_tri_indices.size(); j += 3) {
                uint32_t a = base_tri_indices[j];
                uint32_t b = base_tri_indices[j + 1];
                uint32_t c = base_tri_indices[j + 2];

                // 底面三角形顶点（Z=0，法线向下）
                Vector3<float> bottom_normal = top_normal * -1;  // 底面法线向下
                size_t start_idx = vertices.size() / 8;  // 每个顶点占8个float（位置3+法线3+纹理2）

                // 插入顶点数据（保持原有顶点属性逻辑）
                vertices.insert(vertices.end(), {
                    bottom_pts[a].x, bottom_pts[a].y, bottom_pts[a].z,  // 位置
                    bottom_normal.x, bottom_normal.y, bottom_normal.z,    // 法线
                    0.0f, 0.0f  // 纹理坐标（示例）
                });
                vertices.insert(vertices.end(), {
                    bottom_pts[b].x, bottom_pts[b].y, bottom_pts[b].z,
                    bottom_normal.x, bottom_normal.y, bottom_normal.z,
                    1.0f, 0.0f
                });
                vertices.insert(vertices.end(), {
                    bottom_pts[c].x, bottom_pts[c].y, bottom_pts[c].z,
                    bottom_normal.x, bottom_normal.y, bottom_normal.z,
                    0.5f, 1.0f
                });

                // 正确添加索引（指向刚插入的3个顶点）
                indices.push_back(start_idx);
                indices.push_back(start_idx + 1);
                indices.push_back(start_idx + 2);
            }
            #endif
            // ---------------------------
            // 生成侧面（连接底面和顶面的边，改为三角带）
            // ---------------------------
            for (size_t j = 0; j < base_polygon.size(); ++j) {
                size_t next_j = (j + 1) % base_polygon.size();
                Vector3<float> bottom_j = bottom_pts[j];
                Vector3<float> bottom_next = bottom_pts[next_j];
                Vector3<float> top_next = top_pts[next_j];
                Vector3<float> top_j = top_pts[j];

                // 计算侧面法线（垂直于侧面的方向，保持原有逻辑）
                Vector3<float> edge_dir(bottom_next.x - bottom_j.x, bottom_next.y - bottom_j.y, 0);
                Vector3<float> up_dir(0, 0, 1);
                Vector3<float> normal = up_dir.cross(edge_dir).normalization();

                // 插入侧面四边形的4个顶点（顺序：底面j → 底面next_j → 顶面next_j → 顶面j）
                size_t start_idx = side_vertices.size() / 8;  // 当前顶点起始索引
                if (start_idx > 0) { // 第二个面，多插入第一个点，防止两个面之间连起来
                        side_vertices.insert(side_vertices.end(), {  // 顶点0：底面j
                        bottom_j.x, bottom_j.y, bottom_j.z,
                        normal.x, normal.y, normal.z,
                        0.0f, 0.0f  // 纹理坐标（左下）
                    });
                    side_indices.push_back(start_idx);
                    start_idx ++;
                }

                side_vertices.insert(side_vertices.end(), {  // 顶点0：底面j
                    bottom_j.x, bottom_j.y, bottom_j.z,
                    normal.x, normal.y, normal.z,
                    0.0f, 0.0f  // 纹理坐标（左下）
                });
                side_vertices.insert(side_vertices.end(), {  // 顶点3：顶面j
                    top_j.x, top_j.y, top_j.z,
                    normal.x, normal.y, normal.z,
                    0.0f, 1.0f  // 纹理坐标（左上）
                });
                side_vertices.insert(side_vertices.end(), {  // 顶点1：底面next_j
                    bottom_next.x, bottom_next.y, bottom_next.z,
                    normal.x, normal.y, normal.z,
                    1.0f, 0.0f  // 纹理坐标（右下）
                });
                side_vertices.insert(side_vertices.end(), {  // 顶点2：顶面next_j
                    top_next.x, top_next.y, top_next.z,
                    normal.x, normal.y, normal.z,
                    1.0f, 1.0f  // 纹理坐标（右上）
                });
               

                // 三角带索引：[start_idx, start_idx+1, start_idx+2, start_idx+3]
                // 对应三角形：(0,1,2), (1,2,3)
                side_indices.push_back(start_idx);
                side_indices.push_back(start_idx + 1);
                side_indices.push_back(start_idx + 2);
                side_indices.push_back(start_idx + 3);
                // 2个侧面不要连起来
                 side_vertices.insert(side_vertices.end(), {  // 顶点2：顶面next_j
                    top_next.x, top_next.y, top_next.z,
                    normal.x, normal.y, normal.z,
                    1.0f, 1.0f  // 纹理坐标（右上）
                });
                side_indices.push_back(start_idx + 3);
            }
            
            // 生成顶面三角形（基于三角带索引）
            // 原循环：for (size_t j = 0; j < base_tri_indices.size(); j += 3)
            // 改为直接遍历三角带的连续顶点序列
            if (!base_tri_indices.empty()) {
            //     printf("[Building] 顶面三角化索引数：%zu（应生成 %zu 个三角形）\n", 
            //   base_tri_indices.size(), base_tri_indices.size() / 3);
            // printf("[Building] result_strip顶点数：%zu（索引应小于此值）\n", result_strip.size());
                size_t start_idx = top_vertices.size() / 8;  // 当前顶点起始索引
                for (size_t j = 0; j < base_tri_indices.size(); ++j) {
                    uint32_t vertex_idx = base_tri_indices[j];  // 三角带中的顶点索引
                    const auto& pt = top_strip_pts[vertex_idx];  // 顶面顶点（Z=building_height）

                    // 插入顶点数据（位置+法线+纹理坐标）
                    top_vertices.insert(top_vertices.end(), {
                        pt.x, pt.y, pt.z,          // 位置
                        top_normal.x, top_normal.y, top_normal.z,  // 法线（向上）
                        0.0f, 0.0f  // 纹理坐标（示例，可根据需求调整）
                    });
                }

                // 三角带绘制时，索引直接使用0,1,2,3,...（连续序列）
                for (size_t j = 0; j < base_tri_indices.size(); ++j) {
                    top_indices.push_back(start_idx + j);
                }
                
            }

            // arealayer.RenderBuilding(building.category, base_polygon);
            BuildingData building_data;
            building_data.top_vertices = top_vertices;
            building_data.side_vertices = side_vertices;
            building_data.top_indices = top_indices;
            building_data.side_indices = side_indices;
            building_data.floor_count = building.floor_count;
            building_data.base_polygon = base_polygon;
            building_data.category = building.category;  // 假设 BuildingData 添加 category 字段
            building_list.push_back(building_data);
        }
    
        // 按深度排序（Y大的先画）
        std::sort(building_list.begin(), building_list.end(),
            [](const BuildingData& a, const BuildingData& b) {
                return a.floor_count > b.floor_count;
            });
    
        // 调用渲染接口（假设 RenderAreaLayer 支持 BuildingData 列表）
         
        return building_list;
    }

    void RenderLayerBuilder::BuildPolineLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        parser::PolylineFeature* polylines = nullptr;
        uint32_t polyline_count = reader.GetPolylines(polylines);

        RenderLineLayer linelayer;
        linelayer.Init(backend, theme);
        auto data_logic_level = reader.GetTileID().level;
        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto scale = std::pow(2, request_logic_level - data_logic_level);
        std::vector<aurora::Point> pointlist;
        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();

        aurora::Point pt;
        for (uint32_t i = 0; i < polyline_count; ++i) {
            parser::PolylineFeature& polyline = polylines[i];

            if (!reader.IsShow(request_logic_level, reader.GetTileID(), polyline.zoom_rank)) {
                continue;
            }

            pointlist.clear();
            for (uint16_t i = 0; i < polyline.point_size; ++i) {
                parser::TileXY& pos = polyline.points[i];
                pt.x = pos.x() * kTileRtio;
                pt.y = pos.y() * kTileRtio;
                pointlist.push_back(pt);
                // std::cout << "Poline pos : " << pos.x() << " " << pos.y() << std::endl;
            }
            linelayer.ImplRender(polyline.category, pointlist);
        }
    }

    void RenderLayerBuilder::DrawTileID(const TileID& id, BackendPtr backend, float scale)
    {
         std::vector<aurora::Point> points;
         points.push_back(Point(0.,0.));
         points.push_back(Point(512* scale, 0.));
         points.push_back(Point(512* scale, 512* scale));
         points.push_back(Point(0.,512* scale));
         backend->SetFillStyle(aurora::STYLE_STROKE);
         backend->SetAntiAlias(true);
         backend->SetLineStyle(aurora::CAP_ROUNT, aurora::JOIN_ROUND);
         backend->SetColor(Color::Red);
         backend->SetLineWidth(2);
         backend->DrawPolyline(points);
         TextStyle style(26, 1, Color(Color::Red), Color(Color::White), "Arial Unicode.ttf");
         TextHolderPtr holder;
         backend->DrawText(std::to_string(id.x_) + "," + std::to_string(id.y_) + "," + std::to_string(id.z_), {10, 20}, 0, style, holder);
    }

} //namespace
