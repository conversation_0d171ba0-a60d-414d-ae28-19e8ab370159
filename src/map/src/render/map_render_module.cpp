/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

 #include "map_render_module.h"
 #include "map_render_service.h"
 #include "map_data_provider.h"

 namespace aurora {
 
    IMapRenderService::IMapRenderService() {}
    
    IMapRenderService::~IMapRenderService() {}
     
    int32_t IMapRenderService::Init(const AuroraMapConfig& map_config, void* themeconfig)
    {
        render_service_ = std::make_unique<MapRenderService>(map_config);
        auto dataprovider_shared = std::make_shared<MapDataProvider>(map_config.data_path_);
        if (nullptr == dataprovider_shared || map_config.data_path_.empty()) {
            printf("Init FAILED!!!! new dataprovider is null or map_config.data_path_ is empty,  CHECK IT\n");
            return -1;
        }

        return render_service_->Init(dataprovider_shared, themeconfig);
    }

    int32_t IMapRenderService::SetScreenSize(uint32_t width, uint32_t height)
    {
        if (render_service_ != nullptr)
        {
            render_service_->SetScreenSize(width, height);
        }
        return 0;
    }
 
    void IMapRenderService::Destory()
    {
        if (render_service_ != nullptr)
        {
            render_service_->Destory();
        }
    }
 
    int32_t IMapRenderService::SetMapCenter(double lon, double lat, uint32_t animation_duration_ms)
    {
        if (render_service_ != nullptr)
        {
            return render_service_->SetMapCenter(lon, lat, animation_duration_ms);
        }
        return 0;
    }
 
    Point64 IMapRenderService::GetMapCenter()
    {
        Point64 center;
        if (render_service_ != nullptr)
        {
            auto center_lonlat = render_service_->GetMapCenter();
            center.x_ = center_lonlat.x;
            center.y_ = center_lonlat.y;
        }
        return center;
    }
     
    int32_t IMapRenderService::SetMapScale(double scale, uint32_t animation_duration_ms)
    {
        if (render_service_ != nullptr)
        {
            return render_service_->SetMapScale(scale, animation_duration_ms);
        }
        return 0;
    }
 
    void IMapRenderService::RenderMap()
    {
        if (render_service_ != nullptr)
        {
            render_service_->RenderMap();
        }
    }
 
    int32_t IMapRenderService::SetMapRotation(float rot, uint32_t animation_duration_ms)
    {
        if (render_service_ != nullptr)
        {
            render_service_->SetMapRotation(rot, animation_duration_ms);
        }
        return 0;
    }
 
    Point64 IMapRenderService::ScreenToMap(const Point64& pt)
    {
        Point64 pt_ret; 
        if (render_service_!= nullptr)
        {
            auto ret = render_service_->ScreenToMap({pt.x_, pt.y_});
            pt_ret.x_ = ret.x;
            pt_ret.y_ = ret.y;
        }
        return pt_ret;
    }
 
    Point64 IMapRenderService::MapToScreen(const Point64& geo)
    {
        Point64 pt; 
        if (render_service_!= nullptr)
        {
            auto ret = render_service_->MapToScreen({geo.x_, geo.y_});
             
            pt.x_ = ret.x;
            pt.y_ = ret.y;
        }
        return pt;
    }

    void IMapRenderService::MoveMap(double delta_x, double delta_y, bool move_end, uint32_t animation_duration_ms)
    {
        if (render_service_!= nullptr)
        {
            render_service_->MoveMap(delta_x, delta_y, move_end, animation_duration_ms);
        }
    }

    float IMapRenderService::GetMapScale()
    {
        if (render_service_!= nullptr)
        {
            return render_service_->GetMapScale();
        }
        return 0;
    }

    void IMapRenderService::SetMapPitch(float pitch, uint32_t animation_duration_ms)
    {
        if (render_service_!= nullptr)
        {
            render_service_->SetMapPitch(pitch, animation_duration_ms);
        }
    }

    float IMapRenderService::GetMapPitch()
    {
        if (render_service_!= nullptr)
        {
            return render_service_->GetMapPitch();
        }
        return 0;
    }

    void IMapRenderService::FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms)
    {
        if (render_service_!= nullptr)
        {
            render_service_->FlyTo(lon, lat, dest_scale, animation_duration_ms);
        }
    }

    void IMapRenderService::SetPath(uint32_t type, std::vector<Point2d>& path)
    {
        if (render_service_!= nullptr)
        {
            render_service_->SetPath(type, path);
        }
    }

    void IMapRenderService::ClearPath(uint32_t type)
    {
        if (render_service_!= nullptr)
        {
            render_service_->ClearPath(type);
        }
    }

    int32_t IMapRenderService::SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name)
    {
        if (render_service_!= nullptr)
        {
            return render_service_->SetMarkInfo(mark_type, mark_lnglat_pos, mark_anchor, mark_name);
        }
        return -1;
    }

    void IMapRenderService::UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name)
    {
        if (render_service_!= nullptr)
        {
            render_service_->UpdateMarkInfo(mark_type, mark_id, mark_anchor, mark_name);
        }
    }

    void IMapRenderService::ClearMark(uint16_t mark_type, int32_t mark_id)
    {
        if (render_service_!= nullptr)
        {
            render_service_->ClearMark(mark_type, mark_id);
        }
    }

    void IMapRenderService::UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree)
    {
        if (render_service_!= nullptr)
        {
            render_service_->UpdateMarkInfo(mark_type, mark_id, mark_lnglat_pos, degree);
        }
    }
 } //namespace 
 /* EOF */
