/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

 #include "map_render_manager.h"
 #include "map_render_service.h"
 #include "map_render_theme.h"
 #include "map_render_scene.h"
 #include "map_render_module.h"
#include "mark/mark_manager.h"

 namespace aurora {

     MapRenderService::MapRenderService(const AuroraMapConfig& map_config)
     : render_manager_(nullptr)
     , render_theme_(nullptr)
     {
        map_config_ = std::make_shared<AuroraMapConfig>(map_config);
     }

     MapRenderService::~MapRenderService()
     {
     }

     int32_t MapRenderService::Init(MapDataProviderPtr data_provider, void* themeconfig)
     {
         //render_thread_ = new std::thread(RenderService::run, *this);
         if (render_theme_ == nullptr)
         {
             render_theme_ = new MapRenderTheme();
             render_theme_->Init(themeconfig);
         }

         LoadMapConfig(); // themeconfig->
         if (map_config_ == nullptr) { // 配置信息加载失败
             printf("Init FAILED!!!! map_config_ is null,  CHECK IT\n");
            return -1;
         }
         if (render_manager_ == nullptr )
         {
             render_manager_ = new MapRenderManager(map_config_->default_fontPath_, map_config_->default_fontName_);
             render_manager_->Init(data_provider, render_theme_);
         }

         MarkManager::Instance().Init(map_config_->default_mark_path_.c_str());

         return 0;
     }

     void MapRenderService::Destory()
     {
         if (render_manager_ != nullptr)
         {
             render_manager_->Destory();
             delete render_manager_;
             render_manager_ = nullptr;
         }

         if (render_theme_ != nullptr)
         {
             render_theme_->Destory();
             delete render_theme_;
             render_theme_ = nullptr;
         }
     }

     void MapRenderService::RenderMap()
     {
         if (render_manager_ != nullptr)
         {
             render_manager_->RenderMap();
         }
     }
 
     int32_t MapRenderService::SetMapScale(float scale, uint32_t animation_duration_ms)
     {
         if (render_manager_ != nullptr)
         {
             render_manager_->SetMapScale(scale, animation_duration_ms);
         }

         return 0;
     }

     int32_t MapRenderService::SetMapCenter(double lon, double lat, uint32_t animation_duration_ms)
     {
         if (render_manager_ != nullptr)
         {
             render_manager_->SetMapCenter(lon, lat, animation_duration_ms);
         }

         return 0;
     }

     Vector2<double> MapRenderService::GetMapCenter()
     {
        if (render_manager_ != nullptr)
        {
            auto lonlat = render_manager_->GetMapCenter();
            return {lonlat.longitude_, lonlat.latitude_};
        }
        return Vector2<double>();
     }

     int32_t MapRenderService::SetMapRotation(float rot, uint32_t animation_duration_ms)
     {
         if (render_manager_ != nullptr)
         {
             render_manager_->SetMapRotation(rot, animation_duration_ms);
         }

         return 0;
     }

     int32_t MapRenderService::SetScreenSize(uint32_t width, uint32_t height)
     {
         if (render_manager_ != nullptr)
         {
             render_manager_->SetScreenSize(width, height);
         }
         return 0;
     }

     Vector2<double>  MapRenderService::ScreenToMap(const Vector2<int32_t>& pt)
     {
         if (render_manager_ != nullptr)
         {
             return render_manager_->ScreenToMap(pt);
         }
         return Vector2<double>();
     }

     Vector2<int32_t> MapRenderService::MapToScreen(const Vector2<double>& geo)
     {
         if (render_manager_ != nullptr)
         {
            return  render_manager_->MapToScreen(geo);
         }
         return Vector2<int32_t>();
     }

     void MapRenderService::LoadMapConfig()
     {
         if (render_theme_ != nullptr)
         {
            render_theme_->LoadThemeconfig();
         }
     }

     void MapRenderService::MoveMap(double delta_x, double delta_y, bool move_end, uint32_t animation_duration_ms)
     {
         if (render_manager_!= nullptr)
         {
             render_manager_->MoveMap(delta_x, delta_y, move_end, animation_duration_ms);
         }
     }

     float MapRenderService::GetMapScale()
     {
         if (render_manager_!= nullptr)
         {
             return render_manager_->GetMapScale();
         }
         return 0;
     }

     void MapRenderService::SetMapPitch(float pitch, uint32_t animation_duration_ms)
     {
         if (render_manager_!= nullptr)
         {
             render_manager_->SetMapPitch(pitch, animation_duration_ms);
         }
     }

     float MapRenderService::GetMapPitch()
     {
         if (render_manager_!= nullptr)
         {
             return render_manager_->GetMapPitch();
         }
         return 0;
     }

     //  默认flyto最少5s动画
     void MapRenderService::FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms)
     {
        if (render_manager_!= nullptr)
        {
            render_manager_->FlyTo(lon, lat, dest_scale, animation_duration_ms);
        }
     }

     void MapRenderService::SetPath(uint32_t type, std::vector<Point2d>& path)
     {
        if (render_manager_!= nullptr)
        {
            render_manager_->SetPath(type, path);
        }
     }

     void MapRenderService::ClearPath(uint32_t type)
     {
        if (render_manager_!= nullptr)
        {
            render_manager_->ClearPath(type);
        }
     }

     int32_t MapRenderService::SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name)
     {
        if (render_manager_!= nullptr)
        {
            return render_manager_->SetMarkInfo(mark_type, mark_lnglat_pos, mark_anchor, mark_name);
        }
        return -1;
     }


     void MapRenderService::UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name)
     {
        if (render_manager_!= nullptr)
        {
            render_manager_->UpdateMarkInfo(mark_type, mark_id, mark_anchor, mark_name);
        }
     }

     void MapRenderService::UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree)
     {
        if (render_manager_!= nullptr)
        {
            render_manager_->UpdateMarkInfo(mark_type, mark_id, mark_lnglat_pos, degree);
        }
     }

     void MapRenderService::ClearMark(uint16_t mark_type, int32_t mark_id)
     {
        if (render_manager_!= nullptr)
        {
            render_manager_->ClearMark(mark_type, mark_id);
        }
     }
 } //namespace
 /* EOF */
