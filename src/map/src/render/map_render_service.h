/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_service.h
 * @brief Declaration file of class MapRenderService.
 * @attention used for C/C++ only.
 */

#ifndef MAPRENDE_RENDERSERVICE_H_
#define MAPRENDE_RENDERSERVICE_H_

#include <thread> 
#include <cstdint> 
#include "util_vector2.h"

namespace aurora {
    
class MapDataProvider;

typedef std::shared_ptr<MapDataProvider> MapDataProviderPtr;

class IMapRenderManager; 
class MapRenderThemeIF;
struct AuroraMapConfig;
/**
* class breif description
*
* MapRenderService
*
*/
class MapRenderService {
public:
    MapRenderService(const AuroraMapConfig& map_config);                
    ~MapRenderService();
    
    int32_t Init(MapDataProviderPtr data_provider, void* themeconfig);

    void Destory();

    void RenderMap();

    int32_t SetMapScale(float scale, uint32_t animation_duration_ms = 0);

    int32_t SetMapCenter(double lon, double lat, uint32_t animation_duration_ms = 0);

    Vector2<double> GetMapCenter();

    int32_t SetMapRotation(float rot, uint32_t animation_duration_ms = 0);

    int32_t SetScreenSize(uint32_t width, uint32_t height);

    Vector2<double> ScreenToMap(const Vector2<int32_t>& pt);

    Vector2<int32_t> MapToScreen(const Vector2<double>& geo);

    // 注意：地图内部左下角为0,0点，所以如果窗口给的y是按左上角做为0，0点，delta_y应该取反传入
    void MoveMap(double delta_x, double delta_y, bool move_end = false, uint32_t animation_duration_ms = 0);

    float GetMapScale();

    void SetMapPitch(float pitch, uint32_t animation_duration_ms = 0);

    float GetMapPitch();

    //  默认flyto最少5s动画
    void FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms);

    void SetPath(uint32_t type, std::vector<Point2d>& path);

    void ClearPath(uint32_t type);

    // 返回 >= 0 MarkId, < 0 失败
    int32_t  SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name);

    void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name);

    void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree = 0.f );

    // mark_id < 0 清除所有mark_type
    void ClearMark(uint16_t mark_type, int32_t mark_id = -1);

public: // For_Test
    IMapRenderManager* GetRenerManager() { return render_manager_; };

private:
    void LoadMapConfig();

private:
    IMapRenderManager* render_manager_ {nullptr}; 
    MapRenderThemeIF*   render_theme_ {nullptr};
    std::thread*        render_thread_ {nullptr};
    std::shared_ptr<AuroraMapConfig>    map_config_ {nullptr};
};
    
} //namespace 

#endif //MAPRENDE_RENDERSERVICE_H_
/* EOF */