/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_camera.h
 * @brief Declaration file of class MapRenderCamera.
 * @attention used for C/C++ only.
 */

#ifndef MAPRENDER_MAPRENDERCAMERA_H_
#define MAPRENDER_MAPRENDERCAMERA_H_

#include "render_geometry_type.h"
#include "util_matrix4.h"
#include "map_render_camera_if.h"
#include "util_lonlat.h"
#include "aabb2.h"
#include <unordered_map>
#include <vector>
#include <atomic>
#include "spin_shared_mutex.h"

namespace aurora {

enum class CameraType : uint8_t {  // 根据比例尺，pitch模式等自动计算
    perspective = 0,
    isometric,
    flat,
};


/**
* class breif description
*
* MapRenderCamera
*
*/
class MapRenderCamera :public MapRenderCameraIF {

public:
    MapRenderCamera();              
    ~MapRenderCamera();

    virtual void SetViewport(int left, int top, int w, int h);

    virtual bool GetProjViewMatrix(Matrix4<double>& projViewMatrix, const TimePoint& now = Clock::now());

    virtual bool GetOrthViewMatrix(Matrix4<double>& orthViewMatrix, const TimePoint& now = Clock::now());

    virtual bool CalcRenderTiles(std::vector<TileID>& tiles);

    virtual bool GetMapCenter(Lonlat& mapCenter, const TimePoint& now = Clock::now());

    virtual Lonlat GetMapCenter(const TimePoint& now = Clock::now());

    virtual void SetMapCenter(double lon, double lat, const AnimationOptions& options = {});

    virtual void SetMapScale(float scale, const AnimationOptions& options = {});

    virtual float GetMapScale(const TimePoint& now = Clock::now());

    virtual void SetMapRotation(float rot, const AnimationOptions& options = {});

    virtual float GetMapRotation(const TimePoint& now = Clock::now());

    virtual void SetYawAngle(float yaw_angle, const AnimationOptions& options = {});

    virtual void SetPitchAngle(float pitch_angle, const AnimationOptions& options = {});

    virtual void SetRollAngle(float roll_angle, const AnimationOptions& options = {});

    virtual void SetPitchMode(const PitchAngleMode& pitch_mode, const AnimationOptions& options = {});

    virtual void Update();

    virtual void SetScreenSize(uint32_t width, uint32_t height, bool left_bottom_0_0 = true);

    virtual void OnMove(int32_t x, int32_t y, const bool move_end = false, const AnimationOptions& options = {});

    virtual bool Screen2WorldOld(double x, double y, double& world_x, double& world_y);
    virtual bool Screen2World(double x, double y, double& world_x, double& world_y);

     virtual bool World2Screen(double world_x, double world_y, double& x, double& y);
    
    virtual bool World2ScreenOld(double world_x, double world_y, double& x, double& y);

   

    virtual bool MercatorWorld2Screen(double world_x, double world_y, double& x, double& y);

    virtual void SetDpi(uint16_t dpi);

    virtual void OnMove(float deltax, float deltay, const bool move_end = false, const AnimationOptions& options = {});

    virtual bool UpdateTileMbr(const std::map<int64_t, AABB2<Point2d> >& tile_area_map);

    virtual  bool GetTilePosMatrix(const int64_t& tile_id, Matrix4<double>& orthViewMatrix);

    virtual void GetViewSize(uint32_t& width, uint32_t& height);

    virtual float GetMapPitchAngle(const TimePoint& now = Clock::now());

    virtual bool IsAnimationFinished(const TimePoint& now = Clock::now());

    virtual void FlyTo(double lon, double lat, unsigned int dest_scale, AnimationOptions& options);

    virtual void SetMinScale(uint32_t min_scale);

    virtual void SetMaxScale(uint32_t max_scale);

    virtual uint32_t GetMinScale();

    virtual uint32_t GetMaxScale();

    virtual AABB2<Point2d> GetMapRange();

    virtual void SetSkyBoxHeight(uint32_t height);

    virtual uint32_t GetSkyBoxHeight();

    virtual void SetShowSkyBoxPitchAngle(float pitch_angle);

    virtual float GetShowSkyBoxPitchAngle();

    virtual uint32_t GetEffectiveHeight();

    virtual AnimationOptions GetCurAnimationOptions();

private:

    void UpdateMatrixes(const TimePoint& now = Clock::now());

    void UpdateProjViewMatrix(const TimePoint& now = Clock::now());

    void UpdateOrthViewMatrix(const TimePoint& now = Clock::now());

    void UpdateModelMatrix(const TimePoint& now = Clock::now());

    void UpdateViewMatrix(const TimePoint& now = Clock::now());

    void UpdateProjMatrix(const TimePoint& now = Clock::now());

    void UpdateCameraHeight(const TimePoint& now = Clock::now());
    
    void MarkDirtyByKey(const std::string& key);

    virtual double GetLonSpan(const TimePoint& now = Clock::now());

    // 动画的时候带小数的比例尺
    float GetAccurateScale(const TimePoint& now = Clock::now());

    float GetRectifiedPitchAngle(const TimePoint& now = Clock::now());


    virtual bool IsDirty() {
        return projMatrixDirty_ || orthMatrixDirty_ || modelMatrixDirty_ || viewMatrixDirty_ || cameraHeightDirty_ || animationDirty_;
    }

    void GetFlyToZoomOutScale(const Lonlat& start_pos, const Lonlat& dest_pos, unsigned int dest_scale, unsigned int& zoom_out_scale);

    void CancelAnimation(bool finishfun_call = false);

    void UpdateCurrentBBox();

    // 内部之间调用，不用update，不然可能会死循环调用
    bool Screen2WorldInternal(double x, double y, double& world_x, double& world_y);



private:
    Lonlat map_center_  = {0.0, 0.0};
    float map_scale_ = 0;  // 外部设进来的逻辑比例尺
    float data_scale_ = 0; // 数据比例尺
    uint32_t max_zoom_level_ = 0;
    uint32_t min_zoom_level_ = 0;
    float logic_data_scale = 1.0f; // 逻辑比例尺/数据比例尺的放大缩小系数
    float yaw_angle_ = 0.0; 
    float pitch_angle_ = 0.0; // 角度的形式
    float roll_angle_ = 0.0;   // 角度的形式
    PitchAngleMode pitch_mode_ = PITCH_ANGLE_MODE_FIXED;
    int view_left_ = 0;
    int view_top_ = 0;
    int view_width_ = 0;
    int view_height_ = 0;
    float dpi_ = 0.0;
    float camera_heigth_ = 0.0;
    
    Matrix4<double> m_view_;
    Matrix4<double> m_orthoViewport_;
    Matrix4<double> m_proj_;
    Matrix4<double> m_proj_orth_;
    Matrix4<double> m_viewProj_;
    Matrix4<double> m_viewProjorth_;
    Matrix4<double> m_model_;

    // TODO:这些参数后续全部外部可配，目前先写死
    float m_aspect_ = 1.0f;
    float m_pixelScale_ = 1.0f;
    float m_fov_ = 0.25 * M_PI;
    float m_maxPitch_ = 60.f;
    float m_minPitch_ = 0.f;
    float m_minZoom_ = 0.f;
    float m_maxZoom_ = 20.5f;
    uint16_t tile_size_pix_ = 512; // 一个tile画多少个像素，由外部定义，默认512


    std::atomic<bool>  projMatrixDirty_ = false;
    std::atomic<bool>  orthMatrixDirty_ = false;
    std::atomic<bool> modelMatrixDirty_ = false;
    std::atomic<bool>  viewMatrixDirty_ = false;
    std::atomic<bool>  cameraHeightDirty_ = false;
    std::atomic<bool>  animationDirty_ = false;


    float data_scale_in_pixel_ = 1.0;

    int32_t screen_width_ = 0;  // 和view_width不一定一样
    int32_t screen_height_ = 0;


    float m_width;  // 对应的比例尺下的实际屏幕宽代表的m数
    float m_height;
    double worldTileSize_ = 1220.0; // 一个tile代表多少米, 给默认15级的tile的大小
    double lonSpan_ = 0.010986; // 一个tile跨的经度范围，给默认15级的tile的大小

    CameraType m_type = aurora::CameraType::flat;


    std::map<int64_t, AABB2<Point2d> > tile_area_map_;


    
    AnimationOptions  animation_options_;
    FlyToState flyto_state_;
    // 动画相关
    // bool is_scaling_animation_ = false;       // 是否处于比例尺动画中
    // unsigned int start_scale_ = 0;            // 动画起始比例尺
    // unsigned int target_scale_ = 0;           // 动画目标比例尺
    // float animation_duration_ = 0.5f;         // 动画持续时间（秒）
    // std::chrono::steady_clock::time_point animation_start_time_;  // 动画开始时间

    SpinSharedMutex map_render_camera_mutex_; // 可重入读写锁

    bool screen_left_bottom_0_0_ = true;

    AABB2<Point2d> map_current_boundingbox_;

    uint32_t sky_box_height_ = 100;

    float show_sky_box_pitch_angle_ = 40.0f;

    // 惯性move成员
    TimePoint last_move_time_ = TimePoint::min();  // 上次移动时间戳
    Point2d last_move_pos_;  // 上次移动位置（屏幕坐标）
    double last_move_speed_ = 0.0; // 上次移动速度（像素/秒）
   
private:
    // 新增：dirty 标志监控列表（键为操作类型，值为需要触发的标志指针列表）
    std::unordered_map<std::string, std::vector<std::atomic<bool>*>> dirty_watch_list_;


};
    
} //namespace 

#endif //MAPRENDER_MAPRENDERCAMERA_H_
/* EOF */
