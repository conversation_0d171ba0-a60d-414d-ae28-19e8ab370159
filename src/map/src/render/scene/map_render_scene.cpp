/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "map_render_scene.h"
#include "map_render_camera.h"
#include "map_render_backend.h"
#include "map_render_manager.h"
#include "i_map_render_scene.h"
#include "render_tile_layer.h"
#include "render_raster_tile.h"

#include "include/core/SkPixmap.h"
#include "map_render_context.h"
#include "scale_converter.h"
#include <unistd.h>
#include "render_data_cache_lru.h"
#include "annotation/annotation_manager.h"
#include "map_render_backend_gl.h"

namespace aurora {

    moodycamel::ConcurrentQueue<TileImagePtr>  MapRenderScene::raster_tiles_;

    MapRenderScene::MapRenderScene()
    {
        
    }   

    MapRenderScene::~MapRenderScene()
    {
        render_list_.clear();
    }
    
    int32_t MapRenderScene::Init()
    {
        camera_ = std::make_shared<MapRenderCamera>();
        backend_ = MapRenderBackend::CreateBackend(BackendAPIType::API_GLES);
        backend_->Init(kTileSize, kTileSize, Color(0xFFEAF4F6));
        state_.AttachCamera(camera_);
        return 0;
    }

    void MapRenderScene::Destory()
    {

    }

    void MapRenderScene::SetCamera(CameraPtr camera)
    {
        // camera_ = camera;

        // camera_ set view port for test
    }
 
    CameraPtr MapRenderScene::GetCamera() const
    {
        return camera_;
    }

    void MapRenderScene::UpdateMapCenter(double lon, double lat)
    {

    }

    void MapRenderScene::SetCurRenderMapTiles(const std::vector<std::shared_ptr<CacheTile>>& current_render_tiles, const std::vector<TileID>& all_tile_ids) {
        if (currRenderTiles_ != all_tile_ids) {
            UpdatePredictVisbleBBox();
            current_visible_bbox_ = camera_->GetMapRange();
        }
        current_render_tiles_ = current_render_tiles;
        currRenderTiles_ = all_tile_ids;

    }

    void MapRenderScene::RenderMap()
    {
        // TileImagePtr tileImage = nullptr;
        // if (MapRenderScene::PopRasterTileToQueue(tileImage)) 
        // {
        //     clock_t sttime = clock();
        //     SkPixmap pixmap;
        //     if (tileImage->image_->peekPixels(&pixmap)) 
        //     {
        //         std::shared_ptr<RenderTileLayer> tile = std::make_shared<RenderTileLayer>();
        //         tile->SetTileID(tileImage->tile_id_);
        //         tile->SetRasterTileData(pixmap.addr(), aurora::FORMAT_RGBA, 512, 512);
        //         backgrounds_.push_back(tile);
        //         // tiles_.insert_or_assign(tileImage->tile_id_, tile);
        //     }
        //     clock_t edtime = clock();
        //     printf("time to skia peekPixels:( %f ms)\n", double(edtime-sttime)/1000);
        //     printf("time to skia peekPixels:( %f s)\n", double(edtime-sttime)/CLOCKS_PER_SEC);
        // }
        uint32_t screen_width_ = 1;
        uint32_t screen_height_ = 1;
        state_.GetScreenSize(screen_width_, screen_height_);
        glViewport(0, 0, screen_width_, screen_height_);

        MapRenderContext* context = backend_->GetContext();
        if (context != nullptr)
        {
            context->Clear();
        }
        TimePoint now = Clock::now();
        // 判断是否所有数据都收到
        bool all_received = true;
        for (auto& tileid : currRenderTiles_) {
            if (std::find_if(current_render_tiles_.begin(), current_render_tiles_.end(), [&tileid](const std::shared_ptr<CacheTile>& tile) {
                return tile && tile->raster_tile && tile->raster_tile->GetTileID() == tileid;
            }) == current_render_tiles_.end()) {
                // #ifdef DEBUG
                printf(" ==========this frame %d - %d tileid %d %d %d not received========== \n", 
                    currRenderTiles_.size(), current_render_tiles_.size(), tileid.x_, tileid.y_, tileid.z_);
                // #endif
                all_received = false;
                break;
            }
        }
        // 简单点判断size相等就认为全部数据都收到了，不做上面这么复杂的判断
       
        // if (currRenderTiles_.size() == current_render_tiles_.size()) {
        //     all_received = true;
            
        // }
        // else {
        //     all_received = false;
        //     printf(" ==========this frame %d - %d all data NOT received [%d]========== \n", currRenderTiles_.size(), current_render_tiles_.size(), all_received);
        // }
         #ifdef DEBUG
            printf("tick:%d ==========this frame %d - %d all data received [%d]========== \n", 
                std::chrono::duration_cast<std::chrono::milliseconds>(Clock::now().time_since_epoch()).count(),
                currRenderTiles_.size(), current_render_tiles_.size(), all_received);
        #endif
        UpdatePredictVisbleBBox();
        
        for (auto& cache_tile: current_render_tiles_) {
            if (cache_tile && cache_tile->raster_tile) {
                 cache_tile->raster_tile->Render(camera_, now );
            }
        }
       
        // draw background tile
        for(auto& tile : backgrounds_)
        {
            int tiledataLevel = GetDataLevel(tile->GetTileID().z_);
            int mapStateDataLevel = GetDataLevel(state_.GetMapScale());
            if (tile && (tiledataLevel == mapStateDataLevel))
            {   
                tile->Render(camera_);
            }
        }
        
        // camera_->CalcRenderTiles(currRenderTiles_);
        // for (auto& tileid : currRenderTiles_)
        // {
        //    if (tiles_[tileid] != nullptr)
        //    {
        //         tiles_[tileid]->Render(camera_);
        //    }
        // }

        for(auto& renderlayer : layer_list)
        {
            if (renderlayer)
            {
                renderlayer->Render(camera_);
            }
        }
        bool is_animation_move = false;
        if (camera_ && !all_received) {
            auto animation_options = camera_->GetCurAnimationOptions();
            if (animation_options.type ==  AnimationType::kAnimationTypeMoveTo) {
                // move的时候，如果target和当前的center距离不是特别大的move，all_received 没收全也画poi和building
                // Point2d target_center;
                // animation_options.GetAnimatePos(target_center, Clock::now() + std::chrono::milliseconds(200));

                is_animation_move = true;
            }
        }
        

        if (all_received || is_animation_move)
        {
            AnnotationManager::Instance().Clear();
            building_layer_.ClearBuildings();
           
            for (auto tile : current_render_tiles_) {
                if (tile && tile->collision_tile) {
                    AnnotationManager::Instance().AddTileData(tile->collision_tile);
                }

                if (tile && !tile->building_list.empty()) {
                    building_layer_.AddBuildings(tile->building_list);
                }
                    
            }
            building_layer_.Update(camera_);
            building_layer_.Render(camera_);
            
            over_layer_.Update(camera_);
            over_layer_.Render();
        }
    }

    void MapRenderScene::UpdateScale(unsigned int scale)
    {

    }

    void MapRenderScene::AddRenderLayer(std::list<RenderLayerIF>& renderlayers)
    {

    }

    void MapRenderScene::RemoveRenderLayer(std::list<RenderLayerIF>& renderlayers)
    {

    }

    bool MapRenderScene::Select(const Rect& rect)
    {
        return false;
    }

    void MapRenderScene::GetSeletedObjects(std::list<RenderLayerIF>& renderlayers)
    {
    }

    MapRenderState& MapRenderScene::GetRenderState()
    {
        return state_;
    }

    void MapRenderScene::AddRasterTileToQueue(TileImagePtr tileImage)
    {
        // int size = static_cast<int>(raster_tiles_.size_approx());
        // printf("tick:%d AddRasterTileToQueue raster_tiles size:%d\n", 
        //     std::chrono::duration_cast<std::chrono::milliseconds>(Clock::now().time_since_epoch()).count(), size);
        raster_tiles_.enqueue(tileImage);
    }
    
    int MapRenderScene::PopRasterTileToQueue(TileImagePtr& tileImage)
    {
        int size = static_cast<int>(raster_tiles_.size_approx());
        // printf("tick:%d PopRasterTileToQueue raster_tiles size:%d\n", 
        //     std::chrono::duration_cast<std::chrono::milliseconds>(Clock::now().time_since_epoch()).count(), size);
        raster_tiles_.try_dequeue(tileImage);
        return size;
    }

    void MapRenderScene::MoveMap(double delta_x, double delta_y, const bool move_end, const uint32_t animation_duration_ms ) {
        if (delta_x == 0.0 && delta_y == 0.0 && !move_end) {
            return;
        }
        if (camera_ != nullptr) {
            if (animation_duration_ms == 0) {
                camera_->OnMove(delta_x, delta_y, move_end);
                return;
            }

            AnimationOptions animation(Milliseconds(animation_duration_ms), 
            {camera_->GetMapCenter().longitude_, camera_->GetMapCenter().latitude_}, {delta_x, delta_y}, AnimationType::kAnimationTypeMoveTo);
            camera_->OnMove(delta_x, delta_y, move_end, animation);
        }
    }

    void MapRenderScene::SetMapPitch(float pitch, const uint32_t animation_duration_ms) {
        if (camera_ != nullptr) {
            if (animation_duration_ms == 0) {
                camera_->SetPitchAngle(pitch);
                return;
            }
            AnimationOptions animation(Milliseconds(animation_duration_ms), 
            {camera_->GetMapPitchAngle()}, {pitch}, AnimationType::kAnimationTypeRollTo);
            camera_->SetPitchAngle(pitch, animation);
        }
    }

    float MapRenderScene::GetMapPitch() {
        if (camera_ != nullptr) {
            return camera_->GetMapPitchAngle();
        }
        return 0;
    }

    AABB2<Point2d> MapRenderScene::GetMapVisibleBBox() {
        if (camera_ != nullptr) {
            return camera_->GetMapRange();
        }
        return AABB2<Point2d>();
    }

    AABB2<Point2d> MapRenderScene::GetPredictVisbleBBox() {
        // 获取当前和上一帧的bbox
        if (camera_ == nullptr) {
            return AABB2<Point2d>();
        }
        std::lock_guard<std::mutex> lock(predict_bbox_mutex_);
        return predict_visible_bbox_;
    }

    void MapRenderScene::UpdatePredictVisbleBBox() {
        if (camera_ == nullptr) {
            return;
        }
        std::lock_guard<std::mutex> lock(predict_bbox_mutex_);
        AABB2<Point2d> current_bbox = camera_->GetMapRange();
        AnimationOptions animation_options = camera_->GetCurAnimationOptions();
        AABB2<Point2d> prev_bbox = current_visible_bbox_;
        #ifdef DEBUG
        printf("--------------currentbox:%f %f-%f %f, prev_bbox:%f %f - %f %f   prev_width:%f, prev_height:%f, "
            "current_width:%f, current_height:%f\n",
        current_bbox.minpt().x(), current_bbox.minpt().y(), current_bbox.maxpt().x(), current_bbox.maxpt().y(),
        prev_bbox.minpt().x(), prev_bbox.minpt().y(), prev_bbox.maxpt().x(), prev_bbox.maxpt().y(),
        prev_bbox.Width(), prev_bbox.Height(), current_bbox.Width(), current_bbox.Height());
        #endif
        // 计算趋势（位移/缩放）
        if (prev_bbox.Width() > 0.f && prev_bbox.Height() > 0.f && current_bbox.Width() > 0.f && current_bbox.Height() > 0.f) {
            Point2d current_center = current_bbox.Center();
            Point2d prev_center = prev_bbox.Center();
            auto delta_center = current_center - prev_center;  // 位移向量
            if (animation_options.type == AnimationType::kAnimationTypeMoveTo) {
                // 直接用moveto的target 目标点算delta_center
                Point2d target_center;
                animation_options.GetAnimatePos(target_center, Clock::now() + std::chrono::milliseconds(200));
                delta_center = target_center - prev_center;
            }

            // 如果位移向量和当前比例尺对应的tile宽度比过大，则不做预测的请求，这种可能是缩放比例尺或者move跳转到较远的地方
            double delta_width = delta_center.x();
            double delta_height = delta_center.y();
            double tile_width = 0;
            double tile_height = 0;
            calcGridLonLatSpan(camera_->GetMapScale(), tile_width, tile_height);
            #ifdef DEBUG
            printf("--------------------------delta_width:%f, delta_height:%f, tile_width:%f, tile_height:%f\n", delta_width, delta_height, tile_width, tile_height);
            #endif
            if (std::abs(delta_width) > tile_width * 10 || std::abs(delta_height) > tile_width * 10) {
                 #ifdef DEBUG
                 printf("--------------------------delta_width too big\n");
                 #endif
                predict_visible_bbox_ = AABB2<Point2d>();
                return;
            }
            // 过小也不预测
            if (std::abs(delta_width) < tile_width *0.3 && std::abs(delta_height) < tile_width * 0.3 ) {
                 #ifdef DEBUG
                 printf("--------------------------delta_width too little\n");
                 #endif
                predict_visible_bbox_ = AABB2<Point2d>();
                return;
            }
            
            double current_width = current_bbox.Width();
            double current_height = current_bbox.Height();
            double prev_width = prev_bbox.Width();
            double prev_height = prev_bbox.Height();
            
            // 预测下一帧bbox（简单线性外推，可根据需求调整）
            double predict_width = current_width + (current_width - prev_width) * 2;
            double predict_height = current_height + (current_height - prev_height)* 2;
            Point2d predict_center = current_center + delta_center;
            
            AABB2<Point2d> predict_bbox(
                {predict_center.x() - predict_width/2, predict_center.y() - predict_height/2},
                {predict_center.x() + predict_width/2, predict_center.y() + predict_height/2}
            );
            predict_visible_bbox_ = predict_bbox;
            #ifdef DEBUG
            printf("tick:%d predict_visible_bbox:%f %f-%f %f\n", 
                std::chrono::duration_cast<std::chrono::milliseconds>(Clock::now().time_since_epoch()).count(),
                predict_bbox.minpt().x(), predict_bbox.minpt().y(), predict_bbox.maxpt().x(), predict_bbox.maxpt().y());
            #endif

          
        }
        
    }
    

} //namespace 
/* EOF */
