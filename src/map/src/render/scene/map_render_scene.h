/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_scene.h
 * @brief Declaration file of class MapRenderScene
 * @attention used for C/C++ only.
 */

 #ifndef MAPRENDER_RENDER_SCENE_H_
 #define MAPRENDER_RENDER_SCENE_H_
 
 #include <vector>
 #include <unordered_map>
 #include "i_map_render_scene.h"
 #include "util_tile_define.h"
 #include "map_render_state.h" 
 #include "map_render_camera_if.h"
 #include "map_render_backend.h"
 #include "map_render_layer.h"
 #include "concurrentqueue.h"
#include "render_over_layer.h"
#include "render_building_layer.h"
#include <mutex>

namespace aurora {
 
class RenderTileLayer;

 /**
 * class breif description
 *
 * MapRenderScene
 *
 */
 class MapRenderScene : public IMapRenderScene
 {
 public:         
    MapRenderScene();     
     virtual ~MapRenderScene();

     virtual int32_t Init();

     virtual void Destory();
     
     virtual void SetCamera(CameraPtr camera);

     virtual CameraPtr GetCamera() const;

     virtual void UpdateMapCenter(double lon, double lat);

     virtual void RenderMap();

     virtual void UpdateScale(unsigned int scale);

     virtual void AddRenderLayer(std::list<RenderLayerIF>& renderlayers);

     virtual void RemoveRenderLayer(std::list<RenderLayerIF>& renderlayers);
 
     virtual bool Select(const Rect& rect);
 
     virtual void GetSeletedObjects(std::list<RenderLayerIF>& renderlayers);

     virtual MapRenderState& GetRenderState();

     virtual void MoveMap(double delta_x, double delta_y, const bool move_end = false, const uint32_t animation_duration_ms = 0);

     virtual void SetCurRenderMapTiles(const std::vector<std::shared_ptr<CacheTile>>& current_render_tiles, const std::vector<TileID>& all_tile_ids);

     virtual void SetMapPitch(float pitch, const uint32_t animation_duration_ms);

     virtual float GetMapPitch();

     virtual AABB2<Point2d> GetMapVisibleBBox();

     virtual AABB2<Point2d> GetPredictVisbleBBox();

public:
    static void AddRasterTileToQueue(TileImagePtr tileImage);
    static int PopRasterTileToQueue(TileImagePtr& tileImage);
    // Raster tile for test
    static moodycamel::ConcurrentQueue<TileImagePtr>  raster_tiles_;


private:
    void UpdatePredictVisbleBBox();

 private:
     CameraPtr camera_{};
     BackendPtr backend_{};
     // RenderTree render_tree_
     MapRenderState state_{};
     std::vector<TileID> render_list_{};
     std::vector<RenderLayerPtr> layer_list{};
     std::vector<std::shared_ptr<RenderTileLayer>> backgrounds_{};
     std::unordered_map<TileID, std::shared_ptr<RenderTileLayer>, TileIDHashFunc> tiles_{};
     std::vector<TileID> currRenderTiles_{};
     std::vector<std::shared_ptr<CacheTile>> current_render_tiles_;

     RenderOverLayer over_layer_;

     RenderBuildingLayer building_layer_;
     AABB2<Point2d> current_visible_bbox_;
     AABB2<Point2d> predict_visible_bbox_;
     std::mutex predict_bbox_mutex_;
 };
     
 } //namespace 
 
 #endif //MAPRENDER_RENDER_SCENE_H_
 /* EOF */
