/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "scale_converter.h"
#include <map>
#include <cmath>
namespace aurora
{
    // 比例尺	逻辑层	物理层 
        // 2000km	1		        180.00 
        // 1000km	2	    0	    90.00 
        // 500km	3		         45.00 
        // 200km	4	     1	       22.50 
        // 100km	5		        11.25 
        // 50km	6	     2	        5.625 
        // 30km	7		        2.8125 
        // 20km	8	       3	1.40625 
        // 10km	9		        0.703125 
        // 5km	    10	        4	0.3515625 
        // 2km	    11		        0.17578125 
        // 1km	    12	        5	0.087890625 
        // 500m	13		        0.0439453125 
        // 200m	14		        0.02197265625 
        // 100m	15	    6	        0.010986328125 
        // 50m	    16		        0.0054931640625 
        // 20m 	17		        0.00274658203125 
        // 10m	    18		        0.001373291015625 
        // 5m	    19	    7	        0.0006866455078125 


    static std::map <int, int> s_scale_logicLevl_map = {
        {MAP_SCALE_2000KM, TILE_LOGIC_LEVEL_1},
        {MAP_SCALE_1000KM, TILE_LOGIC_LEVEL_2},
        {MAP_SCALE_500KM, TILE_LOGIC_LEVEL_3},
        {MAP_SCALE_200KM, TILE_LOGIC_LEVEL_4},
        {MAP_SCALE_100KM, TILE_LOGIC_LEVEL_5},
        {MAP_SCALE_50KM, TILE_LOGIC_LEVEL_6},
        {MAP_SCALE_30KM, TILE_LOGIC_LEVEL_7},
        {MAP_SCALE_20KM, TILE_LOGIC_LEVEL_8},
        {MAP_SCALE_10KM, TILE_LOGIC_LEVEL_9},
        {MAP_SCALE_5KM, TILE_LOGIC_LEVEL_10},
        {MAP_SCALE_2KM, TILE_LOGIC_LEVEL_11},
        {MAP_SCALE_1KM, TILE_LOGIC_LEVEL_12},
        {MAP_SCALE_500M, TILE_LOGIC_LEVEL_13},
        {MAP_SCALE_200M, TILE_LOGIC_LEVEL_14},
        {MAP_SCALE_100M, TILE_LOGIC_LEVEL_15},
        {MAP_SCALE_50M, TILE_LOGIC_LEVEL_16},
        {MAP_SCALE_20M, TILE_LOGIC_LEVEL_17},
        {MAP_SCALE_10M, TILE_LOGIC_LEVEL_18},
        {MAP_SCALE_5M, TILE_LOGIC_LEVEL_19}
    };

    static std::map <int, int> s_logicLevl_scale_map = {
        {TILE_LOGIC_LEVEL_19, MAP_SCALE_5M}, 
        {TILE_LOGIC_LEVEL_18, MAP_SCALE_10M}, 
        {TILE_LOGIC_LEVEL_17, MAP_SCALE_20M},
        {TILE_LOGIC_LEVEL_16, MAP_SCALE_50M},
        {TILE_LOGIC_LEVEL_15, MAP_SCALE_100M},
        {TILE_LOGIC_LEVEL_14, MAP_SCALE_200M},
        {TILE_LOGIC_LEVEL_13, MAP_SCALE_500M},
        {TILE_LOGIC_LEVEL_12, MAP_SCALE_1KM},
        {TILE_LOGIC_LEVEL_11, MAP_SCALE_2KM},
        {TILE_LOGIC_LEVEL_10, MAP_SCALE_5KM},
        {TILE_LOGIC_LEVEL_9, MAP_SCALE_10KM},
        {TILE_LOGIC_LEVEL_8, MAP_SCALE_20KM},
        {TILE_LOGIC_LEVEL_7, MAP_SCALE_30KM},
        {TILE_LOGIC_LEVEL_6, MAP_SCALE_50KM},
        {TILE_LOGIC_LEVEL_5, MAP_SCALE_100KM},
        {TILE_LOGIC_LEVEL_4, MAP_SCALE_200KM},
        {TILE_LOGIC_LEVEL_3, MAP_SCALE_500KM},
        {TILE_LOGIC_LEVEL_2, MAP_SCALE_1000KM},
        {TILE_LOGIC_LEVEL_1, MAP_SCALE_2000KM}
    };


    static std::map <int, int> s_logicLevl_physicalLevl_map = {
        {TILE_LOGIC_LEVEL_1, DATA_LEVEL_0},
        {TILE_LOGIC_LEVEL_2, DATA_LEVEL_0},
        {TILE_LOGIC_LEVEL_3, DATA_LEVEL_1},
        {TILE_LOGIC_LEVEL_4, DATA_LEVEL_1},
        {TILE_LOGIC_LEVEL_5, DATA_LEVEL_2},
        {TILE_LOGIC_LEVEL_6, DATA_LEVEL_2},
        {TILE_LOGIC_LEVEL_7, DATA_LEVEL_3},
        {TILE_LOGIC_LEVEL_8, DATA_LEVEL_3},
        {TILE_LOGIC_LEVEL_9, DATA_LEVEL_4},
        {TILE_LOGIC_LEVEL_10, DATA_LEVEL_4},
        {TILE_LOGIC_LEVEL_11, DATA_LEVEL_5},
        {TILE_LOGIC_LEVEL_12, DATA_LEVEL_5},
        {TILE_LOGIC_LEVEL_13, DATA_LEVEL_5},
        {TILE_LOGIC_LEVEL_14, DATA_LEVEL_6},
        {TILE_LOGIC_LEVEL_15, DATA_LEVEL_6},
        {TILE_LOGIC_LEVEL_16, DATA_LEVEL_6},
        {TILE_LOGIC_LEVEL_17, DATA_LEVEL_6},
        {TILE_LOGIC_LEVEL_18, DATA_LEVEL_7},
        {TILE_LOGIC_LEVEL_19, DATA_LEVEL_7}
    };

    static std::map<int, int> s_physicalLevl_logicLevl_map = {
        {DATA_LEVEL_0, TILE_LOGIC_LEVEL_2},
        {DATA_LEVEL_1, TILE_LOGIC_LEVEL_4},
        {DATA_LEVEL_2, TILE_LOGIC_LEVEL_6},
        {DATA_LEVEL_3, TILE_LOGIC_LEVEL_8},
        {DATA_LEVEL_4, TILE_LOGIC_LEVEL_10},
        {DATA_LEVEL_5, TILE_LOGIC_LEVEL_12},
        {DATA_LEVEL_6, TILE_LOGIC_LEVEL_15},
        {DATA_LEVEL_7, TILE_LOGIC_LEVEL_19}
    };

    int32_t Scale2Level(int32_t scale)
    {
        int32_t level = TILE_LOGIC_LEVEL_15; // 默认14层100m
        auto it = s_scale_logicLevl_map.find(scale);
        if (it != s_scale_logicLevl_map.end()) {
            level = it->second;
        }
        return level;
    }

    int32_t Level2Scale(int32_t level)
    {
        int32_t scale = MAP_SCALE_100M; // 默认100m
        auto it = s_logicLevl_scale_map.find(level); 
        if (it != s_logicLevl_scale_map.end()) {
            scale = it->second; 
        }
        return scale;
    }

    int32_t GetDataLevel(int32_t logic_level)
    {
        int32_t level = DATA_LEVEL_6;
        auto it = s_logicLevl_physicalLevl_map.find(logic_level);
        if (it != s_logicLevl_physicalLevl_map.end()) {
            level = it->second;
        }
        return level;
    }

    int32_t GetLogicLevel(int32_t data_level)
    {
        int32_t level = TILE_LOGIC_LEVEL_15;
        auto it = s_physicalLevl_logicLevl_map.find(data_level);
        if (it!= s_physicalLevl_logicLevl_map.end()) {
            level = it->second;
        }
        return level;
    }

    std::vector<int32_t> GetLogicLevels(int32_t data_level)
    {
        std::vector<int32_t> levels;
        for(auto iter: s_logicLevl_physicalLevl_map) {
            if (iter.second == data_level) {
                levels.push_back(iter.first);
            }
        }
        return levels;
    }

    double GetLogicDataScale(int32_t TILE_LOGIC_LEVEL, int32_t data_level)
    {
        int32_t data_logic_level = GetLogicLevel(data_level);
        double scale = ::pow(2.0, TILE_LOGIC_LEVEL - data_logic_level);
        return scale;
    }


    // 计算网格的经纬度跨度
void calcGridLonLatSpan_zFix(int z, double& lonSpan, double& latSpan) {
    // 计算经度跨度
    lonSpan = 360.0 / std::pow(2.0, z);

    // 计算每个网格在墨卡托 y 坐标上的跨度
    double mercatorYSpan = 2 * M_PI / std::pow(2.0, z);

    // 以第一个网格为例，计算其 y 坐标范围
    double yMin = -M_PI;
    double yMax = yMin + mercatorYSpan;

    // 将墨卡托 y 坐标转换为纬度
    auto mercatorYToLat = [](double y) {
        return (180.0 / M_PI) * (2 * std::atan(std::exp(y)) - M_PI / 2);
    };

    double latMin = mercatorYToLat(yMin);
    double latMax = mercatorYToLat(yMax);

    // 计算纬度跨度
    latSpan = latMax - latMin;
    // printf("z:%d, lonSpan:%f, latSpan:%f\n", z, lonSpan, latSpan);
}

void calcGridLonLatSpan(double z, double& lonSpan, double& latSpan) {
    // 分解浮点缩放级别为整数基础层级和小数插值系数
    int base_z = static_cast<int>(std::floor(z));  // 基础层级（向下取整，如 z=14.3 则 base_z=14）
    double t = z - base_z;                         // 插值系数（0~1 之间，如 z=14.3 则 t=0.3）
    
    // 递归计算基础层级和下一层级的经纬度跨度
    double base_lon, base_lat, next_lon, next_lat;
    calcGridLonLatSpan_zFix(base_z, base_lon, base_lat);       // 计算基础层级（整数）的经纬度跨度
    calcGridLonLatSpan_zFix(base_z + 1, next_lon, next_lat);   // 计算下一层级（base_z+1）的经纬度跨度
    
    // 线性插值：根据基础层级和下一层级的跨度，计算当前浮点 z 对应的经纬度跨度
    lonSpan = base_lon * (1 - t) + next_lon * t;  // 经度跨度插值（基础层级占比 1-t，下一层级占比 t）
    latSpan = base_lat * (1 - t) + next_lat * t;  // 纬度跨度插值（基础层级占比 1-t，下一层级占比 t）
}

};
