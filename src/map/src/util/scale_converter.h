/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#ifndef MAPRENDERSERVICE_SCALE_CONVERTER_H_
#define MAPRENDERSERVICE_SCALE_CONVERTER_H_
#include <cstdint>
#include <vector>

namespace aurora
{
    const int32_t MAP_SCALE_INVALID = -1;
    const int32_t MAP_SCALE_5M = 5;
    const int32_t MAP_SCALE_10M = 10;
    const int32_t MAP_SCALE_20M = 20;
    const int32_t MAP_SCALE_50M = 50;
    const int32_t MAP_SCALE_100M = 100;
    const int32_t MAP_SCALE_200M = 200;
    const int32_t MAP_SCALE_500M = 500;
    const int32_t MAP_SCALE_1KM = 1000;
    const int32_t MAP_SCALE_2KM = 2000;
    const int32_t MAP_SCALE_5KM = 5000;
    const int32_t MAP_SCALE_10KM = 10000;
    const int32_t MAP_SCALE_20KM = 20000;
    const int32_t MAP_SCALE_30KM = 30000;
    const int32_t MAP_SCALE_50KM = 50000;
    const int32_t MAP_SCALE_100KM = 100000;
    const int32_t MAP_SCALE_200KM = 200000;
    const int32_t MAP_SCALE_500KM = 500000;
    const int32_t MAP_SCALE_1000KM = 1000000;
    const int32_t MAP_SCALE_2000KM = 2000000;

    const int32_t DATA_LEVEL_0 = 0;
    const int32_t DATA_LEVEL_1 = 1;
    const int32_t DATA_LEVEL_2 = 2;
    const int32_t DATA_LEVEL_3 = 3;
    const int32_t DATA_LEVEL_4 = 4;
    const int32_t DATA_LEVEL_5 = 5;
    const int32_t DATA_LEVEL_6 = 6;
    const int32_t DATA_LEVEL_7 = 7;

    const int32_t TILE_LOGIC_LEVEL_0 = 0;
    const int32_t TILE_LOGIC_LEVEL_1 = 1;
    const int32_t TILE_LOGIC_LEVEL_2 = 2;
    const int32_t TILE_LOGIC_LEVEL_3 = 3;
    const int32_t TILE_LOGIC_LEVEL_4 = 4;
    const int32_t TILE_LOGIC_LEVEL_5 = 5;
    const int32_t TILE_LOGIC_LEVEL_6 = 6;
    const int32_t TILE_LOGIC_LEVEL_7 = 7;
    const int32_t TILE_LOGIC_LEVEL_8 = 8;
    const int32_t TILE_LOGIC_LEVEL_9 = 9;
    const int32_t TILE_LOGIC_LEVEL_10 = 10;
    const int32_t TILE_LOGIC_LEVEL_11 = 11;
    const int32_t TILE_LOGIC_LEVEL_12 = 12;
    const int32_t TILE_LOGIC_LEVEL_13 = 13;
    const int32_t TILE_LOGIC_LEVEL_14 = 14;
    const int32_t TILE_LOGIC_LEVEL_15 = 15;
    const int32_t TILE_LOGIC_LEVEL_16 = 16;
    const int32_t TILE_LOGIC_LEVEL_17 = 17;
    const int32_t TILE_LOGIC_LEVEL_18 = 18;
    const int32_t TILE_LOGIC_LEVEL_19 = 19;
    const int32_t TILE_LOGIC_LEVEL_20 = 20;
    const int32_t TILE_LOGIC_LEVEL_21 = 21;

    const int32_t MAX_VALID_TILE_LOGIC_LEVEL = TILE_LOGIC_LEVEL_19;

    const int32_t MIN_VALID_TILE_LOGIC_LEVEL = TILE_LOGIC_LEVEL_1;

    int32_t Scale2Level(int32_t scale);

    int32_t Level2Scale(int32_t level);

    int32_t GetDataLevel(int32_t logic_level);

    int32_t GetLogicLevel(int32_t data_level);

    // 一个数据层可能对应多个逻辑层
    std::vector<int32_t> GetLogicLevels(int32_t data_level);

    double GetLogicDataScale(int32_t TILE_LOGIC_LEVEL, int32_t data_level);

    void calcGridLonLatSpan_zFix(int z, double& lonSpan, double& latSpan);

    void calcGridLonLatSpan(double z, double& lonSpan, double& latSpan);


};

#endif // MAPRENDERSERVICE_SCALE_CONVERTER_H_