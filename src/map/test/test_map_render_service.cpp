#include "util_coord_converter.h"
#include "util_coord_converter2D.h"
#include "util_height_cvt.h"
#include "util_vector2.h"
#include <unistd.h> 
#include <fstream>
#include <thread>
#include <chrono>
#include <GLES3/gl3.h>

#include "map_render_module.h"
#include "util_coord_converter.h"
#include "util_coord_converter2D.h"
#include "util_height_cvt.h"
#include "util_vector2.h"
#include <GLFW/glfw3.h>
#include "util_color.h"
#include "util_basic_type.h"
#include "BS_thread_pool.hpp"
#include "map_render_manager.h"
#include "gpu_shader_cache.h"
#include "gpu_program.h"
#include "gl_texture2d.h"
#include "render_raster_tile.h"
#include "map_render_camera_if.h"
#include "map_render_scene.h"
#include "render_font.h"
#include "render_point_layer.h"
#include "map_data_provider.h"
#include "display_data/display_data_def.h"
#include "config_data/cfg_manager.h"
#include <GL/gl.h>

using namespace aurora;

#define ForTest 1

// Matrix4<float> mat4(
//     1.f,0.f,0.f,0.2f,
//     0.f,1.f,0.f,-0.2f,
//     0.f,0.f,1.f,0.f,
//     0.f,0.f,0.f,1.f); 

const int kWIDTH = 800;
const int kHEIGHT = 600;
 aurora::CameraPtr camera = nullptr;
 IMapRenderManager* rendermanager = nullptr;
 IMapRenderService* renderService = nullptr;
 parser::CfgManager* cfg = nullptr;
 int32_t rotate_angle = 0;
  int32_t pitch_angle = 0;

  int flyto_idx = 0;
  std::pair<double, double> flyto_lonlats[] = {
      {121.31604686379432, 31.189158148023324},
    {113.960000, 22.540000},
    {115.85531, 28.67511},
    {116.4043, 39.8768}
  };

namespace testrender {

// Drag state tracking structure
struct DragState {
    bool isDragging = false;      // Whether dragging is in progress
    double startX, startY;        // Starting coordinates of the drag operation
    double currentX, currentY;    // Current coordinates during dragging
    double deltaX, deltaY;        // Drag distance in the current frame
};
DragState g_DragState; // The global drag state tracking structure
// 新增：延迟移动事件结构体
    struct DelayedMoveEvent {
        double deltaX;
        double deltaY;
        std::chrono::steady_clock::time_point triggerTime; // 触发时间（当前时间+3ms）
    };

    // 新增：延迟事件队列（需线程安全）
    std::vector<DelayedMoveEvent> delayedMoveEvents;
    std::mutex delayedMoveMutex; // 保护队列的互斥锁
// mouse button callback
void mouse_button_callback(GLFWwindow* window, int button, int action, int mods) {
    if (button == GLFW_MOUSE_BUTTON_LEFT) {
        if (action == GLFW_PRESS) {
            // Record the starting position and start dragging
            glfwGetCursorPos(window, &g_DragState.startX, &g_DragState.startY);
            g_DragState.currentX = g_DragState.startX;
            g_DragState.currentY = g_DragState.startY;
            g_DragState.isDragging = true;
        } else if (action == GLFW_RELEASE) {
            // end move
            g_DragState.isDragging = false;
        }
    }

    if (button == GLFW_MOUSE_BUTTON_LEFT) {
        if (action == GLFW_PRESS) {
            printf("Left mouse button pressed\n");
        } else if (action == GLFW_RELEASE) {
            printf("Left mouse button released move: %f, %f\n",
            g_DragState.deltaX, -g_DragState.deltaY);
              // 结束拖动，触发特殊动画
            // if (g_DragState.isDragging ) {
                 g_DragState.isDragging = false;
                //  std::lock_guard<std::mutex> lock(testrender::delayedMoveMutex);
                // testrender::delayedMoveEvents.clear();
                renderService->MoveMap(g_DragState.deltaX, -g_DragState.deltaY ,true, 200 );

            // }   

        }
    }
}

// mouse move
void mouse_move_callback(GLFWwindow* window, double xpos, double ypos) {
    if (g_DragState.isDragging) {
        auto currentTime = std::chrono::steady_clock::now();
        static auto lastProcessTime = std::chrono::steady_clock::time_point::min();
        if (lastProcessTime == std::chrono::steady_clock::time_point::min()) {
            lastProcessTime = currentTime;
        }
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastProcessTime).count();

        printf("Dragging elapsed:%ld\n ", elapsed);
        if (elapsed >= 20) {
            g_DragState.deltaX = xpos - g_DragState.currentX;
            g_DragState.deltaY = ypos - g_DragState.currentY;
            g_DragState.currentX = xpos;
            g_DragState.currentY = ypos;

            printf("Dragging: deltaX=%.2f, deltaY=%.2f\n", g_DragState.deltaX, g_DragState.deltaY);

            // 关键修改：不立即调用MoveMap，而是加入延迟队列（3ms后触发）
            // std::lock_guard<std::mutex> lock(testrender::delayedMoveMutex);
            // testrender::delayedMoveEvents.push_back({
            //     g_DragState.deltaX,
            //     g_DragState.deltaY,
            //     currentTime 
            // });
            renderService->MoveMap(g_DragState.deltaX, -g_DragState.deltaY);

            lastProcessTime = currentTime;
        } else {
            g_DragState.deltaX += xpos - g_DragState.currentX;
            g_DragState.deltaY += ypos - g_DragState.currentY;
            g_DragState.currentX = xpos;
            g_DragState.currentY = ypos;
        }
    }
}

void mouse_callback(GLFWwindow* window, double xpos, double ypos) {
    // the window origin pos(0, 0) is upper left
    // xpos, ypos is the current window coordinate of the mouse
    // printf("Mouse position: (%.2f, %.2f)\n", xpos, ypos);
}

void key_callback(GLFWwindow* window, int key, int scancode, int action, int mods) {
    if (action == GLFW_PRESS) {
        // key down
        switch (key) {
            case GLFW_KEY_I:
               {
                printf("I Down\n");
                 auto scale = renderService->GetMapScale() +1.3 ;
                renderService->SetMapScale(scale, 300);

               }  
             break;
            case GLFW_KEY_K:
            {
                printf("K Down\n");
                auto scale = renderService->GetMapScale() -1.6 ;
                renderService->SetMapScale(scale, 300);
            }
            break;
            case GLFW_KEY_J:
               {
                printf("J Down\n");
                 rotate_angle += 20;
                 
                renderService->SetMapRotation(rotate_angle, 2000);

               }  
             break;
            case GLFW_KEY_L:
            {
                printf("L Down\n");
                rotate_angle -= 20;
                renderService->SetMapRotation(rotate_angle, 2000);
            }
                
                break;

             case GLFW_KEY_U:
               {
                printf("U Down\n");
                 pitch_angle += 5;
                 
                renderService->SetMapPitch(pitch_angle, 300);


               }  
             break;
            case GLFW_KEY_O:
            {
                printf("O Down\n");
                pitch_angle -= 5;
                renderService->SetMapPitch(pitch_angle, 300);
            }
                
            break;

            case GLFW_KEY_F:
            {
                printf("F Down\n");
                flyto_idx++;
                flyto_idx %= (sizeof(flyto_lonlats) / sizeof(flyto_lonlats[0]));
                renderService->FlyTo(flyto_lonlats[flyto_idx].first, flyto_lonlats[flyto_idx].second, 9, 10000);
            }
                
            break;
        }
    } else if (action == GLFW_RELEASE) {
        // key up
        if (key == GLFW_KEY_I) {
            printf("I Up\n");
        }

        if (key == GLFW_KEY_K) {
            printf("K Up\n");
        }
    }
}

void window_close_callback(GLFWwindow* window) {
    printf("Quit...\n");
}

// 新增：鼠标滚轮回调函数
// 获取当前缩放级别
// 计算新缩放级别（yoffset>0为向上滚动，放大；yoffset<0为向下滚动，缩小）
// 限制最小/最大缩放级别（示例值，需根据实际地图配置调整）
// 调用地图服务设置新缩放级别（200ms动画过渡）
// 鼠标滚轮的无极缩放功能，需要添加GLFW滚轮事件回调并处理缩放逻辑。以下是修改步骤和代码调整：
// 关键修改说明
// 1. 添加滚轮事件回调函数，处理垂直滚动量（yoffset）
// 2. 根据滚动方向调整地图缩放级别（向上滚动放大，向下滚动缩小）
// 3. 使用平滑动画过渡缩放过程
// 4. 在窗口创建时注册滚轮回调
void scroll_callback(GLFWwindow* window, double xoffset, double yoffset) {
    if (!renderService) return;
    
    // 获取当前缩放级别
    double current_scale = renderService->GetMapScale();
    // 计算新缩放级别（yoffset>0为向上滚动，放大；yoffset<0为向下滚动，缩小）
    double new_scale = current_scale + yoffset * 0.3;  // 0.3为缩放步长系数，可根据需求调整
    
    // 限制最小/最大缩放级别（示例值，需根据实际地图配置调整）
    const double min_scale = 3.0;
    const double max_scale = 17.0;
    new_scale = std::clamp(new_scale, min_scale, max_scale);
    printf("Scroll new_scale:%f cur_scale:%f\n", new_scale, current_scale);
    // 调用地图服务设置新缩放级别（200ms动画过渡）
    renderService->SetMapScale(new_scale, 200);
}

void GLThread() {
    if (!glfwInit()) {
        std::cerr << "Failed to initialize GLFW!" << std::endl;
        return;
    }
    cfg = new parser::CfgManager();
#ifdef AURORA_DEBUG
    std::string file_path = "../data/cfg.json";
#else
    std::string file_path = "/home/<USER>/workspace/source/map_engine/distribution/data/cfg.json";
#endif
    if (0 != cfg->Init(file_path)) {
      return;
    }
    glfwWindowHint(GLFW_SAMPLES, 4);
    glEnable(GL_MULTISAMPLE);
    glfwWindowHint(GLFW_TRANSPARENT_FRAMEBUFFER, GLFW_FALSE);  
    glfwWindowHint(GLFW_DECORATED, GLFW_TRUE);  
    
    GLFWwindow* window = glfwCreateWindow(kWIDTH, kHEIGHT, "Aurora Engine", nullptr, nullptr);
    if (!window) {
        std::cerr << "Failed to create GLFW window!" << std::endl;
        glfwTerminate();
        return;
    }
    //  glViewport(0, 0, static_cast<GLsizei>(512), static_cast<GLsizei>(512));
    glfwSetCursorPosCallback(window, mouse_callback); 
    glfwSetMouseButtonCallback(window, mouse_button_callback);
    glfwSetKeyCallback(window, key_callback);
    glfwSetWindowCloseCallback(window, window_close_callback);
    glfwSetCursorPosCallback(window, mouse_move_callback);
    
    glfwSetScrollCallback(window, scroll_callback);
    glfwSetWindowSizeCallback(window, [](GLFWwindow* window, int width, int height) {
        // 在这里处理窗口大小改变的逻辑
        printf("Window size changed to %d x %d\n", width, height);
        if (renderService) {
            // glViewport(0,0,width,height);
            renderService->SetScreenSize(width, height);
        }
    });

    // 修复此处代码调用，添加 glfwSetWindowMaximizeCallback
    glfwSetWindowMaximizeCallback(window, [](GLFWwindow* window, int maximized) {
        // 处理事件
    glfwPollEvents();

        // 在这里处理窗口最大化/还原的逻辑
        printf("Window maximized: %d\n", maximized);
        if (renderService && maximized) {
            // 获取与窗口关联的显示器
            GLFWmonitor* monitor = glfwGetWindowMonitor(window);
            if (!monitor) {
                // 如果窗口没有关联特定显示器，获取主显示器
                monitor = glfwGetPrimaryMonitor();
            }
            if (monitor) {
                // 获取显示器的视频模式
                const GLFWvidmode* mode = glfwGetVideoMode(monitor);
                if (mode) {
                    // 获取屏幕的宽度和高度
                    int width = mode->width;
                    int height = mode->height;
                    // 设置屏幕大小
                    renderService->SetScreenSize(width, height);
                }
            }
        }
    });

    glfwMakeContextCurrent(window);

    // create mapservice
    renderService = new IMapRenderService();
    AuroraMapConfig map_config;
    // cfg.config_path_ = "/home/<USER>/workspace/source/map_engine/distribution/data/cfg.json";
    map_config.data_path_ = "/home/<USER>/workspace/data/china/display/";
    map_config.default_fontPath_ = "../data/fonts";
    map_config.default_fontName_ = "Arial Unicode.ttf";
    map_config.default_mark_path_ = "../data/mark/mark.png";
    renderService->Init(map_config, cfg);
    renderService->SetScreenSize(kWIDTH, kHEIGHT);
     double lon = 121.31604686379432;
     double lat = 31.189158148023324;
    //   double lon = 114.297826;
    //  double lat = 22.688325;

    double centerLon = lon;
    double centerLat = lat;
    renderService->SetMapCenter(centerLon, centerLat);


    //  camera->SetViewport(0, 0, kWIDTH, kHEIGHT);
    // camera->SetMapCenter(centerLon, centerLat);
    
    // aurora::parser::GeoMbr mbr(lon - 0.02, lat - 0.002, lon + 0.000001, lat + 0.000001);
    // dataProvider->GetDisplayData(14, mbr);
     auto center = renderService->GetMapCenter();
      // 初始化缩放层级
    unsigned int startScale = 15;
    unsigned int endScale = 18;
    unsigned int currentScale = startScale;
    renderService->SetMapScale(currentScale);
     double x, y ;
      auto screenPos = renderService->MapToScreen(center);
     printf("------------0center lon %f, lat %f, x:%f, y:%f\n", center.x_, center.y_, screenPos.x_, screenPos.y_);

   

    // 记录上一次更新缩放层级的时间
    auto lastScaleUpdateTime = std::chrono::high_resolution_clock::now();
    auto lastAngledateTime = std::chrono::high_resolution_clock::now();
    // 定义更新间隔为 2 秒
    const std::chrono::seconds scaleUpdateInterval(3);

    // 记录开始时间
    auto startTime = std::chrono::high_resolution_clock::now();

    while (!glfwWindowShouldClose(window)) {
        // 获取当前时间
        static int pitch_idx = 0;
        auto currentTime = std::chrono::high_resolution_clock::now();
        // 计算距离上一次更新缩放层级过去了多长时间
        auto elapsedSinceLastUpdate = std::chrono::duration_cast<std::chrono::seconds>(currentTime - lastScaleUpdateTime);

        // 如果过去了 2 秒且当前缩放层级小于终止缩放层级，则更新缩放层级
        if (elapsedSinceLastUpdate >= scaleUpdateInterval && currentScale < endScale) {
            currentScale++;
            // camera->SetMapScale(currentScale);
            auto center = renderService->GetMapCenter();
            double x, y ;
            Point64 test_coord = {121.327034, 31.200145};
            auto screenPos = renderService->MapToScreen(test_coord);
            
            printf("-----------1center lon MapToScreen corrd: [%f %f] screen: x:%f, y:%f\n", test_coord.x_, test_coord.y_, screenPos.x_, screenPos.y_);
           
            double lon  =0.0f;    
            double lat = 0.0f;
            auto mapPos = renderService->ScreenToMap(screenPos);
   
            printf("-----------1center lon Screen2World %f, lat %f x:%f, y:%f\n", mapPos.x_, mapPos.y_, screenPos.x_, screenPos.y_);
            
            // camera->OnMove(10, 10);
           
            screenPos = renderService->MapToScreen(center);
            printf("------------2center lon %f, lat %f x:%f, y:%f\n", center.x_, center.y_, screenPos.x_, screenPos.y_);

            mapPos = renderService->ScreenToMap(screenPos);
            printf("------------3center lon %f, lat %f x:%f, y:%f\n", center.x_, center.y_, screenPos.x_, screenPos.y_);
            // 更新上一次更新缩放层级的时间

            lastScaleUpdateTime = currentTime;
            // lon - 0.01, lat - 0.001, lon + 0.0000001, lat + 0.0000001
           
        }

    
       
    //  camera->SetPitchAngle(test_pitch_angles[pitch_idx%test_pitch_angles.size()]);
    if (pitch_idx %10 == 0) {
        // camera->SetMapRotation(test_roll_angles[pitch_idx%test_roll_angles.size()]);
    }
    if (pitch_idx %15 == 0) {
        //  camera->SetPitchAngle(test_pitch_angles[pitch_idx%test_pitch_angles.size()]);
    }
    //  camera->SetMapCenter(centerLon + 0.001*(pitch_idx%10), centerLat + 0.00000001 * (pitch_idx%10));

     // 计算经过的时间
        
        std::chrono::duration<double> elapsedTime = currentTime - startTime;
        double elapsedSeconds = elapsedTime.count();

        // 根据时间计算旋转角度，例如每秒旋转 30 度
         auto elapsedAngleLastUpdate = std::chrono::duration_cast<std::chrono::seconds>(currentTime - lastAngledateTime);
         if (elapsedAngleLastUpdate >= scaleUpdateInterval) {

            lastAngledateTime = currentTime;
            float rollAngle = static_cast<float>(pitch_idx  * 30.0);
            // 设置相机的 roll 角度

            //  camera->SetMapRotation(rollAngle);
            // camera->SetPitchAngle(rollAngle);

             pitch_idx++;
         }
        

        
       
      

       // 每帧递增缩放层级，直到达到 18 层
       

       // 关键新增：处理延迟的MoveMap事件
       {
           std::lock_guard<std::mutex> lock(testrender::delayedMoveMutex);
           auto now = std::chrono::steady_clock::now();
           // 遍历队列，触发3ms前的事件
           for (auto it = testrender::delayedMoveEvents.begin(); it != testrender::delayedMoveEvents.end();) {
               if (now >= it->triggerTime) {
                   // 仅当鼠标仍在拖动时执行MoveMap（避免释放后执行）
                   printf("+++++++++++++delayedMoveEvents triggerTime: %ld\n", std::chrono::duration_cast<std::chrono::milliseconds>(it->triggerTime - now).count());
                   if (g_DragState.isDragging) {
                       renderService->MoveMap(it->deltaX, -it->deltaY);
                   }
                   else {
                     printf("+++++++++delayedMoveEvents End animation:%d %d", it->deltaX, -it->deltaY);
                     if (it->deltaX !=0 && it->deltaY != 0) {
                        renderService->MoveMap(it->deltaX, -it->deltaY, true, 200);
                        testrender::delayedMoveEvents.clear();
                        break;
                     }
                        
                   }
                   it = testrender::delayedMoveEvents.erase(it); // 执行后移除事件
               } else {
                   ++it;
               }
           }
       }

       renderService->RenderMap();

       glfwSwapBuffers(window);
       glfwPollEvents();
   }
   renderService->Destory();
   glfwTerminate();
 }
}

int main() {

    std::thread renderthread(testrender::GLThread);
    renderthread.join();
    return 0;

}
/*EOF*/
