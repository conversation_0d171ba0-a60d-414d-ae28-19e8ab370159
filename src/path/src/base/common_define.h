// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-11
//

#ifndef AURORA_PATH_SRC_BASE_COMMON_DEFINE_H_
#define AURORA_PATH_SRC_BASE_COMMON_DEFINE_H_

#include <cassert>
#include "logger.h"

namespace aurora {
namespace path {

#ifdef PATH_DEBUG
#define DCHECK(x) assert(x)
#else
#define DCHECK(x)
#endif

#ifdef PATH_DEBUG
// Define Path module tag
#define PATH_TAG "[PATH]"

// Path module log macros with predefined tag
#define PATH_LOG_TRACE(fmt, ...)     LOG_TRACE_TAG(PATH_TAG, fmt, ##__VA_ARGS__)
#define PATH_LOG_DEBUG(fmt, ...)     LOG_DEBUG_TAG(PATH_TAG, fmt, ##__VA_ARGS__)
#define PATH_LOG_INFO(fmt, ...)      LOG_INFO_TAG(PATH_TAG, fmt, ##__VA_ARGS__)
#define PATH_LOG_WARN(fmt, ...)      LOG_WARN_TAG(PATH_TAG, fmt, ##__VA_ARGS__)
#define PATH_LOG_ERROR(fmt, ...)     LOG_ERROR_TAG(PATH_TAG, fmt, ##__VA_ARGS__)
#define PATH_LOG_FATAL(fmt, ...)     LOG_FATAL_TAG(PATH_TAG, fmt, ##__VA_ARGS__)
#else
#define PATH_LOG_TRACE(fmt, ...)
#define PATH_LOG_DEBUG(fmt, ...)
#define PATH_LOG_INFO(fmt, ...)
#define PATH_LOG_WARN(fmt, ...)
#define PATH_LOG_ERROR(fmt, ...)
#define PATH_LOG_FATAL(fmt, ...)
#endif

} // namespace path
} // namespace aurora

#endif  // AURORA_PATH_SRC_BASE_COMMON_DEFINE_H_
