// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#ifndef AURORA_PATH_INCLUDE_DATA_INTERFACE_H_
#define AURORA_PATH_INCLUDE_DATA_INTERFACE_H_

#include <algorithm>
#include <vector>
#include <string>
#include <memory>
#include <cassert>
#include <fstream>
#include <iomanip>
#include "logger.h"
#include "path_metrics.h"


#include "base/include/module.h"
#include "graphid.h"
#include "base/include/pointll.h"
#include "base/scope_time.h"
#include "base/costconstants.h"
#include "base/common_define.h"
#include "base/path_memory_pool.h"
#include "data_provider/include/data_provider.h"
#include "data_provider/include/route_data/route_tile_reader.h"
#include "data_provider/include/route_data/feature/augment_edge.h"
// #include "data_provider/route_data/route_tile_package.h"

namespace aurora {
namespace path {

using SubEdge = parser::SubEdge;

enum class TileLoadStatus
{
	TILE_LOAD_NONE = 0, //未加载
	TILE_FWD_LOAD,		//正向探索加载
	TILE_REV_LOAD,      //反向探索加载
	TILE_LOAD_BOTH      //双向加载
};

// Edge label status
enum class EdgeSet : uint8_t {
  kUnreachedOrReset = 0, // Unreached - not yet encountered in search _or_ encountered but
                         // reset due to encountering a complex restriction:
                         // https://github.com/valhalla/valhalla/issues/2103
  kPermanent = 1,        // Permanent - shortest path to this edge has been found
  kTemporary = 2,        // Temporary - edge has been encountered but there could
                         //   still be a shorter path to this edge. This edge will
                         //   be "adjacent" to an edge that is permanently labeled.
  kSkipped = 3           // Skipped - edge has been encountered but was thrown out
                         // of consideration.
};

// Store the edge label status and its index in the EdgeLabels list
struct EdgeStatusInfo {
  uint32_t index_ : 28;
  uint32_t set_ : 4;

  EdgeStatusInfo() : index_(0), set_(0) {
  }

  EdgeStatusInfo(const EdgeSet set, const uint32_t index)
      : index_(index), set_(static_cast<uint32_t>(set)) {
  }

  uint32_t index() const {
    return index_;
  }

  EdgeSet set() const {
    return static_cast<EdgeSet>(set_);
  }
};

struct Restriction {
    // MapId from;  // 起始边ID
    // MapId  to;    // 目标边ID
    // std::vector<MapId > vias; // 必经边序列 // Inner link
    // bool has_dt; // 是否有日期时间信息
    // // TimeRestriction time; //时间相关字段

    uint32_t in_tile_id : 8;   ///< Tile identifier for the incoming edge (8 bits)
    uint32_t in_edge_dir : 1;  ///< Direction flag of the incoming edge (forward/reverse)
    uint32_t in_edge_id : 23;  ///< Unique identifier of the incoming edge within its tile (23 bits)
  
    uint32_t out_tile_id : 8;   ///< Tile identifier for the outgoing edge (8 bits)
    uint32_t out_edge_dir : 1;  ///< Direction flag of the outgoing edge (forward/reverse)
    uint32_t out_edge_id : 23;  ///< Unique identifier of the outgoing edge within its tile (23 bits)
    uint8_t access_ctrl_type : 3;      ///< defined in enum AccessCtrlType
    uint8_t access_ctrl_relation : 1;  ///< defined in enum AccessCtrlRelation
    uint8_t has_time_domain: 1;
    uint8_t unused: 3;

    Restriction(const parser::LimitPassBase* base);
    std::string ToString();
    uint32_t InDirectId();
    uint32_t OutDirectId();
};
using RestrictionPtr = std::shared_ptr<Restriction>;

class graphTile;
using graphTilePtr = std::shared_ptr<graphTile>;
using grapthTileRawPtr = graphTile*;

struct DirectEdgeInfo {
    DirectEdgeInfo(grapthTileRawPtr tile_ptr, parser::TopolEdge& edge, const parser::RouteTileID& route_tile_id,
        std::shared_ptr<parser::RouteTileReader>& tile_reader, const GraphId& direct_id);

    bool is_forward();
    uint16_t GetStartHeading();
    uint16_t GetEndHeading();

    GraphId graphid() const { return id; }
    uint32_t level() const { return level_id; }

    GraphId id;
    parser::RouteTileID tile_id;

    uint16_t start_heading;
    uint16_t end_heading;

    EdgeStatusInfo edge_status;
    void Set(const EdgeSet& set, const uint32_t& index) {
        edge_status = {set, index};
    }
    void Update(const EdgeSet& set) {
        edge_status.set_ = static_cast<uint32_t>(set);
    }
    // std::weak_ptr<graphTile> tile_ptr;
    grapthTileRawPtr tile_ptr;
    DirectEdgeInfo* predecessor_edge;

    void UpdateCost(DirectEdgeInfo* pred_edge, uint32_t predecessor_idx, Cost cost, float sortcost, const Cost& transition_cost, const Cost& edge_cost);
    float sortcost() const { return sortcost_; }
    const Cost& cost() const { return cost_; }
    Cost transition_cost() const { return transition_cost_; }
    Cost edge_cost() const { return edge_cost_; }
    uint32_t turn_degree() const { return turn_degree_; }
    void set_turn_degree(uint32_t turn_degree) { turn_degree_ = turn_degree; }
    uint32_t predecessor() const { return predecessor_idx_; }
    uint32_t predecessor_idx_;
    Cost cost_;             // Cost and elapsed time along the path.
    float sortcost_;        // Sort cost - includes A* heuristic.
    Cost transition_cost_;  // node turn cost
    Cost edge_cost_;  // node turn cost
    uint32_t turn_degree_ : 9;
    uint32_t level_id: 2;
    uint32_t access_ctrl_type : 3;
    uint32_t origin: 1; // 起点
    uint32_t destination : 1; // 终点
    uint32_t unsed : 16;

    // Getter functions for member variables (now all via topo_edge.GetBaseInfo())
    uint16_t length();
    uint32_t startnode();
    uint32_t endnode();
    uint8_t function_class();
    uint8_t edge_type();
    uint8_t direction();
    uint8_t need_toll();
    uint32_t positive_speed_limit();
    uint32_t is_overhead();
    uint32_t is_inner_edge();
    uint32_t is_separate();
    uint32_t negtive_speed_limit();
    uint8_t is_area_link();
    uint8_t is_city_edge();
    uint8_t is_ramp();
    uint8_t edge_form();
    uint8_t speed_grade();
    uint8_t forward_lane_count();
    uint8_t backward_lane_count();
    uint8_t lane_count();
    uint8_t road_class();
    uint8_t is_left();
    uint8_t is_limit_in_edge();
    uint8_t is_limit_out_edge();
    uint8_t is_building();
    uint8_t is_paved();
    uint8_t is_gate();
    uint8_t no_crossing();
    uint8_t is_private();
    uint8_t has_traffic_light();

    std::vector<PointLL> geos;

    parser::TopolEdge& topo_edge;

};  // struct DirectEdgeInfo

struct AugmentEdgeInfo {
    GraphId id;
    std::vector<SubEdge> sub_edges;
    std::vector<PointLL> geos;
};

using AugmentEdgeInfoPtr = std::shared_ptr<AugmentEdgeInfo>;

struct NodeInfo {
    NodeInfo(grapthTileRawPtr tile_ptr, parser::RouteNode& node, const parser::RouteTileID& route_tile_id);

    parser::RouteTileID tile_id;
    GraphId id;
    uint32_t local_idx; // raw node id
    grapthTileRawPtr tile_ptr;

    PointLL GetPoint();
    bool IsTransUp();
    uint32_t level();
    GraphId GetTranUpId();

    std::vector<uint32_t> GetTransUpEdgeInfo();

    parser::RouteNode& route_node;
};  // struct NodeInfo

struct GraphHeader {
    uint32_t node_count;
    uint32_t edge_count;
    parser::RouteTileID tile_id;
    uint16_t load_flag: 2; // //加载标示 0：未加载 1: 前向已加载  2：反向已加载 3:两端加载
    uint16_t reserved: 14;

    bool TileLoadBoth() {
        return load_flag == static_cast<uint16_t>(TileLoadStatus::TILE_LOAD_BOTH);
    }
    // todo: direct edge count , use dir to split edge
};  // struct GraphHeader
using GraphHeaderPtr = std::shared_ptr<GraphHeader>;

using DirectEdgeInfoPtr = std::shared_ptr<DirectEdgeInfo>;
using NodeInfoPtr = std::shared_ptr<NodeInfo>;

using DirectEdgeInfoRawPtr = DirectEdgeInfo*;
using NodeInfoRawPtr = NodeInfo*;

struct graphTile {
    graphTile(parser::RouteTilePackagePtr tile_package, std::unique_ptr<PathMemoryPool>& memory_pool, TileLoadStatus load_status = TileLoadStatus::TILE_LOAD_NONE);

    GraphHeaderPtr tile_header;
    std::shared_ptr<parser::RouteTileReader> reader;
    GraphHeaderPtr& header();

    std::vector<DirectEdgeInfoRawPtr> edges_;
    std::vector<AugmentEdgeInfoPtr> aug_edges_;
    std::vector<NodeInfoRawPtr> nodes_;
    std::vector<RestrictionPtr> enter_restrictions_;
    std::vector<RestrictionPtr> exit_restrictions_;

    std::unique_ptr<PathMemoryPool>& memory_pool_;

    DirectEdgeInfoRawPtr* fwd_edges_;
    DirectEdgeInfoRawPtr* bwd_edges_;

    const std::vector<AugmentEdgeInfoPtr>& aug_edges();
    AugmentEdgeInfoPtr GetAugmentEdgeInfo(const GraphId& id);
    const DirectEdgeInfoRawPtr& GetDirectEdgeInfo(const GraphId& id, bool forward); // todo: use template
    const parser::TopolEdge* GetTopoEdgeById(const uint32_t& local_id);
    const NodeInfoRawPtr& GetNodeInfo(const uint32_t& local_id);

    DirectEdgeInfoRawPtr FindDirectEdgeInfo(uint32_t direct_id, bool forward);

};  // struct graphTile

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_INCLUDE_DATA_INTERFACE_H_
