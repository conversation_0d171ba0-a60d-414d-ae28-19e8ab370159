/**
 * @file path_memory_pool.cpp
 * @brief Implementation of Memory Pool (Single-threaded )
 * <AUTHOR>
 * @date 2025-06-26
 * @note single-threaded usage
 */

#include "path_memory_pool.h"
#include <algorithm>
#include <sstream>
#include "logger.h"
#include "common_define.h"

namespace aurora::path {

// ============================================================================
// MemoryBuffer Implementation
// ============================================================================

MemoryBuffer::MemoryBuffer(std::size_t size) 
    : buffer_(size), used_size_(0) {
    if (size == 0) {
        throw MemoryPoolException("Buffer size cannot be zero");
    }
}

void* MemoryBuffer::allocate(std::size_t size) {
    if (size == 0) {
        return nullptr;
    }
    
    const std::size_t aligned_size = align_size(size);
    
    if (used_size_ + aligned_size > buffer_.size()) {
        return nullptr;  // Insufficient space
    }
    
    void* ptr = buffer_.data() + used_size_;
    used_size_ += aligned_size;
    
    // Zero out the allocated memory
    std::memset(ptr, 0, size);
    
    return ptr;
}

void MemoryBuffer::reset() noexcept {
    used_size_ = 0;
}

void MemoryBuffer::zero_memory() noexcept {
    if (used_size_ > 0) {
        std::memset(buffer_.data(), 0, used_size_);
    }
}

void MemoryBuffer::free_memory() noexcept {
    // Clear the vector and free its memory
    buffer_.clear();
    buffer_.shrink_to_fit();
    used_size_ = 0;
}

std::size_t MemoryBuffer::align_size(std::size_t size) noexcept {
    const std::size_t alignment = MemoryPoolConfig::kAlignment;
    return (size + alignment - 1) & ~(alignment - 1);
}

// ============================================================================
// PathMemoryPool Implementation
// ============================================================================

PathMemoryPool::PathMemoryPool(std::size_t initial_block_size, std::size_t max_blocks)
    : block_size_(initial_block_size), max_blocks_(max_blocks) {

    if (block_size_ == 0) {
        throw MemoryPoolException("Block size cannot be zero");
    }

    if (block_size_ < MemoryPoolConfig::kMinBlockSize) {
        throw MemoryPoolException(
            "Block size (" + std::to_string(block_size_) + ") is smaller than minimum (" +
            std::to_string(MemoryPoolConfig::kMinBlockSize) + ")"
        );
    }

    if (block_size_ > MemoryPoolConfig::kMaxBlockSize) {
        throw MemoryPoolException(
            "Block size (" + std::to_string(block_size_) + ") exceeds maximum (" +
            std::to_string(MemoryPoolConfig::kMaxBlockSize) + ")"
        );
    }

    if (max_blocks_ == 0) {
        throw MemoryPoolException("Maximum block count cannot be zero");
    }

    // Reserve space for buffers to avoid reallocations
    buffers_.reserve(max_blocks_);
}

PathMemoryPool::~PathMemoryPool() {
    // Print final statistics in debug builds
    #ifdef DEBUG
    print_statistics(std::cout);
    #endif

    // Free all buffers on destruction
    free_buffers();
}

void* PathMemoryPool::allocate(std::size_t size) {
    // if (size == 0) {
    //     return nullptr;
    // }
    DCHECK(size != 0);

    // Check if size is too large for a single buffer
    // if (size > block_size_) {
    //     throw MemoryPoolException(
    //         "Requested size (" + std::to_string(size) + " bytes) exceeds block size (" +
    //         std::to_string(block_size_) + " bytes)"
    //     );
    // }

    // Try to allocate from current buffer first
    if (current_buffer_index_ < buffers_.size()) {
        void* ptr = buffers_[current_buffer_index_]->allocate(size);
        if (ptr != nullptr) {
            return ptr;
        }
    }

    // Try to find space in other existing buffers (avoid fragmentation)
    // for (std::size_t i = current_buffer_index_ + 1; i < buffers_.size(); ++i) {
    //     void* ptr = buffers_[i]->allocate(size);
    //     if (ptr != nullptr) {
    //         // Update current buffer index to the one that had space
    //         current_buffer_index_ = i;
    //         return ptr;
    //     }
    // }

    // All existing buffers are full, try to add a new one
    try {
        MemoryBuffer& new_buffer = add_buffer();
        void* ptr = new_buffer.allocate(size);
        if (ptr != nullptr) {
            return ptr;
        }
    } catch (const MemoryPoolException&) {
        // Maximum buffer count reached or allocation failed
    }

    throw MemoryPoolException(
        "Failed to allocate " + std::to_string(size) + " bytes. "
        "Pool exhausted with " + std::to_string(buffers_.size()) + " buffers."
    );
}

void PathMemoryPool::reset() noexcept {
    for (auto& buffer : buffers_) {
        buffer->reset();
    }

    current_buffer_index_ = 0;
}

void PathMemoryPool::zero_memory() noexcept {
    for (auto& buffer : buffers_) {
        buffer->zero_memory();
    }
}

void PathMemoryPool::free_buffers() noexcept {
    // Free memory from all buffers
    for (auto& buffer : buffers_) {
        buffer->free_memory();
    }

    // Clear the buffer vector and free its memory
    buffers_.clear();
    buffers_.shrink_to_fit();

    // Reset buffer index
    current_buffer_index_ = 0;
}

void PathMemoryPool::free_unused_buffers() noexcept {
    // Remove empty buffers from the end
    auto it = std::remove_if(buffers_.begin(), buffers_.end(),
        [](const std::unique_ptr<MemoryBuffer>& buffer) {
            return buffer->empty();
        });

    // Free memory from removed buffers
    for (auto iter = it; iter != buffers_.end(); ++iter) {
        (*iter)->free_memory();
    }

    buffers_.erase(it, buffers_.end());

    // Adjust current buffer index if necessary
    if (current_buffer_index_ >= buffers_.size()) {
        current_buffer_index_ = buffers_.empty() ? 0 : buffers_.size() - 1;
    }
}

std::size_t PathMemoryPool::total_size() const noexcept {
    std::size_t total = 0;
    for (const auto& buffer : buffers_) {
        total += buffer->size();
    }
    return total;
}

std::size_t PathMemoryPool::used_size() const noexcept {
    std::size_t used = 0;
    for (const auto& buffer : buffers_) {
        used += buffer->used_size();
    }
    return used;
}

std::size_t PathMemoryPool::buffer_count() const noexcept {
    return buffers_.size();
}

std::string PathMemoryPool::get_statistics() const {
    std::ostringstream oss;

    // Calculate totals directly
    std::size_t total = 0;
    std::size_t used = 0;
    for (const auto& buffer : buffers_) {
        total += buffer->size();
        used += buffer->used_size();
    }

    const double usage_percent = total > 0 ? (static_cast<double>(used) / total) * 100.0 : 0.0;
    
    oss << "PathMemoryPool Statistics:\n";
    oss << "  Buffers: " << buffers_.size() << "/" << max_blocks_ << "\n";
    oss << "  Total Size: " << (total / 1024.0 / 1024.0) << " MB\n";
    oss << "  Used Size: " << (used / 1024.0 / 1024.0) << " MB\n";
    oss << "  Usage: " << std::fixed << std::setprecision(2) << usage_percent << "%\n";
    oss << "  Block Size: " << (block_size_ / 1024.0 / 1024.0) << " MB\n";
    
    // if (!buffers_.empty()) {
    //     oss << "  Buffer Details:\n";
    //     for (std::size_t i = 0; i < buffers_.size(); ++i) {
    //         const auto& buffer = buffers_[i];
    //         const double buffer_usage = buffer->size() > 0 ? 
    //             (static_cast<double>(buffer->used_size()) / buffer->size()) * 100.0 : 0.0;
            
    //         oss << "    Buffer " << i << ": " 
    //             << (buffer->used_size() / 1024.0) << " KB / " 
    //             << (buffer->size() / 1024.0) << " KB ("
    //             << std::fixed << std::setprecision(1) << buffer_usage << "%)\n";
    //     }
    // }
    
    return oss.str();
}

void PathMemoryPool::print_statistics(std::ostream& os) const {
    os << get_statistics() << std::endl;
}

MemoryBuffer& PathMemoryPool::add_buffer() {
    if (buffers_.size() >= max_blocks_) {
        throw MemoryPoolException(
            "Maximum buffer count (" + std::to_string(max_blocks_) + ") reached"
        );
    }
    
    try {
        auto buffer = std::make_unique<MemoryBuffer>(block_size_);
        buffers_.push_back(std::move(buffer));
        current_buffer_index_ = buffers_.size() - 1;
        
        return *buffers_.back();
    } catch (const std::bad_alloc&) {
        throw MemoryPoolException(
            "Failed to allocate new buffer of size " + std::to_string(block_size_) + " bytes"
        );
    }
}

} // namespace aurora::path
