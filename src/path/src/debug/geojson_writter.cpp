// <PERSON><PERSON><PERSON> asserts by default but we dont want to crash running server
// its more useful to throw and catch for our use case
#define RAPIDJSON_ASSERT_THROWS
#undef RAPIDJSON_ASSERT
#define RAPIDJSON_ASSERT(x)                                                                          \
  if (!(x))                                                                                          \
  throw std::logic_error(RAPIDJSON_STRINGIFY(x))
// Because we now throw exceptions, we need to turn off RAPIDJSON_NOEXCEPT
#define RAPIDJSON_HAS_CXX11_NOEXCEPT 0
// Enable std::string overloads
#define RAPIDJSON_HAS_STDSTRING 1

#include "geojson_writter.h"
#include <algorithm>
#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/prettywriter.h"

namespace aurora {
namespace path {


    std::vector<RestrictionPtr> GetEnterRestrictions(const std::vector<RestrictionPtr>& restrictions, const uint32_t& edge_id) {
        std::vector<RestrictionPtr> enter_restrictions;

        // std::vector<RestrictionPtr> enter1_restrictions;
        // for (const auto& restriction : restrictions) {
        //     if (restriction->in_edge_id == edge_id) {
        //         enter1_restrictions.push_back(restriction);
        //     }
        //     // if (restriction->out_edge_id == edge_id) {
        //     //     enter1_restrictions.push_back(restriction);
        //     // }
        // }

        // if (!enter1_restrictions.empty()) {
        //     int xxx = 0;
        // }

        auto first_iter = std::lower_bound(restrictions.begin(), restrictions.end(), edge_id, [](const auto& restriction, const auto& edge_id) {
            return restriction->in_edge_id < edge_id;
        });
        auto last_iter = std::upper_bound(restrictions.begin(), restrictions.end(), edge_id, [](const auto& edge_id, const auto& restriction) {
            return edge_id < restriction->in_edge_id;
        });
        enter_restrictions.insert(enter_restrictions.end(), first_iter, last_iter);
        return enter_restrictions;
    }

    std::vector<RestrictionPtr> GetExitRestrictions(const std::vector<RestrictionPtr>& restrictions, const uint32_t& edge_id) {
        std::vector<RestrictionPtr> exit_restrictions;

        // std::vector<RestrictionPtr> enter1_restrictions;
        // for (const auto& restriction : restrictions) {
        //     // if (restriction->in_edge_id == edge_id) {
        //     //     enter1_restrictions.push_back(restriction);
        //     // }
        //     if (restriction->out_edge_id == edge_id) {
        //         enter1_restrictions.push_back(restriction);
        //     }
        // }

        // if (!enter1_restrictions.empty()) {
        //     int xxx = 0;
        // }

        auto first_iter = std::lower_bound(restrictions.begin(), restrictions.end(), edge_id, [](const auto& restriction, const auto& edge_id) {
            return restriction->out_edge_id < edge_id;
        });
        auto last_iter = std::upper_bound(restrictions.begin(), restrictions.end(), edge_id, [](const auto& edge_id, const auto& restriction) {
            return edge_id < restriction->out_edge_id;
        });
        exit_restrictions.insert(exit_restrictions.end(), first_iter, last_iter);

        return exit_restrictions;
    }

    void GeoJsonWritter::WriteDirectEdgeInfo(const DirectEdgeInfoRawPtr& edge_info, const std::string& filename) {
        if (!edge_info) {
            return;
        }

        rapidjson::Document doc;
        doc.SetObject();
        auto& allocator = doc.GetAllocator();

        // Create GeoJSON FeatureCollection
        doc.AddMember("type", "FeatureCollection", allocator);
        
        rapidjson::Value features(rapidjson::kArrayType);
        
        // Create a feature for the edge
        rapidjson::Value feature(rapidjson::kObjectType);
        feature.AddMember("type", "Feature", allocator);
        
        // Create geometry
        rapidjson::Value geometry(rapidjson::kObjectType);
        geometry.AddMember("type", "LineString", allocator);
        
        // Create coordinates array
        rapidjson::Value coordinates(rapidjson::kArrayType);
        
        // Add points to coordinates
        for (const auto& point : edge_info->geos) {
            rapidjson::Value coord(rapidjson::kArrayType);
            coord.PushBack(point.x(), allocator);
            coord.PushBack(point.y(), allocator);
            coordinates.PushBack(coord, allocator);
        }
        
        geometry.AddMember("coordinates", coordinates, allocator);
        feature.AddMember("geometry", geometry, allocator);
        
        // Add properties
        rapidjson::Value properties(rapidjson::kObjectType);
        properties.AddMember("id", rapidjson::Value(edge_info->id.ToString(), allocator), allocator);
        // properties.AddMember("speed",edge_info->speed_, allocator);
        feature.AddMember("properties", properties, allocator);
        
        features.PushBack(feature, allocator);
        doc.AddMember("features", features, allocator);

        // Write to file
        std::ofstream file(filename);
        if (!file.is_open()) {
            return;
        }

        rapidjson::StringBuffer buffer;
        rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
        doc.Accept(writer);
        
        file << buffer.GetString();
        file.close();
    }

    void GeoJsonWritter::WriteDirectEdgeInfos(const std::vector<DirectEdgeInfoRawPtr>& edge_infos, const std::string& filename) {
        if (edge_infos.empty()) {
            return;
        }

        rapidjson::Document doc;
        doc.SetObject();
        auto& allocator = doc.GetAllocator();
        doc.AddMember("type", "FeatureCollection", allocator);

        rapidjson::Value features(rapidjson::kArrayType);

        for (const auto& edge_info : edge_infos) {
            rapidjson::Value feature(rapidjson::kObjectType);
            feature.AddMember("type", "Feature", allocator);
            
            rapidjson::Value geometry(rapidjson::kObjectType);
            geometry.AddMember("type", "LineString", allocator);

            rapidjson::Value coordinates(rapidjson::kArrayType);

            for (const auto& point : edge_info->geos) {
                rapidjson::Value coord(rapidjson::kArrayType);
                coord.PushBack(point.x(), allocator);
                coord.PushBack(point.y(), allocator);
                coordinates.PushBack(coord, allocator);
            }

            geometry.AddMember("coordinates", coordinates, allocator);
            feature.AddMember("geometry", geometry, allocator);

            rapidjson::Value properties(rapidjson::kObjectType);
            properties.AddMember("id", rapidjson::Value(edge_info->id.ToString(), allocator), allocator);
            // level_id
            properties.AddMember("level", edge_info->level_id, allocator);
            // local index
            properties.AddMember("local_idx", edge_info->id.local_id_no_dir(), allocator);
            properties.AddMember("direction", edge_info->direction(), allocator);
            properties.AddMember("search_forward", edge_info->id.is_forward(), allocator);
            // properties.AddMember("speed", edge_info->speed_, allocator);
            feature.AddMember("properties", properties, allocator);

            features.PushBack(feature, allocator);
        }

        doc.AddMember("features", features, allocator);

        std::ofstream file(filename);
        if (!file.is_open()) {
            return;
        }

        rapidjson::StringBuffer buffer;
        rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
        doc.Accept(writer);

        file << buffer.GetString();
        file.close();
    }

    void GeoJsonWritter::WriteEdgeLabels(const std::vector<DirectEdgeInfoRawPtr>& edge_labels, const std::string& filename) {
        if (edge_labels.empty()) {
            return;
        }

        rapidjson::Document doc;
        doc.SetObject();
        auto& allocator = doc.GetAllocator();
        doc.AddMember("type", "FeatureCollection", allocator);

        rapidjson::Value features(rapidjson::kArrayType);
        
        size_t idx = 0;
        for (const auto& edge : edge_labels) {
            // auto edge = graph_reader_ptr->GetDirectEdgeInfo(edge_label.graphid());
            // const auto& edge = edge_label->edge_ptr();
            if (!edge) {
                continue;
            }
            rapidjson::Value feature(rapidjson::kObjectType);
            feature.AddMember("type", "Feature", allocator);

            rapidjson::Value geometry(rapidjson::kObjectType);
            geometry.AddMember("type", "LineString", allocator);

            rapidjson::Value coordinates(rapidjson::kArrayType);

            for (const auto& point : edge->geos) {
                rapidjson::Value coord(rapidjson::kArrayType);
                coord.PushBack(point.x(), allocator);
                coord.PushBack(point.y(), allocator);
                coordinates.PushBack(coord, allocator);
            }

            geometry.AddMember("coordinates", coordinates, allocator);
            feature.AddMember("geometry", geometry, allocator);

            rapidjson::Value properties(rapidjson::kObjectType);
            properties.AddMember("graphid", rapidjson::Value(edge->id.ToString(), allocator), allocator);
            // forward
            properties.AddMember("forward", edge->is_forward(), allocator);
            properties.AddMember("idx", idx, allocator);
            properties.AddMember("predecessor", edge->predecessor(), allocator);

            // local index
            properties.AddMember("local_idx", edge->id.local_id_no_dir(), allocator);
            properties.AddMember("level_id", edge->level_id, allocator);
            // parser::RouteTileID route_tile_id(edge->tile_id());
            properties.AddMember("mesh_col", edge->tile_id.mesh_col, allocator);
            properties.AddMember("mesh_row", edge->tile_id.mesh_row, allocator);

            properties.AddMember("cost", edge->cost().cost, allocator);
            properties.AddMember("cost_sec", edge->cost().secs, allocator);
            properties.AddMember("node_cost", edge->transition_cost().cost, allocator);
            properties.AddMember("edge_cost", edge->edge_cost().cost, allocator);
            properties.AddMember("sortcost", edge->sortcost(), allocator);
            properties.AddMember("degree", edge->turn_degree(), allocator);
            properties.AddMember("length", edge->length(), allocator);
            properties.AddMember("inner_link", edge->is_inner_edge(), allocator);
            properties.AddMember("has_traffic_light", edge->has_traffic_light(), allocator);

            feature.AddMember("properties", properties, allocator);

            features.PushBack(feature, allocator);
            idx++;
        }

        doc.AddMember("features", features, allocator);

        std::ofstream file(filename);
        if (!file.is_open()) {
            return;
        }

        rapidjson::StringBuffer buffer;
        rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
        doc.Accept(writer);

        file << buffer.GetString();
        file.close();
    }

void GeoJsonWritter::WriteRouteTileEdge(const aurora::parser::RouteTilePackagePtr& tile_package, const std::string& filename) {
    auto reader = std::make_shared<parser::RouteTileReader>();
    reader->SetTarget(tile_package);
    if (!tile_package) {
        return;
    }

    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();

    doc.AddMember("type", "FeatureCollection", allocator);
    rapidjson::Value features(rapidjson::kArrayType);

    auto&limit_pass = reader->GetLimitPass();

    std::vector<RestrictionPtr> restrictions;
    for (size_t idx = 0; idx < limit_pass.size(); ++idx) {
        auto& limit = limit_pass.at(idx);
        const auto* base = limit.GetBaseInfo();
        auto restriction = std::make_shared<Restriction>(base);
        restrictions.push_back(restriction);
    }
    LOG_INFO("restrictions size: {}", restrictions.size());
    // sort restrictions by out_edge_id
    std::vector<RestrictionPtr> exit_restrictions = restrictions;
    std::sort(exit_restrictions.begin(), exit_restrictions.end(), [](const auto& a, const auto& b) {
        return a->out_edge_id < b->out_edge_id;
    });

    auto& topo_links = reader->GetTopolEdges();
    auto& topo_augment_links = reader->GetAugmentEdges();
    DCHECK(topo_links.size() == topo_augment_links.size());
    LOG_INFO("topo_links size: {}, topo_augment_links size: {}", topo_links.size(), topo_augment_links.size());

    for (size_t i = 0; i < topo_links.size(); ++i) {
        auto& link = topo_augment_links[i];
        auto& base_link = topo_links[i];
        rapidjson::Value feature(rapidjson::kObjectType);
        feature.AddMember("type", "Feature", allocator);

        rapidjson::Value geometry(rapidjson::kObjectType);
        geometry.AddMember("type", "LineString", allocator);

        rapidjson::Value coordinates(rapidjson::kArrayType);

        auto points = link.GetGeoPoints();
        for (const auto& point : points) {
            rapidjson::Value coord(rapidjson::kArrayType);
            coord.PushBack(static_cast<double>(point.x()), allocator);
            coord.PushBack(static_cast<double>(point.y()), allocator);
            coordinates.PushBack(coord, allocator);
        }

        geometry.AddMember("coordinates", coordinates, allocator);
        feature.AddMember("geometry", geometry, allocator);
    
        rapidjson::Value properties(rapidjson::kObjectType);
        properties.AddMember("id", link.GetID(), allocator);
        // base link
        auto* baseinfo = base_link.GetBaseInfo();
        properties.AddMember("function_class", static_cast<int32_t>(baseinfo->function_class), allocator);
        properties.AddMember("direction", static_cast<int32_t>(baseinfo->direction), allocator);
        properties.AddMember("start_node_id", baseinfo->start_node_id, allocator);
        properties.AddMember("end_node_id", baseinfo->end_node_id, allocator);
        properties.AddMember("length", baseinfo->length, allocator);
        properties.AddMember("speed_grade", baseinfo->speed_grade, allocator);
        properties.AddMember("function_class", static_cast<int32_t>(baseinfo->function_class), allocator);
        properties.AddMember("is_area_link", baseinfo->is_area_edge, allocator);
        properties.AddMember("is_inner_edge", baseinfo->is_inner_edge, allocator);
        properties.AddMember("road_class", static_cast<int32_t>(baseinfo->road_class), allocator);

        properties.AddMember("edge_type", static_cast<int32_t>(baseinfo->edge_type), allocator);
        properties.AddMember("need_toll", static_cast<int32_t>(baseinfo->need_toll), allocator);
        properties.AddMember("positive_speed_limit", static_cast<int32_t>(baseinfo->positive_speed_limit), allocator);
        properties.AddMember("is_overhead", static_cast<int32_t>(baseinfo->is_overhead), allocator);
        properties.AddMember("is_separate", static_cast<int32_t>(baseinfo->is_separate), allocator);
        properties.AddMember("negtive_speed_limit", static_cast<int32_t>(baseinfo->negtive_speed_limit), allocator);
        properties.AddMember("is_city_edge", static_cast<int32_t>(baseinfo->is_city_edge), allocator);
        properties.AddMember("is_ramp", static_cast<int32_t>(baseinfo->is_ramp), allocator);
        properties.AddMember("edge_form", static_cast<int32_t>(baseinfo->edge_form), allocator);
        properties.AddMember("fwd_lane_cnt", static_cast<int32_t>(baseinfo->forward_lane_count), allocator);
        properties.AddMember("rev_lane_cnt", static_cast<int32_t>(baseinfo->backward_lane_count), allocator);
        properties.AddMember("lane_count", static_cast<int32_t>(baseinfo->lane_count), allocator);
        properties.AddMember("is_building", static_cast<int32_t>(baseinfo->is_building), allocator);
        properties.AddMember("is_paved", static_cast<int32_t>(baseinfo->is_paved), allocator);
        properties.AddMember("is_gate", static_cast<int32_t>(baseinfo->is_gate), allocator);
        properties.AddMember("no_crossing", static_cast<int32_t>(baseinfo->no_crossing), allocator);
        properties.AddMember("is_private", static_cast<int32_t>(baseinfo->is_private), allocator);

        const auto& sub_links = link.GetSubedges();
        std::string sub_link_str;
        for (const auto& link : sub_links) {
            sub_link_str += std::to_string(link->tile_id) + "_" + std::to_string(link->edge_id) + "_" + std::to_string(link->edge_dir);
        }
        properties.AddMember("sublink", sub_link_str, allocator);

        properties.AddMember("is_limit_in_edge", baseinfo->is_limit_in_edge, allocator);
        properties.AddMember("is_limit_out_edge", baseinfo->is_limit_out_edge, allocator);

        bool has_time_domain = false;
        std::string restrictions_str;
        auto link_restrictions = GetEnterRestrictions(restrictions, link.GetID());
        for (const auto& ele : link_restrictions) {
            // if (!baseinfo->is_all_day_limit && !ele->has_time_domain && !ele->access_ctrl_type && !ele->access_ctrl_relation) {
            //     LOG_INFO("start id {} xx:{}", link.GetID(), ele->ToString());
            // }
            restrictions_str += ele->ToString();
        }
        properties.AddMember("enter_restrictions", restrictions_str, allocator);
        link_restrictions = GetExitRestrictions(exit_restrictions, link.GetID());
        restrictions_str.clear();
        for (const auto& ele : link_restrictions) {
            // if (!baseinfo->is_all_day_limit && !ele->has_time_domain && !ele->access_ctrl_type && !ele->access_ctrl_relation) {
            //     LOG_INFO("end id {} xx:{}", link.GetID(), ele->ToString());
            // }
            restrictions_str += ele->ToString();
        }
        properties.AddMember("exit_restrictions", restrictions_str, allocator);
        properties.AddMember("has_time_domain", has_time_domain, allocator);

        feature.AddMember("properties", properties, allocator);

        features.PushBack(feature, allocator);
    }
    doc.AddMember("features", features, allocator);

    // Write to file
    std::ofstream file(filename);
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);
    
    file << buffer.GetString();
    file.close();
}

void GeoJsonWritter::WriteRouteTileNode(const aurora::parser::RouteTilePackagePtr& tile_package, const std::string& filename) {
    if (!tile_package) {
        return;
    }
    auto reader = std::make_shared<parser::RouteTileReader>();
    reader->SetTarget(tile_package);

    rapidjson::Document document;
    document.SetObject();
    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
  
    PointLL center(121.46611, 31.2246);
    rapidjson::Value features(rapidjson::kArrayType);
    std::vector<parser::RouteNode>& nodes = reader->GetNodes();
    int node_count = nodes.size();

    for (int i = 0; i < node_count; ++i) {
      auto& current_node = nodes[i];
    //   auto ll = current_node->latlng(header()->base_ll());
    //   if (ll.Distance(center) > 5000) {
    //     continue;
    //   }
      auto point = current_node.GetPosition();
  
      rapidjson::Value feature(rapidjson::kObjectType);
      feature.AddMember("type", "Feature", allocator);
  
      rapidjson::Value geometry(rapidjson::kObjectType);
      geometry.AddMember("type", "Point", allocator);
      rapidjson::Value coordinates(rapidjson::kArrayType);
      coordinates.PushBack(static_cast<double>(point.x()), allocator);
      coordinates.PushBack(static_cast<double>(point.y()), allocator);
      geometry.AddMember("coordinates", coordinates, allocator);
      feature.AddMember("geometry", geometry, allocator);
  
      rapidjson::Value properties(rapidjson::kObjectType);
      // properties.AddMember("node_id", current_node->id().value, allocator);
      properties.AddMember("id", current_node.GetID(), allocator);
      auto* base_info = current_node.GetBaseInfo();
      properties.AddMember("link_count", static_cast<int32_t>(base_info->connect_edge_count), allocator);
//      properties.AddMember("traffic_light", static_cast<int32_t>(base_info->has_traffic_light), allocator);

      auto connected_links = current_node.GetConnectedEdge();
      std::string connected_links_str("");
      for (size_t idx = 0; idx < connected_links.size(); ++idx) {
        connected_links_str += std::to_string(connected_links.at(idx)->edge_id) + ";";
      }
      std::string connected_angle_str("");
      for (size_t idx = 0; idx < connected_links.size(); ++idx) {
        connected_angle_str += std::to_string(connected_links.at(idx)->angle) + ";";
      }
      properties.AddMember("connected_links", connected_links_str, allocator);
      properties.AddMember("connected_angle", connected_angle_str, allocator);

      auto boundary_node = current_node.GetBoundaryNode();
      std::string boundary_node_str("");
      for (size_t idx = 0; idx < boundary_node.size(); ++idx) {
        boundary_node_str += std::to_string(boundary_node.at(idx)->adj_node_id) + ";";
      }
      properties.AddMember("boundary_node", boundary_node_str, allocator);

      auto transup_node = current_node.GetTransUpNodeInfo();
      if (transup_node) {
        std::string transup_node_str("");
        transup_node_str += std::to_string(transup_node->opp_tile_id) + "_" + std::to_string(transup_node->opp_node_id);
        properties.AddMember("transup_node", transup_node_str, allocator);
        properties.AddMember("is_transup", true, allocator);
      }

      auto transdown_node = current_node.GetTransDownNodeInfo();
      if (transdown_node) {
        std::string transdown_node_str("");
        transdown_node_str += std::to_string(transdown_node->opp_tile_id) + "_" + std::to_string(transdown_node->opp_node_id);
        properties.AddMember("transdown_node", transdown_node_str, allocator);
        properties.AddMember("is_transdown", true, allocator);
      }

      auto transup_edges = current_node.GetTransUpEdgeInfo();
      std::string transup_edge_str("");
      for (size_t idx = 0; idx < transup_edges.size(); ++idx) {
        transup_edge_str += std::to_string(transup_edges.at(idx)->opp_tile_id) + "_" + std::to_string(transup_edges.at(idx)->opp_edge_id) + ";";
      }
      properties.AddMember("transup_edges", transup_edge_str, allocator);

      auto transdown_edges = current_node.GetTransDownEdgeInfo();
      std::string transdown_edge_str("");
      for (size_t idx = 0; idx < transdown_edges.size(); ++idx) {
        transdown_edge_str += std::to_string(transdown_edges.at(idx)->opp_tile_id) + "_" + std::to_string(transdown_edges.at(idx)->opp_edge_id) + ";";
      }
      properties.AddMember("transdown_edges", transdown_edge_str, allocator);
      
      
    //   properties.AddMember("tile_id", tile_id, allocator);
    //   properties.AddMember("level", level_id, allocator);
    //   properties.AddMember("local_idx", i, allocator);
    //   properties.AddMember("edge_idx", current_node->edge_index(), allocator);
    //   properties.AddMember("edge_cnt", current_node->edge_count(), allocator);
    //   properties.AddMember("access", current_node->access(), allocator);
    //   properties.AddMember("type", static_cast<int>(current_node->type()), allocator);
    //   // properties.AddMember("named", static_cast<int>(current_node->named()), allocator);
    //   properties.AddMember("transition_index", current_node->transition_index(), allocator);
    //   properties.AddMember("transition_count_", current_node->transition_count(), allocator);
    //   properties.AddMember("local_edge_count", current_node->local_edge_count(), allocator);
    //   const NodeTransition* trans = transition(current_node->transition_index()); // 跨层搜索。
    //   std::string tran_str("");
    //   for (uint32_t i = 0; i < current_node->transition_count(); ++i, ++trans) {
    //     tran_str += std::to_string(trans->up()) + "_" + std::to_string(trans->endnode()) + ";";
    //   }
    //   properties.AddMember("tran_info", tran_str, allocator);
      // properties.AddMember("headings", current_node->headings(), allocator);
      // properties.AddMember("driveable", static_cast<int>(current_node->driveability()), allocator);
      feature.AddMember("properties", properties, allocator);
  
      features.PushBack(feature, allocator);
    }

    document.AddMember("type", "FeatureCollection", allocator);
    document.AddMember("features", features, allocator);
  
    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);
    // uint32_t tile_id = 1;
    uint32_t level_id = 0;
    uint32_t iden_id = 1;
    parser::RouteTileID tile_id = reader->GetTileID(); 
    std::ofstream file(filename);
    file << buffer.GetString();
}

void GeoJsonWritter::WriteRouteTileEdges(const std::vector<aurora::parser::RouteTilePackagePtr>& tile_packages, const std::string& filename) {
    if (tile_packages.empty()) {
        return;
    }
    auto reader = std::make_shared<parser::RouteTileReader>();

    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();

    doc.AddMember("type", "FeatureCollection", allocator);
    rapidjson::Value features(rapidjson::kArrayType);

    for (const auto& tile_package : tile_packages) {
        if (!tile_package) {
            continue;
        }
        reader->SetTarget(tile_package);

        auto&limit_pass = reader->GetLimitPass();

        std::vector<RestrictionPtr> restrictions;
        for (size_t idx = 0; idx < limit_pass.size(); ++idx) {
            auto& limit = limit_pass.at(idx);
            const auto* base = limit.GetBaseInfo();
            auto restriction = std::make_shared<Restriction>(base);
            restrictions.push_back(restriction);
        }
        LOG_INFO("restrictions size: {}", restrictions.size());
        std::vector<RestrictionPtr> exit_restrictions = restrictions;
        std::sort(exit_restrictions.begin(), exit_restrictions.end(), [](const auto& a, const auto& b) {
            return a->out_edge_id < b->out_edge_id;
        });

        auto& topo_links = reader->GetTopolEdges();
        auto& topo_augment_links = reader->GetAugmentEdges();
        DCHECK(topo_links.size() == topo_augment_links.size());
        LOG_INFO("topo_links size: {}, topo_augment_links size: {}", topo_links.size(), topo_augment_links.size());

        for (size_t i = 0; i < topo_links.size(); ++i) {
            auto& link = topo_augment_links[i];
            auto& base_link = topo_links[i];
            rapidjson::Value feature(rapidjson::kObjectType);
            feature.AddMember("type", "Feature", allocator);

            rapidjson::Value geometry(rapidjson::kObjectType);
            geometry.AddMember("type", "LineString", allocator);

            rapidjson::Value coordinates(rapidjson::kArrayType);

            auto points = link.GetGeoPoints();
            for (const auto& point : points) {
                rapidjson::Value coord(rapidjson::kArrayType);
                coord.PushBack(static_cast<double>(point.x()), allocator);
                coord.PushBack(static_cast<double>(point.y()), allocator);
                coordinates.PushBack(coord, allocator);
            }

            geometry.AddMember("coordinates", coordinates, allocator);
            feature.AddMember("geometry", geometry, allocator);
        
            rapidjson::Value properties(rapidjson::kObjectType);
            properties.AddMember("id", link.GetID(), allocator);
            // base link
            auto* baseinfo = base_link.GetBaseInfo();
            properties.AddMember("function_class", static_cast<int32_t>(baseinfo->function_class), allocator);
            properties.AddMember("direction", static_cast<int32_t>(baseinfo->direction), allocator);
            properties.AddMember("start_node_id", baseinfo->start_node_id, allocator);
            properties.AddMember("end_node_id", baseinfo->end_node_id, allocator);
            properties.AddMember("length", baseinfo->length, allocator);
            properties.AddMember("speed_grade", baseinfo->speed_grade, allocator);
            properties.AddMember("function_class", static_cast<int32_t>(baseinfo->function_class), allocator);
            properties.AddMember("is_area_link", baseinfo->is_area_edge, allocator);
            properties.AddMember("is_inner_edge", baseinfo->is_inner_edge, allocator);
            properties.AddMember("road_class", static_cast<int32_t>(baseinfo->road_class), allocator);

            properties.AddMember("edge_type", static_cast<int32_t>(baseinfo->edge_type), allocator);
            properties.AddMember("need_toll", static_cast<int32_t>(baseinfo->need_toll), allocator);
            properties.AddMember("positive_speed_limit", static_cast<int32_t>(baseinfo->positive_speed_limit), allocator);
            properties.AddMember("is_overhead", static_cast<int32_t>(baseinfo->is_overhead), allocator);
            properties.AddMember("is_separate", static_cast<int32_t>(baseinfo->is_separate), allocator);
            properties.AddMember("negtive_speed_limit", static_cast<int32_t>(baseinfo->negtive_speed_limit), allocator);
            properties.AddMember("is_city_edge", static_cast<int32_t>(baseinfo->is_city_edge), allocator);
            properties.AddMember("is_ramp", static_cast<int32_t>(baseinfo->is_ramp), allocator);
            properties.AddMember("edge_form", static_cast<int32_t>(baseinfo->edge_form), allocator);
            properties.AddMember("fwd_lane_cnt", static_cast<int32_t>(baseinfo->forward_lane_count), allocator);
            properties.AddMember("rev_lane_cnt", static_cast<int32_t>(baseinfo->backward_lane_count), allocator);
            properties.AddMember("lane_count", static_cast<int32_t>(baseinfo->lane_count), allocator);
            properties.AddMember("is_building", static_cast<int32_t>(baseinfo->is_building), allocator);
            properties.AddMember("is_paved", static_cast<int32_t>(baseinfo->is_paved), allocator);
            properties.AddMember("is_gate", static_cast<int32_t>(baseinfo->is_gate), allocator);
            properties.AddMember("no_crossing", static_cast<int32_t>(baseinfo->no_crossing), allocator);
            properties.AddMember("is_private", static_cast<int32_t>(baseinfo->is_private), allocator);

            const auto& sub_links = link.GetSubedges();
            std::string sub_link_str;
            for (const auto& link : sub_links) {
                sub_link_str += std::to_string(link->tile_id) + "_" + std::to_string(link->edge_id) + "_" + std::to_string(link->edge_dir);
            }
            properties.AddMember("sublink", sub_link_str, allocator);

            properties.AddMember("is_limit_in_edge", baseinfo->is_limit_in_edge, allocator);
            properties.AddMember("is_limit_out_edge", baseinfo->is_limit_out_edge, allocator);

            bool has_time_domain = false;
            std::string restrictions_str;
            auto link_restrictions = GetEnterRestrictions(restrictions, link.GetID());
            for (const auto& ele : link_restrictions) {
                // if (!baseinfo->is_all_day_limit && !ele->has_time_domain && !ele->access_ctrl_type && !ele->access_ctrl_relation) {
                //     LOG_INFO("start id {} xx:{}", link.GetID(), ele->ToString());
                // }
                restrictions_str += ele->ToString();
            }
            properties.AddMember("enter_restrictions", restrictions_str, allocator);
            link_restrictions = GetExitRestrictions(exit_restrictions, link.GetID());
            restrictions_str.clear();
            for (const auto& ele : link_restrictions) {
                // if (!baseinfo->is_all_day_limit && !ele->has_time_domain && !ele->access_ctrl_type && !ele->access_ctrl_relation) {
                //     LOG_INFO("end id {} xx:{}", link.GetID(), ele->ToString());
                // }
                restrictions_str += ele->ToString();
            }
            properties.AddMember("exit_restrictions", restrictions_str, allocator);
            properties.AddMember("has_time_domain", has_time_domain, allocator);

            // std::string restrictions_str;
            // auto link_restrictions = GetEnterRestrictions(restrictions, link.GetID());
            // for (const auto& ele : link_restrictions) {
            //     restrictions_str += ele->ToString();
            // }
            // properties.AddMember("restrictions", restrictions_str, allocator);

            feature.AddMember("properties", properties, allocator);

            features.PushBack(feature, allocator);
        }
    }
    
    doc.AddMember("features", features, allocator);

    // Write to file
    std::ofstream file(filename);
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);
    
    file << buffer.GetString();
    file.close();
}

void GeoJsonWritter::WriteRouteTileNodes(const std::vector<aurora::parser::RouteTilePackagePtr>& tile_packages, const std::string& filename) {
    if (tile_packages.empty()) {
        return;
    }
    auto reader = std::make_shared<parser::RouteTileReader>();

    rapidjson::Document document;
    document.SetObject();
    rapidjson::Document::AllocatorType& allocator = document.GetAllocator();
  
    PointLL center(121.46611, 31.2246);
    rapidjson::Value features(rapidjson::kArrayType);
    
    for (const auto& tile_package : tile_packages) {
        if (!tile_package) {
            continue;
        }
        reader->SetTarget(tile_package);
        
        std::vector<parser::RouteNode>& nodes = reader->GetNodes();
        int node_count = nodes.size();

        for (int i = 0; i < node_count; ++i) {
            auto& current_node = nodes[i];
            auto point = current_node.GetPosition();
      
            rapidjson::Value feature(rapidjson::kObjectType);
            feature.AddMember("type", "Feature", allocator);
      
            rapidjson::Value geometry(rapidjson::kObjectType);
            geometry.AddMember("type", "Point", allocator);
            rapidjson::Value coordinates(rapidjson::kArrayType);
            coordinates.PushBack(static_cast<double>(point.x()), allocator);
            coordinates.PushBack(static_cast<double>(point.y()), allocator);
            geometry.AddMember("coordinates", coordinates, allocator);
            feature.AddMember("geometry", geometry, allocator);
      
            rapidjson::Value properties(rapidjson::kObjectType);
            properties.AddMember("id", current_node.GetID(), allocator);
            auto* base_info = current_node.GetBaseInfo();
            properties.AddMember("link_count", static_cast<int32_t>(base_info->connect_edge_count), allocator);
//            properties.AddMember("traffic_light", static_cast<int32_t>(base_info->has_traffic_light), allocator);

            auto connected_links = current_node.GetConnectedEdge();
            std::string connected_links_str("");
            for (size_t idx = 0; idx < connected_links.size(); ++idx) {
                connected_links_str += std::to_string(connected_links.at(idx)->edge_id) + ";";
            }
            properties.AddMember("connected_links", connected_links_str, allocator);

            auto boundary_node = current_node.GetBoundaryNode();
            std::string boundary_node_str("");
            for (size_t idx = 0; idx < boundary_node.size(); ++idx) {
                boundary_node_str += std::to_string(boundary_node.at(idx)->adj_node_id) + ";";
            }
            properties.AddMember("boundary_node", boundary_node_str, allocator);
            
            feature.AddMember("properties", properties, allocator);
      
            features.PushBack(feature, allocator);
        }
    }

    document.AddMember("type", "FeatureCollection", allocator);
    document.AddMember("features", features, allocator);
  
    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    document.Accept(writer);
    
    std::ofstream file(filename);
    file << buffer.GetString();
}

void GeoJsonWritter::WritePathResult(const PathResult& path_result, const std::string& filename) {
    if (path_result.paths.empty()) {
        return;
    }

    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();
    doc.AddMember("type", "FeatureCollection", allocator);

    rapidjson::Value features(rapidjson::kArrayType);

    for (const auto& path : path_result.paths) {
        rapidjson::Value feature(rapidjson::kObjectType);
        feature.AddMember("type", "Feature", allocator);

        rapidjson::Value geometry(rapidjson::kObjectType);
        geometry.AddMember("type", "LineString", allocator);

        rapidjson::Value coordinates(rapidjson::kArrayType);
        for (const auto& point : path.points) {
            rapidjson::Value coord(rapidjson::kArrayType);
            coord.PushBack(static_cast<double>(point.x()), allocator);
            coord.PushBack(static_cast<double>(point.y()), allocator);
            coordinates.PushBack(coord, allocator);
        }

        geometry.AddMember("coordinates", coordinates, allocator);
        feature.AddMember("geometry", geometry, allocator);

        rapidjson::Value properties(rapidjson::kObjectType);
        properties.AddMember("id", path.path_id, allocator);
        properties.AddMember("travel_time", path.travel_time, allocator);
        properties.AddMember("length", path.length, allocator);
        properties.AddMember("sec_size", path.sections.size(), allocator);
        std::string sec_str;
        for (const auto& sec : path.sections) {
            sec_str += std::to_string(sec.index) +", "+ std::to_string(sec.num) +", " + std::to_string(sec.length) + ", "
                + std::to_string(sec.time) + ", offset " + std::to_string(sec.start_offset)  + ", "+ std::to_string(sec.end_offset) + ";";
        }
        properties.AddMember("sec_str", sec_str, allocator);

        properties.AddMember("uuid", path_result.uuid, allocator);
        properties.AddMember("status", path_result.status, allocator);
        properties.AddMember("tag", path_result.tag, allocator);
        properties.AddMember("code", path_result.code, allocator);
        properties.AddMember("query_time", path_result.metadata.query_time_ms, allocator);

        feature.AddMember("properties", properties, allocator);

        features.PushBack(feature, allocator);
    }

    doc.AddMember("features", features, allocator);

    std::ofstream file(filename);
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);

    file << buffer.GetString();
    file.close();
}

void GeoJsonWritter::WritePathQuery(const PathQueryPtr& path_query, const std::string& filename) {
    if (!path_query) {
        return;
    }

    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();
    doc.AddMember("type", "FeatureCollection", allocator);

    rapidjson::Value features(rapidjson::kArrayType);
    for (const auto& land_mark : path_query->path_points) {
        rapidjson::Value feature(rapidjson::kObjectType);
        feature.AddMember("type", "Feature", allocator);

        rapidjson::Value geometry(rapidjson::kObjectType);
        geometry.AddMember("type", "Point", allocator);

        rapidjson::Value coord(rapidjson::kArrayType);
        coord.PushBack(static_cast<double>(land_mark->pt.x()), allocator);
        coord.PushBack(static_cast<double>(land_mark->pt.y()), allocator);

        geometry.AddMember("coordinates", coord, allocator);
        feature.AddMember("geometry", geometry, allocator);

        rapidjson::Value properties(rapidjson::kObjectType);
        properties.AddMember("id", "via", allocator);
        properties.AddMember("waypoint_type", static_cast<int32_t>(land_mark->waypoint_type), allocator);
        properties.AddMember("landmark_type", static_cast<int32_t>(land_mark->landmark_type), allocator);
        properties.AddMember("valid", land_mark->valid, allocator);
        // add name
        properties.AddMember("name", land_mark->name, allocator);
        // merge candidates to one string
        std::string candidates_str("");
        for (const auto& candidate : land_mark->candidates) {
            candidates_str += std::to_string(candidate.link->id) + "_" + std::to_string(candidate.offset) + ";";
        }
        properties.AddMember("candidates", candidates_str, allocator);
        feature.AddMember("properties", properties, allocator);
        features.PushBack(feature, allocator);
    }
    doc.AddMember("features", features, allocator);

    std::ofstream file(filename);
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);

    file << buffer.GetString();
    file.close();
}

} // namespace path 
} // namespace aurora


