// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-15
//

#include "route_evaluator.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cmath>
#include "logger.h"

namespace aurora {
namespace path {

RouteEvaluator::RouteEvaluator() {
    // 初始化
}

RouteEvaluationResult RouteEvaluator::EvaluateRoute(const PathQueryPtr& query, 
                                                  const PathResultPtr& result, 
                                                  size_t path_index) {
    RouteEvaluationResult eval_result;
    
    // 检查路径索引是否有效
    if (result->paths.size() <= path_index) {
        std::cerr << "Invalid path index: " << path_index << ", total paths: " << result->paths.size() << std::endl;
        return eval_result;
    }
    
    const auto& path = result->paths[path_index];
    
    // 填充基本信息
    eval_result.path_id = path.path_id;
    eval_result.route_name = "Route " + std::to_string(path_index + 1);
    eval_result.length = path.length;
    eval_result.travel_time = path.travel_time;
    eval_result.traffic_light_num = path.traffic_light_num;
    eval_result.point_count = path.points.size();
    
    // 几何相似度默认为1.0（与自身比较）
    eval_result.geometric_similarity = 1.0;
    
    return eval_result;
}

RouteComparisonResult RouteEvaluator::CompareRoutes(const PathQueryPtr& query,
                                                  const PathResultPtr& result,
                                                  size_t path_index1,
                                                  size_t path_index2) {
    RouteComparisonResult comparison;
    
    // 检查路径索引是否有效
    if (result->paths.size() <= path_index1 || result->paths.size() <= path_index2) {
        std::cerr << "Invalid path indices: " << path_index1 << ", " << path_index2 
                  << ", total paths: " << result->paths.size() << std::endl;
        return comparison;
    }
    
    // 评测两条路线
    comparison.route1 = EvaluateRoute(query, result, path_index1);
    comparison.route2 = EvaluateRoute(query, result, path_index2);
    
    // 计算差异
    comparison.length_diff_percent = CalculatePercentageDiff(comparison.route1.length, comparison.route2.length);
    comparison.time_diff_percent = CalculatePercentageDiff(comparison.route1.travel_time, comparison.route2.travel_time);
    comparison.traffic_light_diff = static_cast<int32_t>(comparison.route1.traffic_light_num) - 
                                   static_cast<int32_t>(comparison.route2.traffic_light_num);
    
    // 计算几何相似度
    const auto& points1 = result->paths[path_index1].points;
    const auto& points2 = result->paths[path_index2].points;
    comparison.geometric_similarity = CalculateGeometricSimilarity(points1, points2);
    
    return comparison;
}

RouteComparisonResult RouteEvaluator::CompareRoutes(const PathQueryPtr& query1,
                                                  const PathResultPtr& result1,
                                                  size_t path_index1,
                                                  const PathQueryPtr& query2,
                                                  const PathResultPtr& result2,
                                                  size_t path_index2) {
    RouteComparisonResult comparison;
    
    // 检查路径索引是否有效
    if (result1->paths.size() <= path_index1 || result2->paths.size() <= path_index2) {
        std::cerr << "Invalid path indices: " << path_index1 << ", " << path_index2 
                  << ", total paths: " << result1->paths.size() << ", " << result2->paths.size() << std::endl;
        return comparison;
    }
    
    // 评测两条路线
    comparison.route1 = EvaluateRoute(query1, result1, path_index1);
    comparison.route2 = EvaluateRoute(query2, result2, path_index2);
    
    // 计算差异
    comparison.length_diff_percent = CalculatePercentageDiff(comparison.route1.length, comparison.route2.length);
    comparison.time_diff_percent = CalculatePercentageDiff(comparison.route1.travel_time, comparison.route2.travel_time);
    comparison.traffic_light_diff = static_cast<int32_t>(comparison.route1.traffic_light_num) - 
                                   static_cast<int32_t>(comparison.route2.traffic_light_num);
    
    // 计算几何相似度
    const auto& points1 = result1->paths[path_index1].points;
    const auto& points2 = result2->paths[path_index2].points;
    comparison.geometric_similarity = CalculateGeometricSimilarity(points1, points2);
    
    return comparison;
}

void RouteEvaluator::PrintEvaluationResult(const RouteEvaluationResult& result) {
    LOG_INFO("===== 路线评测结果 =====");
    LOG_INFO("路线名称: {} (ID: {})", result.route_name, result.path_id);
    LOG_INFO("路线长度: {}", FormatDistance(result.length));
    LOG_INFO("通行时间: {}", FormatTime(result.travel_time));
    LOG_INFO("红绿灯数量: {}", result.traffic_light_num);
    LOG_INFO("路径点数量: {}", result.point_count);
    LOG_INFO("========================");
}

void RouteEvaluator::PrintComparisonResult(const RouteComparisonResult& result) {
    LOG_INFO("======= 路线对比结果 =======");
    LOG_INFO("路线1: {} (ID: {})", result.route1.route_name, result.route1.path_id);
    LOG_INFO("  长度: {}", FormatDistance(result.route1.length));
    LOG_INFO("  时间: {}", FormatTime(result.route1.travel_time));
    LOG_INFO("  红绿灯: {}", result.route1.traffic_light_num);
    LOG_INFO("");
    
    LOG_INFO("路线2: {} (ID: {})", result.route2.route_name, result.route2.path_id);
    LOG_INFO("  长度: {}", FormatDistance(result.route2.length));
    LOG_INFO("  时间: {}", FormatTime(result.route2.travel_time));
    LOG_INFO("  红绿灯: {}", result.route2.traffic_light_num);
    LOG_INFO("");
    
    LOG_INFO("差异分析:");
    LOG_INFO("  长度差异: {:.2f}%", result.length_diff_percent);
    LOG_INFO("  时间差异: {:.2f}%", result.time_diff_percent);
    LOG_INFO("  红绿灯差异: {}", result.traffic_light_diff);
    LOG_INFO("  几何相似度: {:.2f}%", result.geometric_similarity * 100);
    LOG_INFO("===========================");
}

std::string RouteEvaluator::GenerateReport(const std::vector<RouteEvaluationResult>& results) {
    std::stringstream ss;
    
    ss << "=============== 路线评测报告 ===============" << std::endl;
    ss << "总计评测路线数: " << results.size() << std::endl << std::endl;
    
    // 表头
    ss << std::setw(10) << "路线" << std::setw(15) << "长度" << std::setw(15) 
       << "时间" << std::setw(10) << "红绿灯" << std::setw(10) << "点数量" << std::endl;
    ss << std::string(60, '-') << std::endl;
    
    // 表内容
    for (const auto& result : results) {
        ss << std::setw(10) << result.route_name 
           << std::setw(15) << FormatDistance(result.length) 
           << std::setw(15) << FormatTime(result.travel_time) 
           << std::setw(10) << result.traffic_light_num 
           << std::setw(10) << result.point_count << std::endl;
    }
    
    ss << std::string(60, '-') << std::endl;
    ss << "==========================================" << std::endl;
    
    std::string report = ss.str();
    LOG_INFO("{}", report);
    return report;
}

std::string RouteEvaluator::GenerateComparisonReport(const RouteComparisonResult& comparison) {
    std::stringstream ss;
    
    ss << "=============== 路线对比报告 ===============" << std::endl;
    
    // 基本信息表格
    ss << std::setw(20) << "指标" << std::setw(20) << comparison.route1.route_name 
       << std::setw(20) << comparison.route2.route_name << std::setw(20) << "差异" << std::endl;
    ss << std::string(80, '-') << std::endl;
    
    // 长度对比
    ss << std::setw(20) << "路线长度" 
       << std::setw(20) << FormatDistance(comparison.route1.length) 
       << std::setw(20) << FormatDistance(comparison.route2.length)
       << std::setw(20) << std::fixed << std::setprecision(2) << comparison.length_diff_percent << "%" << std::endl;
    
    // 时间对比
    ss << std::setw(20) << "通行时间" 
       << std::setw(20) << FormatTime(comparison.route1.travel_time) 
       << std::setw(20) << FormatTime(comparison.route2.travel_time)
       << std::setw(20) << std::fixed << std::setprecision(2) << comparison.time_diff_percent << "%" << std::endl;
    
    // 红绿灯对比
    ss << std::setw(20) << "红绿灯数量" 
       << std::setw(20) << comparison.route1.traffic_light_num 
       << std::setw(20) << comparison.route2.traffic_light_num
       << std::setw(20) << comparison.traffic_light_diff << std::endl;
    
    // 几何相似度
    ss << std::setw(20) << "几何相似度" 
       << std::setw(60) << std::fixed << std::setprecision(2) << comparison.geometric_similarity * 100 << "%" << std::endl;
    
    ss << std::string(80, '-') << std::endl;
    
    // 结论
    ss << "结论: ";
    if (comparison.length_diff_percent < 5.0 && comparison.time_diff_percent < 5.0 && 
        comparison.geometric_similarity > 0.9) {
        ss << "两条路线非常相似，差异很小。" << std::endl;
    } else if (comparison.length_diff_percent < 10.0 && comparison.time_diff_percent < 10.0 && 
               comparison.geometric_similarity > 0.7) {
        ss << "两条路线较为相似，存在一定差异。" << std::endl;
    } else if (comparison.geometric_similarity < 0.3) {
        ss << "两条路线差异显著，几何形状相差较大。" << std::endl;
    } else {
        ss << "两条路线存在明显差异。" << std::endl;
    }
    
    ss << "============================================" << std::endl;
    
    std::string report = ss.str();
    LOG_INFO("{}", report);
    return report;
}

double RouteEvaluator::CalculateGeometricSimilarity(const std::vector<PointLL>& points1,
                                                  const std::vector<PointLL>& points2) {
    // 如果任一路径为空，则相似度为0
    if (points1.empty() || points2.empty()) {
        return 0.0;
    }
    
    // 如果两条路径完全相同，则相似度为1
    if (points1.size() == points2.size()) {
        bool identical = true;
        for (size_t i = 0; i < points1.size(); ++i) {
            if (!points1[i].ApproximatelyEqual(points2[i])) {
                identical = false;
                break;
            }
        }
        if (identical) {
            return 1.0;
        }
    }
    
    // 使用Fréchet距离的简化版本计算相似度
    // 这里使用一个简化的方法：对每个点找到另一条路径上最近的点，计算平均距离
    double total_distance = 0.0;
    size_t count = 0;
    
    // 从路径1采样点
    size_t step1 = std::max(size_t(1), points1.size() / 20); // 最多采样20个点
    for (size_t i = 0; i < points1.size(); i += step1) {
        double min_distance = std::numeric_limits<double>::max();
        for (const auto& p2 : points2) {
            double dist = points1[i].Distance(p2);
            min_distance = std::min(min_distance, dist);
        }
        total_distance += min_distance;
        count++;
    }
    
    // 从路径2采样点
    size_t step2 = std::max(size_t(1), points2.size() / 20); // 最多采样20个点
    for (size_t i = 0; i < points2.size(); i += step2) {
        double min_distance = std::numeric_limits<double>::max();
        for (const auto& p1 : points1) {
            double dist = points2[i].Distance(p1);
            min_distance = std::min(min_distance, dist);
        }
        total_distance += min_distance;
        count++;
    }
    
    // 计算平均距离
    double avg_distance = total_distance / count;
    
    // 将距离转换为相似度 (0-1)
    // 使用指数衰减函数: similarity = e^(-avg_distance/scale_factor)
    // 这里scale_factor是一个缩放因子，可以根据实际情况调整
    double scale_factor = 500.0; // 500米作为参考距离
    double similarity = std::exp(-avg_distance / scale_factor);
    
    return similarity;
}

double RouteEvaluator::CalculateDistance(const PointLL& p1, const PointLL& p2) {
    return p1.Distance(p2);
}

double RouteEvaluator::CalculatePercentageDiff(double value1, double value2) {
    if (value1 == 0 && value2 == 0) {
        return 0.0;
    }
    
    double base = std::max(std::abs(value1), std::abs(value2));
    return std::abs(value1 - value2) / base * 100.0;
}

std::string RouteEvaluator::FormatTime(double seconds) {
    int hours = static_cast<int>(seconds / 3600);
    int minutes = static_cast<int>((seconds - hours * 3600) / 60);
    int secs = static_cast<int>(seconds - hours * 3600 - minutes * 60);
    
    std::stringstream ss;
    if (hours > 0) {
        ss << hours << "小时";
    }
    if (minutes > 0 || hours > 0) {
        ss << minutes << "分";
    }
    ss << secs << "秒";
    
    return ss.str();
}

std::string RouteEvaluator::FormatDistance(double meters) {
    if (meters >= 1000) {
        return std::to_string(static_cast<int>(meters / 100) / 10.0) + "公里";
    } else {
        return std::to_string(static_cast<int>(meters)) + "米";
    }
}

std::string RouteEvaluator::GenerateVisualization(const std::vector<RouteEvaluationResult>& results) {
    if (results.empty()) {
        return "无数据可视化\n";
    }

    std::stringstream ss;
    ss << "=============== 路线对比可视化 ===============" << std::endl;

    // 找到最大值用于归一化
    double max_length = 0;
    double max_time = 0;
    uint32_t max_lights = 0;

    for (const auto& result : results) {
        max_length = std::max(max_length, result.length);
        max_time = std::max(max_time, result.travel_time);
        max_lights = std::max(max_lights, result.traffic_light_num);
    }

    // 生成长度对比图
    ss << "\n路线长度对比:" << std::endl;
    for (const auto& result : results) {
        int bar_length = static_cast<int>((result.length / max_length) * 40);
        ss << std::setw(10) << result.route_name << " |";
        for (int i = 0; i < bar_length; ++i) {
            ss << "█";
        }
        ss << " " << FormatDistance(result.length) << std::endl;
    }

    // 生成时间对比图
    ss << "\n通行时间对比:" << std::endl;
    for (const auto& result : results) {
        int bar_length = static_cast<int>((result.travel_time / max_time) * 40);
        ss << std::setw(10) << result.route_name << " |";
        for (int i = 0; i < bar_length; ++i) {
            ss << "█";
        }
        ss << " " << FormatTime(result.travel_time) << std::endl;
    }

    // 生成红绿灯对比图
    if (max_lights > 0) {
        ss << "\n红绿灯数量对比:" << std::endl;
        for (const auto& result : results) {
            int bar_length = static_cast<int>((static_cast<double>(result.traffic_light_num) / max_lights) * 40);
            ss << std::setw(10) << result.route_name << " |";
            for (int i = 0; i < bar_length; ++i) {
                ss << "█";
            }
            ss << " " << result.traffic_light_num << "个" << std::endl;
        }
    }

    ss << "=============================================" << std::endl;
    std::string report = ss.str();
    LOG_INFO("{}", report);
    return report;
}

std::string RouteEvaluator::GenerateComparisonVisualization(const RouteComparisonResult& comparison) {
    std::stringstream ss;

    ss << "=============== 路线差异可视化 ===============" << std::endl;

    // 长度差异可视化
    ss << "\n长度差异: " << std::fixed << std::setprecision(1) << comparison.length_diff_percent << "%" << std::endl;
    int length_diff_bar = static_cast<int>(std::min(comparison.length_diff_percent, 50.0) / 50.0 * 30);
    ss << "差异程度: |";
    for (int i = 0; i < length_diff_bar; ++i) {
        ss << "█";
    }
    for (int i = length_diff_bar; i < 30; ++i) {
        ss << "░";
    }
    ss << "|" << std::endl;

    // 时间差异可视化
    ss << "\n时间差异: " << std::fixed << std::setprecision(1) << comparison.time_diff_percent << "%" << std::endl;
    int time_diff_bar = static_cast<int>(std::min(comparison.time_diff_percent, 50.0) / 50.0 * 30);
    ss << "差异程度: |";
    for (int i = 0; i < time_diff_bar; ++i) {
        ss << "█";
    }
    for (int i = time_diff_bar; i < 30; ++i) {
        ss << "░";
    }
    ss << "|" << std::endl;

    // 几何相似度可视化
    ss << "\n几何相似度: " << std::fixed << std::setprecision(1) << comparison.geometric_similarity * 100 << "%" << std::endl;
    int similarity_bar = static_cast<int>(comparison.geometric_similarity * 30);
    ss << "相似程度: |";
    for (int i = 0; i < similarity_bar; ++i) {
        ss << "█";
    }
    for (int i = similarity_bar; i < 30; ++i) {
        ss << "░";
    }
    ss << "|" << std::endl;

    // 综合评价
    ss << "\n综合评价:" << std::endl;
    if (comparison.length_diff_percent < 5.0 && comparison.time_diff_percent < 5.0 &&
        comparison.geometric_similarity > 0.9) {
        ss << "★★★★★ 路线高度相似" << std::endl;
    } else if (comparison.length_diff_percent < 10.0 && comparison.time_diff_percent < 10.0 &&
               comparison.geometric_similarity > 0.7) {
        ss << "★★★★☆ 路线较为相似" << std::endl;
    } else if (comparison.length_diff_percent < 20.0 && comparison.time_diff_percent < 20.0 &&
               comparison.geometric_similarity > 0.5) {
        ss << "★★★☆☆ 路线存在差异" << std::endl;
    } else if (comparison.geometric_similarity > 0.3) {
        ss << "★★☆☆☆ 路线差异较大" << std::endl;
    } else {
        ss << "★☆☆☆☆ 路线差异显著" << std::endl;
    }

    ss << "=============================================" << std::endl;
    std::string report = ss.str();
    LOG_INFO("{}", report);
    return report;
}

} // namespace path
} // namespace aurora
