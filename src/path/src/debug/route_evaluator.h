// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-15
//

#ifndef AURORA_PATH_TOOLS_ROUTE_EVALUATOR_H_
#define AURORA_PATH_TOOLS_ROUTE_EVALUATOR_H_

#include <memory>
#include <vector>
#include <string>
#include <iostream>
#include <iomanip>
#include <cmath>

#include "path/include/path_def.h"
#include "pointll.h"

namespace aurora {
namespace path {

/**
 * 路线评测结果结构体
 */
struct RouteEvaluationResult {
    // 基本信息
    std::string route_name;
    uint64_t path_id;
    
    // 路线长度 (米)
    double length;
    
    // 通行时间 (秒)
    double travel_time;
    
    // 红绿灯数量
    uint32_t traffic_light_num;
    
    // 几何相似度 (0-1, 1表示完全相同)
    double geometric_similarity;
    
    // 路径点数量
    size_t point_count;
    
    // 构造函数
    RouteEvaluationResult() 
        : path_id(0), length(0.0), travel_time(0.0), traffic_light_num(0), 
          geometric_similarity(0.0), point_count(0) {}
};

/**
 * 路线对比结果结构体
 */
struct RouteComparisonResult {
    RouteEvaluationResult route1;
    RouteEvaluationResult route2;
    
    // 长度差异 (百分比)
    double length_diff_percent;
    
    // 时间差异 (百分比)
    double time_diff_percent;
    
    // 红绿灯数量差异
    int32_t traffic_light_diff;
    
    // 几何相似度
    double geometric_similarity;
    
    // 构造函数
    RouteComparisonResult() 
        : length_diff_percent(0.0), time_diff_percent(0.0), 
          traffic_light_diff(0), geometric_similarity(0.0) {}
};

/**
 * 路线评测类
 * 用于评测备选路线的合理性，包括路线长度、通行时间和几何相似度比较
 */
class RouteEvaluator {
public:
    RouteEvaluator();
    ~RouteEvaluator() = default;

    /**
     * 评测单条路线
     * @param query 算路请求
     * @param result 算路结果
     * @param path_index 路径索引 (默认为0，即第一条路径)
     * @return 评测结果
     */
    RouteEvaluationResult EvaluateRoute(const PathQueryPtr& query, 
                                       const PathResultPtr& result, 
                                       size_t path_index = 0);

    /**
     * 比较两条路线
     * @param query 算路请求
     * @param result 算路结果
     * @param path_index1 第一条路径索引
     * @param path_index2 第二条路径索引
     * @return 对比结果
     */
    RouteComparisonResult CompareRoutes(const PathQueryPtr& query,
                                       const PathResultPtr& result,
                                       size_t path_index1,
                                       size_t path_index2);

    /**
     * 比较两个不同算路结果的路线
     * @param query1 第一个算路请求
     * @param result1 第一个算路结果
     * @param path_index1 第一条路径索引
     * @param query2 第二个算路请求
     * @param result2 第二个算路结果
     * @param path_index2 第二条路径索引
     * @return 对比结果
     */
    RouteComparisonResult CompareRoutes(const PathQueryPtr& query1,
                                       const PathResultPtr& result1,
                                       size_t path_index1,
                                       const PathQueryPtr& query2,
                                       const PathResultPtr& result2,
                                       size_t path_index2);

    /**
     * 打印单条路线评测结果
     * @param result 评测结果
     */
    void PrintEvaluationResult(const RouteEvaluationResult& result);

    /**
     * 打印路线对比结果
     * @param result 对比结果
     */
    void PrintComparisonResult(const RouteComparisonResult& result);

    /**
     * 生成可视化报告 (简单的文本格式)
     * @param results 多个评测结果
     * @return 报告字符串
     */
    std::string GenerateReport(const std::vector<RouteEvaluationResult>& results);

    /**
     * 生成对比报告
     * @param comparison 对比结果
     * @return 报告字符串
     */
    std::string GenerateComparisonReport(const RouteComparisonResult& comparison);

    /**
     * 生成简单的ASCII图表可视化
     * @param results 多个评测结果
     * @return 图表字符串
     */
    std::string GenerateVisualization(const std::vector<RouteEvaluationResult>& results);

    /**
     * 生成对比可视化图表
     * @param comparison 对比结果
     * @return 图表字符串
     */
    std::string GenerateComparisonVisualization(const RouteComparisonResult& comparison);

private:
    /**
     * 计算两条路径的几何相似度
     * @param points1 第一条路径的点集
     * @param points2 第二条路径的点集
     * @return 相似度 (0-1)
     */
    double CalculateGeometricSimilarity(const std::vector<PointLL>& points1,
                                       const std::vector<PointLL>& points2);

    /**
     * 计算两点之间的距离 (米)
     * @param p1 第一个点
     * @param p2 第二个点
     * @return 距离 (米)
     */
    double CalculateDistance(const PointLL& p1, const PointLL& p2);

    /**
     * 计算百分比差异
     * @param value1 第一个值
     * @param value2 第二个值
     * @return 百分比差异
     */
    double CalculatePercentageDiff(double value1, double value2);

    /**
     * 格式化时间显示
     * @param seconds 秒数
     * @return 格式化的时间字符串
     */
    std::string FormatTime(double seconds);

    /**
     * 格式化距离显示
     * @param meters 米数
     * @return 格式化的距离字符串
     */
    std::string FormatDistance(double meters);
};

} // namespace path
} // namespace aurora

#endif // AURORA_PATH_TOOLS_ROUTE_EVALUATOR_H_
