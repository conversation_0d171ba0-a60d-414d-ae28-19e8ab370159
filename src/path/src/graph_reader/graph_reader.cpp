// Copyright (c) 2025 BYD Corporation. All rights reserved.
// Author: <EMAIL>
//
// Created: 2025-05-12

#include "graph_reader.h"
#include <mutex>
#include "path_util.h"
#include "path_metrics.h"

namespace aurora {
namespace path {

std::once_flag GraphReader::init_flag_;

GraphReader::GraphReader()
    : tile_(nullptr),
      tile_cache_(std::make_shared<TileCache>()),
      data_provider_ptr_(std::make_shared<aurora::parser::DataProvider>()),
      tile_reader_(std::make_shared<parser::RouteTileReader>()),
      memory_pool_(std::make_unique<PathMemoryPool>(2 * 1024 * 1024, 500)),
      search_tiles_(2, nullptr)
{
    tile_cache_->Reserve(100000);
    std::call_once(init_flag_, &GraphReader::InitDataProvider, this);
}

GraphReader::GraphReader(const std::string& data_dir)
    : tile_(nullptr),
      tile_cache_(std::make_shared<TileCache>()),
      data_provider_ptr_(std::make_shared<aurora::parser::DataProvider>()),
      data_dir_(data_dir),
      tile_reader_(std::make_shared<parser::RouteTileReader>()),
      memory_pool_(std::make_unique<PathMemoryPool>(2 * 1024 * 1024, 100)),
      search_tiles_(2, nullptr) {
  tile_cache_->Reserve(100000);
  std::call_once(init_flag_, &GraphReader::InitDataProvider, this);
}

GraphReader::~GraphReader() {}

bool GraphReader::InitDataProvider() {

    // Get data directory from configuration
    std::string data_dir = data_dir_; // Default value
    try {
        const auto& config = aurora::path::PathConfigManager::Instance();
        if (config.HasKey("data_dir")) {
            data_dir = config.GetDataDir();
            LOG_INFO("Using data directory from config: {}", data_dir);
        } else {
            LOG_WARN("No data_dir in config, using default: {}", data_dir);
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to get data directory from config: {}", e.what());
    }

    std::filesystem::path data_path(data_dir);
    if (!std::filesystem::exists(data_path)) {
        LOG_FATAL("data_dir not found");
        return false;
    }

    // Initialize route parser with data directory from configuration
    bool ret = data_provider_ptr_->InitRouteParser(data_dir.c_str());
    data_provider_ptr_->SwitchRouteTileCacheMode(true);
    LOG_INFO("Initialized route parser with data directory: {}, ret: {}", data_dir, ret);
    return true;
}

bool GraphReader::Init() {
    search_tiles_.resize(2, nullptr);
    SwitchRouteTileCacheMode(true);
    return true;
}

void GraphReader::Clear() {
    search_tiles_.resize(2, nullptr);
    index_tile_map_.clear();
    tile_cache_->Release();
    SwitchRouteTileCacheMode(false);
    memory_pool_->free_buffers();
    PathMetrics::GetInstance().Reset();
}

void GraphReader::SwitchRouteTileCacheMode(bool cache_flag) {
    if (data_provider_ptr_ == nullptr) {
        return;
    }
    data_provider_ptr_->SwitchRouteTileCacheMode(cache_flag);
}

bool GraphReader::LoadGeoJsonMap(const std::string& edge_file, const std::string& node_file) {
    // Use GeoJsonReader to load the map data into a tile
    tile_ = json_reader_.ReadGraphTile(edge_file, node_file);

    if (tile_ != nullptr) {
        tile_cache_->Put(tile_->header()->tile_id.value, tile_);
    }
    return tile_ != nullptr;
}

const DirectEdgeInfoRawPtr& GraphReader::GetDirectEdgeInfo(const EdgeId& edge_id, bool forward) {
    OperationTimer timer(OperationType::GET_DIRECT_EDGE_INFO);
    // add check for via point route
    if (!tile_cache_->IsTileExist(edge_id.tileid())) {
        LoadTile(edge_id.tileid());
    }
    const auto& tile_ptr = tile_cache_->Get(edge_id.tileid());
    DCHECK(tile_ptr);
    return tile_ptr->GetDirectEdgeInfo(edge_id, forward);
}

// only for debug
DirectEdgeInfoRawPtr GraphReader::GetDirectEdgeInfo(const EdgeId& edge_id) {
    // OperationTimer timer(OperationType::GET_DIRECT_EDGE_INFO);
    const auto& tile_ptr = tile_cache_->Get(edge_id.tileid());
    if (tile_ptr) {
        auto edge_ptr = tile_ptr->FindDirectEdgeInfo(edge_id.direct_id(), true);
        if (edge_ptr) {
            return edge_ptr;
        }
        edge_ptr = tile_ptr->FindDirectEdgeInfo(edge_id.direct_id(), false);
        if (edge_ptr) {
            return edge_ptr;
        }
        return tile_cache_->Get(edge_id.tileid())->GetDirectEdgeInfo(edge_id, true); // 临时放在正向表里
    } else {
        LoadTile(edge_id.tileid());
        return tile_cache_->Get(edge_id.tileid())->GetDirectEdgeInfo(edge_id, true); // 临时放在正向表里
    }
}

parser::TopolEdge* GraphReader::GetTopoEdgeById(const parser::FeatureID& feature_id) {

    // todo:
    return nullptr;
}

AugmentEdgeInfoPtr GraphReader::GetAugmentEdgeInfo(const EdgeId& edge_id, bool cache) {
    OperationTimer timer(OperationType::GET_AUGMENT_EDGE_INFO);
    if (!tile_cache_->IsTileExist(edge_id.tileid())) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(edge_id.tileid());
        if (tile_package && !cache) {
            tile_reader_->SetTarget(tile_package);
            auto tile_id = tile_reader_->GetTileID();
             auto new_edge = std::make_shared<AugmentEdgeInfo>();
             parser::RouteEdgeID route_edge_id(tile_id, edge_id.local_id_no_dir());
             auto aug_edge = tile_reader_->GetAugmentEdgeByID(edge_id.local_id_no_dir());
             new_edge->id = GraphId(tile_id.value, aug_edge->GetID(), edge_id.is_forward());
             const auto& points = aug_edge->GetGeoPoints();
             new_edge->geos.insert(new_edge->geos.end(), points.begin(),
                                   points.end());
             const auto& sub_edges = aug_edge->GetSubedges();
             for (const auto& sublink : sub_edges) {
               new_edge->sub_edges.push_back(*sublink);
             }
             return new_edge;
        }

        if (tile_package) {
            // Measure graphTile creation time
            OperationTimer timer(OperationType::TILE_CREATION);
            auto tile_ptr = std::make_shared<graphTile>(tile_package, memory_pool_);
            timer.Stop(); // Stop timer after graphTile creation

            tile_cache_->Put(tile_ptr->header()->tile_id.value, tile_ptr);
        }
    }

    return tile_cache_->Get(edge_id.tileid())->GetAugmentEdgeInfo(edge_id);
}

const NodeInfoRawPtr& GraphReader::GetNodeInfo(const NodeId& node_id, bool need_load) {
    OperationTimer timer(OperationType::GET_NODE_INFO);
    if (need_load) { // 只在跨层时才有可能tile没有加载。
        LoadTile(node_id.tileid());
    }
    return tile_cache_->Get(node_id.tileid())->GetNodeInfo(node_id.local_id_no_dir());
}

graphTilePtr GraphReader::GetTile(const GraphId& graph_id) {
    OperationTimer timer(OperationType::GET_TILE);
    LoadTile(graph_id.tileid());
    return tile_cache_->Get(graph_id.tileid());
}

void GraphReader::LoadTile(const uint64_t& tile_id) {
    OperationTimer timer(OperationType::LOAD_TILE);
    if (!tile_cache_->IsTileExist(tile_id)) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
        if (tile_package) {
            // Measure graphTile creation time
            OperationTimer timer(OperationType::TILE_CREATION);
            auto tile_ptr = std::make_shared<graphTile>(tile_package, memory_pool_);
            timer.Stop(); // Stop timer after graphTile creation

            tile_cache_->Put(tile_id, tile_ptr);
        }
    }
}

// todo: return iter of restritions, not copy
std::vector<RestrictionPtr> GraphReader::GetEnterRestrictions(const DirectEdgeInfoRawPtr& edge) {
    auto edge_id = edge->id;
    auto tile_ptr = edge->tile_ptr;
    if (tile_ptr == nullptr) {
        DCHECK(false);
        return {};
    }
    // 0 means the same as link direction
    uint32_t target_edge_dir = edge_id.is_forward() ? 0 : 1;
    std::vector<RestrictionPtr> restrictions;
    auto first_iter = std::lower_bound(tile_ptr->enter_restrictions_.begin(), tile_ptr->enter_restrictions_.end(), edge_id,
        [](const auto& restriction, const auto& edge_id) {
        return restriction->in_edge_id < edge_id.local_id_no_dir();
    });
    auto last_iter = std::upper_bound(tile_ptr->enter_restrictions_.begin(), tile_ptr->enter_restrictions_.end(), edge_id,
        [](const auto& edge_id, const auto& restriction) {
        return edge_id.local_id_no_dir() < restriction->in_edge_id;
    });
    // restrictions.insert(restrictions.end(), first_iter, last_iter);
    for (auto iter = first_iter; iter != last_iter; ++iter) {
        if ((*iter)->in_edge_dir == target_edge_dir) {
            restrictions.push_back(*iter);
        }
    }
    return restrictions;
}

std::vector<RestrictionPtr> GraphReader::GetExitRestrictions(const DirectEdgeInfoRawPtr& edge) {
    auto edge_id = edge->id;
    auto tile_ptr = edge->tile_ptr;
    if (tile_ptr == nullptr) {
        DCHECK(false);
        return {};
    }

    // 0 means the same as link direction
    uint32_t target_edge_dir = edge_id.is_forward() ? 0 : 1;

    std::vector<RestrictionPtr> restrictions;
    auto first_iter = std::lower_bound(tile_ptr->exit_restrictions_.begin(), tile_ptr->exit_restrictions_.end(), edge_id,
        [](const auto& restriction, const auto& edge_id) {
        return restriction->out_edge_id < edge_id.local_id_no_dir();
    });
    auto last_iter = std::upper_bound(tile_ptr->exit_restrictions_.begin(), tile_ptr->exit_restrictions_.end(), edge_id,
        [](const auto& edge_id, const auto& restriction) {
        return edge_id.local_id_no_dir() < restriction->out_edge_id;
    });
    // restrictions.insert(restrictions.end(), first_iter, last_iter);

    for (auto iter = first_iter; iter != last_iter; ++iter) {
        if ((*iter)->out_edge_dir == target_edge_dir) {
            restrictions.push_back(*iter);
        }
    }
    return restrictions;
}

bool GraphReader::GetAllEnterEdges(const NodeInfoRawPtr& node_ptr, std::vector<DirectEdgeInfoRawPtr>& edges) {
    OperationTimer timer1(OperationType::DATA_PROVIDER_GetInEdgeID);
    if (node_ptr == nullptr) {
        return false;
    }

    auto tile = node_ptr->tile_ptr;
    parser::RouteEdgeIDSet edge_ids;
    std::vector<bool> is_forwards;
    parser::RouteNodeID node_id(tile->header()->tile_id, node_ptr->id.local_id_no_dir());

    tile->reader->GetInEdgeID(node_id, edge_ids, is_forwards);
    timer1.Stop();
    DCHECK(edge_ids.size() == is_forwards.size());
    OperationTimer timer(OperationType::GET_ENTER_EDGES);
    // 在这里去创建DirectedEdge, 以及在StaticMatching里面创建，其他场景都不需要创建。
    edges.reserve(edge_ids.size());
    for (size_t i = 0; i < edge_ids.size(); ++i) {
        auto edge_id = edge_ids[i];
        GraphId graph_edge_id{edge_id.tile_id.value, edge_id.feature_id, is_forwards[i]};
        if (edge_id.tile_id.value != node_ptr->id.tileid()) {
            LoadTile(edge_id.tile_id.value);
            const auto& directededge = GetDirectEdgeInfo(graph_edge_id, false);  // search form rev_edges
            if (directededge == nullptr) {
                DCHECK(false);
            }
            edges.emplace_back(directededge);
        } else {
            const auto& directededge =
                node_ptr->tile_ptr->GetDirectEdgeInfo(graph_edge_id, false);     // search form rev_edges
            if (directededge == nullptr) {
                DCHECK(false);
            }
            edges.emplace_back(directededge);
        }
    }
    return true;
}

bool GraphReader::GetAllExitEdges(const NodeInfoRawPtr& node_ptr, std::vector<DirectEdgeInfoRawPtr>& edges) {
    OperationTimer timer(OperationType::GET_EXIT_EDGES);
    if (node_ptr == nullptr) {
        return false;
    }

    auto tile = node_ptr->tile_ptr;
    parser::RouteEdgeIDSet edge_ids;
    std::vector<bool> is_forwards;
    parser::RouteNodeID node_id(tile->header()->tile_id, node_ptr->id.local_id_no_dir());
    tile->reader->GetOutEdgeID(node_id, edge_ids, is_forwards);
    DCHECK(edge_ids.size() == is_forwards.size());

    for (size_t i = 0; i < edge_ids.size(); ++i) {
        auto edge_id = edge_ids[i];
        GraphId graph_edge_id{edge_id.tile_id.value, edge_id.feature_id, is_forwards[i]};

        if (edge_id.tile_id.value != node_ptr->id.tileid()) {
            LoadTile(edge_id.tile_id.value);
            const auto& directededge = GetDirectEdgeInfo(graph_edge_id, true);
            if (directededge == nullptr) {
                DCHECK(false);
            }
            edges.push_back(directededge);
        } else {
            const auto& directededge = node_ptr->tile_ptr->GetDirectEdgeInfo(graph_edge_id, true);
            if (directededge == nullptr) {
                DCHECK(false);
            }
            edges.push_back(directededge);
        }
    }
    return true;
}

void GraphReader::WriteRouteData(const std::string& file_name, const PointLL& pt, double radius) {
    aurora::parser::GeoMbr mbr(pt.x() - radius/1e5, pt.y() - radius/1e5, pt.x() + radius/1e5, pt.y() + radius/1e5);

    // Collect all tile packages for each level
    std::vector<aurora::parser::RouteTilePackagePtr> all_tile_packages;
    int32_t level = 0;
    // Level 0
    std::vector<aurora::parser::RouteTileID> tile_ids;
    data_provider_ptr_->GetRouteTileIDsByMBR(0, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
        if (tile_package) {
            WriteRouteTilePackage(file_name, tile_package);
        }
    }

    // Level 1
    tile_ids.clear();
    level = 1;
    all_tile_packages.clear();
    data_provider_ptr_->GetRouteTileIDsByMBR(1, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
        if (tile_package) {
            WriteRouteTilePackage(file_name, tile_package);
            all_tile_packages.push_back(tile_package);
        }
    }

    // Level 1
    tile_ids.clear();
    level = 1;
    all_tile_packages.clear();
    data_provider_ptr_->GetRouteTileIDsByMBR(1, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
        if (tile_package) {
            WriteRouteTilePackage(file_name, tile_package);
            all_tile_packages.push_back(tile_package);
        }
    }

    // Write all edges and nodes to files
    std::string edges_file1 = file_name + "/level_" + std::to_string(level)+ "_all_edges.geojson";
    std::string nodes_file1 = file_name + "/level_" + std::to_string(level)+ "_all_nodes.geojson";

    LOG_INFO("Writing all edges to: {}", edges_file1);
    GeoJsonWritter::WriteRouteTileEdges(all_tile_packages, edges_file1);

    LOG_INFO("Writing all nodes to: {}", nodes_file1);
    GeoJsonWritter::WriteRouteTileNodes(all_tile_packages, nodes_file1);

    // Level 2
    level = 2;
    tile_ids.clear();
    all_tile_packages.clear();
    data_provider_ptr_->GetRouteTileIDsByMBR(2, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
        if (tile_package) {
            WriteRouteTilePackage(file_name, tile_package);
            all_tile_packages.push_back(tile_package);
        }
    }

    // Write all edges and nodes to files
    std::string edges_file = file_name + "/level_" + std::to_string(level)+ "_all_edges.geojson";
    std::string nodes_file = file_name + "/level_" + std::to_string(level)+ "_all_nodes.geojson";
    
    LOG_INFO("Writing all edges to: {}", edges_file);
    GeoJsonWritter::WriteRouteTileEdges(all_tile_packages, edges_file);
    
    LOG_INFO("Writing all nodes to: {}", nodes_file);
    GeoJsonWritter::WriteRouteTileNodes(all_tile_packages, nodes_file);

    // Also write individual tile files using the existing method
    // for (const auto& tile_package : all_tile_packages) {
    //     WriteRouteTilePackage(file_name, tile_package);
    // }


    // {
    //     double lon = 121.29811;
    //     double lat = 31.14291;
    //     aurora::parser::GeoMbr mbr(lon - 0.001, lat - 0.001, lon + 0.001, lat + 0.001);
    //     std::vector<aurora::parser::RouteTileID> tile_ids;
    //     aurora::parser::DataProvider::Instance().GetRouteTileIdsByMBR(0, mbr, tile_ids);
    //     for (const auto& tile_id : tile_ids) {
    //         auto tile_package = aurora::parser::DataProvider::Instance().GetRouteTileByID(tile_id);
    //         if (tile_package) {
    //             WriteRouteTilePackage("/mnt/d/osm/map_engine/route_data_1.geojson", tile_package);
    //         }
    //     }
    // }
}

void GraphReader::WriteRouteTilePackage(const std::string& file_name, const aurora::parser::RouteTilePackagePtr& tile_package) {
    auto reader = std::make_shared<parser::RouteTileReader>();
    reader->SetTarget(tile_package);
    std::string name = std::to_string(reader->GetTileID().adcode) + "_" +
        std::to_string(reader->GetTileID().level) + "_" + std::to_string(reader->GetTileID().mesh_col) +
        "_" + std::to_string(reader->GetTileID().mesh_row) + "_" + std::to_string(reader->GetTileID().tile_id);
    std::string edge_name = file_name + "/" + name + "_edge.geojson";
    std::string node_name = file_name + "/" + name + "_node.geojson";
    LOG_INFO("WriteRouteTileEdge: {}", edge_name);
    LOG_INFO("WriteRouteTileNode: {}", node_name);
    GeoJsonWritter::WriteRouteTileEdge(tile_package, edge_name);
    GeoJsonWritter::WriteRouteTileNode(tile_package, node_name);
}

bool GraphReader::LoadTileData(const PointLL& pointll, uint8_t level, double radius) {
    OperationTimer timer(OperationType::LOAD_TILE_DATA);
    aurora::parser::GeoMbr mbr(pointll.x() - radius/1e5, pointll.y() - radius/1e5, pointll.x() + radius/1e5, pointll.y() + radius/1e5);
    std::vector<aurora::parser::RouteTileID> tile_ids;
    data_provider_ptr_->GetRouteTileIDsByMBR(0, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);

        if (tile_package) {
            // Measure graphTile creation time
            OperationTimer timer(OperationType::TILE_CREATION);
            auto tile_ptr = std::make_shared<graphTile>(tile_package, memory_pool_);
            timer.Stop(); // Stop timer after graphTile creation

            tile_cache_->Put(tile_ptr->header()->tile_id.value, tile_ptr);
        }
    }

    return tile_ids.size() > 0;
}

/**
 * Calculate the projection distance and point from a point to an edge
 * @param pt The point to project
 * @param edge_geos The edge geometry points
 * @param proj_dis The projection distance (output)
 * @param proj_pt The projection point (output)
 * @param proj_index The projection index (output)
 */
void GraphReader::proj2d(const PointLL& pt, const std::vector<PointLL>& edge_geos, double& proj_dis, PointLL& proj_pt, size_t& proj_index) {
    // Initialize with a large value
    proj_dis = std::numeric_limits<double>::max();
    
    // Handle edge cases
    if (edge_geos.size() < 2) {
        if (edge_geos.size() == 1) {
            // If only one point, calculate direct distance
            proj_pt = edge_geos[0];
            proj_dis = pt.Distance(edge_geos[0]);
        }
        return;
    }
    
    // Check each segment of the edge
    for (size_t i = 0; i < edge_geos.size() - 1; i++) {
        // Create line segment
        aurora::LineSegment2<PointLL> segment(edge_geos[i], edge_geos[i+1]);
        
        // Calculate closest point and distance
        PointLL closest;
        double dist = segment.Distance(pt, closest);
        
        // Update if this is closer than previous best
        if (dist < proj_dis) {
            proj_dis = dist;
            proj_pt = closest;
            proj_index = i;
        }
    }
}

bool GraphReader::GetLinksByRange(const PointLL& pt, double radius, std::vector<EdgeInfoPtr>& edges) {
    OperationTimer timer(OperationType::GET_LINKS_BY_RANGE);
    // get interact tileid, and for eachtile , do indextile search
    aurora::parser::GeoMbr mbr(pt.x() - radius/1e5, pt.y() - radius/1e5, pt.x() + radius/1e5, pt.y() + radius/1e5);
    std::vector<aurora::parser::RouteTileID> tile_ids;
    data_provider_ptr_->GetRouteTileIDsByMBR(0, mbr, tile_ids);
    // auto index_tile = index_tile_map_[tile_ids.   tile_ids ->id.id];
    // index_tile->GetLinksByRange(pt, radius, edges);

    for (const auto& tile_id : tile_ids) {
        // check if tile_id exist in tile_cache_
        const auto& tile_ptr = tile_cache_->Get(tile_id.value);
        if (tile_ptr) {
            // check if tile_id exist in index_tile_map_
            if (index_tile_map_.find(tile_ptr->header()->tile_id.value) == index_tile_map_.end()) {
                // construct index tile
                // IndexTile index_tile(tile);
                auto index_tile = std::make_shared<IndexTile>(tile_ptr);
                index_tile_map_[tile_ptr->header()->tile_id.value] = index_tile;
            }
            auto index_tile = index_tile_map_[tile_ptr->header()->tile_id.value];
            index_tile->Search(pt, radius/1e5, edges);
            if (!edges.empty()) {
                break;
            }
        }
    }

    // filter by proj to edges
    std::vector<EdgeInfoPtr> filtered_edges;
    double min_proj_dis = std::numeric_limits<double>::max();
    EdgeInfoPtr closest_edge = nullptr;
    PointLL closest_proj_pt;
    size_t closest_proj_index;

    for (const auto& edge : edges) {
        double proj_dis;
        PointLL proj_pt;
        size_t proj_index;
        // Calculate projection distance and point
        proj2d(pt, edge->geos, proj_dis, proj_pt, proj_index);
        
        // Keep track of the closest edge
        if (proj_dis < min_proj_dis) {
            min_proj_dis = proj_dis;
            closest_edge = edge;
            closest_proj_pt = proj_pt;
            closest_proj_index = proj_index;
        }
    }

    // print linepoint of closest_proj_pt
    LOG_INFO("closest_proj_pt: {}, {}", closest_proj_pt.x(), closest_proj_pt.y());
    
    // Only keep the closest edge
    if (closest_edge) {
        // calc the offset from the first point of edge to proj point
        double offset = 0.0;
        for (size_t i = 0; i < closest_proj_index; i++) {
            offset += closest_edge->geos[i].Distance(closest_edge->geos[i+1]);
        }
        offset += closest_proj_pt.Distance(closest_edge->geos[closest_proj_index]);
        LOG_INFO("offset: {}", offset);
        filtered_edges.push_back(closest_edge);
    }
    
    edges = filtered_edges;
    return !edges.empty();
}

bool GraphReader::StaticMatching(PathLandmarkPtr landmark, double radius) {
    OperationTimer timer(OperationType::STATIC_MATCHING);
    if (landmark == nullptr || !landmark->valid) {
        return false;
    }

    const auto& pt = landmark->pt;
    double match_radius = radius / 1e5;

    std::vector<EdgeInfoPtr> total_edges;
    double search_radius = match_radius;
    for (; search_radius < 10000.0f/ 1e5; search_radius += match_radius) {
    aurora::parser::GeoMbr mbr(pt.x() - search_radius, pt.y() - search_radius, pt.x() + search_radius, pt.y() + search_radius);
    std::vector<aurora::parser::RouteTileID> tile_ids;
    data_provider_ptr_->GetRouteTileIDsByMBR(0, mbr, tile_ids);
    // auto index_tile = index_tile_map_[tile_ids.   tile_ids ->id.id];
    // index_tile->GetLinksByRange(pt, radius, edges);
    std::vector<EdgeInfoPtr> edges;
    for (const auto& tile_id : tile_ids) {
        // load tile to tile_cache_ if not exist
        LoadTile(tile_id.value);
        const auto& tile_ptr = tile_cache_->Get(tile_id.value);
        if (tile_ptr) {
            // check if tile_id exist in index_tile_map_
            if (index_tile_map_.find(tile_ptr->header()->tile_id.value) ==
                index_tile_map_.end()) {
                // construct index tile
                // IndexTile index_tile(tile);
                auto index_tile = std::make_shared<IndexTile>(tile_ptr);
                index_tile_map_[tile_ptr->header()->tile_id.value] = index_tile;
            }
            auto index_tile = index_tile_map_[tile_ptr->header()->tile_id.value];
            index_tile->Search(pt, search_radius, edges);
        }
    }
    // erase edges which is_inner_edge or not through
    edges.erase(std::remove_if(edges.begin(), edges.end(), [](const EdgeInfoPtr& edge) {
        return edge->is_inner_link > 0 || edge->direction == 0;
    }), edges.end());
    if (!edges.empty()) {
        total_edges = edges;
        break;
    }
    }

    // bool ret = FilterCloestEdge(landmark, total_edges);
    bool ret = MatchMultiEdges(landmark, total_edges, radius);
    return ret;
}

bool GraphReader::FilterCloestEdge(PathLandmarkPtr landmark, const std::vector<EdgeInfoPtr>& edges) {
    const auto& pt = landmark->pt;
    // filter by proj to edges
    std::vector<EdgeInfoPtr> filtered_edges;
    double min_proj_dis = std::numeric_limits<double>::max();
    EdgeInfoPtr closest_edge = nullptr;
    PointLL closest_proj_pt;
    size_t closest_proj_index;

    for (const auto& edge : edges) {
        // if (edge->is_area_link()) {
        //     continue;
        // }
        double proj_dis;
        PointLL proj_pt;
        size_t proj_index;
        // Calculate projection distance and point
        proj2d(pt, edge->geos, proj_dis, proj_pt, proj_index);
        
        // Keep track of the closest edge
        if (proj_dis < min_proj_dis) {
            min_proj_dis = proj_dis;
            closest_edge = edge;
            closest_proj_pt = proj_pt;
            closest_proj_index = proj_index;
        }
    }

    if (closest_edge) {
        Candidate candidate;
        // calc the offset from the first point of edge to proj point
        double offset = 0.0;
        for (size_t i = 0; i < closest_proj_index; i++) {
            offset += closest_edge->geos[i].Distance(closest_edge->geos[i+1]);
        }
        offset += closest_proj_pt.Distance(closest_edge->geos[closest_proj_index]);
        filtered_edges.push_back(closest_edge);
        candidate.offset = std::fmin(offset, closest_edge->length);
        candidate.proj_index = closest_proj_index;
        candidate.proj_pt = closest_proj_pt;
        candidate.link = closest_edge;
        landmark->candidates.push_back(candidate);
        LOG_INFO("Matched pt: ({} {}), closest_proj_pt: {}, {}, tile_id: {}, local_id: {} offset: {}, min_proj_dis: {}", pt.x(), pt.y(),
            closest_proj_pt.x(), closest_proj_pt.y(), closest_edge->tile_id, closest_edge->id, offset,
            min_proj_dis);
        return true;
    }
    return false;
}

bool GraphReader::MatchMultiEdges(PathLandmarkPtr landmark, const std::vector<EdgeInfoPtr>& edges, double search_radius) {
    const auto& pt = landmark->pt;
    std::vector<Candidate> raw_candidates;
    for (const auto& edge : edges) {
        Candidate candidate;

        double proj_dis;
        PointLL proj_pt;
        size_t proj_index;
        // Calculate projection distance and point
        proj2d(pt, edge->geos, proj_dis, proj_pt, proj_index);
        // if (proj_dis > search_radius) {
        //     continue;
        // }

        // calc the offset from the first point of edge to proj point
        double offset = 0.0;
        for (size_t i = 0; i < proj_index; i++) {
            offset += edge->geos[i].Distance(edge->geos[i+1]);
        }
        offset += proj_pt.Distance(edge->geos[proj_index]);
        candidate.offset = std::fmin(offset, edge->length);
        candidate.proj_dis = proj_dis;
        candidate.proj_index = proj_index;
        candidate.proj_pt = proj_pt;
        candidate.link = edge;
        raw_candidates.push_back(candidate);
    }

    // sort raw_candidates by proj_dis
    std::sort(raw_candidates.begin(), raw_candidates.end(), [](const Candidate& a, const Candidate& b) {
        return a.proj_dis < b.proj_dis;
    });
    LOG_INFO("raw_candidates size: {}", raw_candidates.size());
    // Check if the closest candidate is an inner road
    if (!raw_candidates.empty()) {
        if (raw_candidates.front().link->is_area_link ) {
            // Keep up to 4 inner road candidates
            auto it = std::remove_if(raw_candidates.begin(), raw_candidates.end(),
                [this](const Candidate& c) {
                    return !c.link->is_area_link;
                });
            raw_candidates.erase(it, raw_candidates.end());
            if (raw_candidates.size() > 4) {
                raw_candidates.resize(4);
            }
        } else if (raw_candidates.front().link->is_separate) {
            // Keep only the closest one separate road candidate
            raw_candidates.resize(1);
        } else {
            // Keep only the closest two non-inner road candidate
            if (raw_candidates.size() > 2) {
              raw_candidates.resize(2);
            }
        }
    }

    landmark->candidates = raw_candidates;
    // print detail candiates info
    uint32_t idx = 0;
    for (const auto& candidate : landmark->candidates) {
        LOG_INFO("idx: {}, Matched pt: ({} {}), proj_pt: {}, {}, graph_id: {}_{}, offset: {}, proj_dis: {}", idx, pt.x(), pt.y(),
            candidate.proj_pt.x(), candidate.proj_pt.y(), candidate.link->tile_id, candidate.link->id, candidate.offset,
            candidate.proj_dis);
        idx++;
    }
    return !landmark->candidates.empty();
}

bool GraphReader::EgoMatch(const PointLL& pt, GraphId id, uint8_t direction, double offset, const PathLandmarkPtr& landmark) {
    auto aug_edge_ptr = GetAugmentEdgeInfo(id, false); // todo: use no dir id
    auto edge_ptr = GetDirectEdgeInfo(id, true);
    DCHECK(aug_edge_ptr);
    DCHECK(edge_ptr);

    auto link_geos = aug_edge_ptr->geos;
    if (direction) { // reverse
        std::reverse(link_geos.begin(), link_geos.end());
    }

    Candidate candidate;
    if (!FindIndexInGeoByOffset(link_geos, offset, candidate.proj_index, candidate.proj_pt)) {
        return false;
    }
    candidate.offset = offset;
    candidate.proj_dis = pt.Distance(candidate.proj_pt);
    candidate.link = std::make_shared<EdgeInfo>();
    candidate.link->tile_id = id.tileid();
    candidate.link->id = id.local_id_no_dir();
    candidate.link->geos = aug_edge_ptr->geos;
    candidate.link->length = edge_ptr->length();//
    candidate.link->direction = direction > 0 ? 2 : 1;

    landmark->candidates.push_back(std::move(candidate));
    LOG_INFO("Ego Matched pt: ({} {}), proj_pt: {}, {}, graph_id: {}_{}, offset: {}, proj_dis: {}", pt.x(), pt.y(),
        candidate.proj_pt.x(), candidate.proj_pt.y(), candidate.link->tile_id, candidate.link->id, candidate.offset,
        candidate.proj_dis);
    return true;
}

void GraphReader::PrintStatistics() {
    // print LRU cache max size
    LOG_INFO("LRU cache size: {}", tile_cache_->GetCacheSize());
    // print mempool info
    LOG_INFO("{}", memory_pool_->get_statistics());

    tile_cache_->PrintMemInfo();

    // print path metrics
    PathMetrics::GetInstance().PrintMetrics();
}


void GraphReader::UpdateSearchCache(bool forward, const graphTilePtr& tile_ptr) {
    if (forward) {
        search_tiles_[0] = tile_ptr;
    } else {
        search_tiles_[1] = tile_ptr;
    }
}

} // namespace path
} // namespace aurora
