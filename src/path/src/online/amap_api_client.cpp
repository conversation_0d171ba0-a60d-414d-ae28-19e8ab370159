// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-18
//

#include "amap_api_client.h"

#include <sstream>
#include <iomanip>
#include <regex>

#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/error/en.h"
#include "search/src/online/httplib.h"
#include "logger.h"

namespace aurora {
namespace path {

AmapApiClient::AmapApiClient(const std::string& api_key, int timeout_ms)
    : api_key_(api_key.empty() ? DEFAULT_API_KEY : api_key)
    , timeout_ms_(timeout_ms)
    , base_url_(AMAP_DRIVING_API_URL) {
    LOG_INFO("AmapApiClient initialized with API key: {}...", api_key_.substr(0, 8));
}

void AmapApiClient::SetApiKey(const std::string& api_key) {
    api_key_ = api_key.empty() ? DEFAULT_API_KEY : api_key;
    LOG_INFO("AmapApiClient API key updated: {}...", api_key_.substr(0, 8));
}

void AmapApiClient::SetTimeout(int timeout_ms) {
    timeout_ms_ = timeout_ms;
    LOG_DEBUG("AmapApiClient timeout set to: {}ms", timeout_ms_);
}

bool AmapApiClient::IsValid() const {
    return !api_key_.empty() && timeout_ms_ > 0;
}

AmapRouteResponse AmapApiClient::CalculateRoute(
    const PointLL& start_point,
    const PointLL& end_point,
    const std::vector<PointLL>& waypoints,
    PathStrategy strategy) {
    
    AmapRouteResponse response;
    
    if (!IsValid()) {
        response.error_message = "AmapApiClient is not properly configured";
        LOG_ERROR("AmapApiClient: {}", response.error_message);
        return response;
    }
    
    try {
        // 构建请求URL
        int amap_strategy = ConvertStrategyToAmapValue(strategy);
        std::string request_url = BuildRequestUrl(start_point, end_point, waypoints, amap_strategy);
        
        LOG_DEBUG("AmapApiClient: Requesting route from ({}, {}) to ({}, {}), strategy: {}",
                 start_point.lng(), start_point.lat(), end_point.lng(), end_point.lat(), amap_strategy);
        
        // 解析URL获取主机和路径
        std::regex url_regex(R"(https?://([^/]+)(/.*))");
        std::smatch matches;
        
        if (!std::regex_match(request_url, matches, url_regex)) {
            response.error_message = "Invalid request URL format";
            LOG_ERROR("AmapApiClient: {}", response.error_message);
            return response;
        }
        
        std::string host = matches[1].str();
        std::string path = matches[2].str();
        
        // 创建HTTP客户端
        httplib::Client client(host);
        client.set_connection_timeout(timeout_ms_ / 1000);
        client.set_read_timeout(timeout_ms_ / 1000);
        client.set_write_timeout(timeout_ms_ / 1000);
        
        // 发送GET请求
        auto result = client.Get(path.c_str());
        
        if (!result) {
            response.error_message = "HTTP request failed: connection error";
            LOG_ERROR("AmapApiClient: {}", response.error_message);
            return response;
        }
        
        if (result->status != 200) {
            response.error_message = "HTTP request failed with status: " + std::to_string(result->status);
            LOG_ERROR("AmapApiClient: {}", response.error_message);
            return response;
        }
        
        // 解析响应
        response = ParseResponse(result->body);
        
        if (response.success) {
            LOG_INFO("AmapApiClient: Route calculation successful, {} routes returned", response.routes.size());
        } else {
            LOG_ERROR("AmapApiClient: Route calculation failed: {}", response.error_message);
        }
        
    } catch (const std::exception& e) {
        response.error_message = "Exception during route calculation: " + std::string(e.what());
        LOG_ERROR("AmapApiClient: {}", response.error_message);
    }
    
    return response;
}

int AmapApiClient::ConvertStrategyToAmapValue(PathStrategy strategy) const {
    switch (strategy) {
        case PathStrategy::kTimeFirst:
            return 0;  // 最短时间
        case PathStrategy::kDistanceFirst:
            return 1;  // 最短距离
        case PathStrategy::KHighWayFirst:
            return 0;  // 高速优先，使用最短时间策略
        case PathStrategy::kAvoidToll:
            return 2;  // 避开拥堵（最接近避开收费的策略）
        default:
            return 0;  // 默认最短时间
    }
}

std::string AmapApiClient::BuildRequestUrl(
    const PointLL& start_point,
    const PointLL& end_point,
    const std::vector<PointLL>& waypoints,
    int strategy) const {
    
    std::ostringstream url;
    url << base_url_ << "?";
    url << "key=" << api_key_;
    url << "&origin=" << std::fixed << std::setprecision(6) << start_point.lng() << "," << start_point.lat();
    url << "&destination=" << std::fixed << std::setprecision(6) << end_point.lng() << "," << end_point.lat();
    url << "&extensions=all";
    url << "&strategy=" << strategy;
    
    // 添加途经点（如果有）
    if (!waypoints.empty()) {
        url << "&waypoints=";
        for (size_t i = 0; i < waypoints.size(); ++i) {
            if (i > 0) url << ";";
            url << std::fixed << std::setprecision(6) << waypoints[i].lng() << "," << waypoints[i].lat();
        }
    }
    
    return url.str();
}

AmapRouteResponse AmapApiClient::ParseResponse(const std::string& response_body) const {
    AmapRouteResponse response;
    
    try {
        rapidjson::Document doc;
        doc.Parse(response_body.c_str());
        
        if (doc.HasParseError()) {
            response.error_message = "JSON parse error: " + std::string(rapidjson::GetParseError_En(doc.GetParseError()));
            return response;
        }
        
        // 检查API状态
        if (!doc.HasMember("status") || doc["status"].GetString() != std::string("1")) {
            response.error_message = "Amap API error: " + 
                (doc.HasMember("info") ? std::string(doc["info"].GetString()) : "Unknown error");
            return response;
        }
        
        // 解析路径数据
        if (doc.HasMember("route") && doc["route"].HasMember("paths") && doc["route"]["paths"].IsArray()) {
            const auto& paths = doc["route"]["paths"];

            for (rapidjson::SizeType i = 0; i < paths.Size(); ++i) {
                // 将单个路径转换为JSON字符串
                rapidjson::StringBuffer buffer;
                rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
                paths[i].Accept(writer);
                std::string path_json = buffer.GetString();

                auto route_info = ExtractRouteInfo(path_json);
                if (!route_info.coordinates.empty()) {
                    response.routes.push_back(std::move(route_info));
                }
            }
        }
        
        response.success = !response.routes.empty();
        if (!response.success && response.error_message.empty()) {
            response.error_message = "No valid routes found in response";
        }
        
    } catch (const std::exception& e) {
        response.error_message = "Exception parsing response: " + std::string(e.what());
    }
    
    return response;
}

AmapRouteResponse::RouteInfo AmapApiClient::ExtractRouteInfo(const std::string& path_data_json) const {
    AmapRouteResponse::RouteInfo route_info;

    try {
        rapidjson::Document path_doc;
        path_doc.Parse(path_data_json.c_str());

        if (path_doc.HasParseError()) {
            LOG_ERROR("AmapApiClient: Error parsing path data JSON");
            return route_info;
        }

        // 提取基本信息
        if (path_doc.HasMember("distance") && path_doc["distance"].IsString()) {
            route_info.distance = std::stod(path_doc["distance"].GetString());
        }

        if (path_doc.HasMember("duration") && path_doc["duration"].IsString()) {
            route_info.duration = std::stod(path_doc["duration"].GetString());
        }

        if (path_doc.HasMember("tolls") && path_doc["tolls"].IsString()) {
            route_info.tolls = std::stod(path_doc["tolls"].GetString());
        }

        if (path_doc.HasMember("toll_distance") && path_doc["toll_distance"].IsString()) {
            route_info.toll_distance = std::stod(path_doc["toll_distance"].GetString());
        }

        // 提取坐标点
        route_info.coordinates = ExtractRouteCoordinates(path_data_json);

    } catch (const std::exception& e) {
        LOG_ERROR("AmapApiClient: Error extracting route info: {}", e.what());
    }

    return route_info;
}

std::vector<PointLL> AmapApiClient::ExtractRouteCoordinates(const std::string& route_data_json) const {
    std::vector<PointLL> coordinates;

    try {
        rapidjson::Document route_doc;
        route_doc.Parse(route_data_json.c_str());

        if (route_doc.HasParseError()) {
            LOG_ERROR("AmapApiClient: Error parsing route data JSON");
            return coordinates;
        }

        if (route_doc.HasMember("steps") && route_doc["steps"].IsArray()) {
            const auto& steps = route_doc["steps"];

            for (rapidjson::SizeType i = 0; i < steps.Size(); ++i) {
                const auto& step = steps[i];

                if (step.HasMember("polyline") && step["polyline"].IsString()) {
                    std::string polyline = step["polyline"].GetString();

                    // 解析polyline字符串 (格式: "lng1,lat1;lng2,lat2;...")
                    std::istringstream ss(polyline);
                    std::string point_str;

                    while (std::getline(ss, point_str, ';')) {
                        size_t comma_pos = point_str.find(',');
                        if (comma_pos != std::string::npos) {
                            double lng = std::stod(point_str.substr(0, comma_pos));
                            double lat = std::stod(point_str.substr(comma_pos + 1));
                            coordinates.emplace_back(lng, lat);
                        }
                    }
                }
            }
        }
    } catch (const std::exception& e) {
        LOG_ERROR("AmapApiClient: Error extracting coordinates: {}", e.what());
    }

    return coordinates;
}

} // namespace path
} // namespace aurora
