// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-18
//

#ifndef AURORA_PATH_SRC_ONLINE_AMAP_API_CLIENT_H_
#define AURORA_PATH_SRC_ONLINE_AMAP_API_CLIENT_H_

#include <string>
#include <vector>
#include <memory>
#include <optional>

#include "path/include/path_def.h"
#include "pointll.h"
#include "logger.h"
#include "rapidjson/document.h"

namespace aurora {
namespace path {

/**
 * 高德API响应数据结构
 */
struct AmapRouteResponse {
    bool success = false;
    std::string error_message;
    
    struct RouteInfo {
        double distance = 0.0;      // 距离（米）
        double duration = 0.0;      // 时间（秒）
        double tolls = 0.0;         // 过路费（元）
        double toll_distance = 0.0; // 收费路段距离（米）
        std::vector<PointLL> coordinates; // 路径坐标点（GCJ02）
    };
    
    std::vector<RouteInfo> routes;
};

/**
 * 高德算路API客户端
 * 
 * 负责与高德Web API进行通信，提供算路服务
 * - 支持多种算路策略
 * - 自动处理HTTP请求和响应
 * - 提供坐标系转换（如需要）
 */
class AmapApiClient {
public:
    /**
     * 构造函数
     * @param api_key 高德API密钥
     * @param timeout_ms 请求超时时间（毫秒）
     */
    explicit AmapApiClient(const std::string& api_key, int timeout_ms = 10000);
    
    /**
     * 析构函数
     */
    ~AmapApiClient() = default;
    
    // 禁用拷贝构造和赋值
    AmapApiClient(const AmapApiClient&) = delete;
    AmapApiClient& operator=(const AmapApiClient&) = delete;
    
    /**
     * 调用高德算路API
     * @param start_point 起点坐标（WGS84）
     * @param end_point 终点坐标（WGS84）
     * @param waypoints 途经点列表（WGS84）
     * @param strategy 算路策略
     * @return 算路响应结果
     */
    AmapRouteResponse CalculateRoute(
        const PointLL& start_point,
        const PointLL& end_point,
        const std::vector<PointLL>& waypoints = {},
        PathStrategy strategy = PathStrategy::kTimeFirst
    );
    
    /**
     * 设置API密钥
     * @param api_key 新的API密钥
     */
    void SetApiKey(const std::string& api_key);
    
    /**
     * 设置请求超时时间
     * @param timeout_ms 超时时间（毫秒）
     */
    void SetTimeout(int timeout_ms);
    
    /**
     * 检查客户端是否可用
     * @return true 如果客户端配置正确
     */
    bool IsValid() const;

private:
    /**
     * 将PathStrategy转换为高德API的strategy参数
     * @param strategy 路径策略
     * @return 高德API策略值
     */
    int ConvertStrategyToAmapValue(PathStrategy strategy) const;
    
    /**
     * 构建算路请求URL
     * @param start_point 起点
     * @param end_point 终点
     * @param waypoints 途经点
     * @param strategy 策略
     * @return 请求URL
     */
    std::string BuildRequestUrl(
        const PointLL& start_point,
        const PointLL& end_point,
        const std::vector<PointLL>& waypoints,
        int strategy
    ) const;
    
    /**
     * 解析高德API响应
     * @param response_body 响应体JSON字符串
     * @return 解析后的响应数据
     */
    AmapRouteResponse ParseResponse(const std::string& response_body) const;
    
    /**
     * 提取路径坐标点
     * @param route_data 路径数据JSON对象
     * @return 坐标点列表
     */
    std::vector<PointLL> ExtractRouteCoordinates(const std::string& route_data_json) const;

    /**
     * 提取路径基本信息
     * @param path_data 路径数据JSON对象
     * @return 路径信息
     */
    AmapRouteResponse::RouteInfo ExtractRouteInfo(const std::string& path_data_json) const;

private:
    std::string api_key_;           // 高德API密钥
    int timeout_ms_;                // 请求超时时间
    std::string base_url_;          // API基础URL
    
    // 常量定义
    static constexpr const char* AMAP_DRIVING_API_URL = "https://restapi.amap.com/v3/direction/driving";
    static constexpr const char* DEFAULT_API_KEY = "c6dbca449fbd6baadc69b8540565ef59";
};

// 类型别名
using AmapApiClientPtr = std::unique_ptr<AmapApiClient>;

} // namespace path
} // namespace aurora

#endif // AURORA_PATH_SRC_ONLINE_AMAP_API_CLIENT_H_
