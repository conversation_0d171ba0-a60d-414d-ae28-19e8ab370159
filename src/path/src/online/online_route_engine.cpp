// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-18
//

#include "online_route_engine.h"

#include <chrono>
#include <sstream>
#include <iomanip>

#include "path/src/base/path_util.h"
#include "errorcode.h"
#include "logger.h"

namespace aurora {
namespace path {

OnlineRouteEngine::OnlineRouteEngine(const std::string& api_key, int timeout_ms)
    : amap_client_(std::make_unique<AmapApiClient>(api_key, timeout_ms))
    , interrupt_callback_(nullptr)
    , is_cancelled_(false)
    , initialized_(true) {
    
    LOG_INFO("OnlineRouteEngine initialized with timeout: {}ms", timeout_ms);
    
    // 重置性能指标
    metrics_.Reset();
}

PathResultPtr OnlineRouteEngine::CalculateRoute(const PathQuery& query) {
    if (!IsInitialized()) {
        LOG_ERROR("OnlineRouteEngine: Engine not initialized");
        return CreateErrorResult("Engine not initialized", static_cast<int32_t>(ErrorCode::kErrorCodeFailed));
    }
    
    // 验证查询参数
    if (!ValidateQuery(query)) {
        LOG_ERROR("OnlineRouteEngine: Invalid query parameters");
        return CreateErrorResult("Invalid query parameters", static_cast<int32_t>(ErrorCode::kErrorCodeFailed));
    }
    
    // 检查中断标志
    if (is_cancelled_.load()) {
        LOG_INFO("OnlineRouteEngine: Calculation cancelled before start");
        return CreateErrorResult("Calculation cancelled", static_cast<int32_t>(ErrorCode::kErrorCodePathUserCalcel));
    }
    
    // 记录开始时间
    auto start_time = std::chrono::high_resolution_clock::now();
    
    LOG_INFO("OnlineRouteEngine: Starting online route calculation with strategy: {}", 
             GetStrategyString(query.strategy));
    
    try {
        // 提取起点和终点
        auto start_point = query.path_points.front()->pt;
        auto end_point = query.path_points.back()->pt;
        
        // 提取途经点
        auto waypoints = ExtractWaypoints(query);
        
        // 调用高德API
        auto amap_response = amap_client_->CalculateRoute(start_point, end_point, waypoints, query.strategy);
        
        // 检查中断标志
        if (is_cancelled_.load()) {
            LOG_INFO("OnlineRouteEngine: Calculation cancelled during API call");
            return CreateErrorResult("Calculation cancelled", static_cast<int32_t>(ErrorCode::kErrorCodePathUserCalcel));
        }
        
        // 计算耗时
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        int64_t calculation_time_ms = duration.count();
        
        // 更新性能指标
        UpdateMetrics(calculation_time_ms, amap_response.success);
        
        if (!amap_response.success) {
            LOG_ERROR("OnlineRouteEngine: API call failed: {}", amap_response.error_message);
            return CreateErrorResult(amap_response.error_message, static_cast<int32_t>(ErrorCode::kErrorCodeFailed));
        }
        
        // 转换为PathResult
        auto result = ConvertAmapResponseToPathResult(amap_response, query, calculation_time_ms);
        
        LOG_INFO("OnlineRouteEngine: Route calculation completed successfully, {} paths found, time: {}ms",
                 result->paths.size(), calculation_time_ms);
        
        return result;
        
    } catch (const std::exception& e) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        UpdateMetrics(duration.count(), false);
        
        std::string error_msg = "Exception during route calculation: " + std::string(e.what());
        LOG_ERROR("OnlineRouteEngine: {}", error_msg);
        return CreateErrorResult(error_msg, static_cast<int32_t>(ErrorCode::kErrorCodeFailed));
    }
}

void OnlineRouteEngine::SetInterruptCallback(const std::function<void()>* interrupt_callback) {
    interrupt_callback_ = interrupt_callback;
    LOG_DEBUG("OnlineRouteEngine: Interrupt callback set");
}

void OnlineRouteEngine::CancelCurrentCalculation() {
    is_cancelled_.store(true);
    LOG_INFO("OnlineRouteEngine: Current calculation cancelled");
}

void OnlineRouteEngine::Clear() {
    is_cancelled_.store(false);
    LOG_DEBUG("OnlineRouteEngine: Cleared temporary data");
}

const OnlineRouteEngineMetrics& OnlineRouteEngine::GetMetrics() const {
    // 更新平均响应时间
    const_cast<OnlineRouteEngineMetrics&>(metrics_).UpdateAverageResponseTime();
    return metrics_;
}

bool OnlineRouteEngine::IsInitialized() const {
    return initialized_ && amap_client_ && amap_client_->IsValid();
}

std::string OnlineRouteEngine::GetCurrentAlgorithmType() const {
    return ALGORITHM_TYPE;
}

void OnlineRouteEngine::SetApiKey(const std::string& api_key) {
    if (amap_client_) {
        amap_client_->SetApiKey(api_key);
        LOG_INFO("OnlineRouteEngine: API key updated");
    }
}

void OnlineRouteEngine::SetTimeout(int timeout_ms) {
    if (amap_client_) {
        amap_client_->SetTimeout(timeout_ms);
        LOG_INFO("OnlineRouteEngine: Timeout updated to {}ms", timeout_ms);
    }
}

PathResultPtr OnlineRouteEngine::ConvertAmapResponseToPathResult(
    const AmapRouteResponse& amap_response,
    const PathQuery& query,
    int64_t calculation_time_ms) {
    
    auto result = std::make_shared<PathResult>();
    
    // 设置基本信息
    result->status = "success";
    result->code = static_cast<int32_t>(ErrorCode::kErrorCodeOk);
    result->metadata.query_time_ms = calculation_time_ms;
    result->metadata.build_version = "online_amap_1.0";
    result->metadata.data_version = "amap_api";
    
    // 转换路径信息
    for (size_t i = 0; i < amap_response.routes.size(); ++i) {
        const auto& amap_route = amap_response.routes[i];
        
        PathInfo path_info;
        path_info.path_id = i + 1;
        path_info.length = amap_route.distance;
        path_info.travel_time = amap_route.duration;
        path_info.traffic_light_num = 0; // 高德API不直接提供红绿灯数量
        path_info.points = amap_route.coordinates;
        
        // 计算边界框
        if (!path_info.points.empty()) {
            double min_lng = path_info.points[0].lng();
            double max_lng = min_lng;
            double min_lat = path_info.points[0].lat();
            double max_lat = min_lat;
            
            for (const auto& point : path_info.points) {
                min_lng = std::min(min_lng, point.lng());
                max_lng = std::max(max_lng, point.lng());
                min_lat = std::min(min_lat, point.lat());
                max_lat = std::max(max_lat, point.lat());
            }
            
            path_info.bbox = AABB2<PointLL>(PointLL(min_lng, min_lat), PointLL(max_lng, max_lat));
        }
        
        // 创建单个section（简化处理）
        Section section;
        section.index = 0;
        section.num = path_info.points.size();
        section.length = path_info.length;
        section.time = path_info.travel_time;
        section.start_offset = 0.0;
        section.end_offset = 0.0;
        path_info.sections.push_back(section);
        
        result->paths.push_back(std::move(path_info));
    }
    
    // 生成路径标签
    if (!result->paths.empty()) {
        result->tag = GenerateRouteTag(query.strategy, amap_response.routes[0]);
    }
    
    return result;
}

PathResultPtr OnlineRouteEngine::CreateErrorResult(const std::string& error_message, int32_t error_code) {
    auto result = std::make_shared<PathResult>();
    result->status = "error";
    result->code = error_code;
    result->metadata.query_time_ms = 0;
    result->metadata.build_version = "online_amap_1.0";
    result->metadata.data_version = "amap_api";
    
    LOG_ERROR("OnlineRouteEngine: Creating error result: {}", error_message);
    return result;
}

void OnlineRouteEngine::UpdateMetrics(int64_t calculation_time_ms, bool success) {
    metrics_.total_requests++;
    metrics_.total_response_time_ms += calculation_time_ms;
    
    if (success) {
        metrics_.successful_requests++;
    } else {
        metrics_.failed_requests++;
    }
    
    metrics_.UpdateAverageResponseTime();
}

bool OnlineRouteEngine::ValidateQuery(const PathQuery& query) const {
    if (query.path_points.size() < 2) {
        LOG_ERROR("OnlineRouteEngine: Query must have at least 2 path points");
        return false;
    }
    
    // 检查起点和终点是否有效
    auto start_point = query.path_points.front();
    auto end_point = query.path_points.back();
    
    if (!start_point || !start_point->valid) {
        LOG_ERROR("OnlineRouteEngine: Invalid start point");
        return false;
    }
    
    if (!end_point || !end_point->valid) {
        LOG_ERROR("OnlineRouteEngine: Invalid end point");
        return false;
    }
    
    return true;
}

std::vector<PointLL> OnlineRouteEngine::ExtractWaypoints(const PathQuery& query) const {
    std::vector<PointLL> waypoints;
    
    // 提取中间的途经点（跳过起点和终点）
    for (size_t i = 1; i < query.path_points.size() - 1; ++i) {
        const auto& point = query.path_points[i];
        if (point && point->valid && point->waypoint_type == WayPointType::kViaPoint) {
            waypoints.push_back(point->pt);
        }
    }
    
    return waypoints;
}

std::string OnlineRouteEngine::GenerateRouteTag(PathStrategy strategy, const AmapRouteResponse::RouteInfo& route_info) const {
    std::ostringstream tag;
    
    switch (strategy) {
        case PathStrategy::kTimeFirst:
            tag << "最短时间";
            break;
        case PathStrategy::kDistanceFirst:
            tag << "最短距离";
            break;
        case PathStrategy::KHighWayFirst:
            tag << "高速优先";
            break;
        case PathStrategy::kAvoidToll:
            tag << "避开拥堵";
            break;
        default:
            tag << "在线算路";
            break;
    }
    
    // 添加距离和时间信息
    tag << " (" << std::fixed << std::setprecision(1) << route_info.distance / 1000.0 << "km, ";
    tag << std::fixed << std::setprecision(0) << route_info.duration / 60.0 << "min)";
    
    return tag.str();
}

} // namespace path
} // namespace aurora
