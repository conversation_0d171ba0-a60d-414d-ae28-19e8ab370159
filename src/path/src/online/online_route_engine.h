// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-18
//

#ifndef AURORA_PATH_SRC_ONLINE_ONLINE_ROUTE_ENGINE_H_
#define AURORA_PATH_SRC_ONLINE_ONLINE_ROUTE_ENGINE_H_

#include <memory>
#include <string>
#include <functional>
#include <chrono>

#include "path/include/path_def.h"
#include "amap_api_client.h"
#include "logger.h"

namespace aurora {
namespace path {

/**
 * 在线算路引擎性能指标
 */
struct OnlineRouteEngineMetrics {
    uint64_t total_requests = 0;        // 总请求数
    uint64_t successful_requests = 0;   // 成功请求数
    uint64_t failed_requests = 0;       // 失败请求数
    double average_response_time_ms = 0.0; // 平均响应时间（毫秒）
    uint64_t total_response_time_ms = 0;   // 总响应时间（毫秒）
    
    // 重置统计信息
    void Reset() {
        total_requests = 0;
        successful_requests = 0;
        failed_requests = 0;
        average_response_time_ms = 0.0;
        total_response_time_ms = 0;
    }
    
    // 更新平均响应时间
    void UpdateAverageResponseTime() {
        if (total_requests > 0) {
            average_response_time_ms = static_cast<double>(total_response_time_ms) / total_requests;
        }
    }
};

/**
 * 在线算路引擎
 * 
 * 提供与PathEngine相同的接口，但使用在线API进行路径计算
 * - 支持高德地图API
 * - 提供性能监控
 * - 支持中断机制
 * - 自动错误处理和重试
 */
class OnlineRouteEngine {
public:
    /**
     * 构造函数
     * @param api_key 高德API密钥
     * @param timeout_ms 请求超时时间（毫秒）
     */
    explicit OnlineRouteEngine(const std::string& api_key = "", int timeout_ms = 10000);
    
    /**
     * 析构函数
     */
    ~OnlineRouteEngine() = default;
    
    // 禁用拷贝构造和赋值
    OnlineRouteEngine(const OnlineRouteEngine&) = delete;
    OnlineRouteEngine& operator=(const OnlineRouteEngine&) = delete;
    
    /**
     * 核心路由计算接口（与PathEngine保持一致）
     * @param query 路径查询请求
     * @return 路径计算结果
     */
    PathResultPtr CalculateRoute(const PathQuery& query);
    
    /**
     * 设置中断回调函数
     * @param interrupt_callback 中断回调函数指针
     */
    void SetInterruptCallback(const std::function<void()>* interrupt_callback);
    
    /**
     * 取消当前计算
     */
    void CancelCurrentCalculation();
    
    /**
     * 清理所有临时数据和缓存
     */
    void Clear();
    
    /**
     * 获取性能统计信息
     * @return 性能指标对象的常量引用
     */
    const OnlineRouteEngineMetrics& GetMetrics() const;
    
    /**
     * 检查引擎是否已初始化
     * @return true 如果引擎已正确初始化
     */
    bool IsInitialized() const;
    
    /**
     * 获取当前算法类型（用于兼容性）
     * @return 算法类型字符串
     */
    std::string GetCurrentAlgorithmType() const;
    
    /**
     * 设置API密钥
     * @param api_key 新的API密钥
     */
    void SetApiKey(const std::string& api_key);
    
    /**
     * 设置请求超时时间
     * @param timeout_ms 超时时间（毫秒）
     */
    void SetTimeout(int timeout_ms);

private:
    /**
     * 将高德API响应转换为PathResult
     * @param amap_response 高德API响应
     * @param query 原始查询
     * @param calculation_time_ms 计算耗时
     * @return PathResult对象
     */
    PathResultPtr ConvertAmapResponseToPathResult(
        const AmapRouteResponse& amap_response,
        const PathQuery& query,
        int64_t calculation_time_ms
    );
    
    /**
     * 创建错误结果
     * @param error_message 错误信息
     * @param error_code 错误代码
     * @return PathResult对象
     */
    PathResultPtr CreateErrorResult(const std::string& error_message, int32_t error_code);
    
    /**
     * 更新性能指标
     * @param calculation_time_ms 计算耗时（毫秒）
     * @param success 是否成功
     */
    void UpdateMetrics(int64_t calculation_time_ms, bool success);
    
    /**
     * 验证查询参数
     * @param query 路径查询
     * @return true 如果参数有效
     */
    bool ValidateQuery(const PathQuery& query) const;
    
    /**
     * 提取途经点
     * @param query 路径查询
     * @return 途经点列表
     */
    std::vector<PointLL> ExtractWaypoints(const PathQuery& query) const;
    
    /**
     * 生成路径标签
     * @param strategy 路径策略
     * @param route_info 路径信息
     * @return 标签字符串
     */
    std::string GenerateRouteTag(PathStrategy strategy, const AmapRouteResponse::RouteInfo& route_info) const;

private:
    // 高德API客户端
    AmapApiClientPtr amap_client_;
    
    // 性能指标
    mutable OnlineRouteEngineMetrics metrics_;
    
    // 中断回调函数
    const std::function<void()>* interrupt_callback_;
    
    // 中断标志
    std::atomic<bool> is_cancelled_;
    
    // 初始化状态
    bool initialized_;
    
    // 算法类型（用于兼容性）
    static constexpr const char* ALGORITHM_TYPE = "online_amap";
};

// 类型别名
using OnlineRouteEnginePtr = std::unique_ptr<OnlineRouteEngine>;

} // namespace path
} // namespace aurora

#endif // AURORA_PATH_SRC_ONLINE_ONLINE_ROUTE_ENGINE_H_
