// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#ifndef PATH_SRC_ROUTE_ALGORITHM_CANDIDATE_CONNECTION_H_
#define PATH_SRC_ROUTE_ALGORITHM_CANDIDATE_CONNECTION_H_

#include <unordered_set>
#include "base/graphconstants.h"
#include "base/common_define.h"
#include "constants.h"
#include "graph_reader/graph_reader.h"
#include "graphid.h"

namespace aurora {
namespace path {

struct CandidateConnection {
    CandidateConnection() = default;
    
    CandidateConnection(DirectEdgeInfoRawPtr pred, DirectEdgeInfoRawPtr rev_pred, double c, const std::shared_ptr<GraphReader>& graph_reader_ptr)
        : edgeid(pred->graphid())
        , cost(c)
        , total_length(0U)
        , forward_edge(pred)
        , reverse_edge(rev_pred)
        , path_id_length_vec() {
        UpdatePathEdges(graph_reader_ptr);
    }
    GraphId edgeid;
    double cost;
    uint32_t total_length;

    // meet edges
    DirectEdgeInfoRawPtr forward_edge;
    DirectEdgeInfoRawPtr reverse_edge;

    // Edge IDs -> length in the path
    std::vector<std::pair<GraphId, uint32_t>> path_id_length_vec;

    void UpdatePathEdges(const std::shared_ptr<GraphReader>& graph_reader_ptr) {
        auto get_sub_edgeid = [](const DirectEdgeInfoRawPtr& direct_edge, SubEdge subedge) {
            parser::RouteTileID sub_tile_id(direct_edge->id.tileid());
            DCHECK(direct_edge->level_id > 0);
            sub_tile_id.level = 0; // subedge is level 0
            sub_tile_id.tile_id = subedge.tile_id;
            return GraphId(sub_tile_id.value, subedge.edge_id, subedge.edge_dir == 0 ? true : false);
        };
        // Collect forward path edge IDs
        auto cur_edge = forward_edge;
        while (cur_edge != nullptr) {
            uint32_t cur_length = cur_edge->origin ? static_cast<uint32_t>(cur_edge->edge_cost().length) : cur_edge->length();
            total_length += cur_length;
            if (cur_edge->level_id == 0) {
                path_id_length_vec.emplace_back(cur_edge->graphid(), cur_length);
            } else {
                auto aug_direct_edge = graph_reader_ptr->GetAugmentEdgeInfo(cur_edge->graphid(), true);
                for (const auto& subedge : aug_direct_edge->sub_edges) {
                    path_id_length_vec.emplace_back(get_sub_edgeid(cur_edge, subedge), cur_length);
                }
            }
            cur_edge = cur_edge->predecessor_edge;
        }

        // Collect reverse path edge IDs
        DCHECK(reverse_edge);
        cur_edge = reverse_edge->predecessor_edge;
        while (cur_edge != nullptr) {
            uint32_t cur_length = cur_edge->destination ? static_cast<uint32_t>(cur_edge->edge_cost().length) : cur_edge->length();
            total_length += cur_length;

            if (cur_edge->level_id == 0) {
                path_id_length_vec.emplace_back(cur_edge->graphid(), cur_length);
            } else {
                auto aug_direct_edge = graph_reader_ptr->GetAugmentEdgeInfo(cur_edge->graphid(), true);
                for (const auto& subedge : aug_direct_edge->sub_edges) {
                    path_id_length_vec.emplace_back(get_sub_edgeid(cur_edge, subedge), cur_length);
                }
            }

            cur_edge = cur_edge->predecessor_edge;
        }
        std::sort(path_id_length_vec.begin(), path_id_length_vec.end(), [](const auto& a, const auto& b) {
            return a.first < b.first;
        });
    }

    bool operator<(const CandidateConnection& o) const {
        return cost < o.cost;
    }
    bool operator<(double c) const {
        return cost < c;
    }

    bool operator==(const CandidateConnection& o) const {
        // cost is the same, float compare
        return std::abs(cost - o.cost) < kEpsilon;
    }
};

}  // namespace path
}  // namespace aurora

#endif  // PATH_SRC_ROUTE_ALGORITHM_CANDIDATE_CONNECTION_H_ 