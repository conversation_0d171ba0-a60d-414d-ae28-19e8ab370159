#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析双路径算路比较结果
从GeoJSON文件中提取和分析距离数据
"""

import json
from collections import defaultdict

def load_geojson_results(filename: str):
    """加载GeoJSON结果文件"""
    with open(filename, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def extract_features_by_type(geojson_data, feature_type):
    """按类型提取特征数据"""
    features = []

    for feature in geojson_data['features']:
        if feature['properties']['type'] == feature_type:
            features.append(feature)

    return features

def extract_original_points(geojson_data):
    """提取原点数据（包括起点和终点）"""
    original_points = []

    for feature in geojson_data['features']:
        feature_type = feature['properties']['type']
        if feature_type in ['original', 'original_start', 'original_end']:
            original_points.append(feature['properties'])

    return original_points

def extract_start_points(geojson_data):
    """提取起点数据"""
    start_points = []

    for feature in geojson_data['features']:
        feature_type = feature['properties']['type']
        if feature_type in ['original', 'original_start']:
            start_points.append(feature['properties'])

    return start_points

def extract_end_points(geojson_data):
    """提取终点数据"""
    end_points = []

    for feature in geojson_data['features']:
        feature_type = feature['properties']['type']
        if feature_type in ['original_end']:
            end_points.append(feature['properties'])

    return end_points

def analyze_distance_distribution(geojson_data):
    """分析距离分布"""
    print("=== 距离分布分析 ===\n")

    start_points = extract_start_points(geojson_data)
    end_points = extract_end_points(geojson_data)

    # 分析起点距离分布
    print("起点距离分布分析:")
    analyze_point_distances(start_points, 'start')

    # 分析终点距离分布
    if end_points:
        print("\n终点距离分布分析:")
        analyze_point_distances(end_points, 'end')

def analyze_point_distances(points, point_type):
    """分析点距离分布"""
    if not points:
        print(f"没有找到{point_type}数据")
        return

    # 按POI类型分析
    poi_type_stats = defaultdict(lambda: {
        'count': 0,
        'path_distances': [],
        'amap_distances': [],
        'comparison_distances': []
    })

    # 按区域分析
    district_stats = defaultdict(lambda: {
        'count': 0,
        'path_distances': [],
        'amap_distances': [],
        'comparison_distances': []
    })

    for point in points:
        poi_type = point['poi_type']
        district = point['district']

        # 根据点类型获取相应的距离字段
        if point_type == 'start':
            path_distance = point.get('distance_to_path_start', 0)
            amap_distance = point.get('distance_to_amap_start', 0)
            comparison_distance = point.get('path_to_amap_start_distance', 0)
        else:  # end
            path_distance = point.get('distance_to_path_end', 0)
            amap_distance = point.get('distance_to_amap_end', 0)
            comparison_distance = point.get('path_to_amap_end_distance', 0)

        # POI类型统计
        poi_type_stats[poi_type]['count'] += 1
        poi_type_stats[poi_type]['path_distances'].append(path_distance)
        poi_type_stats[poi_type]['amap_distances'].append(amap_distance)
        poi_type_stats[poi_type]['comparison_distances'].append(comparison_distance)

        # 区域统计
        district_stats[district]['count'] += 1
        district_stats[district]['path_distances'].append(path_distance)
        district_stats[district]['amap_distances'].append(amap_distance)
        district_stats[district]['comparison_distances'].append(comparison_distance)
    
    # 打印POI类型分析
    point_label = "起点" if point_type == 'start' else "终点"
    print(f"按POI类型分析({point_label}):")
    print("-" * 80)
    print(f"{'POI类型':<15} {'数量':<6} {'Path平均距离':<12} {'高德平均距离':<12} {point_label+'差异平均':<12}")
    print("-" * 80)

    for poi_type, stats in sorted(poi_type_stats.items()):
        if stats['path_distances']:
            avg_path = sum(stats['path_distances']) / len(stats['path_distances'])
            avg_amap = sum(stats['amap_distances']) / len(stats['amap_distances'])
            avg_comp = sum(stats['comparison_distances']) / len(stats['comparison_distances'])

            print(f"{poi_type:<15} {stats['count']:<6} {avg_path:<12.2f} {avg_amap:<12.2f} {avg_comp:<12.2f}")

    print(f"\n按区域分析({point_label}):")
    print("-" * 80)
    print(f"{'区域':<15} {'数量':<6} {'Path平均距离':<12} {'高德平均距离':<12} {point_label+'差异平均':<12}")
    print("-" * 80)

    for district, stats in sorted(district_stats.items()):
        if stats['path_distances']:
            avg_path = sum(stats['path_distances']) / len(stats['path_distances'])
            avg_amap = sum(stats['amap_distances']) / len(stats['amap_distances'])
            avg_comp = sum(stats['comparison_distances']) / len(stats['comparison_distances'])

            print(f"{district:<15} {stats['count']:<6} {avg_path:<12.2f} {avg_amap:<12.2f} {avg_comp:<12.2f}")

def analyze_distance_categories(original_points):
    """分析距离分类"""
    print("\n=== 距离分类分析 ===\n")

    # 分离起点和终点数据
    start_points = [p for p in original_points if p.get('type') in ['original', 'original_start']]
    end_points = [p for p in original_points if p.get('type') == 'original_end']

    # 分析起点分类
    if start_points:
        print("起点距离分类统计:")
        analyze_point_categories(start_points, 'start')

    # 分析终点分类
    if end_points:
        print("\n终点距离分类统计:")
        analyze_point_categories(end_points, 'end')

def analyze_point_categories(points, point_type):
    """分析点距离分类"""
    path_categories = defaultdict(int)
    amap_categories = defaultdict(int)
    comparison_categories = defaultdict(int)

    for point in points:
        if point_type == 'start':
            path_cat = point.get('path_start_distance_category', point.get('path_distance_category', ''))
            amap_cat = point.get('amap_start_distance_category', point.get('amap_distance_category', ''))
            comp_cat = point.get('start_comparison_distance_category', point.get('comparison_distance_category', ''))
        else:  # end
            path_cat = point.get('path_end_distance_category', '')
            amap_cat = point.get('amap_end_distance_category', '')
            comp_cat = point.get('end_comparison_distance_category', '')

        if path_cat:
            path_categories[path_cat] += 1
        if amap_cat:
            amap_categories[amap_cat] += 1
        if comp_cat:
            comparison_categories[comp_cat] += 1

    categories = ['0-1m', '1-5m', '5-10m', '10m+']
    point_label = "起点" if point_type == 'start' else "终点"

    print("-" * 60)
    print(f"{'分类':<10} {'Path'+point_label:<12} {'高德'+point_label:<12} {point_label+'差异':<12}")
    print("-" * 60)

    for category in categories:
        print(f"{category:<10} {path_categories[category]:<12} {amap_categories[category]:<12} {comparison_categories[category]:<12}")

def find_outliers(original_points, threshold=20.0):
    """找出距离异常的点"""
    print(f"\n=== 距离异常点分析 (>{threshold}m) ===\n")

    outliers = []
    for point in original_points:
        point_type = point.get('type', '')

        # 根据点类型获取相应的距离字段
        if point_type in ['original', 'original_start']:
            path_dist = point.get('distance_to_path_start', 0)
            amap_dist = point.get('distance_to_amap_start', 0)
            comp_dist = point.get('path_to_amap_start_distance', 0)
        elif point_type == 'original_end':
            path_dist = point.get('distance_to_path_end', 0)
            amap_dist = point.get('distance_to_amap_end', 0)
            comp_dist = point.get('path_to_amap_end_distance', 0)
        else:
            continue

        if (path_dist > threshold or amap_dist > threshold or comp_dist > threshold):
            point_info = point.copy()
            point_info['path_distance'] = path_dist
            point_info['amap_distance'] = amap_dist
            point_info['comparison_distance'] = comp_dist
            outliers.append(point_info)

    if outliers:
        print(f"发现 {len(outliers)} 个异常点:")
        print("-" * 110)
        print(f"{'POI名称':<30} {'类型':<10} {'区域':<10} {'角色':<8} {'Path距离':<10} {'高德距离':<10} {'差异距离':<10}")
        print("-" * 110)

        for point in outliers:
            role = point.get('point_role', '未知')
            print(f"{point['name'][:28]:<30} {point['poi_type']:<10} {point['district']:<10} "
                  f"{role:<8} {point['path_distance']:<10.2f} {point['amap_distance']:<10.2f} "
                  f"{point['comparison_distance']:<10.2f}")
    else:
        print(f"没有发现距离超过 {threshold}m 的异常点")

def generate_summary_report(original_points):
    """生成总结报告"""
    print("\n=== 总结报告 ===\n")

    # 分离起点和终点数据
    start_points = [p for p in original_points if p.get('type') in ['original', 'original_start']]
    end_points = [p for p in original_points if p.get('type') == 'original_end']

    if start_points:
        print("起点精度分析:")
        generate_point_summary(start_points, 'start')

    if end_points:
        print("\n终点精度分析:")
        generate_point_summary(end_points, 'end')

def generate_point_summary(points, point_type):
    """生成点总结报告"""
    total_points = len(points)

    # 根据点类型获取距离数据
    path_distances = []
    amap_distances = []
    comparison_distances = []

    for p in points:
        if point_type == 'start':
            path_distances.append(p.get('distance_to_path_start', 0))
            amap_distances.append(p.get('distance_to_amap_start', 0))
            comparison_distances.append(p.get('path_to_amap_start_distance', 0))
        else:  # end
            path_distances.append(p.get('distance_to_path_end', 0))
            amap_distances.append(p.get('distance_to_amap_end', 0))
            comparison_distances.append(p.get('path_to_amap_end_distance', 0))

    # 计算平均距离
    avg_path_distance = sum(path_distances) / total_points if total_points > 0 else 0
    avg_amap_distance = sum(amap_distances) / total_points if total_points > 0 else 0
    avg_comparison_distance = sum(comparison_distances) / total_points if total_points > 0 else 0
    
    point_label = "起点" if point_type == 'start' else "终点"

    print(f"测试总数: {total_points}")
    print(f"")
    print(f"原点到Path{point_label}距离:")
    print(f"  平均: {avg_path_distance:.2f}m")
    if path_distances:
        print(f"  最小: {min(path_distances):.2f}m")
        print(f"  最大: {max(path_distances):.2f}m")
    print(f"")
    print(f"原点到高德{point_label}距离:")
    print(f"  平均: {avg_amap_distance:.2f}m")
    if amap_distances:
        print(f"  最小: {min(amap_distances):.2f}m")
        print(f"  最大: {max(amap_distances):.2f}m")
    print(f"")
    print(f"Path{point_label}与高德{point_label}差异:")
    print(f"  平均: {avg_comparison_distance:.2f}m")
    if comparison_distances:
        print(f"  最小: {min(comparison_distances):.2f}m")
        print(f"  最大: {max(comparison_distances):.2f}m")

    # 精度评估
    high_precision_path = sum(1 for d in path_distances if d <= 5.0)
    high_precision_amap = sum(1 for d in amap_distances if d <= 5.0)
    high_precision_comparison = sum(1 for d in comparison_distances if d <= 5.0)

    print(f"")
    print(f"精度评估 (≤5m):")
    print(f"  Path{point_label}精度: {high_precision_path}/{total_points} ({high_precision_path/total_points*100:.1f}%)")
    print(f"  高德{point_label}精度: {high_precision_amap}/{total_points} ({high_precision_amap/total_points*100:.1f}%)")
    print(f"  {point_label}一致性: {high_precision_comparison}/{total_points} ({high_precision_comparison/total_points*100:.1f}%)")

def analyze_route_features(geojson_data):
    """分析路线特征"""
    print("\n=== 路线特征分析 ===\n")

    # 提取路线特征
    path_routes = extract_features_by_type(geojson_data, 'path_route')
    amap_routes = extract_features_by_type(geojson_data, 'amap_route')

    print(f"Path算路路线: {len(path_routes)} 条")
    print(f"高德算路路线: {len(amap_routes)} 条")

    if path_routes:
        print("\nPath路线统计:")
        print("-" * 60)
        print(f"{'起点名称':<30} {'目标名称':<20} {'路线点数':<10}")
        print("-" * 60)

        for route in path_routes[:5]:  # 只显示前5条
            props = route['properties']
            point_count = len(route['geometry']['coordinates']) if route['geometry']['type'] == 'LineString' else 0
            print(f"{props['origin_name'][:28]:<30} {props['dest_name'][:18]:<20} {point_count:<10}")

        if len(path_routes) > 5:
            print(f"... 还有 {len(path_routes) - 5} 条路线")

    if amap_routes:
        print("\n高德路线统计:")
        print("-" * 60)
        print(f"{'起点名称':<30} {'目标名称':<20} {'路线点数':<10}")
        print("-" * 60)

        for route in amap_routes[:5]:  # 只显示前5条
            props = route['properties']
            point_count = len(route['geometry']['coordinates']) if route['geometry']['type'] == 'LineString' else 0
            print(f"{props['origin_name'][:28]:<30} {props['dest_name'][:18]:<20} {point_count:<10}")

        if len(amap_routes) > 5:
            print(f"... 还有 {len(amap_routes) - 5} 条路线")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='分析双路径算路比较结果')
    parser.add_argument('geojson_file', help='GeoJSON结果文件路径')
    parser.add_argument('--threshold', '-t', type=float, default=20.0, 
                       help='异常点距离阈值(米) (默认: 20.0)')
    
    args = parser.parse_args()
    
    print(f"分析文件: {args.geojson_file}")
    print("=" * 80)
    
    # 加载数据
    geojson_data = load_geojson_results(args.geojson_file)
    original_points = extract_original_points(geojson_data)
    
    if not original_points:
        print("未找到原点数据")
        return
    
    # 执行分析
    analyze_distance_distribution(geojson_data)
    analyze_distance_categories(original_points)
    find_outliers(original_points, args.threshold)
    generate_summary_report(original_points)
    analyze_route_features(geojson_data)

if __name__ == "__main__":
    main()
