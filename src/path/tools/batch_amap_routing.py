#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量高德算路工具
使用上海POI数据进行批量高德算路测试
输出GeoJSON和CSV格式结果
"""

import csv
import json
import requests
import random
import math
import time
import os
import argparse
from typing import List, Dict, Tuple, Optional
from collections import defaultdict

# 设置代理环境变量，绕过本地连接的代理
os.environ['NO_PROXY'] = 'localhost,127.0.0.1'

# 高德API配置
AMAP_CONFIG = {
    'key': 'c6dbca449fbd6baadc69b8540565ef59',
    'driving_url': 'https://restapi.amap.com/v3/direction/driving'
}

class BatchAmapRouting:
    def __init__(self, max_distance: float = 5000):
        self.poi_data = []
        self.route_results = []
        self.max_distance = max_distance

    def load_existing_results_from_csv(self, csv_file: str) -> int:
        """从现有CSV文件加载已完成的测试结果"""
        try:
            print(f"尝试加载现有结果: {csv_file}")
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                existing_count = 0
                for row in reader:
                    # 重构结果数据结构
                    result = {
                        'origin_poi': {
                            'name': row['origin_name'],
                            'type': row['origin_type'],
                            'district': row['origin_district'],
                            'longitude': float(row['origin_lon']),
                            'latitude': float(row['origin_lat']),
                            'address': row['origin_address']
                        },
                        'dest_poi': {
                            'name': row['dest_name'],
                            'type': row['dest_type'],
                            'district': row['dest_district'],
                            'longitude': float(row['dest_lon']),
                            'latitude': float(row['dest_lat']),
                            'address': row['dest_address'],
                            'distance_to_origin': float(row['straight_distance'])
                        },
                        'original_start_point': (float(row['origin_lon']), float(row['origin_lat'])),
                        'original_end_point': (float(row['dest_lon']), float(row['dest_lat'])),
                        'route_start_point': (float(row['origin_lon']), float(row['origin_lat'])),  # 简化处理
                        'route_end_point': (float(row['dest_lon']), float(row['dest_lat'])),      # 简化处理
                        'route_coordinates': [],  # 无法从CSV恢复完整路线
                        'route_info': {
                            'distance': int(row['route_distance']),
                            'duration': int(row['route_duration']),
                            'tolls': 0,
                            'toll_distance': 0
                        },
                        'deviations': {
                            'start_deviation': float(row['start_deviation']),
                            'end_deviation': float(row['end_deviation'])
                        },
                        'strategy': int(row['strategy']),
                        'amap_result': {}  # 无法从CSV恢复原始API结果
                    }
                    self.route_results.append(result)
                    existing_count += 1

            print(f"成功加载 {existing_count} 个现有结果")
            return existing_count

        except FileNotFoundError:
            print(f"文件 {csv_file} 不存在，从头开始测试")
            return 0
        except Exception as e:
            print(f"加载现有结果失败: {e}")
            return 0
        
    def load_poi_data(self, csv_file: str) -> List[Dict]:
        """加载POI数据"""
        print(f"加载POI数据: {csv_file}")
        poi_data = []
        
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                poi_data.append({
                    'name': row['POIName'],
                    'type': row['POIType'],
                    'longitude': float(row['DistLon']),
                    'latitude': float(row['DistLat']),
                    'district': row['DistName'],
                    'address': row.get('POIAddress', '')
                })
        
        print(f"加载了 {len(poi_data)} 个POI点")
        self.poi_data = poi_data
        return poi_data
    
    def calculate_distance(self, lon1: float, lat1: float, lon2: float, lat2: float) -> float:
        """计算两点间距离（米）"""
        # 使用Haversine公式计算球面距离
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def find_nearby_pois(self, origin_poi: Dict, min_distance: float = 1000, 
                        max_distance: float = None) -> List[Dict]:
        """找到距离起点在指定范围内的POI点"""
        if max_distance is None:
            max_distance = self.max_distance
            
        nearby_pois = []
        origin_lon = origin_poi['longitude']
        origin_lat = origin_poi['latitude']
        
        for poi in self.poi_data:
            if poi['name'] == origin_poi['name']:  # 跳过自己
                continue
                
            distance = self.calculate_distance(
                origin_lon, origin_lat,
                poi['longitude'], poi['latitude']
            )
            
            if min_distance <= distance <= max_distance:
                poi_with_distance = poi.copy()
                poi_with_distance['distance_to_origin'] = distance
                nearby_pois.append(poi_with_distance)
        
        return nearby_pois
    
    def call_amap_route_api(self, start_lon: float, start_lat: float,
                           end_lon: float, end_lat: float, strategy: int = 0) -> Optional[Dict]:
        """调用高德算路API"""
        try:
            params = {
                'key': AMAP_CONFIG['key'],
                'origin': f"{start_lon},{start_lat}",
                'destination': f"{end_lon},{end_lat}",
                'extensions': 'all',
                'strategy': strategy  # 0:最短时间, 1:最短距离, 2:避开拥堵
            }
            
            response = requests.get(AMAP_CONFIG['driving_url'], params=params, timeout=10)
            
            if response.status_code != 200:
                print(f"高德API请求失败: {response.status_code}")
                return None
            
            result = response.json()
            
            if result.get('status') != '1':
                print(f"高德API错误: {result.get('info')}")
                return None
            
            return result
            
        except Exception as e:
            print(f"高德API调用异常: {e}")
            return None
    
    def extract_route_points(self, route_data: Dict) -> Tuple[Optional[Tuple[float, float]], 
                                                            Optional[Tuple[float, float]], 
                                                            Optional[List[List[float]]]]:
        """提取路线的起点、终点和完整路径"""
        try:
            if 'route' in route_data and 'paths' in route_data['route']:
                paths = route_data['route']['paths']
                if paths and len(paths) > 0:
                    steps = paths[0].get('steps', [])
                    if not steps:
                        return None, None, None
                    
                    # 提取所有坐标点
                    all_coordinates = []
                    for step in steps:
                        polyline = step.get('polyline', '')
                        if polyline:
                            points = polyline.split(';')
                            for point in points:
                                coords = point.split(',')
                                if len(coords) >= 2:
                                    all_coordinates.append([float(coords[0]), float(coords[1])])
                    
                    if all_coordinates:
                        start_point = (all_coordinates[0][0], all_coordinates[0][1])
                        end_point = (all_coordinates[-1][0], all_coordinates[-1][1])
                        return start_point, end_point, all_coordinates
            
            return None, None, None
            
        except Exception as e:
            print(f"提取路线信息失败: {e}")
            return None, None, None
    
    def get_route_info(self, route_data: Dict) -> Dict:
        """提取路线基本信息"""
        try:
            if 'route' in route_data and 'paths' in route_data['route']:
                paths = route_data['route']['paths']
                if paths and len(paths) > 0:
                    path = paths[0]
                    return {
                        'distance': int(path.get('distance', 0)),  # 距离（米）
                        'duration': int(path.get('duration', 0)),  # 时间（秒）
                        'tolls': int(path.get('tolls', 0)),        # 过路费（元）
                        'toll_distance': int(path.get('toll_distance', 0)),  # 收费路段距离（米）
                        'traffic_lights': len(path.get('steps', []))  # 大致的路段数
                    }
            return {}
        except Exception as e:
            print(f"提取路线信息失败: {e}")
            return {}
    
    def test_single_route(self, origin_poi: Dict, dest_poi: Dict, strategy: int = 0) -> Optional[Dict]:
        """测试单个路径"""
        print(f"算路: {origin_poi['name']} -> {dest_poi['name']}")
        
        start_lon = origin_poi['longitude']
        start_lat = origin_poi['latitude']
        end_lon = dest_poi['longitude']
        end_lat = dest_poi['latitude']
        
        # 调用高德API
        amap_result = self.call_amap_route_api(start_lon, start_lat, end_lon, end_lat, strategy)
        
        if not amap_result:
            print("  算路失败，跳过")
            return None
        
        # 提取路线信息
        route_start, route_end, route_coords = self.extract_route_points(amap_result)
        route_info = self.get_route_info(amap_result)
        
        if not route_start or not route_end or not route_coords:
            print("  无法提取路线信息，跳过")
            return None
        
        # 计算起终点偏差
        start_deviation = self.calculate_distance(start_lon, start_lat, route_start[0], route_start[1])
        end_deviation = self.calculate_distance(end_lon, end_lat, route_end[0], route_end[1])
        
        result = {
            'origin_poi': origin_poi,
            'dest_poi': dest_poi,
            'original_start_point': (start_lon, start_lat),
            'original_end_point': (end_lon, end_lat),
            'route_start_point': route_start,
            'route_end_point': route_end,
            'route_coordinates': route_coords,
            'route_info': route_info,
            'deviations': {
                'start_deviation': start_deviation,
                'end_deviation': end_deviation
            },
            'strategy': strategy,
            'amap_result': amap_result
        }
        
        print(f"  成功 - 距离: {route_info.get('distance', 0)}m, "
              f"时间: {route_info.get('duration', 0)}s, "
              f"起点偏差: {start_deviation:.2f}m, "
              f"终点偏差: {end_deviation:.2f}m")
        
        return result

    def run_batch_routing(self, num_tests: int = 100, min_distance: float = 1000,
                         strategy: int = 0, output_geojson: str = None, output_csv: str = None):
        """运行批量算路测试"""
        print(f"开始批量高德算路测试，共 {num_tests} 组路径")
        print(f"距离范围: {min_distance}m - {self.max_distance}m")
        print(f"算路策略: {strategy} (0:最短时间, 1:最短距离, 2:避开拥堵)")
        if output_geojson or output_csv:
            print(f"每100次测试自动保存结果")
        print("-" * 60)

        if not self.poi_data:
            print("请先加载POI数据")
            return

        successful_tests = 0

        for i in range(num_tests):
            print(f"\n=== 测试 {i+1}/{num_tests} ===")

            # 随机选择起点
            origin_poi = random.choice(self.poi_data)

            # 找到指定距离范围内的POI作为终点候选
            nearby_pois = self.find_nearby_pois(origin_poi, min_distance, self.max_distance)

            if not nearby_pois:
                print(f"起点 {origin_poi['name']} 周围{min_distance}-{self.max_distance}m内没有其他POI，跳过")
                continue

            # 随机选择终点
            dest_poi = random.choice(nearby_pois)

            # 测试路径
            result = self.test_single_route(origin_poi, dest_poi, strategy)

            if result:
                self.route_results.append(result)
                successful_tests += 1
                print(f"  测试成功 ({successful_tests}/{i+1})")

            # 每100次成功测试保存一次结果
            if successful_tests > 0 and successful_tests % 100 == 0:
                print(f"\n--- 已完成 {successful_tests} 次测试，保存中间结果 ---")
                if output_csv:
                    backup_csv = f"{output_csv.rsplit('.', 1)[0]}_backup_{successful_tests}.csv"
                    self.save_route_pairs_to_csv(backup_csv)
                if output_geojson:
                    backup_geojson = f"{output_geojson.rsplit('.', 1)[0]}_backup_{successful_tests}.geojson"
                    self.save_results_to_geojson(backup_geojson)
                print(f"--- 中间结果保存完成 ---\n")

            # 避免API调用过于频繁
            time.sleep(0.3)

        print(f"\n批量测试完成，成功 {successful_tests}/{num_tests} 组")

    def save_route_pairs_to_csv(self, filename: str):
        """保存算路起终点对到CSV文件，方便复测"""
        print(f"\n保存起终点对到 {filename}")

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'test_id', 'origin_name', 'origin_type', 'origin_district',
                'origin_lon', 'origin_lat', 'origin_address',
                'dest_name', 'dest_type', 'dest_district',
                'dest_lon', 'dest_lat', 'dest_address',
                'straight_distance', 'route_distance', 'route_duration',
                'start_deviation', 'end_deviation', 'strategy'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()

            for i, result in enumerate(self.route_results):
                row = {
                    'test_id': i + 1,
                    'origin_name': result['origin_poi']['name'],
                    'origin_type': result['origin_poi']['type'],
                    'origin_district': result['origin_poi']['district'],
                    'origin_lon': result['origin_poi']['longitude'],
                    'origin_lat': result['origin_poi']['latitude'],
                    'origin_address': result['origin_poi']['address'],
                    'dest_name': result['dest_poi']['name'],
                    'dest_type': result['dest_poi']['type'],
                    'dest_district': result['dest_poi']['district'],
                    'dest_lon': result['dest_poi']['longitude'],
                    'dest_lat': result['dest_poi']['latitude'],
                    'dest_address': result['dest_poi']['address'],
                    'straight_distance': result['dest_poi']['distance_to_origin'],
                    'route_distance': result['route_info'].get('distance', 0),
                    'route_duration': result['route_info'].get('duration', 0),
                    'start_deviation': result['deviations']['start_deviation'],
                    'end_deviation': result['deviations']['end_deviation'],
                    'strategy': result['strategy']
                }
                writer.writerow(row)

        print(f"已保存 {len(self.route_results)} 组起终点对到 {filename}")

    def save_results_to_geojson(self, filename: str):
        """保存结果到GeoJSON文件"""
        print(f"\n保存结果到 {filename}")

        features = []

        for i, result in enumerate(self.route_results):
            # 原始起点
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['original_start_point'])
                },
                "properties": {
                    "id": i,
                    "type": "original_start",
                    "name": result['origin_poi']['name'],
                    "poi_type": result['origin_poi']['type'],
                    "district": result['origin_poi']['district'],
                    "address": result['origin_poi']['address'],
                    "dest_name": result['dest_poi']['name'],
                    "point_role": "原始起点",
                    "start_deviation": result['deviations']['start_deviation'],
                    "route_start_coords": list(result['route_start_point'])
                }
            })

            # 原始终点
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['original_end_point'])
                },
                "properties": {
                    "id": i,
                    "type": "original_end",
                    "name": result['dest_poi']['name'],
                    "poi_type": result['dest_poi']['type'],
                    "district": result['dest_poi']['district'],
                    "address": result['dest_poi']['address'],
                    "origin_name": result['origin_poi']['name'],
                    "point_role": "原始终点",
                    "end_deviation": result['deviations']['end_deviation'],
                    "route_end_coords": list(result['route_end_point'])
                }
            })

            # 算路起点
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['route_start_point'])
                },
                "properties": {
                    "id": i,
                    "type": "route_start",
                    "name": f"算路起点_{result['origin_poi']['name']}",
                    "original_name": result['origin_poi']['name'],
                    "dest_name": result['dest_poi']['name'],
                    "point_role": "算路起点",
                    "deviation": result['deviations']['start_deviation'],
                    "original_coords": list(result['original_start_point'])
                }
            })

            # 算路终点
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['route_end_point'])
                },
                "properties": {
                    "id": i,
                    "type": "route_end",
                    "name": f"算路终点_{result['dest_poi']['name']}",
                    "original_name": result['dest_poi']['name'],
                    "origin_name": result['origin_poi']['name'],
                    "point_role": "算路终点",
                    "deviation": result['deviations']['end_deviation'],
                    "original_coords": list(result['original_end_point'])
                }
            })

            # 完整路线
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "LineString",
                    "coordinates": result['route_coordinates']
                },
                "properties": {
                    "id": i,
                    "type": "route_line",
                    "name": f"路线_{result['origin_poi']['name']}到{result['dest_poi']['name']}",
                    "origin_name": result['origin_poi']['name'],
                    "dest_name": result['dest_poi']['name'],
                    "distance": result['route_info'].get('distance', 0),
                    "duration": result['route_info'].get('duration', 0),
                    "tolls": result['route_info'].get('tolls', 0),
                    "toll_distance": result['route_info'].get('toll_distance', 0),
                    "strategy": result['strategy'],
                    "start_deviation": result['deviations']['start_deviation'],
                    "end_deviation": result['deviations']['end_deviation'],
                    "original_start_coords": list(result['original_start_point']),
                    "original_end_coords": list(result['original_end_point']),
                    "route_start_coords": list(result['route_start_point']),
                    "route_end_coords": list(result['route_end_point'])
                }
            })

        geojson = {
            "type": "FeatureCollection",
            "features": features
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(geojson, f, ensure_ascii=False, indent=2)

        print(f"已保存 {len(features)} 个特征到 {filename}")

        # 统计特征类型
        feature_counts = {}
        for feature in features:
            feature_type = feature['properties']['type']
            feature_counts[feature_type] = feature_counts.get(feature_type, 0) + 1

        print("特征类型统计:")
        for feature_type, count in feature_counts.items():
            print(f"  {feature_type}: {count} 个")

    def print_statistics(self):
        """打印统计结果"""
        if not self.route_results:
            print("没有算路结果")
            return

        print("\n=== 算路统计结果 ===")

        # 基本统计
        total_routes = len(self.route_results)
        total_distance = sum(r['route_info'].get('distance', 0) for r in self.route_results)
        total_duration = sum(r['route_info'].get('duration', 0) for r in self.route_results)

        avg_distance = total_distance / total_routes
        avg_duration = total_duration / total_routes

        print(f"总路线数: {total_routes}")
        print(f"平均距离: {avg_distance:.0f}m")
        print(f"平均时间: {avg_duration:.0f}s ({avg_duration/60:.1f}分钟)")

        # 偏差统计
        start_deviations = [r['deviations']['start_deviation'] for r in self.route_results]
        end_deviations = [r['deviations']['end_deviation'] for r in self.route_results]

        print(f"\n起点偏差统计:")
        print(f"  平均: {sum(start_deviations)/len(start_deviations):.2f}m")
        print(f"  最小: {min(start_deviations):.2f}m")
        print(f"  最大: {max(start_deviations):.2f}m")

        print(f"终点偏差统计:")
        print(f"  平均: {sum(end_deviations)/len(end_deviations):.2f}m")
        print(f"  最小: {min(end_deviations):.2f}m")
        print(f"  最大: {max(end_deviations):.2f}m")

        # 精度统计
        high_precision_start = sum(1 for d in start_deviations if d <= 5.0)
        high_precision_end = sum(1 for d in end_deviations if d <= 5.0)

        print(f"\n精度评估 (≤5m):")
        print(f"  起点精度: {high_precision_start}/{total_routes} ({high_precision_start/total_routes*100:.1f}%)")
        print(f"  终点精度: {high_precision_end}/{total_routes} ({high_precision_end/total_routes*100:.1f}%)")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量高德算路测试工具')
    parser.add_argument('--num-tests', '-n', type=int, default=50,
                       help='算路测试用例个数 (默认: 50)')
    parser.add_argument('--poi-file', '-f', type=str, default='shanghai_poi_amap_10k.csv',
                       help='POI数据文件路径 (默认: shanghai_poi_amap_10k.csv)')
    parser.add_argument('--min-distance', type=float, default=1000,
                       help='起终点最小距离(米) (默认: 1000)')
    parser.add_argument('--max-distance', '-d', type=float, default=5000,
                       help='起终点最大距离(米) (默认: 5000)')
    parser.add_argument('--strategy', '-s', type=int, default=0, choices=[0, 1, 2],
                       help='算路策略: 0=最短时间, 1=最短距离, 2=避开拥堵 (默认: 0)')
    parser.add_argument('--output-geojson', '-o', type=str, default='amap_routes.geojson',
                       help='输出GeoJSON文件路径 (默认: amap_routes.geojson)')
    parser.add_argument('--output-csv', '-c', type=str, default='amap_route_pairs.csv',
                       help='输出CSV文件路径 (默认: amap_route_pairs.csv)')
    parser.add_argument('--resume', '-r', action='store_true',
                       help='从现有CSV文件恢复测试进度')

    args = parser.parse_args()

    print("批量高德算路测试工具")
    print(f"测试用例数量: {args.num_tests}")
    print(f"POI数据文件: {args.poi_file}")
    print(f"距离范围: {args.min_distance}m - {args.max_distance}m")
    print(f"算路策略: {args.strategy}")
    print(f"输出GeoJSON: {args.output_geojson}")
    print(f"输出CSV: {args.output_csv}")
    print("=" * 60)

    # 创建测试实例
    routing = BatchAmapRouting(max_distance=args.max_distance)

    # 加载POI数据
    routing.load_poi_data(args.poi_file)

    # 如果启用恢复模式，加载现有结果
    existing_count = 0
    if args.resume:
        existing_count = routing.load_existing_results_from_csv(args.output_csv)
        if existing_count > 0:
            remaining_tests = max(0, args.num_tests - existing_count)
            print(f"已有 {existing_count} 个结果，还需测试 {remaining_tests} 个")
            if remaining_tests == 0:
                print("所有测试已完成，无需继续测试")
                routing.print_statistics()
                return
        else:
            print("未找到现有结果，从头开始测试")

    # 运行批量测试
    actual_num_tests = args.num_tests - existing_count if args.resume else args.num_tests
    if actual_num_tests > 0:
        routing.run_batch_routing(actual_num_tests, args.min_distance, args.strategy,
                                 args.output_geojson, args.output_csv)

    # 打印统计结果
    routing.print_statistics()

    # 保存结果
    routing.save_route_pairs_to_csv(args.output_csv)
    routing.save_results_to_geojson(args.output_geojson)

    print("\n测试完成！")

if __name__ == "__main__":
    main()
