# 批量高德算路测试工具

## 功能说明

这个工具专门用于批量高德地图算路测试，主要功能包括：

1. 使用上海POI数据作为起点和终点进行批量算路
2. 支持多种算路策略（最短时间、最短距离、避开拥堵）
3. 可配置起终点距离范围
4. 输出详细的GeoJSON可视化文件
5. 生成CSV文件保存起终点对，方便复测

## 使用方法

### 基本用法

```bash
# 使用默认参数（50个测试用例）
python3 batch_amap_routing.py

# 指定测试用例数量
python3 batch_amap_routing.py -n 100

# 指定距离范围
python3 batch_amap_routing.py --min-distance 2000 --max-distance 8000

# 指定算路策略
python3 batch_amap_routing.py -s 1  # 最短距离策略

# 指定输出文件
python3 batch_amap_routing.py -o my_routes.geojson -c my_pairs.csv

# 从现有结果恢复测试（断点续传）
python3 batch_amap_routing.py -n 1000 --resume
```

### 参数说明

- `--num-tests, -n`: 算路测试用例个数（默认：50）
- `--poi-file, -f`: POI数据文件路径（默认：shanghai_poi_amap_10k.csv）
- `--min-distance`: 起终点最小距离，单位米（默认：1000）
- `--max-distance, -d`: 起终点最大距离，单位米（默认：5000）
- `--strategy, -s`: 算路策略（默认：0）
  - 0: 最短时间
  - 1: 最短距离
  - 2: 避开拥堵
- `--output-geojson, -o`: 输出GeoJSON文件路径（默认：amap_routes.geojson）
- `--output-csv, -c`: 输出CSV文件路径（默认：amap_route_pairs.csv）
- `--resume, -r`: 从现有CSV文件恢复测试进度（断点续传）

### 完整示例

```bash
python3 batch_amap_routing.py -n 100 -f shanghai_poi_amap_10k.csv --min-distance 1500 --max-distance 6000 -s 0 -o routes_100.geojson -c pairs_100.csv

# 大规模测试（1000个用例）
python3 batch_amap_routing.py -n 1000 -o large_test.geojson -c large_test.csv

# 如果测试中断，可以恢复继续
python3 batch_amap_routing.py -n 1000 -o large_test.geojson -c large_test.csv --resume
```

## 输出结果

### 1. 控制台输出

```
批量高德算路测试工具
测试用例数量: 50
POI数据文件: shanghai_poi_amap_10k.csv
距离范围: 1000m - 5000m
算路策略: 0
输出GeoJSON: amap_routes.geojson
输出CSV: amap_route_pairs.csv
============================================================

=== 测试 1/50 ===
算路: 西岑(地铁站) -> 金泽镇淀湖村陈港卫生室
  成功 - 距离: 12450m, 时间: 1234s, 起点偏差: 15.80m, 终点偏差: 8.45m
  测试成功 (1/1)

...

=== 算路统计结果 ===
总路线数: 45
平均距离: 3456m
平均时间: 678s (11.3分钟)

起点偏差统计:
  平均: 12.34m
  最小: 0.50m
  最大: 45.67m

终点偏差统计:
  平均: 8.90m
  最小: 0.30m
  最大: 32.10m

精度评估 (≤5m):
  起点精度: 15/45 (33.3%)
  终点精度: 25/45 (55.6%)
```

### 2. CSV输出文件

`amap_route_pairs.csv` 包含以下字段：

- `test_id`: 测试用例ID
- `origin_name`: 起点名称
- `origin_type`: 起点POI类型
- `origin_district`: 起点所属区域
- `origin_lon`: 起点经度
- `origin_lat`: 起点纬度
- `origin_address`: 起点地址
- `dest_name`: 终点名称
- `dest_type`: 终点POI类型
- `dest_district`: 终点所属区域
- `dest_lon`: 终点经度
- `dest_lat`: 终点纬度
- `dest_address`: 终点地址
- `straight_distance`: 直线距离（米）
- `route_distance`: 路线距离（米）
- `route_duration`: 路线时间（秒）
- `start_deviation`: 起点偏差（米）
- `end_deviation`: 终点偏差（米）
- `strategy`: 算路策略

### 3. GeoJSON输出文件

`amap_routes.geojson` 包含五种类型的特征：

**点特征：**
- `original_start`: 原始起点POI
- `original_end`: 原始终点POI
- `route_start`: 算路实际起点
- `route_end`: 算路实际终点

**线特征：**
- `route_line`: 完整算路路线

#### 特征属性说明

**原始起点/终点属性：**
- `id`: 测试用例ID
- `type`: 特征类型
- `name`: POI名称
- `poi_type`: POI类型
- `district`: 所属区域
- `address`: 地址
- `point_role`: 点角色
- `start_deviation`/`end_deviation`: 偏差距离
- `route_start_coords`/`route_end_coords`: 对应算路点坐标

**算路起点/终点属性：**
- `id`: 测试用例ID
- `type`: 特征类型
- `name`: 点名称
- `original_name`: 原始POI名称
- `point_role`: 点角色
- `deviation`: 偏差距离
- `original_coords`: 原始POI坐标

**路线属性：**
- `id`: 测试用例ID
- `type`: 特征类型 ("route_line")
- `name`: 路线名称
- `origin_name`: 起点名称
- `dest_name`: 终点名称
- `distance`: 路线距离（米）
- `duration`: 路线时间（秒）
- `tolls`: 过路费（元）
- `toll_distance`: 收费路段距离（米）
- `strategy`: 算路策略
- `start_deviation`: 起点偏差
- `end_deviation`: 终点偏差
- 各种坐标信息

## 复测功能

生成的CSV文件可以用于复测，使用 `retest_amap_routing.py` 工具：

### 复测工具使用方法

```bash
# 基本复测（使用原策略）
python3 retest_amap_routing.py test_amap_pairs.csv

# 使用新策略复测
python3 retest_amap_routing.py test_amap_pairs.csv --strategy 1

# 只复测指定的测试用例
python3 retest_amap_routing.py test_amap_pairs.csv --test-ids 1,3,5

# 指定输出文件
python3 retest_amap_routing.py test_amap_pairs.csv -s 2 -o comparison_results.csv
```

### 复测参数说明

- `csv_file`: 输入的CSV文件路径（必需）
- `--strategy, -s`: 新的算路策略（可选，默认使用原策略）
- `--test-ids, -t`: 指定测试用例ID，用逗号分隔
- `--output-csv, -o`: 输出对比结果文件路径

### 复测输出

复测工具会生成对比结果CSV文件，包含：
- 原始和新的距离、时间数据
- 变化量和变化百分比
- 策略对比信息
- 详细的统计分析

### 复测应用场景

1. **策略对比**: 比较不同算路策略的效果
2. **时间对比**: 在不同时间段测试路况变化
3. **稳定性测试**: 验证算路结果的一致性
4. **性能分析**: 分析算路性能变化趋势

## 可视化查看

将生成的GeoJSON文件导入到以下工具中查看：

- **QGIS**: 专业GIS软件，支持复杂的空间分析
- **在线GeoJSON查看器**: 如 geojson.io
- **Web地图库**: Leaflet、OpenLayers等

### QGIS显示建议

1. **图层组织**:
   - 路线图层（底层，线条）
   - 算路起终点图层（中层）
   - 原始POI图层（顶层）

2. **样式设置**:
   - 路线：蓝色线条，线宽2-3px
   - 原始起点：绿色圆点，大小8px
   - 原始终点：红色圆点，大小8px
   - 算路起点：蓝色方块，大小6px
   - 算路终点：红色方块，大小6px

3. **标注显示**:
   - 显示POI名称
   - 显示偏差距离
   - 显示路线距离和时间

## 自动保存和恢复功能

### 自动保存机制
- **每100次成功测试自动保存**: 避免长时间测试中途失败导致结果丢失
- **备份文件命名**: `filename_backup_100.csv`, `filename_backup_200.geojson` 等
- **实时进度保护**: 即使程序意外中断，也能保留已完成的测试结果

### 断点续传功能
```bash
# 开始大规模测试
python3 batch_amap_routing.py -n 1000 -c results.csv

# 如果测试中断，使用 --resume 参数继续
python3 batch_amap_routing.py -n 1000 -c results.csv --resume
```

**恢复机制说明**:
1. 程序会检查输出CSV文件是否存在
2. 如果存在，自动加载已完成的测试结果
3. 计算剩余需要测试的数量
4. 从中断点继续测试

### 备份文件管理
- 备份文件会在每100次测试后自动生成
- 可以手动从任意备份文件恢复测试
- 建议定期清理旧的备份文件

## 注意事项

1. **API限制**: 高德地图API有调用频率限制，脚本已设置0.3秒延迟
2. **网络连接**: 确保网络可以访问高德地图API
3. **数据文件**: 确保POI数据文件存在且格式正确
4. **坐标系统**: 使用GCJ-02坐标系（高德地图标准）
5. **测试规模**: 支持大规模测试，建议使用自动保存和恢复功能
6. **磁盘空间**: 大规模测试会生成较大的文件，确保有足够磁盘空间

## 故障排除

### API调用失败
```
高德API错误: INVALID_USER_KEY
```
**解决方案**: 检查API密钥是否正确，是否有Web服务API权限

### 距离范围问题
```
起点 XXX 周围1000-5000m内没有其他POI，跳过
```
**解决方案**: 调整 `--min-distance` 和 `--max-distance` 参数

### 文件保存失败
```
Permission denied: amap_routes.geojson
```
**解决方案**: 检查文件写入权限，或更改输出路径

## 扩展功能

可以基于此工具进行以下扩展：

1. **多策略对比**: 同时测试多种算路策略
2. **时间段分析**: 在不同时间段进行算路对比
3. **区域分析**: 按区域统计算路性能
4. **路况分析**: 结合实时路况进行分析
