#!/bin/bash

# 编译在线算路测试程序

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

echo "Building online routing test..."
echo "Project root: $PROJECT_ROOT"

# 创建构建目录
BUILD_DIR="$PROJECT_ROOT/build"
if [ ! -d "$BUILD_DIR" ]; then
    mkdir -p "$BUILD_DIR"
fi

cd "$BUILD_DIR"

# 配置CMake（如果需要）
if [ ! -f "Makefile" ]; then
    echo "Configuring CMake..."
    cmake ..
fi

# 编译path模块
echo "Building path module..."
make aurora_path -j$(nproc)

# 编译测试程序
echo "Building online routing test..."
g++ -std=c++17 \
    -I"$PROJECT_ROOT/src/path/include" \
    -I"$PROJECT_ROOT/src/path/src" \
    -I"$PROJECT_ROOT/src/base/include" \
    -I"$PROJECT_ROOT/src/search/src" \
    -I"$PROJECT_ROOT/third_party/rapidjson/include" \
    -I"$PROJECT_ROOT/src/data_provider/include" \
    -I"$PROJECT_ROOT/src/data_provider/include/route_data" \
    -I"$PROJECT_ROOT/src/data_provider" \
    -L"$BUILD_DIR/src/path" \
    -L"$BUILD_DIR/src/base" \
    -L"$BUILD_DIR/src/data_provider" \
    -o test_online_routing \
    "$PROJECT_ROOT/src/path/tools/test_online_routing.cpp" \
    -laurora_path \
    -laurora_base \
    -ldata_provider \
    -lpthread \
    -lcurl

echo "Build completed successfully!"
echo "Test executable: $BUILD_DIR/test_online_routing"
echo ""
echo "To run the test:"
echo "cd $BUILD_DIR && ./test_online_routing"
