#!/bin/bash

# 编译简单在线算路测试程序

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

echo "Building simple online routing test..."
echo "Project root: $PROJECT_ROOT"

# 创建构建目录
BUILD_DIR="$PROJECT_ROOT/build"
cd "$BUILD_DIR"

# 编译测试程序
echo "Building simple online routing test..."
g++ -std=c++17 \
    -I"$PROJECT_ROOT/src/path/include" \
    -I"$PROJECT_ROOT/src/path/src" \
    -I"$PROJECT_ROOT/src/base/include" \
    -I"$PROJECT_ROOT/src/search/src" \
    -I"$PROJECT_ROOT/build/_deps/rapidjson-src/include" \
    -I"$PROJECT_ROOT/src/data_provider/include" \
    -I"$PROJECT_ROOT/src/data_provider/include/route_data" \
    -I"$PROJECT_ROOT/src/data_provider" \
    -I"$PROJECT_ROOT/build/_deps/spdlog-src/include" \
    -L"$BUILD_DIR/src/path" \
    -L"$BUILD_DIR/src/base/src" \
    -L"$BUILD_DIR/src/data_provider" \
    -L"$BUILD_DIR/_deps/spdlog-build" \
    -o simple_online_test \
    "$PROJECT_ROOT/src/path/tools/simple_online_test.cpp" \
    -laurora_path \
    -laurora_base \
    -ldata_provider \
    -lspdlog \
    -lpthread

echo "Build completed successfully!"
echo "Test executable: $BUILD_DIR/simple_online_test"
echo ""
echo "To run the test:"
echo "cd $BUILD_DIR && ./simple_online_test"
