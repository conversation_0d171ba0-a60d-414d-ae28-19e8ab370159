{"type": "FeatureCollection", "features": [{"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.416945, 31.162805]}, "properties": {"id": 0, "type": "original", "name": "上海师范大学徐汇校区西部数理学院", "poi_type": "学校", "district": "徐汇区", "dest_name": "虹莘新村", "distance_to_path_start": 15.668146379572015, "distance_to_amap_start": 13.352183563665333, "path_to_amap_distance": 29.0186408600295, "path_distance_category": "10m+", "amap_distance_category": "10m+", "comparison_distance_category": "10m+", "path_start_coords": [121.416918, 31.162944], "amap_start_coords": [121.416971, 31.162687]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.416918, 31.162944]}, "properties": {"id": 0, "type": "path_start", "name": "Path起点_上海师范大学徐汇校区西部数理学院", "original_poi_name": "上海师范大学徐汇校区西部数理学院", "original_poi_type": "学校", "original_poi_district": "徐汇区", "dest_name": "虹莘新村", "distance_from_original": 15.668146379572015, "distance_category": "10m+", "distance_to_amap_start": 29.0186408600295, "original_coords": [121.416945, 31.162805]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.416971, 31.162687]}, "properties": {"id": 0, "type": "amap_start", "name": "高德起点_上海师范大学徐汇校区西部数理学院", "original_poi_name": "上海师范大学徐汇校区西部数理学院", "original_poi_type": "学校", "original_poi_district": "徐汇区", "dest_name": "虹莘新村", "distance_from_original": 13.352183563665333, "distance_category": "10m+", "distance_to_path_start": 29.0186408600295, "original_coords": [121.416945, 31.162805]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.416918, 31.162944], [121.41737, 31.16303], [121.41737, 31.16303], [121.41773, 31.16311], [121.41773, 31.16311], [121.41835, 31.16329], [121.41835, 31.16329], [121.41894, 31.16337], [121.41894, 31.16337], [121.41903, 31.16339], [121.41903, 31.16339], [121.41919, 31.16341], [121.41919, 31.16341], [121.41929, 31.1628], [121.41929, 31.1628], [121.4195, 31.16158], [121.4195, 31.16158], [121.41977, 31.15992], [121.41977, 31.15992], [121.41995, 31.15883], [121.41995, 31.15883], [121.42003, 31.15835], [121.42003, 31.15835], [121.42005, 31.15825], [121.42005, 31.15825], [121.42021, 31.15733], [121.42029, 31.15708], [121.42046, 31.15675], [121.42046, 31.15675], [121.42056, 31.1566], [121.42119, 31.15582], [121.42119, 31.15582], [121.4218, 31.15506], [121.4218, 31.15506], [121.4224, 31.1543], [121.4224, 31.1543], [121.42281, 31.1538], [121.42281, 31.1538], [121.42384, 31.1525], [121.42384, 31.1525], [121.42389, 31.15244], [121.42389, 31.15244], [121.42318, 31.15177], [121.42318, 31.15177], [121.42093, 31.14959], [121.42093, 31.14959], [121.4208, 31.14947], [121.4208, 31.14947], [121.42025, 31.14894], [121.42025, 31.14894], [121.41967, 31.14837], [121.41967, 31.14837], [121.4191, 31.14783], [121.4191, 31.14783], [121.41866, 31.1474], [121.41866, 31.1474], [121.41863, 31.14737], [121.41863, 31.14737], [121.41826, 31.14699], [121.41815, 31.14691], [121.41799, 31.14674], [121.41799, 31.14674], [121.41784, 31.14663], [121.41784, 31.14663], [121.41751, 31.14637], [121.41751, 31.14637], [121.4171, 31.14596], [121.4171, 31.14596], [121.41696, 31.14584], [121.41696, 31.14584], [121.4168, 31.14567], [121.4168, 31.14567], [121.41554, 31.14448], [121.41554, 31.14448], [121.41548, 31.14442], [121.41548, 31.14442], [121.41484, 31.14384], [121.41484, 31.14384], [121.41397, 31.14294], [121.41397, 31.14294], [121.41387, 31.14284], [121.41387, 31.14284], [121.4137, 31.14268], [121.4137, 31.14268], [121.41358, 31.14257], [121.41358, 31.14257], [121.41193, 31.141], [121.41193, 31.141], [121.41179, 31.14088], [121.41179, 31.14088], [121.41154, 31.14064], [121.41154, 31.14064], [121.40844, 31.13769], [121.40803, 31.13739], [121.40803, 31.13739], [121.40795, 31.13731], [121.40795, 31.13731], [121.40764, 31.13702], [121.40764, 31.13702], [121.40733, 31.13673], [121.40733, 31.13673], [121.40708, 31.13651], [121.40708, 31.13651], [121.40572, 31.13514], [121.40558, 31.13502], [121.40543, 31.13495], [121.40543, 31.13495], [121.40507, 31.1346], [121.40507, 31.1346], [121.40485, 31.13441], [121.40485, 31.13441], [121.40465, 31.13422], [121.40465, 31.13422], [121.40441, 31.13407], [121.40427, 31.13407], [121.40411, 31.13412], [121.40411, 31.13412], [121.40405, 31.13423], [121.40405, 31.13423], [121.40361, 31.13518], [121.40361, 31.13518], [121.40305, 31.13635], [121.40305, 31.13635], [121.40296, 31.13628], [121.40296, 31.13628], [121.4027, 31.1361], [121.40236, 31.1359], [121.4017, 31.13562], [121.40077, 31.13526], [121.40077, 31.13526], [121.3998, 31.13487], [121.3998, 31.13487], [121.39868, 31.13443], [121.39868, 31.13443], [121.39787, 31.13415], [121.39787, 31.13415], [121.39658, 31.13383], [121.39658, 31.13383], [121.39597, 31.13369], [121.39597, 31.13369], [121.39451, 31.13337], [121.39451, 31.13337], [121.39446, 31.13338], [121.39446, 31.13338], [121.3932, 31.13306], [121.3932, 31.13306], [121.39313, 31.13302], [121.39313, 31.13302], [121.39112, 31.1326], [121.39112, 31.1326], [121.38858, 31.13202], [121.38858, 31.13202], [121.38775, 31.13183], [121.38775, 31.13183], [121.38695, 31.13165], [121.38695, 31.13165], [121.38685, 31.13163], [121.38685, 31.13163], [121.38673, 31.1316], [121.38673, 31.1316], [121.38607, 31.13146], [121.38607, 31.13146], [121.38547, 31.13133], [121.38547, 31.13133], [121.38511, 31.13125], [121.38511, 31.13125], [121.38424, 31.13105], [121.38424, 31.13105], [121.38408, 31.13102], [121.38408, 31.13102], [121.38342, 31.13085], [121.38342, 31.13085], [121.38361, 31.13043], [121.38361, 31.13043], [121.38348, 31.13037], [121.38348, 31.13037], [121.38319, 31.13025], [121.38319, 31.13025], [121.38314, 31.13035], [121.38314, 31.13035], [121.38306, 31.13052], [121.38306, 31.13052], [121.38298, 31.13066]]}, "properties": {"id": 0, "type": "path_route", "name": "Path路线_上海师范大学徐汇校区西部数理学院", "origin_name": "上海师范大学徐汇校区西部数理学院", "origin_type": "学校", "origin_district": "徐汇区", "dest_name": "虹莘新村", "route_type": "Path算路", "start_coords": [121.416918, 31.162944], "original_coords": [121.416945, 31.162805]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.416971, 31.162687], [121.416869, 31.162649], [121.416569, 31.162612], [121.41622, 31.162569], [121.41622, 31.162569], [121.416193, 31.162805], [121.416193, 31.162805], [121.416644, 31.16288], [121.416907, 31.162945], [121.417379, 31.16303], [121.41762, 31.163089], [121.41762, 31.163089], [121.417733, 31.162907], [121.417786, 31.16288], [121.417883, 31.162869], [121.418559, 31.162928], [121.418634, 31.162918], [121.419069, 31.162687], [121.41917, 31.162671], [121.419213, 31.162671], [121.419305, 31.162735], [121.419305, 31.162735], [121.419503, 31.161582], [121.419777, 31.15993], [121.419959, 31.158835], [121.420034, 31.158353], [121.420056, 31.158251], [121.420211, 31.157333], [121.420243, 31.157226], [121.420297, 31.157086], [121.420463, 31.156754], [121.42056, 31.156604], [121.420823, 31.156276], [121.420898, 31.156185], [121.421193, 31.15582], [121.421225, 31.155778], [121.421805, 31.155064], [121.422405, 31.154308], [121.422813, 31.153798], [121.423612, 31.1528], [121.423848, 31.152505], [121.423896, 31.152446], [121.42351, 31.152098], [121.42314, 31.151727], [121.42314, 31.151727], [121.422985, 31.151663], [121.421783, 31.150542], [121.420989, 31.149737], [121.420898, 31.14963], [121.420839, 31.14956], [121.420753, 31.149362], [121.420458, 31.149077], [121.420168, 31.148793], [121.419428, 31.14809], [121.418452, 31.147162], [121.418049, 31.146776], [121.417486, 31.14624], [121.417223, 31.145993], [121.414863, 31.143804], [121.414568, 31.143525], [121.414144, 31.143128], [121.413854, 31.142865], [121.413699, 31.142726], [121.412465, 31.141562], [121.41209, 31.141208], [121.411784, 31.140907], [121.410979, 31.140146], [121.410346, 31.139556], [121.410212, 31.139443], [121.410078, 31.139325], [121.409638, 31.13888], [121.409182, 31.13844], [121.408624, 31.137903], [121.408366, 31.137651], [121.408055, 31.137351], [121.407948, 31.137265], [121.407846, 31.137174], [121.407846, 31.137174], [121.407621, 31.137072], [121.406848, 31.136364], [121.406183, 31.135715], [121.405727, 31.135253], [121.405432, 31.134958], [121.405073, 31.134604], [121.404858, 31.134411], [121.40466, 31.134229], [121.404579, 31.134132], [121.404579, 31.134132], [121.404322, 31.134009], [121.404193, 31.13395], [121.404193, 31.13395], [121.404059, 31.13424], [121.403619, 31.135168], [121.403608, 31.135189], [121.403576, 31.135253], [121.403061, 31.136348], [121.402965, 31.136283], [121.402702, 31.136106], [121.402364, 31.135908], [121.401704, 31.135623], [121.400776, 31.135259], [121.40076, 31.135253], [121.3998, 31.134872], [121.398925, 31.134535], [121.398684, 31.134438], [121.397874, 31.134148], [121.397187, 31.133982], [121.396586, 31.133832], [121.395969, 31.133698], [121.39451, 31.13337], [121.394467, 31.133381], [121.393207, 31.13307], [121.393137, 31.133022], [121.391125, 31.132603], [121.389741, 31.132287], [121.388588, 31.132024], [121.387756, 31.131836], [121.386957, 31.131654], [121.386861, 31.131638], [121.386737, 31.131605], [121.386077, 31.131461], [121.385471, 31.131332], [121.385117, 31.131251], [121.384243, 31.131058], [121.384082, 31.131021], [121.383427, 31.130855], [121.383427, 31.130855], [121.383615, 31.130431], [121.383615, 31.130431], [121.383481, 31.130377], [121.383197, 31.130259], [121.383197, 31.130259], [121.383148, 31.13035], [121.383138, 31.130398], [121.383068, 31.130522], [121.382988, 31.130667], [121.382923, 31.130731], [121.382923, 31.130731], [121.382841, 31.130699]]}, "properties": {"id": 0, "type": "amap_route", "name": "高德路线_上海师范大学徐汇校区西部数理学院", "origin_name": "上海师范大学徐汇校区西部数理学院", "origin_type": "学校", "origin_district": "徐汇区", "dest_name": "虹莘新村", "route_type": "高德算路", "start_coords": [121.416971, 31.162687], "original_coords": [121.416945, 31.162805]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.477126, 31.236125]}, "properties": {"id": 1, "type": "original", "name": "莱莱小笼", "poi_type": "餐厅", "district": "黄浦区", "dest_name": "上海今森青年社区公寓", "distance_to_path_start": 5.076408577207063, "distance_to_amap_start": 4.439601312929756, "path_to_amap_distance": 0.6739099663642211, "path_distance_category": "5-10m", "amap_distance_category": "1-5m", "comparison_distance_category": "0-1m", "path_start_coords": [121.477135, 31.23608], "amap_start_coords": [121.477136, 31.236086]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.477135, 31.23608]}, "properties": {"id": 1, "type": "path_start", "name": "Path起点_莱莱小笼", "original_poi_name": "莱莱小笼", "original_poi_type": "餐厅", "original_poi_district": "黄浦区", "dest_name": "上海今森青年社区公寓", "distance_from_original": 5.076408577207063, "distance_category": "5-10m", "distance_to_amap_start": 0.6739099663642211, "original_coords": [121.477126, 31.236125]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.477136, 31.236086]}, "properties": {"id": 1, "type": "amap_start", "name": "高德起点_莱莱小笼", "original_poi_name": "莱莱小笼", "original_poi_type": "餐厅", "original_poi_district": "黄浦区", "dest_name": "上海今森青年社区公寓", "distance_from_original": 4.439601312929756, "distance_category": "1-5m", "distance_to_path_start": 0.6739099663642211, "original_coords": [121.477126, 31.236125]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.477135, 31.23608], [121.47683, 31.23602], [121.47683, 31.23602], [121.47668, 31.23599], [121.47668, 31.23599], [121.47626, 31.23589], [121.47626, 31.23589], [121.47611, 31.23639], [121.47611, 31.23639], [121.47604, 31.23661], [121.47604, 31.23661], [121.47589, 31.23714], [121.47589, 31.23714], [121.47587, 31.23724], [121.47587, 31.23724], [121.47582, 31.23736], [121.47582, 31.23736], [121.4757, 31.23769], [121.4757, 31.23769], [121.47547, 31.23818], [121.47547, 31.23818], [121.47544, 31.23825], [121.47544, 31.23825], [121.47517, 31.23819], [121.47517, 31.23819], [121.47457, 31.23806], [121.47457, 31.23806], [121.47424, 31.23799], [121.47424, 31.23799], [121.47353, 31.2378], [121.47353, 31.2378], [121.47338, 31.23817], [121.47338, 31.23817], [121.4731, 31.23881], [121.4731, 31.23881], [121.47282, 31.23939], [121.4725, 31.23988], [121.4725, 31.23988], [121.4723, 31.24016], [121.4723, 31.24016], [121.47208, 31.24048], [121.47208, 31.24048], [121.47187, 31.24087], [121.47177, 31.24111], [121.47169, 31.24145], [121.47169, 31.24145], [121.47162, 31.2419], [121.47162, 31.2419], [121.47158, 31.24228], [121.47158, 31.24228], [121.47157, 31.24241], [121.47157, 31.24241], [121.47145, 31.24337], [121.47145, 31.24337], [121.47124, 31.24477], [121.47124, 31.24477], [121.4712, 31.24498], [121.4712, 31.24498], [121.47114, 31.24521], [121.47114, 31.24521], [121.47074, 31.24639], [121.47074, 31.24639], [121.47035, 31.24756], [121.47035, 31.24756], [121.47019, 31.24803], [121.47019, 31.24803], [121.47014, 31.24817], [121.47014, 31.24817], [121.46981, 31.24923], [121.46954, 31.25031], [121.46954, 31.25031], [121.46952, 31.2504], [121.46952, 31.2504], [121.46935, 31.25137], [121.46935, 31.25137], [121.46925, 31.25182], [121.46925, 31.25182], [121.46916, 31.2522], [121.46916, 31.2522], [121.46896, 31.25313], [121.46896, 31.25313], [121.46893, 31.25325], [121.46893, 31.25325], [121.46869, 31.25439], [121.46857, 31.25482], [121.46857, 31.25482], [121.46834, 31.25534], [121.46834, 31.25534], [121.46807, 31.25589], [121.46795, 31.25618], [121.46795, 31.25618], [121.46775, 31.25661], [121.46775, 31.25661], [121.46764, 31.25689], [121.46764, 31.25689], [121.46754, 31.25728], [121.46751, 31.25753], [121.46751, 31.25753], [121.46748, 31.25783], [121.46748, 31.25783], [121.46736, 31.2578], [121.46736, 31.2578], [121.46713, 31.2578], [121.46713, 31.2578], [121.4665, 31.2578], [121.4665, 31.2578], [121.46639, 31.2578], [121.46639, 31.2578], [121.46613, 31.25781], [121.46613, 31.25781], [121.46573, 31.25782], [121.46573, 31.25782], [121.46534, 31.25783], [121.46534, 31.25783], [121.46517, 31.25783], [121.46517, 31.25783], [121.4647, 31.25784], [121.4647, 31.25784], [121.46436, 31.25784], [121.46436, 31.25784], [121.46408, 31.25786], [121.46408, 31.25786], [121.46388, 31.25786], [121.46388, 31.25786], [121.46362, 31.25785], [121.46362, 31.25785], [121.46356, 31.25785], [121.46356, 31.25785], [121.46343, 31.2582], [121.46323, 31.25866], [121.46323, 31.25866], [121.46307, 31.25901], [121.46307, 31.25901], [121.46273, 31.2596], [121.46273, 31.2596], [121.46259, 31.25986], [121.46259, 31.25986], [121.46232, 31.26024], [121.46218, 31.26039], [121.46176, 31.26077], [121.46176, 31.26077], [121.46153, 31.26098], [121.46153, 31.26098], [121.46141, 31.26111], [121.46141, 31.26111], [121.46096, 31.26155], [121.46096, 31.26155], [121.46082, 31.26174], [121.46082, 31.26174], [121.46066, 31.26196], [121.46066, 31.26196], [121.46049, 31.26228], [121.46049, 31.26228], [121.46035, 31.26255], [121.46035, 31.26255], [121.46003, 31.26334], [121.46003, 31.26334], [121.45998, 31.26346], [121.45998, 31.26346], [121.45982, 31.26387], [121.45982, 31.26387], [121.45933, 31.26514], [121.45933, 31.26514], [121.45928, 31.26528], [121.45928, 31.26528], [121.45921, 31.26549], [121.45921, 31.26549], [121.45889, 31.2664], [121.45889, 31.2664], [121.45872, 31.26678], [121.45872, 31.26678], [121.45819, 31.26772], [121.45793, 31.26815], [121.45793, 31.26815], [121.45792, 31.26816], [121.45792, 31.26816], [121.45767, 31.26807], [121.45767, 31.26807], [121.4576, 31.26805], [121.4576, 31.26805], [121.45762, 31.268], [121.45762, 31.268], [121.45776, 31.26776], [121.45776, 31.26776], [121.457681, 31.267715]]}, "properties": {"id": 1, "type": "path_route", "name": "Path路线_莱莱小笼", "origin_name": "莱莱小笼", "origin_type": "餐厅", "origin_district": "黄浦区", "dest_name": "上海今森青年社区公寓", "route_type": "Path算路", "start_coords": [121.477135, 31.23608], "original_coords": [121.477126, 31.236125]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.477136, 31.236086], [121.476833, 31.236024], [121.476688, 31.235992], [121.476259, 31.235895], [121.476259, 31.235895], [121.476125, 31.236367], [121.47605, 31.236614], [121.475894, 31.237145], [121.475872, 31.237241], [121.475824, 31.237365], [121.475701, 31.237698], [121.47547, 31.238186], [121.475443, 31.238261], [121.475175, 31.238196], [121.474574, 31.238068], [121.474258, 31.237976], [121.473909, 31.237805], [121.473576, 31.237735], [121.473576, 31.237735], [121.473383, 31.238175], [121.473104, 31.238813], [121.472852, 31.23935], [121.472648, 31.239688], [121.472509, 31.239886], [121.472482, 31.239918], [121.472305, 31.24016], [121.472085, 31.240487], [121.471973, 31.240675], [121.471828, 31.240981], [121.471779, 31.241115], [121.471688, 31.241453], [121.471624, 31.241903], [121.471581, 31.242284], [121.471575, 31.242413], [121.471511, 31.242928], [121.471458, 31.243368], [121.471372, 31.243947], [121.471356, 31.244049], [121.471302, 31.244408], [121.471248, 31.244773], [121.471205, 31.244988], [121.471173, 31.245116], [121.471146, 31.245213], [121.470749, 31.246399], [121.470749, 31.246399], [121.470352, 31.247563], [121.470197, 31.248035], [121.470148, 31.248174], [121.469816, 31.249236], [121.469542, 31.25032], [121.469478, 31.250674], [121.469392, 31.251151], [121.469349, 31.251371], [121.469253, 31.251822], [121.469167, 31.252208], [121.469124, 31.252385], [121.468995, 31.252991], [121.468979, 31.253067], [121.468963, 31.253136], [121.468936, 31.253255], [121.46885, 31.253657], [121.468807, 31.25385], [121.468679, 31.254445], [121.468609, 31.254687], [121.468571, 31.254821], [121.468346, 31.255341], [121.468078, 31.255899], [121.467987, 31.256103], [121.467949, 31.256189], [121.467751, 31.256613], [121.467611, 31.25701], [121.467547, 31.257278], [121.467515, 31.257535], [121.467482, 31.257836], [121.467365, 31.257803], [121.467139, 31.257803], [121.466501, 31.257803], [121.466388, 31.257803], [121.466136, 31.257814], [121.465734, 31.25783], [121.465342, 31.25783], [121.465176, 31.25783], [121.464843, 31.257836], [121.464704, 31.257841], [121.46436, 31.257846], [121.464081, 31.257857], [121.463883, 31.257862], [121.463625, 31.257857], [121.463561, 31.257857], [121.463432, 31.2582], [121.463239, 31.258662], [121.463078, 31.25901], [121.462735, 31.259601], [121.462595, 31.259863], [121.462322, 31.260244], [121.462188, 31.260395], [121.461769, 31.26077], [121.46141, 31.261119], [121.461104, 31.261403], [121.460965, 31.261553], [121.460825, 31.261746], [121.46067, 31.261971], [121.460493, 31.262288], [121.460353, 31.262556], [121.460031, 31.263345], [121.459983, 31.263463], [121.459822, 31.263876], [121.459334, 31.265147], [121.459286, 31.265287], [121.459216, 31.265491], [121.458894, 31.266403], [121.458722, 31.266789], [121.458551, 31.267089], [121.45824, 31.267647], [121.45817, 31.267771], [121.457928, 31.268157], [121.457698, 31.268039], [121.45766, 31.267744], [121.457635, 31.267703]]}, "properties": {"id": 1, "type": "amap_route", "name": "高德路线_莱莱小笼", "origin_name": "莱莱小笼", "origin_type": "餐厅", "origin_district": "黄浦区", "dest_name": "上海今森青年社区公寓", "route_type": "高德算路", "start_coords": [121.477136, 31.236086], "original_coords": [121.477126, 31.236125]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.457532, 31.266527]}, "properties": {"id": 2, "type": "original", "name": "上海商学院高等技术学院共和新路院区", "poi_type": "学校", "district": "静安区", "dest_name": "明园森林都市", "distance_to_path_start": 14.02785263003822, "distance_to_amap_start": 14.301785381320649, "path_to_amap_distance": 1.5568792658459694, "path_distance_category": "10m+", "amap_distance_category": "10m+", "comparison_distance_category": "1-5m", "path_start_coords": [121.457483, 31.266646], "amap_start_coords": [121.457467, 31.266643]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.457483, 31.266646]}, "properties": {"id": 2, "type": "path_start", "name": "Path起点_上海商学院高等技术学院共和新路院区", "original_poi_name": "上海商学院高等技术学院共和新路院区", "original_poi_type": "学校", "original_poi_district": "静安区", "dest_name": "明园森林都市", "distance_from_original": 14.02785263003822, "distance_category": "10m+", "distance_to_amap_start": 1.5568792658459694, "original_coords": [121.457532, 31.266527]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.457467, 31.266643]}, "properties": {"id": 2, "type": "amap_start", "name": "高德起点_上海商学院高等技术学院共和新路院区", "original_poi_name": "上海商学院高等技术学院共和新路院区", "original_poi_type": "学校", "original_poi_district": "静安区", "dest_name": "明园森林都市", "distance_from_original": 14.301785381320649, "distance_category": "10m+", "distance_to_path_start": 1.5568792658459694, "original_coords": [121.457532, 31.266527]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.457483, 31.266646], [121.45737, 31.2666], [121.45737, 31.2666], [121.45729, 31.26656], [121.45729, 31.26656], [121.45752, 31.26614], [121.45757, 31.26611], [121.45786, 31.26613], [121.45791, 31.2661], [121.45794, 31.26605], [121.45794, 31.26605], [121.45837, 31.26622], [121.45837, 31.26622], [121.45852, 31.26627], [121.45852, 31.26627], [121.45898, 31.26523], [121.45898, 31.26523], [121.45835, 31.26503], [121.45791, 31.26486], [121.45791, 31.26486], [121.4573, 31.26463], [121.4573, 31.26463], [121.45681, 31.26442], [121.45681, 31.26442], [121.45661, 31.2643], [121.45644, 31.26417], [121.45644, 31.26417], [121.45626, 31.26401], [121.45626, 31.26401], [121.456, 31.26374], [121.456, 31.26374], [121.45609, 31.26368], [121.45609, 31.26368], [121.45649, 31.26408], [121.4567, 31.26423], [121.45686, 31.26433], [121.45734, 31.26455], [121.45734, 31.26455], [121.45787, 31.26474], [121.45787, 31.26474], [121.45863, 31.265], [121.45904, 31.26509], [121.45904, 31.26509], [121.45913, 31.2651], [121.45913, 31.2651], [121.45933, 31.26514], [121.45933, 31.26514], [121.45928, 31.26528], [121.45928, 31.26528], [121.45921, 31.26549], [121.45921, 31.26549], [121.45889, 31.2664], [121.45889, 31.2664], [121.45872, 31.26678], [121.45872, 31.26678], [121.45819, 31.26772], [121.45793, 31.26815], [121.45793, 31.26815], [121.45792, 31.26816], [121.45792, 31.26816], [121.45699, 31.26952], [121.45699, 31.26952], [121.45649, 31.27026], [121.45646, 31.27033], [121.45646, 31.27042], [121.45646, 31.27042], [121.45629, 31.27069], [121.45629, 31.27069], [121.4557, 31.27153], [121.4557, 31.27153], [121.4555, 31.27181], [121.4555, 31.27181], [121.45541, 31.27192], [121.45541, 31.27192], [121.45528, 31.27211], [121.45528, 31.27211], [121.455, 31.2725], [121.455, 31.2725], [121.4548, 31.27278], [121.4548, 31.27278], [121.4546, 31.27308], [121.4546, 31.27308], [121.45444, 31.27331], [121.45444, 31.27331], [121.45424, 31.27361], [121.45424, 31.27361], [121.45385, 31.27424], [121.45385, 31.27424], [121.45359, 31.27469], [121.45359, 31.27469], [121.45332, 31.27522], [121.45302, 31.27587], [121.45302, 31.27587], [121.45267, 31.2768], [121.45267, 31.2768], [121.45255, 31.2773], [121.45255, 31.2773], [121.45249, 31.27753], [121.45249, 31.27753], [121.45247, 31.27761], [121.45247, 31.27761], [121.45232, 31.27848], [121.45232, 31.27848], [121.45229, 31.27877], [121.45229, 31.27877], [121.45225, 31.27924], [121.45225, 31.27924], [121.45224, 31.27943], [121.45224, 31.27943], [121.45224, 31.27979], [121.45224, 31.27979], [121.45216, 31.27999], [121.45207, 31.2809], [121.45207, 31.2809], [121.45196, 31.28217], [121.45196, 31.28217], [121.45195, 31.28236], [121.45195, 31.28236], [121.45204, 31.28263], [121.45204, 31.28263], [121.45197, 31.28326], [121.45197, 31.28326], [121.45197, 31.28337], [121.45197, 31.28337], [121.45188, 31.28335], [121.45188, 31.28335], [121.45184, 31.28383], [121.45184, 31.28383], [121.45179, 31.28431], [121.45179, 31.28431], [121.45169, 31.28555], [121.45169, 31.28555], [121.45201, 31.28554], [121.45201, 31.28554], [121.45217, 31.28552], [121.45217, 31.28552], [121.4526, 31.28547], [121.4526, 31.28547], [121.45338, 31.28538], [121.45453, 31.28529], [121.45453, 31.28529], [121.45469, 31.28612], [121.45469, 31.28612], [121.45484, 31.28666], [121.45484, 31.28666], [121.45492, 31.28697], [121.45492, 31.28697], [121.45497, 31.28716], [121.45497, 31.28716], [121.45503, 31.28736], [121.45503, 31.28736], [121.45531, 31.28835], [121.45531, 31.28835], [121.45558, 31.28935], [121.45558, 31.28935], [121.455704, 31.289883]]}, "properties": {"id": 2, "type": "path_route", "name": "Path路线_上海商学院高等技术学院共和新路院区", "origin_name": "上海商学院高等技术学院共和新路院区", "origin_type": "学校", "origin_district": "静安区", "dest_name": "明园森林都市", "route_type": "Path算路", "start_coords": [121.457483, 31.266646], "original_coords": [121.457532, 31.266527]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.457467, 31.266643], [121.458057, 31.26688], [121.458223, 31.266945], [121.458223, 31.266945], [121.458384, 31.266612], [121.458529, 31.266279], [121.458985, 31.265233], [121.458985, 31.265233], [121.458476, 31.265072], [121.457987, 31.26489], [121.457918, 31.264863], [121.457826, 31.264831], [121.457306, 31.264638], [121.456813, 31.264423], [121.456609, 31.264305], [121.456443, 31.264171], [121.456265, 31.26401], [121.456008, 31.263747], [121.456088, 31.263688], [121.456496, 31.264091], [121.456705, 31.264241], [121.456872, 31.264337], [121.457349, 31.264552], [121.45788, 31.26474], [121.458631, 31.265003], [121.458797, 31.265051], [121.459039, 31.265094], [121.459286, 31.265287], [121.459216, 31.265491], [121.458894, 31.266403], [121.458722, 31.266789], [121.458551, 31.267089], [121.45824, 31.267647], [121.45817, 31.267771], [121.457928, 31.268157], [121.457923, 31.268168], [121.456995, 31.269525], [121.456496, 31.27026], [121.456469, 31.27034], [121.456464, 31.270431], [121.456292, 31.2707], [121.455933, 31.271209], [121.455729, 31.271493], [121.455691, 31.271547], [121.455504, 31.271815], [121.455418, 31.271928], [121.455289, 31.27211], [121.455005, 31.272507], [121.454806, 31.272786], [121.454603, 31.273087], [121.454447, 31.273312], [121.454243, 31.273612], [121.453851, 31.27424], [121.453594, 31.274696], [121.453326, 31.275222], [121.453025, 31.275876], [121.452682, 31.276804], [121.452548, 31.277303], [121.4525, 31.277539], [121.452473, 31.27762], [121.452387, 31.278076], [121.452366, 31.278226], [121.452301, 31.278773], [121.452253, 31.27924], [121.452248, 31.279433], [121.452242, 31.279798], [121.452242, 31.279798], [121.452162, 31.279996], [121.452076, 31.280903], [121.451968, 31.282217], [121.451958, 31.282362], [121.451893, 31.283274], [121.451888, 31.28336], [121.451845, 31.283831], [121.451797, 31.284314], [121.451695, 31.285559], [121.451695, 31.285559], [121.452011, 31.285543], [121.452172, 31.285521], [121.452601, 31.285473], [121.452795, 31.285452], [121.453648, 31.28536], [121.454533, 31.285296], [121.454533, 31.285296], [121.454699, 31.286128], [121.454838, 31.286664], [121.454914, 31.286927], [121.454924, 31.28698], [121.454962, 31.28712], [121.454978, 31.287168], [121.455032, 31.287367], [121.455058, 31.287458], [121.455123, 31.287678], [121.455311, 31.288348], [121.455493, 31.289014], [121.455504, 31.289062], [121.455584, 31.289357], [121.455706, 31.289865]]}, "properties": {"id": 2, "type": "amap_route", "name": "高德路线_上海商学院高等技术学院共和新路院区", "origin_name": "上海商学院高等技术学院共和新路院区", "origin_type": "学校", "origin_district": "静安区", "dest_name": "明园森林都市", "route_type": "高德算路", "start_coords": [121.457467, 31.266643], "original_coords": [121.457532, 31.266527]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.35534, 31.302831]}, "properties": {"id": 3, "type": "original", "name": "起垒公寓", "poi_type": "住宅小区", "district": "宝山区", "dest_name": "李子园公园", "distance_to_path_start": 13.054598279638453, "distance_to_amap_start": 13.425720314528721, "path_to_amap_distance": 0.39596790534888404, "path_distance_category": "10m+", "amap_distance_category": "10m+", "comparison_distance_category": "0-1m", "path_start_coords": [121.355477, 31.302822], "amap_start_coords": [121.355481, 31.302823]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.355477, 31.302822]}, "properties": {"id": 3, "type": "path_start", "name": "Path起点_起垒公寓", "original_poi_name": "起垒公寓", "original_poi_type": "住宅小区", "original_poi_district": "宝山区", "dest_name": "李子园公园", "distance_from_original": 13.054598279638453, "distance_category": "10m+", "distance_to_amap_start": 0.39596790534888404, "original_coords": [121.35534, 31.302831]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.355481, 31.302823]}, "properties": {"id": 3, "type": "amap_start", "name": "高德起点_起垒公寓", "original_poi_name": "起垒公寓", "original_poi_type": "住宅小区", "original_poi_district": "宝山区", "dest_name": "李子园公园", "distance_from_original": 13.425720314528721, "distance_category": "10m+", "distance_to_path_start": 0.39596790534888404, "original_coords": [121.35534, 31.302831]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.355477, 31.302822], [121.35549, 31.30301], [121.35549, 31.30301], [121.3555, 31.30312], [121.3555, 31.30312], [121.35561, 31.30312], [121.35561, 31.30312], [121.35584, 31.30311], [121.35584, 31.30311], [121.35581, 31.30253], [121.35581, 31.30253], [121.3558, 31.3023], [121.3558, 31.3023], [121.35579, 31.30203], [121.35579, 31.30203], [121.35576, 31.30137], [121.35576, 31.30137], [121.35575, 31.30114], [121.35575, 31.30114], [121.35571, 31.3002], [121.35571, 31.3002], [121.3557, 31.29992], [121.3557, 31.29992], [121.35568, 31.29959], [121.35568, 31.29959], [121.35564, 31.29868], [121.35564, 31.29868], [121.35562, 31.29838], [121.35562, 31.29838], [121.35562, 31.29814], [121.35562, 31.29814], [121.35561, 31.29785], [121.35561, 31.29785], [121.3556, 31.29736], [121.3556, 31.29736], [121.35559, 31.29729], [121.35559, 31.29729], [121.35558, 31.2971], [121.35558, 31.2971], [121.35554, 31.29681], [121.35554, 31.29681], [121.35545, 31.29632], [121.35545, 31.29632], [121.3554, 31.29605], [121.3554, 31.29605], [121.35532, 31.2956], [121.35532, 31.2956], [121.35528, 31.29538], [121.35528, 31.29538], [121.35525, 31.2952], [121.35525, 31.2952], [121.35515, 31.29464], [121.35515, 31.29464], [121.35508, 31.29422], [121.35508, 31.29422], [121.35506, 31.29416], [121.35506, 31.29416], [121.35496, 31.29365], [121.35491, 31.29315], [121.35491, 31.29315], [121.35496, 31.29194], [121.35496, 31.29194], [121.35497, 31.29154], [121.35494, 31.29121], [121.35494, 31.29121], [121.35486, 31.2908], [121.35486, 31.2908], [121.3548, 31.29046], [121.3548, 31.29046], [121.35464, 31.2897], [121.35464, 31.2897], [121.3546, 31.28954], [121.3546, 31.28954], [121.35445, 31.28884], [121.35445, 31.28884], [121.3545, 31.28875], [121.3545, 31.28875], [121.35602, 31.28825], [121.35614, 31.28824], [121.35614, 31.28824], [121.35688, 31.28802], [121.358, 31.28764], [121.35979, 31.28689], [121.36081, 31.28653], [121.36081, 31.28653], [121.36164, 31.28624], [121.36194, 31.28611], [121.36194, 31.28611], [121.3624, 31.28592], [121.3624, 31.28592], [121.36284, 31.28577], [121.36284, 31.28577], [121.3632, 31.28565], [121.3632, 31.28565], [121.36345, 31.28557], [121.36345, 31.28557], [121.36454, 31.28522], [121.36454, 31.28522], [121.3652, 31.28505], [121.3652, 31.28505], [121.3652, 31.28466], [121.3652, 31.28466], [121.3652, 31.28387], [121.3652, 31.28387], [121.3652, 31.28375], [121.3652, 31.28375], [121.36521, 31.2834], [121.36521, 31.2834], [121.36522, 31.28247], [121.36522, 31.28247], [121.36522, 31.28227], [121.36522, 31.28227], [121.36524, 31.28131], [121.36524, 31.28131], [121.36525, 31.28039], [121.36525, 31.28039], [121.36526, 31.27939], [121.36526, 31.27939], [121.36528, 31.27892], [121.36528, 31.27892], [121.36528, 31.27865], [121.36528, 31.27865], [121.36529, 31.27833], [121.36529, 31.27833], [121.36529, 31.27778], [121.36529, 31.27778], [121.36532, 31.27662], [121.36532, 31.27662], [121.36536, 31.27629], [121.36544, 31.27596], [121.36544, 31.27596], [121.36557, 31.27563], [121.3657, 31.2754], [121.3657, 31.2754], [121.36586, 31.27516], [121.36602, 31.27497], [121.36602, 31.27497], [121.36632, 31.27469], [121.36632, 31.27469], [121.36679, 31.27432], [121.36736, 31.27394], [121.36736, 31.27394], [121.36795, 31.27362], [121.36939, 31.2729], [121.36939, 31.2729], [121.37046, 31.27237], [121.37087, 31.27222], [121.37087, 31.27222], [121.37151, 31.27206], [121.37238, 31.27192], [121.37238, 31.27192], [121.37376, 31.2718], [121.37376, 31.2718], [121.3758, 31.27164], [121.3758, 31.27164], [121.3771, 31.27152], [121.3771, 31.27152], [121.37725, 31.2715], [121.37725, 31.2715], [121.37745, 31.27145], [121.37745, 31.27145], [121.37818, 31.27125], [121.37892, 31.27108], [121.37935, 31.27101], [121.37935, 31.27101], [121.37963, 31.27096], [121.37963, 31.27096], [121.38071, 31.27077], [121.38071, 31.27077], [121.38091, 31.27073], [121.38091, 31.27073], [121.38124, 31.27068], [121.38124, 31.27068], [121.38225, 31.27048], [121.38225, 31.27048], [121.38248, 31.27043], [121.38248, 31.27043], [121.38288, 31.27035], [121.38288, 31.27035], [121.38335, 31.27026], [121.38335, 31.27026], [121.38375, 31.27018], [121.38375, 31.27018], [121.38395, 31.27014], [121.38395, 31.27014], [121.38397, 31.27023], [121.38397, 31.27023], [121.38401, 31.27069], [121.38409, 31.27116], [121.38409, 31.27116], [121.38413, 31.27136], [121.38413, 31.27136], [121.38422, 31.27196], [121.38422, 31.27196], [121.38426, 31.27265], [121.38426, 31.27265], [121.38429, 31.27329], [121.38429, 31.27329], [121.38429, 31.27336], [121.38429, 31.27336], [121.3843, 31.27348], [121.3843, 31.27348], [121.38448, 31.27504], [121.38448, 31.27504], [121.38437, 31.27503], [121.38437, 31.27503], [121.38419, 31.27503], [121.38413, 31.27505], [121.38409, 31.27509], [121.3841, 31.27517], [121.38403, 31.27521], [121.383428, 31.275267]]}, "properties": {"id": 3, "type": "path_route", "name": "Path路线_起垒公寓", "origin_name": "起垒公寓", "origin_type": "住宅小区", "origin_district": "宝山区", "dest_name": "李子园公园", "route_type": "Path算路", "start_coords": [121.355477, 31.302822], "original_coords": [121.35534, 31.302831]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.355481, 31.302823], [121.355495, 31.303015], [121.3555, 31.303127], [121.3555, 31.303127], [121.355618, 31.303122], [121.355843, 31.303111], [121.355843, 31.303111], [121.355817, 31.302537], [121.355806, 31.302307], [121.35579, 31.302038], [121.355768, 31.301373], [121.355758, 31.301143], [121.355715, 31.300204], [121.35571, 31.300048], [121.355704, 31.29992], [121.355688, 31.299598], [121.35564, 31.29868], [121.355624, 31.29838], [121.355618, 31.298144], [121.355613, 31.297849], [121.355602, 31.297361], [121.355581, 31.297098], [121.355543, 31.296819], [121.355452, 31.296325], [121.355404, 31.296052], [121.355323, 31.295606], [121.35528, 31.295386], [121.355253, 31.295204], [121.355157, 31.294641], [121.355082, 31.294228], [121.355071, 31.294163], [121.35498, 31.293708], [121.354969, 31.293654], [121.354915, 31.29316], [121.354969, 31.291943], [121.354969, 31.291546], [121.354937, 31.291213], [121.354867, 31.290805], [121.354867, 31.290805], [121.354803, 31.290462], [121.354642, 31.289706], [121.354604, 31.289545], [121.354502, 31.289062], [121.354454, 31.288842], [121.354502, 31.288756], [121.35498, 31.28859], [121.355291, 31.288488], [121.356021, 31.288257], [121.356144, 31.288246], [121.356884, 31.288026], [121.357592, 31.28778], [121.358, 31.287646], [121.359379, 31.287066], [121.360232, 31.286734], [121.360816, 31.28653], [121.361648, 31.28624], [121.361948, 31.286117], [121.362404, 31.285918], [121.36285, 31.285773], [121.363456, 31.285575], [121.364545, 31.285226], [121.36499, 31.285103], [121.36521, 31.285055], [121.36521, 31.285055], [121.365205, 31.284663], [121.36521, 31.283869], [121.36521, 31.283756], [121.36521, 31.283402], [121.365221, 31.28248], [121.365221, 31.282271], [121.365242, 31.281316], [121.365258, 31.280586], [121.365258, 31.280388], [121.365263, 31.280007], [121.365263, 31.27998], [121.365269, 31.279476], [121.365269, 31.279395], [121.365387, 31.279347], [121.368246, 31.278923], [121.369888, 31.278682], [121.371154, 31.278499], [121.371154, 31.278499], [121.374227, 31.278076], [121.374775, 31.278], [121.375906, 31.277829], [121.376352, 31.277759], [121.376556, 31.277727], [121.376738, 31.2777], [121.376953, 31.277668], [121.378116, 31.277507], [121.378218, 31.277496], [121.379045, 31.277389], [121.379635, 31.277308], [121.37993, 31.277244], [121.380536, 31.277072], [121.381067, 31.276928], [121.381587, 31.276863], [121.383508, 31.276885], [121.384634, 31.276895], [121.384736, 31.276928], [121.384736, 31.276928], [121.384726, 31.276847], [121.384602, 31.275908], [121.384602, 31.275908], [121.384457, 31.27593], [121.384211, 31.275967], [121.384007, 31.275967], [121.383948, 31.275951], [121.383921, 31.275908], [121.383958, 31.275839]]}, "properties": {"id": 3, "type": "amap_route", "name": "高德路线_起垒公寓", "origin_name": "起垒公寓", "origin_type": "住宅小区", "origin_district": "宝山区", "dest_name": "李子园公园", "route_type": "高德算路", "start_coords": [121.355481, 31.302823], "original_coords": [121.35534, 31.302831]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.399219, 31.624747]}, "properties": {"id": 4, "type": "original", "name": "管弄小区", "poi_type": "住宅小区", "district": "崇明区", "dest_name": "新崇北路397号小区", "distance_to_path_start": 20.762784353855768, "distance_to_amap_start": 20.198621253993583, "path_to_amap_distance": 0.7783644865219485, "path_distance_category": "10m+", "amap_distance_category": "10m+", "comparison_distance_category": "0-1m", "path_start_coords": [121.39907, 31.62461], "amap_start_coords": [121.39907, 31.624617]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.39907, 31.62461]}, "properties": {"id": 4, "type": "path_start", "name": "Path起点_管弄小区", "original_poi_name": "管弄小区", "original_poi_type": "住宅小区", "original_poi_district": "崇明区", "dest_name": "新崇北路397号小区", "distance_from_original": 20.762784353855768, "distance_category": "10m+", "distance_to_amap_start": 0.7783644865219485, "original_coords": [121.399219, 31.624747]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.39907, 31.624617]}, "properties": {"id": 4, "type": "amap_start", "name": "高德起点_管弄小区", "original_poi_name": "管弄小区", "original_poi_type": "住宅小区", "original_poi_district": "崇明区", "dest_name": "新崇北路397号小区", "distance_from_original": 20.198621253993583, "distance_category": "10m+", "distance_to_path_start": 0.7783644865219485, "original_coords": [121.399219, 31.624747]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.39907, 31.62461], [121.39896, 31.62408], [121.39896, 31.62408], [121.39895, 31.62399], [121.39895, 31.62399], [121.39855, 31.62405], [121.39855, 31.62405], [121.39865, 31.62455], [121.39865, 31.62455], [121.39873, 31.62496], [121.39873, 31.62496], [121.39853, 31.62498], [121.39853, 31.62498], [121.398355, 31.625002]]}, "properties": {"id": 4, "type": "path_route", "name": "Path路线_管弄小区", "origin_name": "管弄小区", "origin_type": "住宅小区", "origin_district": "崇明区", "dest_name": "新崇北路397号小区", "route_type": "Path算路", "start_coords": [121.39907, 31.62461], "original_coords": [121.399219, 31.624747]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.39907, 31.624617], [121.398968, 31.624091], [121.398952, 31.623995], [121.398952, 31.623995], [121.398925, 31.624], [121.39855, 31.624059], [121.39855, 31.624059], [121.398651, 31.624553], [121.398737, 31.62496], [121.398737, 31.62496], [121.398539, 31.624987], [121.398361, 31.625009]]}, "properties": {"id": 4, "type": "amap_route", "name": "高德路线_管弄小区", "origin_name": "管弄小区", "origin_type": "住宅小区", "origin_district": "崇明区", "dest_name": "新崇北路397号小区", "route_type": "高德算路", "start_coords": [121.39907, 31.624617], "original_coords": [121.399219, 31.624747]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.526505, 31.191767]}, "properties": {"id": 5, "type": "original", "name": "泽青公寓", "poi_type": "住宅小区", "district": "浦东新区", "dest_name": "上海市黄浦区顺昌医院", "distance_to_path_start": 7.708576477412469, "distance_to_amap_start": 7.142437612849702, "path_to_amap_distance": 1.1119492664034916, "path_distance_category": "5-10m", "amap_distance_category": "5-10m", "comparison_distance_category": "1-5m", "path_start_coords": [121.526438, 31.191728], "amap_start_coords": [121.526438, 31.191738]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.526438, 31.191728]}, "properties": {"id": 5, "type": "path_start", "name": "Path起点_泽青公寓", "original_poi_name": "泽青公寓", "original_poi_type": "住宅小区", "original_poi_district": "浦东新区", "dest_name": "上海市黄浦区顺昌医院", "distance_from_original": 7.708576477412469, "distance_category": "5-10m", "distance_to_amap_start": 1.1119492664034916, "original_coords": [121.526505, 31.191767]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.526438, 31.191738]}, "properties": {"id": 5, "type": "amap_start", "name": "高德起点_泽青公寓", "original_poi_name": "泽青公寓", "original_poi_type": "住宅小区", "original_poi_district": "浦东新区", "dest_name": "上海市黄浦区顺昌医院", "distance_from_original": 7.142437612849702, "distance_category": "5-10m", "distance_to_path_start": 1.1119492664034916, "original_coords": [121.526505, 31.191767]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.526438, 31.191728], [121.52642, 31.19176], [121.52642, 31.19176], [121.52638, 31.19183], [121.52638, 31.19183], [121.52624, 31.19177], [121.52624, 31.19177], [121.52583, 31.19165], [121.52555, 31.19167], [121.52555, 31.19167], [121.52566, 31.19194], [121.52566, 31.19194], [121.52575, 31.19216], [121.52575, 31.19216], [121.52588, 31.19247], [121.52588, 31.19247], [121.52591, 31.19256], [121.52591, 31.19256], [121.52626, 31.19346], [121.52626, 31.19346], [121.52678, 31.1948], [121.52678, 31.1948], [121.5269, 31.19509], [121.5269, 31.19509], [121.52696, 31.19547], [121.52696, 31.19547], [121.52704, 31.19562], [121.52704, 31.19562], [121.52744, 31.19661], [121.52744, 31.19661], [121.5275, 31.19678], [121.52767, 31.1971], [121.52767, 31.1971], [121.52775, 31.19732], [121.52791, 31.19759], [121.5285, 31.19835], [121.5285, 31.19835], [121.52867, 31.19849], [121.52889, 31.19875], [121.52904, 31.199], [121.52911, 31.1992], [121.52917, 31.19949], [121.52919, 31.19978], [121.52917, 31.19996], [121.52913, 31.20007], [121.52906, 31.20018], [121.52888, 31.2003], [121.52871, 31.20034], [121.52856, 31.20034], [121.52838, 31.2003], [121.52826, 31.20025], [121.52813, 31.20017], [121.52775, 31.19985], [121.52752, 31.1997], [121.52752, 31.1997], [121.52724, 31.19961], [121.52712, 31.19959], [121.52696, 31.1996], [121.52678, 31.19962], [121.52605, 31.1998], [121.52605, 31.1998], [121.52563, 31.1999], [121.52563, 31.1999], [121.52482, 31.20007], [121.52482, 31.20007], [121.52398, 31.2003], [121.52133, 31.20089], [121.52062, 31.20098], [121.5203, 31.201], [121.5203, 31.201], [121.51669, 31.20184], [121.51669, 31.20184], [121.51437, 31.20233], [121.51437, 31.20233], [121.51358, 31.20252], [121.51297, 31.20271], [121.51297, 31.20271], [121.51179, 31.20315], [121.51179, 31.20315], [121.50942, 31.20402], [121.50942, 31.20402], [121.50936, 31.20404], [121.50936, 31.20404], [121.50814, 31.20446], [121.50523, 31.20553], [121.50523, 31.20553], [121.50442, 31.20582], [121.50442, 31.20582], [121.50127, 31.207], [121.50127, 31.207], [121.50098, 31.20715], [121.50085, 31.20726], [121.50085, 31.20726], [121.50065, 31.2075], [121.50054, 31.20776], [121.50051, 31.20806], [121.50056, 31.2083], [121.5007, 31.20857], [121.50085, 31.20873], [121.50096, 31.20881], [121.50128, 31.20897], [121.50154, 31.20904], [121.50199, 31.20903], [121.50223, 31.20896], [121.50242, 31.20887], [121.50253, 31.2088], [121.50268, 31.20867], [121.5028, 31.20853], [121.50292, 31.20826], [121.50293, 31.20796], [121.5029, 31.20785], [121.50283, 31.2077], [121.50273, 31.20756], [121.50266, 31.2075], [121.50266, 31.2075], [121.50249, 31.20737], [121.50234, 31.20728], [121.50224, 31.20724], [121.502, 31.2072], [121.50182, 31.2072], [121.50163, 31.20724], [121.50135, 31.20736], [121.50119, 31.20747], [121.50106, 31.20762], [121.50093, 31.2079], [121.50089, 31.20806], [121.50091, 31.20848], [121.50091, 31.20848], [121.50097, 31.20991], [121.50097, 31.2102], [121.50094, 31.21036], [121.50085, 31.21059], [121.50077, 31.21073], [121.50067, 31.21085], [121.50052, 31.211], [121.5003, 31.21114], [121.49998, 31.21129], [121.49955, 31.21136], [121.49868, 31.21147], [121.49836, 31.21158], [121.49806, 31.21176], [121.4979, 31.21192], [121.49781, 31.21205], [121.49781, 31.21205], [121.4976, 31.21236], [121.4976, 31.21236], [121.49738, 31.21269], [121.49738, 31.21269], [121.49708, 31.21295], [121.49686, 31.21305], [121.49686, 31.21305], [121.49668, 31.21311], [121.49643, 31.21314], [121.4961, 31.21311], [121.4961, 31.21311], [121.49573, 31.21304], [121.49573, 31.21304], [121.49529, 31.21294], [121.49529, 31.21294], [121.4949, 31.21286], [121.49429, 31.21278], [121.49321, 31.21273], [121.49321, 31.21273], [121.49311, 31.21272], [121.49311, 31.21272], [121.49297, 31.21272], [121.49297, 31.21272], [121.4924, 31.21268], [121.4917, 31.21259], [121.4917, 31.21259], [121.49079, 31.21247], [121.49079, 31.21247], [121.49025, 31.2124], [121.49025, 31.2124], [121.48962, 31.21232], [121.48962, 31.21232], [121.48964, 31.21219], [121.48964, 31.21219], [121.48982, 31.21187], [121.48982, 31.21187], [121.48991, 31.21169], [121.48991, 31.21169], [121.4902, 31.21114], [121.4902, 31.21114], [121.48982, 31.21103], [121.48982, 31.21103], [121.48972, 31.21099], [121.48972, 31.21099], [121.48896, 31.21075], [121.48896, 31.21075], [121.48846, 31.21059], [121.48846, 31.21059], [121.48807, 31.21048], [121.48807, 31.21048], [121.48801, 31.21046], [121.48801, 31.21046], [121.48768, 31.21036], [121.48768, 31.21036], [121.48736, 31.21027], [121.48736, 31.21027], [121.48721, 31.21022], [121.48721, 31.21022], [121.48706, 31.21017], [121.48706, 31.21017], [121.48683, 31.2101], [121.48683, 31.2101], [121.48671, 31.21006], [121.48671, 31.21006], [121.48583, 31.20975], [121.48583, 31.20975], [121.48575, 31.20973], [121.48575, 31.20973], [121.48516, 31.20948], [121.48516, 31.20948], [121.48417, 31.20909], [121.48417, 31.20909], [121.48364, 31.2089], [121.48335, 31.20877], [121.48335, 31.20877], [121.48323, 31.20868], [121.48323, 31.20868], [121.48301, 31.20854], [121.48301, 31.20854], [121.48282, 31.20841], [121.48282, 31.20841], [121.48259, 31.20828], [121.48259, 31.20828], [121.48236, 31.20813], [121.48236, 31.20813], [121.48207, 31.20796], [121.48207, 31.20796], [121.48203, 31.20793], [121.48203, 31.20793], [121.48198, 31.2079], [121.48198, 31.2079], [121.48207, 31.20779], [121.48207, 31.20779], [121.48245, 31.20728]]}, "properties": {"id": 5, "type": "path_route", "name": "Path路线_泽青公寓", "origin_name": "泽青公寓", "origin_type": "住宅小区", "origin_district": "浦东新区", "dest_name": "上海市黄浦区顺昌医院", "route_type": "Path算路", "start_coords": [121.526438, 31.191728], "original_coords": [121.526505, 31.191767]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.526438, 31.191738], [121.526421, 31.191767], [121.526384, 31.191832], [121.526384, 31.191832], [121.526244, 31.191778], [121.525831, 31.19166], [121.525761, 31.19165], [121.525558, 31.191671], [121.525558, 31.191671], [121.525665, 31.191939], [121.525756, 31.192159], [121.52588, 31.192476], [121.525917, 31.192561], [121.526266, 31.193468], [121.526781, 31.194804], [121.526904, 31.195099], [121.526904, 31.195099], [121.526963, 31.195469], [121.527044, 31.19563], [121.527441, 31.196617], [121.527553, 31.196869], [121.527676, 31.197105], [121.527757, 31.197325], [121.527838, 31.197481], [121.527961, 31.197668], [121.528331, 31.198146], [121.528508, 31.19835], [121.528508, 31.19835], [121.528578, 31.198398], [121.528674, 31.198495], [121.528814, 31.198645], [121.5289, 31.198757], [121.528975, 31.198881], [121.529045, 31.199004], [121.529114, 31.199203], [121.529136, 31.199283], [121.529179, 31.199498], [121.529189, 31.19961], [121.529195, 31.199782], [121.529173, 31.199964], [121.52913, 31.200077], [121.529066, 31.200184], [121.528953, 31.200265], [121.528889, 31.200302], [121.528808, 31.200329], [121.528717, 31.200351], [121.528562, 31.200345], [121.528385, 31.200308], [121.528261, 31.200259], [121.528138, 31.200179], [121.527752, 31.199857], [121.527612, 31.19975], [121.527526, 31.199701], [121.527376, 31.199648], [121.527242, 31.19961], [121.527124, 31.199594], [121.526963, 31.1996], [121.526786, 31.199626], [121.526228, 31.19976], [121.526056, 31.199809], [121.525638, 31.1999], [121.525638, 31.1999], [121.524823, 31.200077], [121.523986, 31.200302], [121.523423, 31.200431], [121.521336, 31.200898], [121.520622, 31.200989], [121.520306, 31.201], [121.519839, 31.201101], [121.519539, 31.201171], [121.517098, 31.201745], [121.51669, 31.201842], [121.515473, 31.20211], [121.514373, 31.202335], [121.513965, 31.202437], [121.513327, 31.202598], [121.512973, 31.202711], [121.51241, 31.20292], [121.511793, 31.203151], [121.511229, 31.20336], [121.509421, 31.20402], [121.509368, 31.204041], [121.508788, 31.20424], [121.50873, 31.204261], [121.506648, 31.205012], [121.505237, 31.205533], [121.504427, 31.205822], [121.503767, 31.206069], [121.50219, 31.206665], [121.501273, 31.207008], [121.500989, 31.207158], [121.50086, 31.207265], [121.500688, 31.207448], [121.50057, 31.207678], [121.500522, 31.207877], [121.500516, 31.20807], [121.500565, 31.208306], [121.500651, 31.208488], [121.50071, 31.208574], [121.50086, 31.208735], [121.501058, 31.208875], [121.501278, 31.208976], [121.501546, 31.209041], [121.501697, 31.209051], [121.501847, 31.209046], [121.502131, 31.208998], [121.502421, 31.20888], [121.502614, 31.20874], [121.502802, 31.208531], [121.502893, 31.20836], [121.502925, 31.208268], [121.502941, 31.208134], [121.502936, 31.207968], [121.502909, 31.207855], [121.502839, 31.20771], [121.502786, 31.20763], [121.502662, 31.207501], [121.502496, 31.207378], [121.50234, 31.207287], [121.502244, 31.207249], [121.502008, 31.207206], [121.50182, 31.207206], [121.501638, 31.207238], [121.501493, 31.207292], [121.501353, 31.207367], [121.501192, 31.207475], [121.501064, 31.20763], [121.500989, 31.207764], [121.50093, 31.207904], [121.500892, 31.20807], [121.500908, 31.208483], [121.500908, 31.208483], [121.500978, 31.209915], [121.500978, 31.210146], [121.500962, 31.21028], [121.500913, 31.210452], [121.500855, 31.210596], [121.500774, 31.210731], [121.500678, 31.21086], [121.500522, 31.211004], [121.500307, 31.211149], [121.50012, 31.21124], [121.499985, 31.211289], [121.499803, 31.211331], [121.499556, 31.211369], [121.498789, 31.21146], [121.498682, 31.211476], [121.498532, 31.211519], [121.49836, 31.211584], [121.498167, 31.211691], [121.49807, 31.211761], [121.498022, 31.211803], [121.497904, 31.211921], [121.497808, 31.21205], [121.497738, 31.212158], [121.497609, 31.212367], [121.497389, 31.2127], [121.497083, 31.212951], [121.496863, 31.213059], [121.496686, 31.213113], [121.496429, 31.213145], [121.496101, 31.213113], [121.495731, 31.213043], [121.494862, 31.21286], [121.494288, 31.212785], [121.493221, 31.212731], [121.492974, 31.212721], [121.492406, 31.212678], [121.491703, 31.212592], [121.490791, 31.212474], [121.490249, 31.212404], [121.489627, 31.212329], [121.488838, 31.212227], [121.488371, 31.212158], [121.487615, 31.212056], [121.486821, 31.211959], [121.486816, 31.211959], [121.486086, 31.211879], [121.486006, 31.211755], [121.486076, 31.211584], [121.486204, 31.211353], [121.486253, 31.211267], [121.486344, 31.211095], [121.486553, 31.21065], [121.486714, 31.210065], [121.486714, 31.210065], [121.48584, 31.20976], [121.485754, 31.209733], [121.485163, 31.209481], [121.484176, 31.209095], [121.483645, 31.208901], [121.483356, 31.208773], [121.483232, 31.208687], [121.483012, 31.208547], [121.482819, 31.208419], [121.482594, 31.20829], [121.482363, 31.20814], [121.482079, 31.207963], [121.482036, 31.207936], [121.481988, 31.207904], [121.481988, 31.207904], [121.482074, 31.207796], [121.482449, 31.207281]]}, "properties": {"id": 5, "type": "amap_route", "name": "高德路线_泽青公寓", "origin_name": "泽青公寓", "origin_type": "住宅小区", "origin_district": "浦东新区", "dest_name": "上海市黄浦区顺昌医院", "route_type": "高德算路", "start_coords": [121.526438, 31.191738], "original_coords": [121.526505, 31.191767]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.412792, 31.233833]}, "properties": {"id": 6, "type": "original", "name": "天梭 TISSOT(环球港直营店)", "poi_type": "购物中心", "district": "普陀区", "dest_name": "Goose Island精酿啤酒吧(丰盛里店)", "distance_to_path_start": 46.85746879656834, "distance_to_amap_start": 6.057762557175314, "path_to_amap_distance": 49.20036207530586, "path_distance_category": "10m+", "amap_distance_category": "5-10m", "comparison_distance_category": "10m+", "path_start_coords": [121.412397, 31.234085], "amap_start_coords": [121.412773, 31.233781]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.412397, 31.234085]}, "properties": {"id": 6, "type": "path_start", "name": "Path起点_天梭 TISSOT(环球港直营店)", "original_poi_name": "天梭 TISSOT(环球港直营店)", "original_poi_type": "购物中心", "original_poi_district": "普陀区", "dest_name": "Goose Island精酿啤酒吧(丰盛里店)", "distance_from_original": 46.85746879656834, "distance_category": "10m+", "distance_to_amap_start": 49.20036207530586, "original_coords": [121.412792, 31.233833]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.412773, 31.233781]}, "properties": {"id": 6, "type": "amap_start", "name": "高德起点_天梭 TISSOT(环球港直营店)", "original_poi_name": "天梭 TISSOT(环球港直营店)", "original_poi_type": "购物中心", "original_poi_district": "普陀区", "dest_name": "Goose Island精酿啤酒吧(丰盛里店)", "distance_from_original": 6.057762557175314, "distance_category": "5-10m", "distance_to_path_start": 49.20036207530586, "original_coords": [121.412792, 31.233833]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.412397, 31.234085], [121.41198, 31.23343], [121.41198, 31.23343], [121.41189, 31.2333], [121.41189, 31.2333], [121.41174, 31.23338], [121.41174, 31.23338], [121.41195, 31.2337], [121.41195, 31.2337], [121.41214, 31.23395], [121.41214, 31.23395], [121.41247, 31.23443], [121.41247, 31.23443], [121.41283, 31.23495], [121.41283, 31.23495], [121.41325, 31.23562], [121.41325, 31.23562], [121.41335, 31.23576], [121.41335, 31.23576], [121.41342, 31.23587], [121.41342, 31.23587], [121.41349, 31.23597], [121.41349, 31.23597], [121.41361, 31.23612], [121.41361, 31.23612], [121.41377, 31.23634], [121.41385, 31.2366], [121.41385, 31.2366], [121.41394, 31.23673], [121.41394, 31.23673], [121.41415, 31.23698], [121.41415, 31.23698], [121.41449, 31.23747], [121.41449, 31.23747], [121.4152, 31.23839], [121.4152, 31.23839], [121.41572, 31.23896], [121.41572, 31.23896], [121.41584, 31.23909], [121.41584, 31.23909], [121.41598, 31.23923], [121.41598, 31.23923], [121.41644, 31.23966], [121.41644, 31.23966], [121.4167, 31.23988], [121.4167, 31.23988], [121.41696, 31.24009], [121.41696, 31.24009], [121.41717, 31.24024], [121.41717, 31.24024], [121.41752, 31.24037], [121.41774, 31.24047], [121.41774, 31.24047], [121.41841, 31.24086], [121.41841, 31.24086], [121.41889, 31.24115], [121.41929, 31.24137], [121.41934, 31.24151], [121.41934, 31.24151], [121.4202, 31.24082], [121.4202, 31.24082], [121.42155, 31.23987], [121.42155, 31.23987], [121.42237, 31.2392], [121.42252, 31.23913], [121.42252, 31.23913], [121.42288, 31.23883], [121.42288, 31.23883], [121.42428, 31.2377], [121.42428, 31.2377], [121.42437, 31.23763], [121.42437, 31.23763], [121.42598, 31.23637], [121.42598, 31.23637], [121.42836, 31.23454], [121.42836, 31.23454], [121.42971, 31.23351], [121.42971, 31.23351], [121.42977, 31.23347], [121.42977, 31.23347], [121.42984, 31.23343], [121.42984, 31.23343], [121.43045, 31.23314], [121.43045, 31.23314], [121.43092, 31.23292], [121.43092, 31.23292], [121.43099, 31.23289], [121.43099, 31.23289], [121.43124, 31.23274], [121.43175, 31.23233], [121.43175, 31.23233], [121.43256, 31.23147], [121.43256, 31.23147], [121.43352, 31.23043], [121.43352, 31.23043], [121.43425, 31.22937], [121.43425, 31.22937], [121.43456, 31.22889], [121.43497, 31.22837], [121.43497, 31.22837], [121.43508, 31.22826], [121.43585, 31.22761], [121.43585, 31.22761], [121.43625, 31.22714], [121.43643, 31.22699], [121.43643, 31.22699], [121.43671, 31.22677], [121.43671, 31.22677], [121.43691, 31.22659], [121.43691, 31.22659], [121.43717, 31.22635], [121.43717, 31.22635], [121.43778, 31.22578], [121.43778, 31.22578], [121.43809, 31.22549], [121.43809, 31.22549], [121.43837, 31.22547], [121.43837, 31.22547], [121.4386, 31.22546], [121.4386, 31.22546], [121.43912, 31.22544], [121.43912, 31.22544], [121.43985, 31.22548], [121.43985, 31.22548], [121.43998, 31.22548], [121.43998, 31.22548], [121.44023, 31.22546], [121.44023, 31.22546], [121.44048, 31.22542], [121.44048, 31.22542], [121.44072, 31.22537], [121.44072, 31.22537], [121.44094, 31.22528], [121.44094, 31.22528], [121.44143, 31.22513], [121.44143, 31.22513], [121.44186, 31.225], [121.44186, 31.225], [121.44205, 31.22495], [121.44205, 31.22495], [121.44225, 31.22489], [121.44225, 31.22489], [121.44243, 31.22483], [121.44243, 31.22483], [121.44276, 31.22468], [121.44276, 31.22468], [121.44283, 31.22464], [121.44283, 31.22464], [121.4429, 31.22474], [121.4429, 31.22474], [121.44337, 31.22512], [121.44337, 31.22512], [121.44344, 31.22517], [121.44344, 31.22517], [121.44355, 31.22525], [121.44355, 31.22525], [121.44386, 31.22549], [121.44386, 31.22549], [121.4444, 31.2259], [121.44458, 31.22601], [121.44458, 31.22601], [121.4454, 31.22641], [121.4454, 31.22641], [121.44557, 31.22649], [121.44557, 31.22649], [121.44591, 31.22666], [121.44591, 31.22666], [121.44627, 31.22683], [121.44627, 31.22683], [121.44694, 31.22712], [121.44694, 31.22712], [121.44705, 31.22717], [121.44705, 31.22717], [121.44757, 31.22739], [121.44757, 31.22739], [121.44809, 31.2276], [121.44809, 31.2276], [121.44861, 31.22782], [121.44861, 31.22782], [121.4492, 31.22808], [121.4492, 31.22808], [121.44993, 31.22838], [121.44993, 31.22838], [121.45027, 31.22853], [121.45027, 31.22853], [121.45037, 31.22858], [121.45037, 31.22858], [121.45045, 31.22861], [121.45045, 31.22861], [121.45074, 31.22874], [121.45074, 31.22874], [121.45084, 31.22878], [121.45084, 31.22878], [121.45137, 31.22901], [121.45137, 31.22901], [121.45213, 31.22925], [121.45213, 31.22925], [121.45277, 31.22944], [121.45277, 31.22944], [121.45324, 31.22958], [121.45324, 31.22958], [121.45351, 31.22925], [121.45351, 31.22925], [121.45356, 31.22918], [121.45356, 31.22918], [121.45362, 31.22912], [121.45362, 31.22912], [121.45381, 31.22893], [121.45395, 31.22883], [121.45395, 31.22883], [121.45421, 31.22892], [121.45446, 31.22905], [121.45452, 31.22907], [121.45465, 31.22908], [121.45465, 31.22908], [121.45498, 31.22904], [121.45498, 31.22904], [121.45519, 31.22899], [121.45519, 31.22899], [121.45535, 31.22898], [121.45588, 31.2292], [121.45588, 31.2292], [121.45592, 31.22909], [121.45628, 31.22855], [121.45628, 31.22855], [121.45653, 31.22816], [121.45653, 31.22816], [121.45656, 31.22812], [121.45656, 31.22812], [121.45661, 31.22816], [121.45661, 31.22816], [121.45688, 31.22835], [121.45688, 31.22835], [121.45759, 31.22875], [121.45759, 31.22875], [121.45772, 31.22882], [121.45772, 31.22882], [121.45821, 31.22911], [121.45821, 31.22911], [121.45852, 31.22939], [121.45852, 31.22939], [121.45876, 31.22907], [121.45876, 31.22907], [121.45892, 31.22883], [121.45892, 31.22883], [121.45897, 31.22876], [121.45897, 31.22876], [121.45837, 31.22848], [121.45837, 31.22848], [121.45833, 31.22845], [121.45835, 31.22839], [121.45852, 31.22813], [121.45852, 31.22813], [121.45859, 31.22807], [121.45859, 31.22807], [121.45875, 31.22787], [121.45875, 31.22787], [121.45878, 31.22784], [121.45888, 31.22781], [121.45904, 31.22748], [121.45904, 31.22748], [121.45912, 31.22734], [121.45912, 31.22734], [121.459406, 31.227441]]}, "properties": {"id": 6, "type": "path_route", "name": "Path路线_天梭 TISSOT(环球港直营店)", "origin_name": "天梭 TISSOT(环球港直营店)", "origin_type": "购物中心", "origin_district": "普陀区", "dest_name": "Goose Island精酿啤酒吧(丰盛里店)", "route_type": "Path算路", "start_coords": [121.412397, 31.234085], "original_coords": [121.412792, 31.233833]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.412773, 31.233781], [121.41305, 31.233706], [121.41305, 31.233706], [121.412996, 31.23354], [121.412948, 31.233401], [121.4129, 31.23325], [121.412873, 31.233218], [121.41283, 31.233197], [121.412696, 31.233208], [121.412696, 31.233208], [121.412626, 31.232998], [121.412578, 31.23287], [121.41254, 31.232762], [121.412443, 31.232473], [121.412368, 31.232263], [121.412181, 31.231706], [121.412138, 31.231582], [121.412138, 31.231582], [121.412186, 31.231566], [121.412406, 31.231518], [121.412519, 31.231539], [121.412551, 31.231561], [121.41269, 31.231781], [121.41269, 31.231781], [121.412749, 31.231765], [121.412749, 31.231765], [121.412696, 31.231598], [121.412696, 31.231598], [121.412427, 31.231636], [121.412379, 31.231636], [121.412325, 31.231614], [121.412224, 31.231389], [121.412197, 31.23133], [121.412181, 31.231287], [121.412106, 31.231115], [121.412106, 31.231115], [121.411886, 31.23111], [121.411735, 31.23111], [121.411564, 31.231105], [121.411317, 31.231115], [121.410856, 31.231164], [121.410684, 31.231185], [121.410684, 31.231185], [121.410952, 31.231925], [121.411081, 31.232226], [121.411124, 31.232328], [121.411124, 31.232328], [121.411151, 31.232532], [121.411199, 31.232628], [121.41143, 31.233084], [121.411762, 31.233631], [121.4121, 31.23413], [121.412738, 31.235069], [121.413157, 31.235638], [121.413361, 31.235922], [121.413431, 31.236018], [121.41386, 31.236609], [121.413946, 31.236732], [121.414155, 31.23699], [121.414493, 31.237478], [121.414782, 31.237858], [121.414825, 31.237912], [121.415104, 31.238271], [121.415201, 31.238395], [121.415356, 31.238561], [121.415726, 31.238964], [121.415845, 31.239092], [121.415989, 31.239237], [121.41644, 31.239666], [121.416698, 31.239886], [121.416966, 31.240095], [121.417175, 31.240246], [121.417175, 31.240246], [121.417245, 31.240262], [121.417459, 31.240331], [121.41769, 31.240455], [121.417738, 31.240482], [121.418393, 31.240857], [121.418554, 31.240954], [121.418709, 31.241045], [121.419412, 31.241458], [121.419412, 31.241458], [121.4202, 31.240825], [121.420361, 31.240718], [121.420538, 31.240594], [121.420678, 31.240498], [121.420898, 31.240342], [121.421558, 31.23987], [121.421831, 31.23965], [121.421992, 31.239521], [121.422378, 31.2392], [121.422523, 31.23913], [121.422883, 31.238835], [121.422952, 31.238781], [121.423569, 31.238288], [121.424283, 31.237703], [121.424374, 31.237633], [121.425699, 31.236598], [121.425989, 31.236373], [121.426708, 31.23582], [121.42836, 31.234543], [121.428901, 31.23413], [121.429711, 31.233513], [121.429851, 31.233438], [121.430457, 31.233149], [121.430924, 31.232923], [121.430994, 31.232896], [121.43124, 31.232746], [121.431761, 31.232333], [121.432088, 31.231979], [121.432324, 31.231732], [121.432565, 31.23147], [121.43352, 31.23044], [121.434261, 31.229377], [121.434566, 31.2289], [121.43498, 31.22838], [121.435081, 31.228261], [121.435221, 31.228138], [121.435832, 31.227596], [121.436433, 31.226996], [121.436712, 31.226776], [121.436911, 31.226593], [121.437173, 31.226357], [121.437608, 31.22595], [121.437785, 31.225778], [121.438096, 31.225499], [121.43838, 31.225472], [121.4386, 31.225461], [121.439126, 31.225445], [121.439856, 31.225488], [121.44022, 31.225456], [121.440483, 31.225424], [121.440725, 31.22537], [121.440945, 31.225284], [121.441433, 31.225134], [121.441867, 31.225011], [121.442055, 31.224952], [121.442248, 31.224893], [121.442436, 31.224828], [121.442769, 31.224683], [121.442908, 31.224737], [121.443375, 31.225118], [121.44345, 31.225177], [121.443552, 31.225258], [121.443868, 31.225499], [121.444142, 31.225708], [121.444405, 31.225906], [121.444582, 31.226014], [121.445403, 31.226416], [121.445574, 31.226497], [121.445918, 31.226663], [121.446277, 31.226829], [121.446942, 31.227124], [121.447049, 31.227178], [121.447575, 31.227393], [121.448047, 31.227586], [121.448122, 31.227618], [121.44861, 31.227827], [121.4492, 31.228079], [121.44993, 31.22839], [121.450279, 31.228535], [121.450381, 31.228578], [121.45045, 31.228616], [121.450745, 31.228744], [121.45081, 31.228776], [121.451373, 31.229013], [121.451818, 31.229157], [121.45213, 31.229254], [121.452773, 31.229447], [121.45324, 31.229586], [121.4539, 31.229796], [121.454248, 31.229903], [121.454447, 31.229962], [121.455166, 31.230193], [121.455166, 31.230193], [121.4556, 31.229586], [121.455885, 31.229211], [121.455928, 31.229098], [121.456281, 31.228557], [121.456501, 31.228213], [121.456534, 31.22817], [121.456614, 31.22816], [121.456888, 31.228353], [121.457596, 31.22875], [121.457719, 31.228825], [121.457993, 31.228975], [121.458213, 31.22912], [121.458288, 31.229168], [121.458524, 31.229393], [121.458524, 31.229393], [121.45876, 31.229077], [121.458846, 31.228948], [121.458926, 31.228836], [121.458975, 31.228766], [121.459076, 31.2286], [121.459168, 31.228455], [121.45921, 31.228385], [121.459237, 31.228321], [121.459275, 31.228251], [121.459296, 31.228208], [121.459366, 31.228058], [121.459388, 31.228004], [121.459446, 31.227859], [121.459506, 31.22772], [121.459532, 31.227661], [121.459575, 31.227564], [121.459597, 31.227511], [121.459677, 31.227323], [121.459677, 31.227323], [121.459483, 31.227253]]}, "properties": {"id": 6, "type": "amap_route", "name": "高德路线_天梭 TISSOT(环球港直营店)", "origin_name": "天梭 TISSOT(环球港直营店)", "origin_type": "购物中心", "origin_district": "普陀区", "dest_name": "Goose Island精酿啤酒吧(丰盛里店)", "route_type": "高德算路", "start_coords": [121.412773, 31.233781], "original_coords": [121.412792, 31.233833]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.489445, 31.247735]}, "properties": {"id": 7, "type": "original", "name": "中国建设银行(虹口支行)", "poi_type": "银行", "district": "虹口区", "dest_name": "浙影餐厅", "distance_to_path_start": 23.26378249328613, "distance_to_amap_start": 23.335531937869835, "path_to_amap_distance": 0.3468659674768442, "path_distance_category": "10m+", "amap_distance_category": "10m+", "comparison_distance_category": "0-1m", "path_start_coords": [121.489689, 31.247719], "amap_start_coords": [121.48969, 31.247722]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.489689, 31.247719]}, "properties": {"id": 7, "type": "path_start", "name": "Path起点_中国建设银行(虹口支行)", "original_poi_name": "中国建设银行(虹口支行)", "original_poi_type": "银行", "original_poi_district": "虹口区", "dest_name": "浙影餐厅", "distance_from_original": 23.26378249328613, "distance_category": "10m+", "distance_to_amap_start": 0.3468659674768442, "original_coords": [121.489445, 31.247735]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.48969, 31.247722]}, "properties": {"id": 7, "type": "amap_start", "name": "高德起点_中国建设银行(虹口支行)", "original_poi_name": "中国建设银行(虹口支行)", "original_poi_type": "银行", "original_poi_district": "虹口区", "dest_name": "浙影餐厅", "distance_from_original": 23.335531937869835, "distance_category": "10m+", "distance_to_path_start": 0.3468659674768442, "original_coords": [121.489445, 31.247735]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.489689, 31.247719], [121.48968, 31.24758], [121.48968, 31.24758], [121.48965, 31.24735], [121.48965, 31.24735], [121.48959, 31.24688], [121.48959, 31.24688], [121.48944, 31.24584], [121.48944, 31.24584], [121.48878, 31.24574], [121.4878, 31.24562], [121.4878, 31.24562], [121.48782, 31.24551], [121.48782, 31.24551], [121.48785, 31.24512], [121.48785, 31.24512], [121.48786, 31.24482], [121.48785, 31.24466], [121.48785, 31.24466], [121.48776, 31.24419], [121.48776, 31.24419], [121.48769, 31.24383], [121.48757, 31.24359], [121.48757, 31.24359], [121.48744, 31.2434], [121.4874, 31.24328], [121.4874, 31.24284], [121.4874, 31.24284], [121.4874, 31.24259], [121.4874, 31.24259], [121.48738, 31.24214], [121.48738, 31.24214], [121.48737, 31.24176], [121.48737, 31.24176], [121.48737, 31.24136], [121.48737, 31.24136], [121.48738, 31.24118], [121.48738, 31.24118], [121.4874, 31.24073], [121.4874, 31.24073], [121.48742, 31.24051], [121.48737, 31.24039], [121.48696, 31.24017], [121.48696, 31.24017], [121.48704, 31.23995], [121.48704, 31.23995], [121.48721, 31.23946], [121.48721, 31.23946], [121.48679, 31.23933], [121.48679, 31.23933], [121.48666, 31.23929], [121.48666, 31.23929], [121.48608, 31.23911], [121.48608, 31.23911], [121.48547, 31.23892], [121.48547, 31.23892], [121.48453, 31.23865], [121.48453, 31.23865], [121.48438, 31.2386], [121.48438, 31.2386], [121.48409, 31.23849], [121.48409, 31.23849], [121.48366, 31.23835], [121.48366, 31.23835], [121.48356, 31.23831], [121.48356, 31.23831], [121.48345, 31.23827], [121.48345, 31.23827], [121.4832, 31.23819], [121.4832, 31.23819], [121.48284, 31.23806], [121.48284, 31.23806], [121.48253, 31.23796], [121.48253, 31.23796], [121.48232, 31.2379], [121.48232, 31.2379], [121.48226, 31.23788], [121.48226, 31.23788], [121.48201, 31.23779], [121.48201, 31.23779], [121.48162, 31.23763], [121.48162, 31.23763], [121.48109, 31.23747], [121.48109, 31.23747], [121.48099, 31.23743], [121.48099, 31.23743], [121.48062, 31.23731], [121.48062, 31.23731], [121.48023, 31.23719], [121.48023, 31.23719], [121.48007, 31.23713], [121.48007, 31.23713], [121.47986, 31.23707], [121.47986, 31.23707], [121.47941, 31.23693], [121.47941, 31.23693], [121.47907, 31.23682], [121.47907, 31.23682], [121.47859, 31.23667], [121.47859, 31.23667], [121.47838, 31.2366], [121.47838, 31.2366], [121.47821, 31.23654], [121.47821, 31.23654], [121.47803, 31.23644], [121.47803, 31.23644], [121.47782, 31.23629], [121.47782, 31.23629], [121.47796, 31.23608], [121.47796, 31.23608], [121.47802, 31.236], [121.47802, 31.236], [121.47826, 31.23562], [121.47838, 31.23554], [121.47838, 31.23554], [121.47854, 31.23543], [121.47854, 31.23543], [121.47879, 31.23458], [121.47879, 31.23458], [121.4788, 31.23453], [121.4788, 31.23453], [121.47883, 31.23443], [121.47883, 31.23443], [121.47898, 31.23394], [121.47898, 31.23394], [121.47913, 31.23365], [121.47913, 31.23365], [121.47926, 31.23336], [121.47926, 31.23336], [121.47945, 31.23292], [121.47947, 31.23276], [121.47947, 31.23276], [121.47953, 31.23255], [121.47962, 31.23239], [121.47977, 31.23222], [121.47977, 31.23222], [121.4798, 31.23219], [121.4798, 31.23219], [121.479859, 31.232126]]}, "properties": {"id": 7, "type": "path_route", "name": "Path路线_中国建设银行(虹口支行)", "origin_name": "中国建设银行(虹口支行)", "origin_type": "银行", "origin_district": "虹口区", "dest_name": "浙影餐厅", "route_type": "Path算路", "start_coords": [121.489689, 31.247719], "original_coords": [121.489445, 31.247735]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.48969, 31.247722], [121.48968, 31.247584], [121.489654, 31.247353], [121.489589, 31.246887], [121.48945, 31.245846], [121.489439, 31.245771], [121.489412, 31.24554], [121.489364, 31.245116], [121.4893, 31.244693], [121.489278, 31.244532], [121.489241, 31.244301], [121.489364, 31.244258], [121.489809, 31.24414], [121.490351, 31.244033], [121.490351, 31.244033], [121.490185, 31.243518], [121.489991, 31.242858], [121.489954, 31.242585], [121.489933, 31.242161], [121.490077, 31.241882], [121.490115, 31.241775], [121.490179, 31.241571], [121.490222, 31.241356], [121.490244, 31.241147], [121.49026, 31.240981], [121.490244, 31.240637], [121.490142, 31.240117], [121.490056, 31.239634], [121.49004, 31.239511], [121.48997, 31.238948], [121.489981, 31.238663], [121.490024, 31.238336], [121.490045, 31.238164], [121.490077, 31.237934], [121.490201, 31.236946], [121.49027, 31.236421], [121.490362, 31.235916], [121.490426, 31.235632], [121.490426, 31.235632], [121.489632, 31.23545], [121.489283, 31.23537], [121.488913, 31.235289], [121.488345, 31.235192], [121.488221, 31.235171], [121.488141, 31.23516], [121.487948, 31.235128], [121.487309, 31.235031], [121.486816, 31.234924], [121.48673, 31.234908], [121.486253, 31.234811], [121.485802, 31.23471], [121.485663, 31.234677], [121.485217, 31.23456], [121.484815, 31.234468], [121.484541, 31.234398], [121.484118, 31.234286], [121.483603, 31.234152], [121.483527, 31.23413], [121.483146, 31.234018], [121.482846, 31.233932], [121.482471, 31.233819], [121.482133, 31.233723], [121.481789, 31.233621], [121.480985, 31.233331], [121.48091, 31.233299], [121.480475, 31.233138], [121.480341, 31.233084], [121.480169, 31.23302], [121.479477, 31.232768], [121.479477, 31.232768], [121.479536, 31.232558], [121.479622, 31.232392], [121.479767, 31.232236], [121.47981, 31.232188], [121.47987, 31.232125]]}, "properties": {"id": 7, "type": "amap_route", "name": "高德路线_中国建设银行(虹口支行)", "origin_name": "中国建设银行(虹口支行)", "origin_type": "银行", "origin_district": "虹口区", "dest_name": "浙影餐厅", "route_type": "高德算路", "start_coords": [121.48969, 31.247722], "original_coords": [121.489445, 31.247735]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.399828, 31.196413]}, "properties": {"id": 8, "type": "original", "name": "御翠豪庭", "poi_type": "住宅小区", "district": "长宁区", "dest_name": "上海戏剧学院华山路校区", "distance_to_path_start": 27.70057140070531, "distance_to_amap_start": 27.512464309941127, "path_to_amap_distance": 0.19023146942371255, "path_distance_category": "10m+", "amap_distance_category": "10m+", "comparison_distance_category": "0-1m", "path_start_coords": [121.39954, 31.19645], "amap_start_coords": [121.399542, 31.19645]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.39954, 31.19645]}, "properties": {"id": 8, "type": "path_start", "name": "Path起点_御翠豪庭", "original_poi_name": "御翠豪庭", "original_poi_type": "住宅小区", "original_poi_district": "长宁区", "dest_name": "上海戏剧学院华山路校区", "distance_from_original": 27.70057140070531, "distance_category": "10m+", "distance_to_amap_start": 0.19023146942371255, "original_coords": [121.399828, 31.196413]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.399542, 31.19645]}, "properties": {"id": 8, "type": "amap_start", "name": "高德起点_御翠豪庭", "original_poi_name": "御翠豪庭", "original_poi_type": "住宅小区", "original_poi_district": "长宁区", "dest_name": "上海戏剧学院华山路校区", "distance_from_original": 27.512464309941127, "distance_category": "10m+", "distance_to_path_start": 0.19023146942371255, "original_coords": [121.399828, 31.196413]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.39954, 31.19645], [121.39929, 31.19648], [121.39929, 31.19648], [121.39929, 31.19664], [121.39939, 31.19684], [121.39943, 31.19689], [121.39943, 31.19689], [121.39965, 31.19714], [121.39965, 31.19714], [121.39968, 31.19719], [121.39968, 31.19719], [121.39966, 31.19725], [121.39966, 31.19725], [121.39952, 31.1974], [121.39952, 31.1974], [121.39924, 31.19758], [121.39925, 31.19769], [121.39925, 31.19769], [121.3988, 31.19775], [121.3988, 31.19775], [121.39821, 31.19775], [121.39821, 31.19775], [121.39829, 31.19818], [121.39829, 31.19818], [121.39835, 31.1985], [121.39835, 31.1985], [121.3984, 31.19875], [121.3984, 31.19875], [121.39873, 31.20013], [121.39873, 31.20013], [121.39921, 31.2002], [121.39951, 31.2002], [121.39973, 31.20015], [121.40022, 31.19999], [121.40022, 31.19999], [121.40086, 31.19979], [121.40086, 31.19979], [121.40089, 31.19988], [121.40089, 31.19988], [121.40055, 31.19998], [121.40055, 31.19998], [121.40007, 31.20014], [121.40007, 31.20014], [121.40004, 31.20024], [121.40003, 31.20036], [121.40014, 31.2006], [121.40014, 31.2006], [121.40121, 31.2009], [121.40121, 31.2009], [121.40176, 31.2009], [121.40189, 31.20088], [121.40225, 31.20076], [121.40241, 31.20073], [121.40269, 31.20075], [121.40392, 31.20105], [121.40493, 31.20134], [121.40527, 31.20148], [121.40539, 31.20157], [121.40547, 31.20166], [121.40547, 31.20166], [121.40599, 31.20189], [121.40657, 31.20223], [121.40657, 31.20223], [121.40745, 31.20285], [121.40745, 31.20285], [121.40834, 31.20347], [121.40834, 31.20347], [121.4089, 31.20386], [121.4089, 31.20386], [121.40896, 31.2039], [121.40896, 31.2039], [121.40988, 31.20457], [121.4112, 31.2055], [121.4112, 31.2055], [121.41319, 31.20692], [121.41351, 31.20713], [121.41384, 31.20732], [121.41418, 31.20748], [121.41452, 31.20761], [121.4149, 31.20773], [121.41529, 31.20783], [121.41529, 31.20783], [121.41836, 31.20846], [121.41836, 31.20846], [121.41863, 31.20852], [121.41863, 31.20852], [121.42122, 31.20906], [121.42287, 31.20944], [121.42287, 31.20944], [121.42372, 31.20962], [121.42372, 31.20962], [121.42525, 31.20997], [121.42632, 31.21012], [121.42632, 31.21012], [121.42794, 31.21026], [121.42794, 31.21026], [121.42832, 31.21027], [121.42855, 31.21031], [121.42872, 31.21036], [121.42911, 31.21054], [121.42934, 31.21068], [121.42955, 31.21083], [121.42985, 31.21111], [121.43007, 31.21138], [121.43054, 31.2121], [121.43054, 31.2121], [121.43054, 31.21217], [121.43054, 31.21217], [121.43075, 31.21248], [121.43075, 31.21248], [121.43088, 31.21267], [121.43131, 31.21317], [121.43131, 31.21317], [121.43163, 31.21339], [121.43211, 31.21364], [121.43211, 31.21364], [121.43301, 31.21409], [121.43301, 31.21409], [121.43308, 31.21412], [121.43308, 31.21412], [121.43339, 31.21426], [121.43378, 31.21447], [121.43378, 31.21447], [121.43409, 31.21467], [121.43409, 31.21467], [121.43419, 31.21473], [121.43419, 31.21473], [121.43444, 31.21491], [121.43444, 31.21491], [121.43446, 31.21495], [121.43489, 31.21536], [121.43575, 31.21605], [121.43604, 31.21626], [121.43604, 31.21626], [121.43644, 31.21651], [121.43644, 31.21651], [121.43671, 31.21669], [121.43671, 31.21669], [121.43701, 31.21685], [121.43701, 31.21685], [121.43768, 31.21723], [121.43768, 31.21723], [121.43774, 31.21726], [121.43774, 31.21726], [121.43831, 31.21757], [121.43831, 31.21757], [121.4385, 31.21771], [121.4385, 31.21771], [121.43859, 31.21777], [121.43859, 31.21777], [121.43886, 31.21797], [121.43886, 31.21797], [121.43909, 31.21817], [121.43909, 31.21817], [121.43928, 31.21833], [121.43928, 31.21833], [121.43946, 31.21848], [121.43946, 31.21848], [121.43953, 31.2184], [121.43953, 31.2184], [121.43958, 31.21834], [121.43958, 31.21834], [121.4398, 31.21806], [121.4398, 31.21806], [121.43991, 31.21787], [121.43991, 31.21787], [121.44005, 31.21759], [121.44005, 31.21759], [121.44015, 31.21735], [121.44015, 31.21735], [121.440188, 31.217251]]}, "properties": {"id": 8, "type": "path_route", "name": "Path路线_御翠豪庭", "origin_name": "御翠豪庭", "origin_type": "住宅小区", "origin_district": "长宁区", "dest_name": "上海戏剧学院华山路校区", "route_type": "Path算路", "start_coords": [121.39954, 31.19645], "original_coords": [121.399828, 31.196413]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.399542, 31.19645], [121.39929, 31.196488], [121.39929, 31.196488], [121.399279, 31.196166], [121.39929, 31.196134], [121.399311, 31.196075], [121.399397, 31.196016], [121.400095, 31.195887], [121.400304, 31.195855], [121.40046, 31.195839], [121.40069, 31.19585], [121.400878, 31.195936], [121.400931, 31.195973], [121.400985, 31.196016], [121.401092, 31.196359], [121.401157, 31.196531], [121.401157, 31.196531], [121.401232, 31.196515], [121.401291, 31.196499], [121.401334, 31.196493], [121.401409, 31.19674], [121.401554, 31.197207], [121.401516, 31.197212], [121.401275, 31.197277], [121.400481, 31.197427], [121.399923, 31.197556], [121.399923, 31.197556], [121.400218, 31.198269], [121.40025, 31.198328], [121.400298, 31.198451], [121.400325, 31.198505], [121.400433, 31.198763], [121.40047, 31.198865], [121.40069, 31.199423], [121.400776, 31.199605], [121.400862, 31.199798], [121.400894, 31.199878], [121.400561, 31.199986], [121.400078, 31.200141], [121.400078, 31.200141], [121.400062, 31.200179], [121.400036, 31.200308], [121.400041, 31.200361], [121.400148, 31.200608], [121.400148, 31.200608], [121.400679, 31.200753], [121.40121, 31.200903], [121.40121, 31.200903], [121.401511, 31.200903], [121.401607, 31.200914], [121.401763, 31.200903], [121.401892, 31.200887], [121.402256, 31.200764], [121.402417, 31.200737], [121.402573, 31.200737], [121.402696, 31.200753], [121.403034, 31.200828], [121.403925, 31.201059], [121.404333, 31.201171], [121.404585, 31.201241], [121.404933, 31.201348], [121.405277, 31.201482], [121.405341, 31.201525], [121.4054, 31.201574], [121.40547, 31.201665], [121.406038, 31.201922], [121.406575, 31.202233], [121.40746, 31.202861], [121.40834, 31.203478], [121.408903, 31.203864], [121.408967, 31.203902], [121.408967, 31.203902], [121.409885, 31.204578], [121.41084, 31.205254], [121.41121, 31.205506], [121.411478, 31.205704], [121.413189, 31.206927], [121.413516, 31.207136], [121.413844, 31.207319], [121.414187, 31.20748], [121.41452, 31.207614], [121.4149, 31.207737], [121.415292, 31.207834], [121.416107, 31.207995], [121.41836, 31.208467], [121.41864, 31.208526], [121.420898, 31.208998], [121.421225, 31.209068], [121.422877, 31.209443], [121.423247, 31.209524], [121.423725, 31.209626], [121.425254, 31.209974], [121.425919, 31.210076], [121.426321, 31.210125], [121.427947, 31.210264], [121.427947, 31.210264], [121.428322, 31.210269], [121.428558, 31.210312], [121.42873, 31.210361], [121.428901, 31.21043], [121.429121, 31.210543], [121.429347, 31.210682], [121.429556, 31.210838], [121.429744, 31.210993], [121.429856, 31.211111], [121.430071, 31.21138], [121.430253, 31.211664], [121.430275, 31.211761], [121.430463, 31.21204], [121.430532, 31.212158], [121.43057, 31.212216], [121.430683, 31.212383], [121.430881, 31.212678], [121.431005, 31.212828], [121.431316, 31.213171], [121.431632, 31.213391], [121.432115, 31.213649], [121.432195, 31.213686], [121.432319, 31.213745], [121.432721, 31.213944], [121.432796, 31.213981], [121.433016, 31.214089], [121.433086, 31.214121], [121.433311, 31.214228], [121.433397, 31.214271], [121.433788, 31.21448], [121.434094, 31.214668], [121.434191, 31.214727], [121.434443, 31.214915], [121.434443, 31.214915], [121.434459, 31.214953], [121.434894, 31.215366], [121.435752, 31.216052], [121.436047, 31.216267], [121.436449, 31.216508], [121.436718, 31.216691], [121.437013, 31.216857], [121.437688, 31.217238], [121.437742, 31.217265], [121.438316, 31.217581], [121.438509, 31.21771], [121.4386, 31.217774], [121.438863, 31.217973], [121.439099, 31.218176], [121.439282, 31.218332], [121.439464, 31.218488], [121.439464, 31.218488], [121.439534, 31.218402], [121.439582, 31.218348], [121.439802, 31.218069], [121.439909, 31.217871], [121.44006, 31.217592], [121.440156, 31.21735], [121.440196, 31.217242]]}, "properties": {"id": 8, "type": "amap_route", "name": "高德路线_御翠豪庭", "origin_name": "御翠豪庭", "origin_type": "住宅小区", "origin_district": "长宁区", "dest_name": "上海戏剧学院华山路校区", "route_type": "高德算路", "start_coords": [121.399542, 31.19645], "original_coords": [121.399828, 31.196413]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.427535, 31.197378]}, "properties": {"id": 9, "type": "original", "name": "上海市民办西南高级中学", "poi_type": "学校", "district": "徐汇区", "dest_name": "淮海755", "distance_to_path_start": 34.08718722190617, "distance_to_amap_start": 34.38514851215583, "path_to_amap_distance": 0.306244458328162, "path_distance_category": "10m+", "amap_distance_category": "10m+", "comparison_distance_category": "0-1m", "path_start_coords": [121.42789, 31.19742], "amap_start_coords": [121.427893, 31.197421]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.42789, 31.19742]}, "properties": {"id": 9, "type": "path_start", "name": "Path起点_上海市民办西南高级中学", "original_poi_name": "上海市民办西南高级中学", "original_poi_type": "学校", "original_poi_district": "徐汇区", "dest_name": "淮海755", "distance_from_original": 34.08718722190617, "distance_category": "10m+", "distance_to_amap_start": 0.306244458328162, "original_coords": [121.427535, 31.197378]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.427893, 31.197421]}, "properties": {"id": 9, "type": "amap_start", "name": "高德起点_上海市民办西南高级中学", "original_poi_name": "上海市民办西南高级中学", "original_poi_type": "学校", "original_poi_district": "徐汇区", "dest_name": "淮海755", "distance_from_original": 34.38514851215583, "distance_category": "10m+", "distance_to_path_start": 0.306244458328162, "original_coords": [121.427535, 31.197378]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.42789, 31.19742], [121.42824, 31.19769], [121.42824, 31.19769], [121.4284, 31.19766], [121.4284, 31.19766], [121.42861, 31.19761], [121.42861, 31.19761], [121.42865, 31.19782], [121.42865, 31.19782], [121.42873, 31.19803], [121.42873, 31.19803], [121.42891, 31.19841], [121.42891, 31.19841], [121.42896, 31.19851], [121.42896, 31.19851], [121.42904, 31.19864], [121.42904, 31.19864], [121.42918, 31.19892], [121.42918, 31.19892], [121.42927, 31.19909], [121.42927, 31.19909], [121.42939, 31.19932], [121.42939, 31.19932], [121.42947, 31.19947], [121.42947, 31.19947], [121.4295, 31.19952], [121.4295, 31.19952], [121.42961, 31.19974], [121.42966, 31.19992], [121.42966, 31.19992], [121.42969, 31.2], [121.42969, 31.2], [121.43038, 31.20028], [121.43038, 31.20028], [121.43075, 31.20043], [121.43075, 31.20043], [121.43091, 31.20049], [121.43091, 31.20049], [121.43115, 31.20058], [121.43115, 31.20058], [121.43167, 31.20079], [121.43167, 31.20079], [121.43235, 31.20102], [121.43235, 31.20102], [121.43241, 31.20102], [121.43241, 31.20102], [121.43247, 31.20102], [121.43262, 31.20108], [121.43262, 31.20108], [121.43308, 31.20126], [121.4336, 31.20149], [121.4336, 31.20149], [121.43432, 31.20179], [121.43432, 31.20179], [121.43457, 31.2019], [121.43457, 31.2019], [121.43467, 31.20195], [121.43467, 31.20195], [121.4352, 31.2022], [121.4352, 31.2022], [121.43535, 31.20227], [121.43535, 31.20227], [121.43573, 31.20249], [121.43573, 31.20249], [121.43696, 31.20328], [121.43696, 31.20328], [121.43704, 31.20333], [121.43704, 31.20333], [121.43794, 31.20392], [121.43794, 31.20392], [121.43841, 31.20422], [121.43841, 31.20422], [121.43872, 31.20441], [121.43872, 31.20441], [121.43918, 31.20471], [121.43918, 31.20471], [121.43956, 31.20496], [121.43956, 31.20496], [121.44011, 31.20532], [121.44011, 31.20532], [121.44057, 31.20561], [121.44057, 31.20561], [121.44113, 31.20595], [121.44113, 31.20595], [121.44162, 31.20626], [121.44162, 31.20626], [121.44173, 31.20633], [121.44173, 31.20633], [121.44207, 31.20654], [121.44207, 31.20654], [121.44235, 31.20672], [121.44235, 31.20672], [121.44262, 31.20689], [121.44262, 31.20689], [121.44343, 31.2074], [121.44343, 31.2074], [121.44458, 31.20813], [121.44458, 31.20813], [121.44525, 31.20855], [121.44525, 31.20855], [121.44547, 31.20871], [121.44547, 31.20871], [121.44601, 31.20906], [121.44601, 31.20906], [121.44632, 31.20927], [121.44632, 31.20927], [121.44705, 31.20975], [121.44705, 31.20975], [121.44755, 31.21008], [121.44779, 31.21026], [121.44793, 31.2104], [121.44793, 31.2104], [121.44798, 31.21046], [121.44798, 31.21046], [121.44834, 31.21091], [121.44834, 31.21091], [121.44863, 31.21128], [121.44863, 31.21128], [121.449, 31.21175], [121.449, 31.21175], [121.44951, 31.21235], [121.44951, 31.21235], [121.44957, 31.2124], [121.44957, 31.2124], [121.44971, 31.21265], [121.44971, 31.21265], [121.4498, 31.21276], [121.44994, 31.21288], [121.45095, 31.21331], [121.45095, 31.21331], [121.45177, 31.21365], [121.45177, 31.21365], [121.45235, 31.21388], [121.45235, 31.21388], [121.45256, 31.21397], [121.45256, 31.21397], [121.45297, 31.21414], [121.45297, 31.21414], [121.45374, 31.21445], [121.45374, 31.21445], [121.45448, 31.21479], [121.45448, 31.21479], [121.45598, 31.21545], [121.45598, 31.21545], [121.45674, 31.21575], [121.45674, 31.21575], [121.45701, 31.21589], [121.45701, 31.21589], [121.45879, 31.21665], [121.45879, 31.21665], [121.45904, 31.21675], [121.45904, 31.21675], [121.45941, 31.21597], [121.45941, 31.21597], [121.45943, 31.21589], [121.45943, 31.21589], [121.45988, 31.21521], [121.45988, 31.21521], [121.46025, 31.21535], [121.46025, 31.21535], [121.46039, 31.21541], [121.46039, 31.21541], [121.46079, 31.21557], [121.46079, 31.21557], [121.46108, 31.21568], [121.46108, 31.21568], [121.46126, 31.21577], [121.46126, 31.21577], [121.46151, 31.21595], [121.46175, 31.21617], [121.46175, 31.21617], [121.46216, 31.21659], [121.46216, 31.21659], [121.46255, 31.21697], [121.46265, 31.21704], [121.46265, 31.21704], [121.46281, 31.21714], [121.46296, 31.21721], [121.46296, 31.21721], [121.46311, 31.21727], [121.46311, 31.21727], [121.46324, 31.21731], [121.46324, 31.21731], [121.4634, 31.21736], [121.4634, 31.21736], [121.46346, 31.21737], [121.46346, 31.21737], [121.46363, 31.21743], [121.46363, 31.21743], [121.46375, 31.21746], [121.46375, 31.21746], [121.46393, 31.21752], [121.46393, 31.21752], [121.46404, 31.21755], [121.46404, 31.21755], [121.46427, 31.21762], [121.46427, 31.21762], [121.46413, 31.21822], [121.46413, 31.21822], [121.464059, 31.218473]]}, "properties": {"id": 9, "type": "path_route", "name": "Path路线_上海市民办西南高级中学", "origin_name": "上海市民办西南高级中学", "origin_type": "学校", "origin_district": "徐汇区", "dest_name": "淮海755", "route_type": "Path算路", "start_coords": [121.42789, 31.19742], "original_coords": [121.427535, 31.197378]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.427893, 31.197421], [121.428247, 31.197695], [121.428247, 31.197695], [121.428408, 31.197668], [121.428612, 31.197609], [121.428612, 31.197609], [121.42866, 31.197818], [121.42873, 31.198033], [121.428918, 31.198419], [121.428966, 31.198511], [121.429041, 31.19865], [121.429191, 31.198924], [121.429272, 31.199095], [121.429395, 31.199326], [121.42947, 31.199471], [121.429502, 31.199524], [121.42961, 31.199744], [121.429663, 31.199927], [121.42969, 31.200002], [121.42969, 31.200002], [121.430382, 31.200281], [121.430758, 31.200431], [121.430913, 31.200495], [121.431155, 31.200581], [121.431659, 31.200806], [121.431975, 31.200892], [121.432356, 31.201021], [121.432421, 31.201021], [121.43248, 31.201021], [121.43263, 31.201085], [121.43285, 31.201171], [121.433086, 31.201263], [121.433606, 31.201493], [121.433982, 31.201643], [121.434266, 31.201767], [121.43432, 31.201793], [121.434572, 31.201911], [121.434679, 31.20196], [121.434829, 31.20203], [121.43498, 31.202094], [121.43521, 31.202201], [121.435355, 31.202271], [121.435736, 31.202496], [121.436959, 31.203285], [121.437045, 31.203338], [121.437946, 31.203923], [121.438413, 31.204224], [121.438724, 31.204417], [121.439185, 31.204717], [121.439555, 31.204958], [121.440108, 31.205323], [121.440569, 31.205613], [121.441132, 31.205956], [121.441626, 31.206262], [121.441739, 31.206332], [121.442076, 31.206546], [121.442355, 31.206723], [121.442624, 31.20689], [121.44287, 31.207045], [121.443434, 31.207399], [121.443638, 31.207533], [121.443874, 31.207684], [121.444587, 31.208129], [121.444877, 31.208317], [121.445252, 31.208558], [121.445472, 31.208714], [121.44602, 31.209068], [121.446325, 31.209277], [121.44706, 31.209754], [121.447554, 31.210081], [121.447795, 31.210264], [121.447983, 31.210462], [121.448342, 31.210913], [121.448637, 31.211278], [121.449002, 31.211761], [121.449345, 31.212158], [121.449517, 31.212356], [121.449571, 31.212404], [121.449715, 31.212651], [121.449801, 31.212764], [121.449941, 31.212882], [121.450166, 31.212978], [121.450955, 31.213311], [121.451775, 31.213654], [121.451851, 31.213681], [121.45235, 31.213885], [121.452569, 31.213971], [121.452977, 31.214142], [121.45375, 31.214454], [121.454485, 31.214791], [121.455187, 31.215103], [121.455986, 31.215451], [121.456738, 31.215757], [121.457011, 31.215891], [121.457118, 31.215934], [121.457403, 31.216058], [121.457692, 31.216186], [121.457934, 31.216288], [121.457987, 31.21631], [121.45803, 31.216331], [121.458165, 31.216385], [121.458341, 31.21646], [121.458851, 31.216675], [121.459044, 31.216755], [121.459044, 31.216755], [121.459162, 31.216508], [121.459189, 31.216444], [121.459253, 31.216304], [121.459296, 31.216224], [121.459409, 31.215972], [121.459436, 31.215896], [121.459538, 31.215741], [121.45971, 31.215483], [121.459768, 31.215398], [121.459886, 31.21521], [121.459886, 31.21521], [121.460256, 31.21536], [121.460396, 31.215414], [121.460793, 31.215569], [121.461083, 31.215687], [121.46126, 31.215773], [121.461512, 31.215956], [121.461753, 31.216176], [121.462161, 31.216599], [121.462553, 31.21697], [121.462649, 31.217039], [121.462815, 31.217146], [121.462966, 31.217216], [121.463121, 31.217275], [121.463245, 31.217313], [121.463405, 31.217361], [121.463459, 31.217377], [121.463631, 31.217431], [121.463754, 31.217468], [121.463931, 31.217522], [121.464044, 31.21756], [121.464269, 31.217624], [121.464269, 31.217624], [121.464135, 31.218219], [121.46406, 31.218485]]}, "properties": {"id": 9, "type": "amap_route", "name": "高德路线_上海市民办西南高级中学", "origin_name": "上海市民办西南高级中学", "origin_type": "学校", "origin_district": "徐汇区", "dest_name": "淮海755", "route_type": "高德算路", "start_coords": [121.427893, 31.197421], "original_coords": [121.427535, 31.197378]}}]}