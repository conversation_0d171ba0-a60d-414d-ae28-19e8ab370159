#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双路径算路比较测试
使用上海POI数据进行Path算路和高德算路的对比测试
"""

import csv
import json
import requests
import random
import math
import time
import os
from typing import List, Dict, Tuple, Optional
from collections import defaultdict

# 设置代理环境变量，绕过本地连接的代理
os.environ['NO_PROXY'] = 'localhost,127.0.0.1'

# 高德API配置
AMAP_CONFIG = {
    'key': 'c6dbca449fbd6baadc69b8540565ef59',
    'driving_url': 'https://restapi.amap.com/v3/direction/driving'
}

# Path算路服务配置
PATH_SERVER_CONFIG = {
    'url': 'http://localhost:8081',
    'route_endpoint': '/route',
    'result_endpoint': '/result/'
}

class RouteComparison:
    def __init__(self, max_distance: float = 5000):
        self.poi_data = []
        self.test_results = []
        self.distance_stats = defaultdict(int)
        self.max_distance = max_distance
        
    def load_poi_data(self, csv_file: str) -> List[Dict]:
        """加载POI数据"""
        print(f"加载POI数据: {csv_file}")
        poi_data = []
        
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                poi_data.append({
                    'name': row['POIName'],
                    'type': row['POIType'],
                    'longitude': float(row['DistLon']),
                    'latitude': float(row['DistLat']),
                    'district': row['DistName'],
                    'address': row.get('POIAddress', '')
                })
        
        print(f"加载了 {len(poi_data)} 个POI点")
        self.poi_data = poi_data
        return poi_data
    
    def calculate_distance(self, lon1: float, lat1: float, lon2: float, lat2: float) -> float:
        """计算两点间距离（米）"""
        # 使用Haversine公式计算球面距离
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def find_nearby_pois(self, origin_poi: Dict, max_distance: float = None) -> List[Dict]:
        """找到距离起点小于指定距离的POI点"""
        if max_distance is None:
            max_distance = self.max_distance

        nearby_pois = []
        origin_lon = origin_poi['longitude']
        origin_lat = origin_poi['latitude']

        for poi in self.poi_data:
            if poi['name'] == origin_poi['name']:  # 跳过自己
                continue

            distance = self.calculate_distance(
                origin_lon, origin_lat,
                poi['longitude'], poi['latitude']
            )

            if distance <= max_distance:
                poi_with_distance = poi.copy()
                poi_with_distance['distance_to_origin'] = distance
                nearby_pois.append(poi_with_distance)

        return nearby_pois
    
    def call_path_route_api(self, start_lon: float, start_lat: float,
                           end_lon: float, end_lat: float) -> Optional[Dict]:
        """调用Path算路API"""
        try:
            # 发送算路请求
            request_data = {
                'start_lng': start_lon,
                'start_lat': start_lat,
                'end_lng': end_lon,
                'end_lat': end_lat,
                'strategy': 0  # 最短时间策略
            }

            # 设置代理配置
            proxies = {
                'http': None,
                'https': None
            }

            # 发送路径计算请求
            response = requests.post(
                f"{PATH_SERVER_CONFIG['url']}{PATH_SERVER_CONFIG['route_endpoint']}",
                json=request_data,
                timeout=10,
                proxies=proxies
            )

            if response.status_code != 200:
                print(f"Path API请求失败: {response.status_code}")
                return None

            result = response.json()
            uuid = result.get('uuid')

            if not uuid:
                print("Path API未返回UUID")
                return None

            # 轮询获取结果
            for attempt in range(30):  # 最多等待30秒
                time.sleep(1)

                result_response = requests.get(
                    f"{PATH_SERVER_CONFIG['url']}{PATH_SERVER_CONFIG['result_endpoint']}{uuid}",
                    timeout=5,
                    proxies=proxies
                )

                if result_response.status_code != 200:
                    continue

                result_data = result_response.json()

                if result_data.get('status') == 'completed':
                    return result_data
                elif result_data.get('status') == 'failed':
                    print(f"Path算路失败: {result_data.get('error', 'Unknown error')}")
                    return None

            print("Path算路超时")
            return None

        except Exception as e:
            print(f"Path API调用异常: {e}")
            return None
    
    def call_amap_route_api(self, start_lon: float, start_lat: float,
                           end_lon: float, end_lat: float) -> Optional[Dict]:
        """调用高德算路API"""
        try:
            params = {
                'key': AMAP_CONFIG['key'],
                'origin': f"{start_lon},{start_lat}",
                'destination': f"{end_lon},{end_lat}",
                'extensions': 'all',
                'strategy': 0  # 最短时间策略
            }
            
            response = requests.get(AMAP_CONFIG['driving_url'], params=params, timeout=10)
            
            if response.status_code != 200:
                print(f"高德API请求失败: {response.status_code}")
                return None
            
            result = response.json()
            
            if result.get('status') != '1':
                print(f"高德API错误: {result.get('info')}")
                return None
            
            return result
            
        except Exception as e:
            print(f"高德API调用异常: {e}")
            return None
    
    def extract_route_start_point(self, route_data: Dict, api_type: str) -> Optional[Tuple[float, float]]:
        """提取路线的实际起点坐标"""
        try:
            if api_type == 'path':
                # Path API结果格式
                if 'paths' in route_data and len(route_data['paths']) > 0:
                    points = route_data['paths'][0].get('points', [])
                    if points and len(points) > 0:
                        first_point = points[0]
                        return (first_point['lng'], first_point['lat'])

            elif api_type == 'amap':
                # 高德API结果格式
                if 'route' in route_data and 'paths' in route_data['route']:
                    paths = route_data['route']['paths']
                    if paths and len(paths) > 0:
                        steps = paths[0].get('steps', [])
                        if steps and len(steps) > 0:
                            polyline = steps[0].get('polyline', '')
                            if polyline:
                                # 解析polyline的第一个点
                                coords = polyline.split(';')[0].split(',')
                                if len(coords) >= 2:
                                    return (float(coords[0]), float(coords[1]))

            return None

        except Exception as e:
            print(f"提取{api_type}路线起点失败: {e}")
            return None

    def extract_route_end_point(self, route_data: Dict, api_type: str) -> Optional[Tuple[float, float]]:
        """提取路线的实际终点坐标"""
        try:
            if api_type == 'path':
                # Path API结果格式
                if 'paths' in route_data and len(route_data['paths']) > 0:
                    points = route_data['paths'][0].get('points', [])
                    if points and len(points) > 0:
                        last_point = points[-1]  # 最后一个点
                        return (last_point['lng'], last_point['lat'])

            elif api_type == 'amap':
                # 高德API结果格式
                if 'route' in route_data and 'paths' in route_data['route']:
                    paths = route_data['route']['paths']
                    if paths and len(paths) > 0:
                        steps = paths[0].get('steps', [])
                        if steps and len(steps) > 0:
                            # 获取最后一个step的polyline
                            last_step = steps[-1]
                            polyline = last_step.get('polyline', '')
                            if polyline:
                                # 解析polyline的最后一个点
                                coords = polyline.split(';')[-1].split(',')
                                if len(coords) >= 2:
                                    return (float(coords[0]), float(coords[1]))

            return None

        except Exception as e:
            print(f"提取{api_type}路线终点失败: {e}")
            return None

    def test_single_route_pair(self, origin_poi: Dict, dest_poi: Dict) -> Optional[Dict]:
        """测试单个路径对"""
        print(f"测试路径: {origin_poi['name']} -> {dest_poi['name']}")

        start_lon = origin_poi['longitude']
        start_lat = origin_poi['latitude']
        end_lon = dest_poi['longitude']
        end_lat = dest_poi['latitude']

        # 调用两个API
        path_result = self.call_path_route_api(start_lon, start_lat, end_lon, end_lat)
        amap_result = self.call_amap_route_api(start_lon, start_lat, end_lon, end_lat)

        if not path_result or not amap_result:
            print("  算路失败，跳过")
            return None

        # 提取实际路线起点和终点
        path_start = self.extract_route_start_point(path_result, 'path')
        amap_start = self.extract_route_start_point(amap_result, 'amap')
        path_end = self.extract_route_end_point(path_result, 'path')
        amap_end = self.extract_route_end_point(amap_result, 'amap')

        if not path_start or not amap_start or not path_end or not amap_end:
            print("  无法提取路线起点或终点，跳过")
            return None

        # 计算起点距离
        original_start_to_path_start = self.calculate_distance(start_lon, start_lat, path_start[0], path_start[1])
        original_start_to_amap_start = self.calculate_distance(start_lon, start_lat, amap_start[0], amap_start[1])
        path_start_to_amap_start = self.calculate_distance(path_start[0], path_start[1], amap_start[0], amap_start[1])

        # 计算终点距离
        original_end_to_path_end = self.calculate_distance(end_lon, end_lat, path_end[0], path_end[1])
        original_end_to_amap_end = self.calculate_distance(end_lon, end_lat, amap_end[0], amap_end[1])
        path_end_to_amap_end = self.calculate_distance(path_end[0], path_end[1], amap_end[0], amap_end[1])

        result = {
            'origin_poi': origin_poi,
            'dest_poi': dest_poi,
            'original_start_point': (start_lon, start_lat),
            'original_end_point': (end_lon, end_lat),
            'path_start_point': path_start,
            'amap_start_point': amap_start,
            'path_end_point': path_end,
            'amap_end_point': amap_end,
            'distances': {
                # 起点距离
                'original_start_to_path_start': original_start_to_path_start,
                'original_start_to_amap_start': original_start_to_amap_start,
                'path_start_to_amap_start': path_start_to_amap_start,
                # 终点距离
                'original_end_to_path_end': original_end_to_path_end,
                'original_end_to_amap_end': original_end_to_amap_end,
                'path_end_to_amap_end': path_end_to_amap_end
            },
            'path_result': path_result,
            'amap_result': amap_result
        }

        print(f"  起点 - 原点到Path: {original_start_to_path_start:.2f}m, 原点到高德: {original_start_to_amap_start:.2f}m, Path到高德: {path_start_to_amap_start:.2f}m")
        print(f"  终点 - 原点到Path: {original_end_to_path_end:.2f}m, 原点到高德: {original_end_to_amap_end:.2f}m, Path到高德: {path_end_to_amap_end:.2f}m")

        return result

    def categorize_distance(self, distance: float) -> str:
        """将距离分类到区间"""
        if distance <= 1:
            return "0-1m"
        elif distance <= 5:
            return "1-5m"
        elif distance <= 10:
            return "5-10m"
        else:
            return "10m+"

    def update_distance_statistics(self, result: Dict):
        """更新距离统计"""
        distances = result['distances']

        # 统计起点距离
        path_start_category = self.categorize_distance(distances['original_start_to_path_start'])
        self.distance_stats[f"original_start_to_path_start_{path_start_category}"] += 1

        amap_start_category = self.categorize_distance(distances['original_start_to_amap_start'])
        self.distance_stats[f"original_start_to_amap_start_{amap_start_category}"] += 1

        start_comparison_category = self.categorize_distance(distances['path_start_to_amap_start'])
        self.distance_stats[f"path_start_to_amap_start_{start_comparison_category}"] += 1

        # 统计终点距离
        path_end_category = self.categorize_distance(distances['original_end_to_path_end'])
        self.distance_stats[f"original_end_to_path_end_{path_end_category}"] += 1

        amap_end_category = self.categorize_distance(distances['original_end_to_amap_end'])
        self.distance_stats[f"original_end_to_amap_end_{amap_end_category}"] += 1

        end_comparison_category = self.categorize_distance(distances['path_end_to_amap_end'])
        self.distance_stats[f"path_end_to_amap_end_{end_comparison_category}"] += 1

    def run_batch_test(self, num_tests: int = 100):
        """运行批量测试"""
        print(f"开始批量测试，共 {num_tests} 组路径")

        if not self.poi_data:
            print("请先加载POI数据")
            return

        successful_tests = 0

        for i in range(num_tests):
            print(f"\n=== 测试 {i+1}/{num_tests} ===")

            # 随机选择起点
            origin_poi = random.choice(self.poi_data)

            # 找到指定距离内的POI作为终点候选
            nearby_pois = self.find_nearby_pois(origin_poi)

            if not nearby_pois:
                print(f"起点 {origin_poi['name']} 周围{self.max_distance}m内没有其他POI，跳过")
                continue

            # 随机选择终点
            dest_poi = random.choice(nearby_pois)

            # 测试路径对
            result = self.test_single_route_pair(origin_poi, dest_poi)

            if result:
                self.test_results.append(result)
                self.update_distance_statistics(result)
                successful_tests += 1
                print(f"  测试成功 ({successful_tests}/{i+1})")

            # 避免API调用过于频繁
            time.sleep(0.5)

        print(f"\n批量测试完成，成功 {successful_tests}/{num_tests} 组")

    def print_statistics(self):
        """打印统计结果"""
        print("\n=== 距离统计结果 ===")

        categories = ["0-1m", "1-5m", "5-10m", "10m+"]

        print("\n起点距离分布:")
        print("原点到Path算路起点距离分布:")
        for category in categories:
            count = self.distance_stats[f"original_start_to_path_start_{category}"]
            print(f"  {category}: {count} 条")

        print("原点到高德算路起点距离分布:")
        for category in categories:
            count = self.distance_stats[f"original_start_to_amap_start_{category}"]
            print(f"  {category}: {count} 条")

        print("Path起点到高德起点距离分布:")
        for category in categories:
            count = self.distance_stats[f"path_start_to_amap_start_{category}"]
            print(f"  {category}: {count} 条")

        print("\n终点距离分布:")
        print("原点到Path算路终点距离分布:")
        for category in categories:
            count = self.distance_stats[f"original_end_to_path_end_{category}"]
            print(f"  {category}: {count} 条")

        print("原点到高德算路终点距离分布:")
        for category in categories:
            count = self.distance_stats[f"original_end_to_amap_end_{category}"]
            print(f"  {category}: {count} 条")

        print("Path终点到高德终点距离分布:")
        for category in categories:
            count = self.distance_stats[f"path_end_to_amap_end_{category}"]
            print(f"  {category}: {count} 条")

    def extract_route_geometry(self, route_data: Dict, api_type: str) -> Optional[Dict]:
        """提取路线几何信息"""
        try:
            if api_type == 'path':
                # Path API结果格式
                if 'paths' in route_data and len(route_data['paths']) > 0:
                    points = route_data['paths'][0].get('points', [])
                    if points:
                        coordinates = [[point['lng'], point['lat']] for point in points]
                        return {
                            "type": "LineString",
                            "coordinates": coordinates
                        }

            elif api_type == 'amap':
                # 高德API结果格式
                if 'route' in route_data and 'paths' in route_data['route']:
                    paths = route_data['route']['paths']
                    if paths and len(paths) > 0:
                        all_coordinates = []
                        steps = paths[0].get('steps', [])
                        for step in steps:
                            polyline = step.get('polyline', '')
                            if polyline:
                                # 解析polyline
                                points = polyline.split(';')
                                for point in points:
                                    coords = point.split(',')
                                    if len(coords) >= 2:
                                        all_coordinates.append([float(coords[0]), float(coords[1])])

                        if all_coordinates:
                            return {
                                "type": "LineString",
                                "coordinates": all_coordinates
                            }

            return None

        except Exception as e:
            print(f"提取{api_type}路线几何失败: {e}")
            return None

    def save_results_to_geojson(self, filename: str):
        """保存结果到GeoJSON文件"""
        print(f"\n保存结果到 {filename}")

        features = []

        for i, result in enumerate(self.test_results):
            # 原点起点 - 包含所有起点距离信息
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['original_start_point'])
                },
                "properties": {
                    "id": i,
                    "type": "original_start",
                    "name": result['origin_poi']['name'],
                    "poi_type": result['origin_poi']['type'],
                    "district": result['origin_poi']['district'],
                    "dest_name": result['dest_poi']['name'],
                    "point_role": "起点",
                    # 起点距离信息
                    "distance_to_path_start": result['distances']['original_start_to_path_start'],
                    "distance_to_amap_start": result['distances']['original_start_to_amap_start'],
                    "path_to_amap_start_distance": result['distances']['path_start_to_amap_start'],
                    # 起点距离分类
                    "path_start_distance_category": self.categorize_distance(result['distances']['original_start_to_path_start']),
                    "amap_start_distance_category": self.categorize_distance(result['distances']['original_start_to_amap_start']),
                    "start_comparison_distance_category": self.categorize_distance(result['distances']['path_start_to_amap_start']),
                    # 起点坐标信息
                    "path_start_coords": list(result['path_start_point']),
                    "amap_start_coords": list(result['amap_start_point'])
                }
            })

            # 原点终点 - 包含所有终点距离信息
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['original_end_point'])
                },
                "properties": {
                    "id": i,
                    "type": "original_end",
                    "name": result['dest_poi']['name'],
                    "poi_type": result['dest_poi']['type'],
                    "district": result['dest_poi'].get('district', ''),
                    "origin_name": result['origin_poi']['name'],
                    "point_role": "终点",
                    # 终点距离信息
                    "distance_to_path_end": result['distances']['original_end_to_path_end'],
                    "distance_to_amap_end": result['distances']['original_end_to_amap_end'],
                    "path_to_amap_end_distance": result['distances']['path_end_to_amap_end'],
                    # 终点距离分类
                    "path_end_distance_category": self.categorize_distance(result['distances']['original_end_to_path_end']),
                    "amap_end_distance_category": self.categorize_distance(result['distances']['original_end_to_amap_end']),
                    "end_comparison_distance_category": self.categorize_distance(result['distances']['path_end_to_amap_end']),
                    # 终点坐标信息
                    "path_end_coords": list(result['path_end_point']),
                    "amap_end_coords": list(result['amap_end_point'])
                }
            })

            # Path算路起点
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['path_start_point'])
                },
                "properties": {
                    "id": i,
                    "type": "path_start",
                    "name": f"Path起点_{result['origin_poi']['name']}",
                    "original_poi_name": result['origin_poi']['name'],
                    "original_poi_type": result['origin_poi']['type'],
                    "original_poi_district": result['origin_poi']['district'],
                    "dest_name": result['dest_poi']['name'],
                    "point_role": "算路起点",
                    "distance_from_original": result['distances']['original_start_to_path_start'],
                    "distance_category": self.categorize_distance(result['distances']['original_start_to_path_start']),
                    "distance_to_amap_start": result['distances']['path_start_to_amap_start'],
                    "original_coords": list(result['original_start_point'])
                }
            })

            # 高德算路起点
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['amap_start_point'])
                },
                "properties": {
                    "id": i,
                    "type": "amap_start",
                    "name": f"高德起点_{result['origin_poi']['name']}",
                    "original_poi_name": result['origin_poi']['name'],
                    "original_poi_type": result['origin_poi']['type'],
                    "original_poi_district": result['origin_poi']['district'],
                    "dest_name": result['dest_poi']['name'],
                    "point_role": "算路起点",
                    "distance_from_original": result['distances']['original_start_to_amap_start'],
                    "distance_category": self.categorize_distance(result['distances']['original_start_to_amap_start']),
                    "distance_to_path_start": result['distances']['path_start_to_amap_start'],
                    "original_coords": list(result['original_start_point'])
                }
            })

            # Path算路终点
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['path_end_point'])
                },
                "properties": {
                    "id": i,
                    "type": "path_end",
                    "name": f"Path终点_{result['dest_poi']['name']}",
                    "original_poi_name": result['dest_poi']['name'],
                    "original_poi_type": result['dest_poi']['type'],
                    "original_poi_district": result['dest_poi'].get('district', ''),
                    "origin_name": result['origin_poi']['name'],
                    "point_role": "算路终点",
                    "distance_from_original": result['distances']['original_end_to_path_end'],
                    "distance_category": self.categorize_distance(result['distances']['original_end_to_path_end']),
                    "distance_to_amap_end": result['distances']['path_end_to_amap_end'],
                    "original_coords": list(result['original_end_point'])
                }
            })

            # 高德算路终点
            features.append({
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": list(result['amap_end_point'])
                },
                "properties": {
                    "id": i,
                    "type": "amap_end",
                    "name": f"高德终点_{result['dest_poi']['name']}",
                    "original_poi_name": result['dest_poi']['name'],
                    "original_poi_type": result['dest_poi']['type'],
                    "original_poi_district": result['dest_poi'].get('district', ''),
                    "origin_name": result['origin_poi']['name'],
                    "point_role": "算路终点",
                    "distance_from_original": result['distances']['original_end_to_amap_end'],
                    "distance_category": self.categorize_distance(result['distances']['original_end_to_amap_end']),
                    "distance_to_path_end": result['distances']['path_end_to_amap_end'],
                    "original_coords": list(result['original_end_point'])
                }
            })

            # Path算路路线
            path_geometry = self.extract_route_geometry(result['path_result'], 'path')
            if path_geometry:
                features.append({
                    "type": "Feature",
                    "geometry": path_geometry,
                    "properties": {
                        "id": i,
                        "type": "path_route",
                        "name": f"Path路线_{result['origin_poi']['name']}",
                        "origin_name": result['origin_poi']['name'],
                        "origin_type": result['origin_poi']['type'],
                        "origin_district": result['origin_poi']['district'],
                        "dest_name": result['dest_poi']['name'],
                        "route_type": "Path算路",
                        "start_coords": list(result['path_start_point']),
                        "end_coords": list(result['path_end_point']),
                        "original_start_coords": list(result['original_start_point']),
                        "original_end_coords": list(result['original_end_point'])
                    }
                })

            # 高德算路路线
            amap_geometry = self.extract_route_geometry(result['amap_result'], 'amap')
            if amap_geometry:
                features.append({
                    "type": "Feature",
                    "geometry": amap_geometry,
                    "properties": {
                        "id": i,
                        "type": "amap_route",
                        "name": f"高德路线_{result['origin_poi']['name']}",
                        "origin_name": result['origin_poi']['name'],
                        "origin_type": result['origin_poi']['type'],
                        "origin_district": result['origin_poi']['district'],
                        "dest_name": result['dest_poi']['name'],
                        "route_type": "高德算路",
                        "start_coords": list(result['amap_start_point']),
                        "end_coords": list(result['amap_end_point']),
                        "original_start_coords": list(result['original_start_point']),
                        "original_end_coords": list(result['original_end_point'])
                    }
                })

        geojson = {
            "type": "FeatureCollection",
            "features": features
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(geojson, f, ensure_ascii=False, indent=2)

        print(f"已保存 {len(features)} 个特征到 {filename}")

        # 统计特征类型
        feature_counts = {}
        for feature in features:
            feature_type = feature['properties']['type']
            feature_counts[feature_type] = feature_counts.get(feature_type, 0) + 1

        print("特征类型统计:")
        for feature_type, count in feature_counts.items():
            print(f"  {feature_type}: {count} 个")

def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='双路径算路比较测试')
    parser.add_argument('--num-tests', '-n', type=int, default=10,
                       help='算路测试用例个数 (默认: 10)')
    parser.add_argument('--poi-file', '-f', type=str, default='shanghai_poi_amap_10k.csv',
                       help='POI数据文件路径 (默认: shanghai_poi_amap_10k.csv)')
    parser.add_argument('--output', '-o', type=str, default='route_comparison_results.geojson',
                       help='输出GeoJSON文件路径 (默认: route_comparison_results.geojson)')
    parser.add_argument('--max-distance', '-d', type=float, default=5000,
                       help='最大搜索距离(米) (默认: 5000)')

    args = parser.parse_args()

    print("双路径算路比较测试")
    print(f"测试用例数量: {args.num_tests}")
    print(f"POI数据文件: {args.poi_file}")
    print(f"最大搜索距离: {args.max_distance}m")
    print(f"输出文件: {args.output}")
    print("-" * 50)

    # 创建测试实例
    comparison = RouteComparison(max_distance=args.max_distance)

    # 加载POI数据
    comparison.load_poi_data(args.poi_file)

    # 运行批量测试
    comparison.run_batch_test(args.num_tests)

    # 打印统计结果
    comparison.print_statistics()

    # 保存结果到GeoJSON
    comparison.save_results_to_geojson(args.output)

    print("\n测试完成！")

if __name__ == "__main__":
    main()
