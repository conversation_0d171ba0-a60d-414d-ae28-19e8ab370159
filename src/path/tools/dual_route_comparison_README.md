# 双路径算路比较测试工具

## 功能说明

这个工具用于比较Path算路引擎和高德地图算路API的结果，主要功能包括：

1. 使用上海POI数据作为起点和终点进行算路测试
2. 同时调用Path算路服务器和高德地图API
3. 比较两种算路结果的起点坐标差异
4. 统计距离分布并生成可视化GeoJSON文件

## 使用方法

### 基本用法

```bash
# 使用默认参数（10个测试用例）
python3 dual_route_comparison.py

# 指定测试用例数量
python3 dual_route_comparison.py -n 20

# 指定POI数据文件
python3 dual_route_comparison.py -f shanghai_poi_amap_10k.csv

# 指定输出文件
python3 dual_route_comparison.py -o my_results.geojson

# 指定最大搜索距离（米）
python3 dual_route_comparison.py -d 3000
```

### 参数说明

- `--num-tests, -n`: 算路测试用例个数（默认：10）
- `--poi-file, -f`: POI数据文件路径（默认：shanghai_poi_amap_10k.csv）
- `--output, -o`: 输出GeoJSON文件路径（默认：route_comparison_results.geojson）
- `--max-distance, -d`: 最大搜索距离，单位米（默认：5000）

### 完整示例

```bash
python3 dual_route_comparison.py -n 50 -f shanghai_poi_amap_10k.csv -o comparison_50_tests.geojson -d 3000
```

## 前置条件

### 1. 启动Path算路服务器

```bash
cd /home/<USER>/dlc/map_engine
./distribution/bin/simple_route_server /home/<USER>/dlc/map_engine/src/path/config/path.yaml /home/<USER>/dlc/map_engine/distribution/data/route 8081
```

### 2. 确保POI数据文件存在

确保 `shanghai_poi_amap_10k.csv` 文件在当前目录下，该文件应包含以下字段：
- POIName: POI名称
- POIType: POI类型
- DistLon: 经度
- DistLat: 纬度
- DistName: 所属区域
- POIAddress: 地址（可选）

## 输出结果

### 1. 控制台输出

```
双路径算路比较测试
测试用例数量: 10
POI数据文件: shanghai_poi_amap_10k.csv
最大搜索距离: 5000m
输出文件: route_comparison_results.geojson
--------------------------------------------------

=== 测试 1/10 ===
测试路径: 西岑(地铁站) -> 金泽镇淀湖村陈港卫生室
  原点到Path起点: 14.69m
  原点到高德起点: 15.80m
  Path起点到高德起点: 2.12m
  测试成功 (1/1)

...

=== 距离统计结果 ===

原点到Path算路起点距离分布:
  0-1m: 0 条
  1-5m: 1 条
  5-10m: 1 条
  10m+: 7 条

原点到高德算路起点距离分布:
  0-1m: 0 条
  1-5m: 0 条
  5-10m: 3 条
  10m+: 6 条

Path起点到高德起点距离分布:
  0-1m: 4 条
  1-5m: 4 条
  5-10m: 0 条
  10m+: 1 条
```

### 2. GeoJSON输出文件

生成的GeoJSON文件包含九种类型的特征：

**点特征：**
- **original_start**: 原始起点POI（绿色）
- **original_end**: 原始终点POI（橙色）
- **path_start**: Path算路的实际起点（蓝色）
- **amap_start**: 高德算路的实际起点（红色）
- **path_end**: Path算路的实际终点（深蓝色）
- **amap_end**: 高德算路的实际终点（深红色）

**线特征：**
- **path_route**: Path算路的完整路线（蓝色线条）
- **amap_route**: 高德算路的完整路线（红色线条）

#### 原点起点(original_start)属性字段：
- `id`: 测试用例ID
- `type`: 点类型 ("original_start")
- `name`: POI名称
- `poi_type`: POI类型
- `district`: 所属区域
- `dest_name`: 目标点名称
- `point_role`: 点角色 ("起点")
- `distance_to_path_start`: 原点到Path起点距离(米)
- `distance_to_amap_start`: 原点到高德起点距离(米)
- `path_to_amap_start_distance`: Path起点到高德起点距离(米)
- `path_start_distance_category`: Path起点距离分类 ("0-1m", "1-5m", "5-10m", "10m+")
- `amap_start_distance_category`: 高德起点距离分类
- `start_comparison_distance_category`: 起点差异距离分类
- `path_start_coords`: Path起点坐标 [经度, 纬度]
- `amap_start_coords`: 高德起点坐标 [经度, 纬度]

#### 原点终点(original_end)属性字段：
- `id`: 测试用例ID
- `type`: 点类型 ("original_end")
- `name`: POI名称
- `poi_type`: POI类型
- `district`: 所属区域
- `origin_name`: 起点名称
- `point_role`: 点角色 ("终点")
- `distance_to_path_end`: 原点到Path终点距离(米)
- `distance_to_amap_end`: 原点到高德终点距离(米)
- `path_to_amap_end_distance`: Path终点到高德终点距离(米)
- `path_end_distance_category`: Path终点距离分类
- `amap_end_distance_category`: 高德终点距离分类
- `end_comparison_distance_category`: 终点差异距离分类
- `path_end_coords`: Path终点坐标 [经度, 纬度]
- `amap_end_coords`: 高德终点坐标 [经度, 纬度]

#### 算路起点(path_start/amap_start)属性字段：
- `id`: 测试用例ID
- `type`: 点类型 ("path_start" 或 "amap_start")
- `name`: 起点名称
- `original_poi_name`: 原始POI名称
- `original_poi_type`: 原始POI类型
- `original_poi_district`: 原始POI区域
- `dest_name`: 目标点名称
- `point_role`: 点角色 ("算路起点")
- `distance_from_original`: 距离原点的距离(米)
- `distance_category`: 距离分类
- `distance_to_amap_start`/`distance_to_path_start`: 到另一个起点的距离
- `original_coords`: 原点坐标 [经度, 纬度]

#### 算路终点(path_end/amap_end)属性字段：
- `id`: 测试用例ID
- `type`: 点类型 ("path_end" 或 "amap_end")
- `name`: 终点名称
- `original_poi_name`: 原始POI名称
- `original_poi_type`: 原始POI类型
- `original_poi_district`: 原始POI区域
- `origin_name`: 起点名称
- `point_role`: 点角色 ("算路终点")
- `distance_from_original`: 距离原点的距离(米)
- `distance_category`: 距离分类
- `distance_to_amap_end`/`distance_to_path_end`: 到另一个终点的距离
- `original_coords`: 原点坐标 [经度, 纬度]

#### 路线(path_route/amap_route)属性字段：
- `id`: 测试用例ID
- `type`: 路线类型 ("path_route" 或 "amap_route")
- `name`: 路线名称
- `origin_name`: 起点POI名称
- `origin_type`: 起点POI类型
- `origin_district`: 起点POI区域
- `dest_name`: 目标点名称
- `route_type`: 算路类型 ("Path算路" 或 "高德算路")
- `start_coords`: 实际算路起点坐标 [经度, 纬度]
- `end_coords`: 实际算路终点坐标 [经度, 纬度]
- `original_start_coords`: 原始起点POI坐标 [经度, 纬度]
- `original_end_coords`: 原始终点POI坐标 [经度, 纬度]
- **geometry**: LineString几何数据，包含完整的路线坐标点序列

### 3. 结果分析工具

使用 `analyze_route_results.py` 脚本可以对GeoJSON结果进行详细分析：

```bash
# 基本分析
python3 analyze_route_results.py enhanced_results.geojson

# 设置异常点阈值
python3 analyze_route_results.py enhanced_results.geojson --threshold 30.0
```

分析报告包括：
- 按POI类型和区域的距离分布统计
- 距离分类统计
- 异常点识别
- 精度评估和总结报告
- 路线特征分析（路线数量、路线点数统计）

### 4. 可视化查看

可以将生成的GeoJSON文件导入到以下工具中查看：
- QGIS
- 在线GeoJSON查看器
- Leaflet/OpenLayers等Web地图库

#### QGIS中的分类显示建议：

**点特征显示：**
- 按 `type` 字段分类显示不同颜色
- 按 `path_distance_category` 字段设置符号大小
- 使用 `distance_to_path_start` 字段进行渐变色显示

**线特征显示：**
- Path路线使用蓝色线条，线宽2-3px
- 高德路线使用红色线条，线宽2-3px
- 可以按 `route_type` 字段设置不同的线型（实线/虚线）
- 使用透明度50-70%以便重叠路线可见

**图层组织建议：**
1. 路线图层（底层）
2. 起点图层（中层）
3. 原点图层（顶层）

## 距离统计说明

工具会统计三种距离：

1. **原点到Path起点距离**: POI坐标与Path算路实际起点的距离
2. **原点到高德起点距离**: POI坐标与高德算路实际起点的距离  
3. **Path起点到高德起点距离**: 两个算路引擎起点之间的距离

距离分为四个区间：
- 0-1m: 非常精确
- 1-5m: 较精确
- 5-10m: 一般精确
- 10m+: 精确度较低

## 注意事项

1. 确保Path算路服务器正在运行且端口为8081
2. 确保网络可以访问高德地图API
3. 测试过程中会有API调用延迟，请耐心等待
4. 如果某个路径算路失败，会跳过该测试用例
5. 建议测试用例数量不要过大，避免API调用频率过高

## 故障排除

### Path服务器连接失败
```
Path API调用异常: HTTPConnectionPool(host='localhost', port=8081): Max retries exceeded
```
**解决方案**: 检查Path服务器是否正在运行，端口是否正确

### 高德API调用失败
```
高德API错误: INVALID_USER_KEY
```
**解决方案**: 检查API密钥是否正确，是否有调用权限

### 无法提取路线起点
```
无法提取路线起点，跳过
```
**解决方案**: 检查API返回的数据格式是否正确，可能需要调整解析逻辑
