#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成上海市内1万个POI坐标点
通过高德API获取真实POI数据
包括商场、小区、学校、医院等，分布均匀
输出CSV和GeoJSON格式文件
"""

import random
import json
import csv
import math
import requests
import time
from typing import List, Dict, Tuple

# 高德API配置
AMAP_CONFIG = {
    'key': 'c6dbca449fbd6baadc69b8540565ef59',
    'base_url': 'https://restapi.amap.com/v3',
    'place_search_url': 'https://restapi.amap.com/v3/place/text',
    'around_search_url': 'https://restapi.amap.com/v3/place/around',
    'polygon_search_url': 'https://restapi.amap.com/v3/place/polygon'
}

# 备用方案：真实的上海POI数据
REAL_SHANGHAI_POIS = {
    '购物中心': [
        {'name': '上海环球港', 'district': '普陀区', 'lon': 121.397, 'lat': 31.250},
        {'name': '正大广场', 'district': '浦东新区', 'lon': 121.506, 'lat': 31.239},
        {'name': '来福士广场', 'district': '黄浦区', 'lon': 121.474, 'lat': 31.229},
        {'name': '港汇恒隆广场', 'district': '徐汇区', 'lon': 121.436, 'lat': 31.201},
        {'name': '大悦城', 'district': '静安区', 'lon': 121.448, 'lat': 31.229},
        {'name': '万达广场', 'district': '杨浦区', 'lon': 121.526, 'lat': 31.259},
        {'name': '百联世纪购物中心', 'district': '浦东新区', 'lon': 121.567, 'lat': 31.245},
        {'name': '龙之梦购物中心', 'district': '长宁区', 'lon': 121.424, 'lat': 31.220}
    ],
    '地铁站': [
        {'name': '人民广场站', 'district': '黄浦区', 'lon': 121.475, 'lat': 31.232},
        {'name': '陆家嘴站', 'district': '浦东新区', 'lon': 121.505, 'lat': 31.239},
        {'name': '徐家汇站', 'district': '徐汇区', 'lon': 121.436, 'lat': 31.201},
        {'name': '静安寺站', 'district': '静安区', 'lon': 121.448, 'lat': 31.229},
        {'name': '中山公园站', 'district': '长宁区', 'lon': 121.424, 'lat': 31.220},
        {'name': '五角场站', 'district': '杨浦区', 'lon': 121.526, 'lat': 31.259},
        {'name': '虹桥火车站', 'district': '闵行区', 'lon': 121.320, 'lat': 31.197}
    ],
    '医院': [
        {'name': '华山医院', 'district': '静安区', 'lon': 121.442, 'lat': 31.239},
        {'name': '瑞金医院', 'district': '黄浦区', 'lon': 121.474, 'lat': 31.213},
        {'name': '中山医院', 'district': '徐汇区', 'lon': 121.436, 'lat': 31.201},
        {'name': '仁济医院', 'district': '浦东新区', 'lon': 121.506, 'lat': 31.239},
        {'name': '第九人民医院', 'district': '黄浦区', 'lon': 121.474, 'lat': 31.229}
    ],
    '学校': [
        {'name': '复旦大学', 'district': '杨浦区', 'lon': 121.526, 'lat': 31.259},
        {'name': '上海交通大学', 'district': '闵行区', 'lon': 121.436, 'lat': 31.201},
        {'name': '同济大学', 'district': '杨浦区', 'lon': 121.526, 'lat': 31.259},
        {'name': '华东师范大学', 'district': '普陀区', 'lon': 121.397, 'lat': 31.250},
        {'name': '上海大学', 'district': '宝山区', 'lon': 121.489, 'lat': 31.398}
    ]
}

# 上海市边界（经纬度范围）
SHANGHAI_BOUNDS = {
    'min_lon': 120.85,  # 最西边
    'max_lon': 122.12,  # 最东边
    'min_lat': 30.67,   # 最南边
    'max_lat': 31.87    # 最北边
}

# 上海市各区信息
SHANGHAI_DISTRICTS = [
    {'name': '黄浦区', 'code': '310101', 'center_lon': 121.484, 'center_lat': 31.231},
    {'name': '徐汇区', 'code': '310104', 'center_lon': 121.437, 'center_lat': 31.188},
    {'name': '长宁区', 'code': '310105', 'center_lon': 121.424, 'center_lat': 31.220},
    {'name': '静安区', 'code': '310106', 'center_lon': 121.448, 'center_lat': 31.229},
    {'name': '普陀区', 'code': '310107', 'center_lon': 121.397, 'center_lat': 31.250},
    {'name': '虹口区', 'code': '310109', 'center_lon': 121.505, 'center_lat': 31.265},
    {'name': '杨浦区', 'code': '310110', 'center_lon': 121.526, 'center_lat': 31.259},
    {'name': '闵行区', 'code': '310112', 'center_lon': 121.381, 'center_lat': 31.113},
    {'name': '宝山区', 'code': '310113', 'center_lon': 121.489, 'center_lat': 31.398},
    {'name': '嘉定区', 'code': '310114', 'center_lon': 121.250, 'center_lat': 31.383},
    {'name': '浦东新区', 'code': '310115', 'center_lon': 121.567, 'center_lat': 31.245},
    {'name': '金山区', 'code': '310116', 'center_lon': 121.342, 'center_lat': 30.742},
    {'name': '松江区', 'code': '310117', 'center_lon': 121.228, 'center_lat': 31.032},
    {'name': '青浦区', 'code': '310118', 'center_lon': 121.113, 'center_lat': 31.151},
    {'name': '奉贤区', 'code': '310120', 'center_lon': 121.458, 'center_lat': 30.912},
    {'name': '崇明区', 'code': '310151', 'center_lon': 121.397, 'center_lat': 31.623}
]

# POI类型定义 - 高德API搜索关键词
POI_TYPES = [
    {'type': '购物中心', 'weight': 0.15, 'keywords': ['购物中心', '商场', '百货', '万达', '大悦城', '环球港'], 'typecode': '060000'},
    {'type': '住宅小区', 'weight': 0.35, 'keywords': ['住宅小区', '小区', '花园', '新村', '公寓', '家园'], 'typecode': '120300'},
    {'type': '学校', 'weight': 0.12, 'keywords': ['学校', '小学', '中学', '大学', '学院'], 'typecode': '141200'},
    {'type': '医院', 'weight': 0.08, 'keywords': ['医院', '诊所', '卫生院', '卫生服务中心'], 'typecode': '090100'},
    {'type': '地铁站', 'weight': 0.10, 'keywords': ['地铁站', '轨道交通'], 'typecode': '150500'},
    {'type': '公园', 'weight': 0.05, 'keywords': ['公园', '绿地', '广场', '森林公园'], 'typecode': '110101'},
    {'type': '酒店', 'weight': 0.06, 'keywords': ['酒店', '宾馆', '旅馆'], 'typecode': '100000'},
    {'type': '银行', 'weight': 0.04, 'keywords': ['银行', 'ATM'], 'typecode': '160000'},
    {'type': '餐厅', 'weight': 0.05, 'keywords': ['餐厅', '饭店', '美食', '火锅'], 'typecode': '050000'}
]

def call_amap_api(url: str, params: Dict) -> Dict:
    """调用高德API"""
    # 添加API密钥
    params['key'] = AMAP_CONFIG['key']

    # 发送请求
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # 检查HTTP错误
        result = response.json()

        # 检查API返回状态
        if result.get('status') == '1':
            return result
        else:
            print(f"API错误: {result.get('info')}")
            return {'status': '0', 'info': result.get('info'), 'pois': []}
    except Exception as e:
        print(f"请求错误: {e}")
        return {'status': '0', 'info': str(e), 'pois': []}

def search_pois_by_keyword(keyword: str, city: str = '上海', types: str = None, page: int = 1, offset: int = 20) -> List[Dict]:
    """通过关键词搜索POI"""
    url = AMAP_CONFIG['place_search_url']
    params = {
        'keywords': keyword,
        'city': city,
        'offset': offset,  # 每页记录数
        'page': page,      # 当前页数
        'extensions': 'all'  # 返回详细信息
    }

    # 如果指定了类型编码，添加到参数中
    if types:
        params['types'] = types

    result = call_amap_api(url, params)

    if result.get('status') == '1':
        return result.get('pois', [])
    else:
        return []

def search_pois_in_polygon(keywords: str, polygon: str, types: str = None) -> List[Dict]:
    """在多边形区域内搜索POI"""
    url = AMAP_CONFIG['polygon_search_url']
    params = {
        'keywords': keywords,
        'polygon': polygon,
        'extensions': 'all'  # 返回详细信息
    }

    # 如果指定了类型编码，添加到参数中
    if types:
        params['types'] = types

    result = call_amap_api(url, params)

    if result.get('status') == '1':
        return result.get('pois', [])
    else:
        return []

def search_pois_around_point(location: str, keywords: str = '', radius: int = 5000, types: str = None) -> List[Dict]:
    """搜索指定坐标周围的POI"""
    url = AMAP_CONFIG['around_search_url']
    params = {
        'location': location,  # 中心点坐标
        'keywords': keywords,  # 关键词
        'radius': radius,      # 查询半径，单位：米
        'extensions': 'all'    # 返回详细信息
    }

    # 如果指定了类型编码，添加到参数中
    if types:
        params['types'] = types

    result = call_amap_api(url, params)

    if result.get('status') == '1':
        return result.get('pois', [])
    else:
        return []

def parse_poi_data(poi: Dict) -> Dict:
    """解析高德API返回的POI数据"""
    # 解析坐标
    location = poi.get('location', '').split(',')
    if len(location) != 2:
        return None

    try:
        longitude = float(location[0])
        latitude = float(location[1])
    except ValueError:
        return None

    # 获取区域信息
    adname = poi.get('adname', '')  # 区名
    district = assign_district_to_coordinate(longitude, latitude)

    return {
        'name': poi.get('name', ''),
        'type': poi.get('type', ''),
        'address': poi.get('address', ''),
        'longitude': round(longitude, 6),
        'latitude': round(latitude, 6),
        'district': adname if adname else district['name'],
        'district_code': district['code'],
        'typecode': poi.get('typecode', ''),
        'tel': poi.get('tel', ''),
        'business_area': poi.get('business_area', '')
    }

def assign_district_to_coordinate(lon: float, lat: float) -> Dict:
    """根据坐标分配最近的区"""
    min_distance = float('inf')
    closest_district = SHANGHAI_DISTRICTS[0]

    for district in SHANGHAI_DISTRICTS:
        # 计算距离（简化的欧几里得距离）
        distance = math.sqrt((lon - district['center_lon'])**2 + (lat - district['center_lat'])**2)
        if distance < min_distance:
            min_distance = distance
            closest_district = district

    return closest_district

def generate_realistic_pois(target_count: int = 10000) -> List[Dict]:
    """生成真实的POI数据（混合API和本地数据）"""
    all_pois = []
    collected_names = set()

    print("开始生成真实的上海POI数据...")

    # 首先尝试从高德API获取数据
    api_success = False
    try:
        # 测试API连接
        test_result = call_amap_api(AMAP_CONFIG['place_search_url'], {
            'keywords': '购物中心',
            'city': '上海',
            'offset': 1,
            'page': 1
        })

        if test_result.get('status') == '1':
            api_success = True
            print("高德API连接成功，使用API数据...")
        else:
            print(f"高德API连接失败: {test_result.get('info')}")
            print("使用本地真实POI数据...")
    except Exception as e:
        print(f"API测试失败: {e}")
        print("使用本地真实POI数据...")

    if api_success:
        # 使用API获取数据
        all_pois = collect_pois_from_amap_api(target_count)

        # 如果API获取的数量不足，用本地数据补充
        # if len(all_pois) < target_count:
        #     print(f"\nAPI获取到 {len(all_pois)} 个POI，不足目标数量 {target_count}")
        #     print("使用本地数据补充...")

        #     # 获取已有的POI名称，避免重复
        #     existing_names = {poi['name'] for poi in all_pois}

        #     # 生成补充数据
        #     remaining_count = target_count - len(all_pois)
        #     supplement_pois = generate_pois_from_local_data(remaining_count, existing_names)

        #     all_pois.extend(supplement_pois)
        #     print(f"补充了 {len(supplement_pois)} 个POI，总计 {len(all_pois)} 个")
    else:
        # 使用本地真实POI数据生成
        all_pois = generate_pois_from_local_data(target_count)

    return all_pois

def collect_pois_from_amap_api(target_count: int) -> List[Dict]:
    """从高德API收集POI数据"""
    all_pois = []
    collected_names = set()

    # 为每种POI类型收集数据
    for poi_type in POI_TYPES:
        target_count_for_type = int(target_count * poi_type['weight'])
        print(f"\n收集 {poi_type['type']} 类型POI，目标数量: {target_count_for_type}")

        type_pois = []

        # 策略1: 使用关键词搜索
        for keyword in poi_type['keywords']:
            if len(type_pois) >= target_count_for_type:
                break

            print(f"  搜索关键词: {keyword}")

            # 分页搜索，增加页数
            for page in range(1, 11):  # 增加到10页
                if len(type_pois) >= target_count_for_type:
                    break

                pois = search_pois_by_keyword(
                    keyword=keyword,
                    city='上海',
                    types=poi_type.get('typecode'),
                    page=page,
                    offset=20
                )

                if not pois:
                    break

                # 解析POI数据
                for poi in pois:
                    if len(type_pois) >= target_count_for_type:
                        break

                    parsed_poi = parse_poi_data(poi)
                    if parsed_poi and parsed_poi['name'] not in collected_names:
                        if (SHANGHAI_BOUNDS['min_lon'] <= parsed_poi['longitude'] <= SHANGHAI_BOUNDS['max_lon'] and
                            SHANGHAI_BOUNDS['min_lat'] <= parsed_poi['latitude'] <= SHANGHAI_BOUNDS['max_lat']):

                            parsed_poi['type'] = poi_type['type']
                            type_pois.append(parsed_poi)
                            collected_names.add(parsed_poi['name'])

                time.sleep(0.05)  # 减少延迟

        # 策略2: 如果数量不足，使用周边搜索补充
        if len(type_pois) < target_count_for_type:
            print(f"  使用周边搜索补充 {poi_type['type']} POI...")

            # 在各区中心周边搜索
            for district in SHANGHAI_DISTRICTS:
                if len(type_pois) >= target_count_for_type:
                    break

                location = f"{district['center_lon']},{district['center_lat']}"

                # 使用不同的关键词和半径
                for keyword in poi_type['keywords'][:3]:  # 只用前3个关键词
                    if len(type_pois) >= target_count_for_type:
                        break

                    for radius in [1000, 3000, 5000]:  # 不同半径
                        if len(type_pois) >= target_count_for_type:
                            break

                        pois = search_pois_around_point(
                            location=location,
                            keywords=keyword,
                            radius=radius,
                            types=poi_type.get('typecode')
                        )

                        for poi in pois:
                            if len(type_pois) >= target_count_for_type:
                                break

                            parsed_poi = parse_poi_data(poi)
                            if parsed_poi and parsed_poi['name'] not in collected_names:
                                if (SHANGHAI_BOUNDS['min_lon'] <= parsed_poi['longitude'] <= SHANGHAI_BOUNDS['max_lon'] and
                                    SHANGHAI_BOUNDS['min_lat'] <= parsed_poi['latitude'] <= SHANGHAI_BOUNDS['max_lat']):

                                    parsed_poi['type'] = poi_type['type']
                                    type_pois.append(parsed_poi)
                                    collected_names.add(parsed_poi['name'])

                        time.sleep(0.05)

        print(f"  实际收集到 {len(type_pois)} 个 {poi_type['type']} POI")
        all_pois.extend(type_pois)

    # 策略3: 如果总数量仍然不足，使用类型编码搜索补充
    if len(all_pois) < target_count * 0.8:  # 如果少于目标的80%
        print(f"\n总数量不足，使用类型编码搜索补充...")

        for poi_type in POI_TYPES:
            if len(all_pois) >= target_count:
                break

            # 使用类型编码搜索
            pois = search_pois_by_keyword(
                keyword='',  # 空关键词
                city='上海',
                types=poi_type.get('typecode'),
                page=1,
                offset=50  # 增加每页数量
            )

            for poi in pois:
                if len(all_pois) >= target_count:
                    break

                parsed_poi = parse_poi_data(poi)
                if parsed_poi and parsed_poi['name'] not in collected_names:
                    if (SHANGHAI_BOUNDS['min_lon'] <= parsed_poi['longitude'] <= SHANGHAI_BOUNDS['max_lon'] and
                        SHANGHAI_BOUNDS['min_lat'] <= parsed_poi['latitude'] <= SHANGHAI_BOUNDS['max_lat']):

                        parsed_poi['type'] = poi_type['type']
                        all_pois.append(parsed_poi)
                        collected_names.add(parsed_poi['name'])

            time.sleep(0.1)

    return all_pois

def generate_pois_from_local_data(target_count: int, existing_names: set = None) -> List[Dict]:
    """使用本地真实POI数据生成"""
    if existing_names is None:
        existing_names = set()

    all_pois = []

    print("使用本地真实POI数据库生成坐标点...")

    # 为每种POI类型生成数据
    for poi_type in POI_TYPES:
        target_count_for_type = int(target_count * poi_type['weight'])
        print(f"生成 {poi_type['type']} 类型POI，目标数量: {target_count_for_type}")

        # 获取该类型的真实POI基础数据
        base_pois = REAL_SHANGHAI_POIS.get(poi_type['type'], [])

        type_pois = []
        attempts = 0
        max_attempts = target_count_for_type * 3  # 最多尝试3倍数量

        while len(type_pois) < target_count_for_type and attempts < max_attempts:
            attempts += 1

            if base_pois:
                # 基于真实POI生成变体
                base_poi = random.choice(base_pois)

                # 在基础坐标附近生成新坐标
                lon_offset = random.uniform(-0.05, 0.05)  # 约5公里范围
                lat_offset = random.uniform(-0.05, 0.05)

                new_lon = base_poi['lon'] + lon_offset
                new_lat = base_poi['lat'] + lat_offset

                # 确保坐标在上海市范围内
                new_lon = max(SHANGHAI_BOUNDS['min_lon'], min(SHANGHAI_BOUNDS['max_lon'], new_lon))
                new_lat = max(SHANGHAI_BOUNDS['min_lat'], min(SHANGHAI_BOUNDS['max_lat'], new_lat))

                # 生成POI名称变体
                poi_name = generate_poi_name_variant(base_poi['name'], poi_type['type'], attempts)

                # 检查名称是否重复
                if poi_name in existing_names:
                    continue

                # 分配区域
                district = assign_district_to_coordinate(new_lon, new_lat)

                poi_point = {
                    'name': poi_name,
                    'type': poi_type['type'],
                    'longitude': round(new_lon, 6),
                    'latitude': round(new_lat, 6),
                    'district': district['name'],
                    'district_code': district['code'],
                    'address': f"{district['name']}{poi_name}",
                    'tel': '',
                    'typecode': poi_type.get('typecode', ''),
                    'business_area': ''
                }
            else:
                # 如果没有基础数据，随机生成
                district = random.choice(SHANGHAI_DISTRICTS)
                lon, lat = generate_coordinate_near_district(district)
                poi_name = generate_poi_name_variant('', poi_type['type'], attempts)

                # 检查名称是否重复
                if poi_name in existing_names:
                    continue

                poi_point = {
                    'name': poi_name,
                    'type': poi_type['type'],
                    'longitude': round(lon, 6),
                    'latitude': round(lat, 6),
                    'district': district['name'],
                    'district_code': district['code'],
                    'address': f"{district['name']}{poi_name}",
                    'tel': '',
                    'typecode': poi_type.get('typecode', ''),
                    'business_area': ''
                }

            type_pois.append(poi_point)
            existing_names.add(poi_name)

        all_pois.extend(type_pois)

    return all_pois

def generate_coordinate_near_district(district: Dict, radius_km: float = 5.0) -> Tuple[float, float]:
    """在指定区域中心附近生成坐标"""
    radius_deg = radius_km / 111.0

    angle = random.uniform(0, 2 * math.pi)
    distance = random.uniform(0, radius_deg)

    lon = district['center_lon'] + distance * math.cos(angle)
    lat = district['center_lat'] + distance * math.sin(angle)

    # 确保坐标在上海市边界内
    lon = max(SHANGHAI_BOUNDS['min_lon'], min(SHANGHAI_BOUNDS['max_lon'], lon))
    lat = max(SHANGHAI_BOUNDS['min_lat'], min(SHANGHAI_BOUNDS['max_lat'], lat))

    return lon, lat

def generate_poi_name_variant(base_name: str, poi_type: str, index: int) -> str:
    """生成POI名称变体"""
    if poi_type == '购物中心':
        prefixes = ['万达', '大悦城', '环球港', '正大', '来福士', '恒隆', '百联', '新世界', '港汇', '龙之梦']
        suffixes = ['广场', '购物中心', '商场', '百货', '城', '港']
        if base_name:
            return f"{random.choice(prefixes)}{random.choice(suffixes)}"
        else:
            return f"{random.choice(prefixes)}{random.choice(suffixes)}"

    elif poi_type == '住宅小区':
        prefixes = ['阳光', '绿地', '万科', '保利', '中海', '华润', '龙湖', '金地', '招商', '碧桂园']
        suffixes = ['花园', '苑', '新村', '公寓', '家园', '城', '府', '湾', '里', '庭']
        return f"{random.choice(prefixes)}{random.choice(suffixes)}"

    elif poi_type == '学校':
        if '大学' in base_name or '学院' in base_name:
            return base_name
        else:
            districts = ['黄浦', '徐汇', '长宁', '静安', '普陀', '虹口', '杨浦', '闵行', '宝山', '嘉定']
            schools = ['小学', '中学', '实验学校', '外国语学校']
            return f"{random.choice(districts)}{random.choice(schools)}"

    elif poi_type == '地铁站':
        if base_name:
            return base_name
        else:
            stations = ['人民广场', '南京东路', '陆家嘴', '徐家汇', '中山公园', '静安寺', '五角场']
            return f"{random.choice(stations)}站"

    elif poi_type == '医院':
        if base_name:
            return base_name
        else:
            districts = ['黄浦', '徐汇', '长宁', '静安', '普陀', '虹口', '杨浦', '闵行']
            hospitals = ['人民医院', '中心医院', '第一医院', '中医院', '妇幼保健院']
            return f"{random.choice(districts)}{random.choice(hospitals)}"

    else:
        # 其他类型
        districts = ['黄浦', '徐汇', '长宁', '静安', '普陀', '虹口', '杨浦', '闵行']
        return f"{random.choice(districts)}{poi_type}{index % 100}"

def save_to_csv(poi_points: List[Dict], filename: str):
    """保存为CSV格式"""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['ProvName', 'ProvCode', 'ProvLon', 'ProvLat', 'CityName', 'CityCode',
                     'CityLon', 'CityLat', 'DistName', 'DistCode', 'DistLon', 'DistLat',
                     'POIName', 'POIType', 'POIAddress', 'POITel']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()

        # 上海市信息
        shanghai_info = {
            'ProvName': '上海市',
            'ProvCode': '310000',
            'ProvLon': 121.473,
            'ProvLat': 31.230,
            'CityName': '上海市',
            'CityCode': '310000',
            'CityLon': 121.473,
            'CityLat': 31.230
        }

        for poi in poi_points:
            row = shanghai_info.copy()
            row.update({
                'DistName': poi['district'],
                'DistCode': poi['district_code'],
                'DistLon': poi['longitude'],
                'DistLat': poi['latitude'],
                'POIName': poi['name'],
                'POIType': poi['type'],
                'POIAddress': poi.get('address', ''),
                'POITel': poi.get('tel', '')
            })
            writer.writerow(row)

def save_to_geojson(poi_points: List[Dict], filename: str):
    """保存为GeoJSON格式"""
    features = []

    for poi in poi_points:
        feature = {
            "type": "Feature",
            "geometry": {
                "type": "Point",
                "coordinates": [poi['longitude'], poi['latitude']]
            },
            "properties": {
                "name": poi['name'],
                "type": poi['type'],
                "district": poi['district'],
                "district_code": poi['district_code'],
                "address": poi.get('address', ''),
                "tel": poi.get('tel', ''),
                "typecode": poi.get('typecode', ''),
                "business_area": poi.get('business_area', '')
            }
        }
        features.append(feature)

    geojson = {
        "type": "FeatureCollection",
        "features": features
    }

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(geojson, f, ensure_ascii=False, indent=2)

def main():
    """主函数"""
    print("开始生成上海市POI坐标点...")
    print("尝试使用高德地图API获取真实POI数据...")
    print(f"API密钥: {AMAP_CONFIG['key']}")

    # 生成真实POI点（API或本地数据）
    poi_points = generate_realistic_pois(10000)

    print(f"\n成功获取 {len(poi_points)} 个POI点")

    # 统计各类型POI数量
    type_counts = {}
    for poi in poi_points:
        poi_type = poi['type']
        type_counts[poi_type] = type_counts.get(poi_type, 0) + 1

    print("\nPOI类型分布:")
    for poi_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {poi_type}: {count} 个")

    # 统计各区分布
    district_counts = {}
    for poi in poi_points:
        district = poi['district']
        district_counts[district] = district_counts.get(district, 0) + 1

    print("\n各区分布:")
    for district, count in sorted(district_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {district}: {count} 个")

    # 保存文件
    csv_filename = 'shanghai_poi_amap_10k.csv'
    geojson_filename = 'shanghai_poi_amap_10k.geojson'

    print(f"\n正在保存文件...")
    save_to_csv(poi_points, csv_filename)
    print(f"CSV文件已保存: {csv_filename}")

    save_to_geojson(poi_points, geojson_filename)
    print(f"GeoJSON文件已保存: {geojson_filename}")

    print(f"\n文件保存完成!")
    print(f"  CSV文件: {csv_filename} ({len(poi_points)+1} 行，包含表头)")
    print(f"  GeoJSON文件: {geojson_filename}")

    # 显示坐标范围
    lons = [poi['longitude'] for poi in poi_points]
    lats = [poi['latitude'] for poi in poi_points]
    print(f"\n坐标范围:")
    print(f"  经度: {min(lons):.6f} ~ {max(lons):.6f}")
    print(f"  纬度: {min(lats):.6f} ~ {max(lats):.6f}")
    print(f"\n注意: 所有坐标均为高德地图坐标系(GCJ-02)，无需转换")

if __name__ == "__main__":
    main()
