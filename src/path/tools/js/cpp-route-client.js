/**
 * Path算路服务客户端
 * 用于调用本地Path算路服务并获取结果
 */

class CppRouteClient {
    constructor(serverUrl = 'http://localhost:8080') {
        this.serverUrl = serverUrl;
        this.pendingRequests = new Map();
    }

    /**
     * 计算路径（支持途经点和算路策略）
     * @param {number} startLng - 起点经度
     * @param {number} startLat - 起点纬度
     * @param {number} endLng - 终点经度
     * @param {number} endLat - 终点纬度
     * @param {Array} waypoints - 途经点数组，格式：[{lng: number, lat: number}, ...]
     * @param {number} strategy - 算路策略：0=最短时间，1=最短距离，2=高速优先，3=避开收费
     * @returns {Promise} 路径计算结果
     */
    async calculateRoute(startLng, startLat, endLng, endLat, waypoints = [], strategy = 0) {
        try {
            let logData = { startLng, startLat, endLng, endLat, strategy };
            if (waypoints && waypoints.length > 0) {
                logData.waypoints = waypoints;
                console.log(`Path算路请求 (含${waypoints.length}个途经点, 策略${strategy}):`, logData);
            } else {
                console.log(`Path算路请求 (策略${strategy}):`, logData);
            }

            // 发送算路请求
            const requestData = {
                start_lng: startLng,
                start_lat: startLat,
                end_lng: endLng,
                end_lat: endLat,
                strategy: strategy
            };

            // 添加途经点数据
            if (waypoints && waypoints.length > 0) {
                requestData.waypoints = waypoints;
            }

            const response = await fetch(`${this.serverUrl}/route`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.error) {
                throw new Error(result.error);
            }

            if (!result.uuid) {
                throw new Error('No UUID returned from server');
            }

            console.log('Path算路请求已提交, UUID:', result.uuid);

            // 轮询获取结果
            return await this.pollForResult(result.uuid);

        } catch (error) {
            console.error('Path算路请求失败:', error);
            throw error;
        }
    }

    /**
     * 轮询获取算路结果
     * @param {string} uuid - 请求UUID
     * @returns {Promise} 算路结果
     */
    async pollForResult(uuid) {
        const maxAttempts = 10; // 最多尝试30次
        const pollInterval = 1000; // 每秒轮询一次
        
        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            try {
                const response = await fetch(`${this.serverUrl}/result/${uuid}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                
                console.log(`Path算路轮询 ${attempt + 1}/${maxAttempts}:`, result.status);

                switch (result.status) {
                    case 'completed':
                        console.log('Path算路完成:', result);
                        return this.parseRouteResult(result);
                    
                    case 'failed':
                        throw new Error(result.error || 'Route calculation failed');
                    
                    case 'timeout':
                        throw new Error('Route calculation timeout');
                    
                    case 'not_found':
                        throw new Error('Route request not found');
                    
                    case 'processing':
                        // 继续轮询
                        break;
                    
                    default:
                        throw new Error(`Unknown status: ${result.status}`);
                }

                // 等待后继续轮询
                await new Promise(resolve => setTimeout(resolve, pollInterval));

            } catch (error) {
                if (attempt === maxAttempts - 1) {
                    throw error;
                }
                console.warn(`Path算路轮询失败 (${attempt + 1}/${maxAttempts}):`, error.message);
                await new Promise(resolve => setTimeout(resolve, pollInterval));
            }
        }

        throw new Error('Route calculation timeout after maximum attempts');
    }

    /**
     * 解析路径结果
     * @param {Object} result - 服务器返回的结果
     * @returns {Object} 标准化的路径结果
     */
    parseRouteResult(result) {
        // 处理多条路线
        const paths = result.paths || [];

        if (paths.length === 0) {
            throw new Error('No paths found in result');
        }

        // 解析所有路线
        const routes = paths.map((path, index) => {
            // 转换坐标格式
            const coordinates = path.points.map(point => [point.lng, point.lat]);

            // 生成简单的导航指令
            const instructions = this.generateInstructions(coordinates, path.length);

            return {
                routeIndex: index,
                pathId: path.path_id,
                coordinates: coordinates,
                totalDistance: path.length,
                totalTime: path.travel_time,
                trafficLightNum: path.traffic_light_num || 0,
                instructions: instructions,
                pointCount: coordinates.length
            };
        });

        // 返回包含所有路线的结果
        return {
            source: 'cpp',
            uuid: result.uuid,
            code: result.code,
            queryTime: result.query_time_ms,
            routes: routes,
            // 为了向后兼容，保留第一条路线的信息
            coordinates: routes[0].coordinates,
            totalDistance: routes[0].totalDistance,
            totalTime: routes[0].totalTime,
            instructions: routes[0].instructions,
            type: 'driving',
            raw: result
        };
    }

    /**
     * 生成简单的导航指令
     * @param {Array} coordinates - 路径坐标点
     * @param {number} totalDistance - 总距离
     * @returns {Array} 导航指令列表
     */
    generateInstructions(coordinates, totalDistance) {
        const instructions = [];
        
        if (coordinates.length < 2) {
            return instructions;
        }

        // 起点指令
        instructions.push({
            instruction: '从起点出发',
            distance: 0,
            time: 0,
            action: 'start'
        });

        // 中间指令（简化版）
        const segmentCount = Math.min(coordinates.length - 1, 10); // 最多10个段
        const segmentLength = totalDistance / segmentCount;
        
        for (let i = 1; i < segmentCount; i++) {
            instructions.push({
                instruction: `继续行驶 ${this.formatDistance(segmentLength)}`,
                distance: segmentLength,
                time: segmentLength / 15, // 假设平均速度15m/s
                action: 'continue'
            });
        }

        // 终点指令
        instructions.push({
            instruction: '到达目的地',
            distance: 0,
            time: 0,
            action: 'arrive'
        });

        return instructions;
    }

    /**
     * 格式化距离显示
     * @param {number} distance - 距离(米)
     * @returns {string} 格式化后的距离
     */
    formatDistance(distance) {
        if (distance < 1000) {
            return Math.round(distance) + ' 米';
        } else {
            return (distance / 1000).toFixed(1) + ' 公里';
        }
    }

    /**
     * 格式化时间显示
     * @param {number} time - 时间(秒)
     * @returns {string} 格式化后的时间
     */
    formatTime(time) {
        const hours = Math.floor(time / 3600);
        const minutes = Math.floor((time % 3600) / 60);
        
        if (hours > 0) {
            return hours + ' 小时 ' + minutes + ' 分钟';
        } else {
            return minutes + ' 分钟';
        }
    }

    /**
     * 检查服务器状态
     * @returns {Promise<boolean>} 服务器是否可用
     */
    async checkServerStatus() {
        try {
            console.log('检查C++服务器状态:', this.serverUrl);
            const response = await fetch(`${this.serverUrl}/route`, {
                method: 'OPTIONS',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            console.log('C++服务器响应状态:', response.status, response.ok);
            return response.ok;
        } catch (error) {
            console.warn('Path算路服务器不可用:', error.message);
            return false;
        }
    }

    /**
     * 获取服务器信息
     * @returns {Object} 服务器信息
     */
    getServerInfo() {
        return {
            url: this.serverUrl,
            name: 'C++ Route Server',
            version: '1.0.0',
            description: '基于Aurora路径规划引擎的Path算路服务'
        };
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CppRouteClient;
}
