<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路线评测系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 1400px;
            margin: 0 auto;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: #1890ff;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.info { background: #e6f7ff; color: #1890ff; }
        .status.success { background: #f6ffed; color: #52c41a; }
        .status.warning { background: #fffbe6; color: #faad14; }
        .status.error { background: #fff2f0; color: #ff4d4f; }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .config-item {
            display: flex;
            flex-direction: column;
        }
        .config-item label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .config-item input, .config-item select {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #40a9ff; }
        button:disabled { 
            background: #d9d9d9; 
            cursor: not-allowed; 
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #40a9ff);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: #fafafa;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #e8e8e8;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 12px;
        }
        .results-table th,
        .results-table td {
            border: 1px solid #e8e8e8;
            padding: 8px;
            text-align: center;
        }
        .results-table th {
            background: #fafafa;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .results-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        
        .log-container {
            background: #f8f8f8;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .export-buttons {
            margin: 10px 0;
        }
        
        .route-success { background: #f6ffed; }
        .route-failed { background: #fff2f0; }
        .route-partial { background: #fffbe6; }
        .route-anomaly { background: #fff1f0; color: #ff4d4f; }
        
        .anomaly-highlight {
            background: #ffccc7 !important;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 路线评测系统</h1>
        <p>基于上海市POI点的路线合理性评测和异常场景检测</p>

        <!-- 测试配置区域 -->
        <div class="section">
            <h3>⚙️ 测试配置</h3>
            <div class="config-grid">
                <div class="config-item">
                    <label>测试数量</label>
                    <input type="number" id="testCount" value="50" min="10" max="200">
                </div>
                <div class="config-item">
                    <label>并发数</label>
                    <select id="concurrency">
                        <option value="1">1个并发</option>
                        <option value="2" selected>2个并发</option>
                        <option value="3">3个并发</option>
                    </select>
                </div>
                <div class="config-item">
                    <label>超时时间(秒)</label>
                    <input type="number" id="timeout" value="30" min="10" max="120">
                </div>
                <div class="config-item">
                    <label>最小距离(km)</label>
                    <input type="number" id="minDistance" value="2" min="0.5" step="0.5">
                </div>
                <div class="config-item">
                    <label>最大距离(km)</label>
                    <input type="number" id="maxDistance" value="30" min="5" step="1">
                </div>
                <div class="config-item">
                    <label>相似度阈值</label>
                    <input type="number" id="similarityThreshold" value="0.8" min="0.1" max="1.0" step="0.1">
                </div>
            </div>
            <button onclick="startEvaluation()" id="startBtn">开始路线评测</button>
            <button onclick="stopEvaluation()" id="stopBtn" disabled>停止评测</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <!-- 测试进度 -->
        <div class="section">
            <h3>📊 评测进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText" class="status info">等待开始评测...</div>
        </div>

        <!-- 统计信息 -->
        <div class="section">
            <h3>📈 统计信息</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalTests">0</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="completedTests">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="cppSuccessRate">0%</div>
                    <div class="stat-label">C++成功率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="amapSuccessRate">0%</div>
                    <div class="stat-label">高德成功率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="bothSuccessRate">0%</div>
                    <div class="stat-label">双算路成功率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="avgRoutes">0</div>
                    <div class="stat-label">平均路线数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="anomalyCount">0</div>
                    <div class="stat-label">异常场景</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="avgTime">0ms</div>
                    <div class="stat-label">平均耗时</div>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="section">
            <h3>📋 评测结果</h3>
            <div class="export-buttons">
                <button onclick="exportResults('csv')">导出CSV</button>
                <button onclick="exportResults('geojson')">导出GeoJSON</button>
                <button onclick="generateReport()">生成报告</button>
                <button onclick="showAnomalies()">显示异常</button>
            </div>
            <div class="results-container">
                <table class="results-table" id="resultsTable">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>起点</th>
                            <th>终点</th>
                            <th>直线距离</th>
                            <th>C++路线数</th>
                            <th>C++最优长度</th>
                            <th>C++最优时间</th>
                            <th>C++次优长度</th>
                            <th>C++次优时间</th>
                            <th>高德长度</th>
                            <th>高德时间</th>
                            <th>C++内部相似度</th>
                            <th>C++-高德相似度</th>
                            <th>状态</th>
                            <th>异常类型</th>
                            <th>可视化</th>
                        </tr>
                    </thead>
                    <tbody id="resultsBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 详细日志 -->
        <div class="section">
            <h3>📝 评测日志</h3>
            <button onclick="clearLog()">清除日志</button>
            <div class="log-container" id="testLog">等待评测开始...</div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=f7f401fd4ffb75720cba09ffb7b24a4c&plugin=AMap.Driving,AMap.Walking,AMap.Riding"></script>
    <script src="cpp-route-client.js"></script>
    <script src="route-planner.js"></script>
    <script src="coordinate-converter.js"></script>
    <script>
        // 全局变量
        let cppRouteClient = null;
        let routePlanner = null;  // 高德算路客户端
        let isEvaluationRunning = false;
        let evaluationAborted = false;
        let currentTestIndex = 0;
        let testResults = [];
        let stats = {
            total: 0,
            completed: 0,
            cppSuccess: 0,
            amapSuccess: 0,
            bothSuccess: 0,
            anomalies: 0,
            totalTime: 0,
            totalRoutes: 0
        };

        // 上海市常用POI点（商场、小区、交通枢纽等）
        const shanghaiPOIs = [
            // 商场
            { name: "南京路步行街", lng: 121.4737, lat: 31.2304, type: "商场" },
            { name: "淮海中路", lng: 121.4692, lat: 31.2204, type: "商场" },
            { name: "徐家汇商圈", lng: 121.4367, lat: 31.1967, type: "商场" },
            { name: "五角场商圈", lng: 121.5133, lat: 31.2967, type: "商场" },
            { name: "中山公园商圈", lng: 121.4217, lat: 31.2267, type: "商场" },
            { name: "静安寺商圈", lng: 121.4467, lat: 31.2267, type: "商场" },
            { name: "陆家嘴金融区", lng: 121.5067, lat: 31.2467, type: "商场" },
            { name: "新天地", lng: 121.4767, lat: 31.2167, type: "商场" },
            
            // 交通枢纽
            { name: "上海虹桥机场", lng: 121.3367, lat: 31.1967, type: "交通" },
            { name: "上海浦东机场", lng: 121.8067, lat: 31.1467, type: "交通" },
            { name: "上海火车站", lng: 121.4567, lat: 31.2567, type: "交通" },
            { name: "上海南站", lng: 121.4267, lat: 31.1567, type: "交通" },
            { name: "虹桥火车站", lng: 121.3267, lat: 31.1867, type: "交通" },
            
            // 住宅区
            { name: "浦东新区", lng: 121.5667, lat: 31.2167, type: "住宅" },
            { name: "闵行区", lng: 121.3767, lat: 31.1167, type: "住宅" },
            { name: "宝山区", lng: 121.4867, lat: 31.4067, type: "住宅" },
            { name: "嘉定区", lng: 121.2667, lat: 31.3767, type: "住宅" },
            { name: "松江区", lng: 121.2267, lat: 31.0367, type: "住宅" },
            { name: "青浦区", lng: 121.1167, lat: 31.1467, type: "住宅" },
            { name: "奉贤区", lng: 121.4667, lat: 30.9167, type: "住宅" },
            { name: "金山区", lng: 121.3367, lat: 30.7467, type: "住宅" },
            
            // 景点
            { name: "外滩", lng: 121.4867, lat: 31.2367, type: "景点" },
            { name: "豫园", lng: 121.4967, lat: 31.2267, type: "景点" },
            { name: "东方明珠", lng: 121.5067, lat: 31.2467, type: "景点" },
            { name: "上海博物馆", lng: 121.4767, lat: 31.2267, type: "景点" },
            { name: "世纪公园", lng: 121.5567, lat: 31.2167, type: "景点" },
            { name: "上海迪士尼", lng: 121.6567, lat: 31.1467, type: "景点" }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeServices();
        });

        async function initializeServices() {
            try {
                // 初始化C++算路客户端
                cppRouteClient = new CppRouteClient('http://localhost:8080');

                // 检查服务状态
                const cppAvailable = await cppRouteClient.checkServerStatus();
                if (cppAvailable) {
                    log('✅ C++算路服务连接成功');
                } else {
                    log('❌ C++算路服务连接失败，请确保服务已启动');
                }

                // 初始化高德算路客户端
                routePlanner = new RoutePlanner();
                try {
                    await routePlanner.waitForInit();
                    log('✅ 高德地图路径规划服务初始化成功');
                } catch (e) {
                    log('⚠️ 高德地图路径规划服务初始化失败: ' + e.message);
                }

            } catch (error) {
                log(`❌ 服务初始化失败: ${error.message}`);
            }
        }

        function log(message) {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.textContent += `[${timestamp}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').textContent = '日志已清除\n';
        }

        function updateProgress(percentage, message) {
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = message;
        }

        // 生成随机测试点对
        function generateRandomTestPairs() {
            const testCount = parseInt(document.getElementById('testCount').value);
            const minDistance = parseFloat(document.getElementById('minDistance').value);
            const maxDistance = parseFloat(document.getElementById('maxDistance').value);

            const testPairs = [];
            const maxAttempts = testCount * 10; // 避免无限循环
            let attempts = 0;

            while (testPairs.length < testCount && attempts < maxAttempts) {
                attempts++;

                // 随机选择起点和终点
                const startPOI = shanghaiPOIs[Math.floor(Math.random() * shanghaiPOIs.length)];
                const endPOI = shanghaiPOIs[Math.floor(Math.random() * shanghaiPOIs.length)];

                // 避免起点和终点相同
                if (startPOI.name === endPOI.name) continue;

                // 计算直线距离
                const distance = calculateDistance(startPOI.lng, startPOI.lat, endPOI.lng, endPOI.lat);

                // 检查距离是否在范围内
                if (distance >= minDistance && distance <= maxDistance) {
                    testPairs.push({
                        index: testPairs.length + 1,
                        startPoint: startPOI,
                        endPoint: endPOI,
                        directDistance: distance
                    });
                }
            }

            log(`生成了 ${testPairs.length} 个测试点对`);
            return testPairs;
        }

        // 计算两点间距离（公里）
        function calculateDistance(lng1, lat1, lng2, lat2) {
            const R = 6371; // 地球半径（公里）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // 计算几何相似度 - 使用Fréchet距离的简化版本
        function calculateGeometricSimilarity(route1, route2) {
            if (!route1.coordinates || !route2.coordinates) return 0;

            const points1 = route1.coordinates;
            const points2 = route2.coordinates;

            // 如果任一路径为空，则相似度为0
            if (points1.length === 0 || points2.length === 0) {
                return 0.0;
            }

            // 如果两条路径完全相同，则相似度为1
            if (points1.length === points2.length) {
                let identical = true;
                for (let i = 0; i < points1.length; i++) {
                    const dist = calculateDistance(points1[i][0], points1[i][1], points2[i][0], points2[i][1]);
                    if (dist > 0.00001) { // 约1米的误差容忍
                        identical = false;
                        break;
                    }
                }
                if (identical) {
                    return 1.0;
                }
            }

            // 使用Fréchet距离的简化版本计算相似度
            // 对每个点找到另一条路径上最近的点，计算平均距离
            let totalDistance = 0.0;
            let count = 0;

            // 从路径1采样点（最多采样20个点）
            const step1 = Math.max(1, Math.floor(points1.length / 20));
            for (let i = 0; i < points1.length; i += step1) {
                let minDistance = Infinity;
                for (let j = 0; j < points2.length; j++) {
                    const dist = calculateDistance(points1[i][0], points1[i][1], points2[j][0], points2[j][1]);
                    minDistance = Math.min(minDistance, dist);
                }
                totalDistance += minDistance;
                count++;
            }

            // 从路径2采样点（最多采样20个点）
            const step2 = Math.max(1, Math.floor(points2.length / 20));
            for (let i = 0; i < points2.length; i += step2) {
                let minDistance = Infinity;
                for (let j = 0; j < points1.length; j++) {
                    const dist = calculateDistance(points2[i][0], points2[i][1], points1[j][0], points1[j][1]);
                    minDistance = Math.min(minDistance, dist);
                }
                totalDistance += minDistance;
                count++;
            }

            // 计算平均距离
            const avgDistance = totalDistance / count;

            // 将距离转换为相似度 (0-1)
            // 使用指数衰减函数: similarity = e^(-avg_distance/scale_factor)
            const scaleFactor = 0.5; // 500米作为参考距离
            const similarity = Math.exp(-avgDistance / scaleFactor);

            return Math.round(similarity * 100) / 100;
        }

        // 检测异常场景 - 只关注几何相似度差异
        function detectAnomalies(result) {
            const anomalies = [];
            const routes = result.routes || [];

            if (routes.length < 2) {
                anomalies.push('路线数量不足');
                return anomalies;
            }

            // 检查路线几何相似度差异
            const similarityThreshold = parseFloat(document.getElementById('similarityThreshold').value);

            // 计算所有路线对的几何相似度
            for (let i = 0; i < routes.length - 1; i++) {
                for (let j = i + 1; j < routes.length; j++) {
                    const similarity = calculateGeometricSimilarity(routes[i], routes[j]);

                    if (similarity > similarityThreshold) {
                        anomalies.push(`路线${i+1}与路线${j+1}几何相似度过高(${(similarity * 100).toFixed(1)}%)`);
                    }
                }
            }

            return anomalies;
        }

        // 开始路线评测
        async function startEvaluation() {
            if (!cppRouteClient) {
                alert('C++算路服务未初始化，请刷新页面重试');
                return;
            }

            // 检查服务状态
            const cppAvailable = await cppRouteClient.checkServerStatus();
            if (!cppAvailable) {
                alert('C++算路服务不可用，请确保服务已启动');
                return;
            }

            // 生成测试点对
            const testPairs = generateRandomTestPairs();
            if (testPairs.length === 0) {
                alert('无法生成有效的测试点对，请调整参数');
                return;
            }

            // 重置状态
            isEvaluationRunning = true;
            evaluationAborted = false;
            currentTestIndex = 0;
            testResults = [];
            stats = {
                total: testPairs.length,
                completed: 0,
                success: 0,
                anomalies: 0,
                totalTime: 0,
                totalRoutes: 0
            };

            // 更新UI
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            updateProgress(0, `开始路线评测，共 ${testPairs.length} 个测试用例`);
            clearResultsTable();

            log(`🚀 开始路线评测，共 ${testPairs.length} 个测试用例`);

            // 获取并发数
            const concurrency = parseInt(document.getElementById('concurrency').value);

            try {
                await runEvaluationWithConcurrency(testPairs, concurrency);

                if (!evaluationAborted) {
                    log('🎉 路线评测完成！');
                    updateProgress(100, '路线评测完成');
                    generateSummaryReport();
                }
            } catch (error) {
                log(`❌ 路线评测失败: ${error.message}`);
                updateProgress(0, `评测失败: ${error.message}`);
            } finally {
                isEvaluationRunning = false;
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
            }
        }

        // 并发执行评测
        async function runEvaluationWithConcurrency(testPairs, concurrency) {
            const chunks = [];
            for (let i = 0; i < testPairs.length; i += concurrency) {
                chunks.push(testPairs.slice(i, i + concurrency));
            }

            for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
                if (evaluationAborted) break;

                const chunk = chunks[chunkIndex];
                const promises = chunk.map((pair, index) =>
                    runSingleEvaluation(chunkIndex * concurrency + index, pair)
                );

                await Promise.allSettled(promises);

                // 更新进度
                const progress = ((chunkIndex + 1) / chunks.length) * 100;
                updateProgress(progress, `已完成 ${stats.completed}/${stats.total} 个评测`);
            }
        }

        // 执行单个评测
        async function runSingleEvaluation(index, testPair) {
            const startTime = Date.now();

            const result = {
                index: index + 1,
                startPoint: testPair.startPoint,
                endPoint: testPair.endPoint,
                directDistance: testPair.directDistance,
                cppStatus: 'pending',
                amapStatus: 'pending',
                routes: [],
                amapRoute: null,
                routeCount: 0,
                anomalies: [],
                totalTime: 0
            };

            try {
                log(`开始评测 ${index + 1}: ${testPair.startPoint.name} -> ${testPair.endPoint.name}`);

                // 并行执行C++算路和高德算路
                const promises = [];

                // C++算路
                if (cppRouteClient) {
                    promises.push(
                        cppRouteClient.calculateRoute(
                            testPair.startPoint.lng, testPair.startPoint.lat,
                            testPair.endPoint.lng, testPair.endPoint.lat,
                            [], // 无途经点
                            0   // 最短时间策略
                        ).then(routeResult => ({ source: 'cpp', result: routeResult }))
                        .catch(error => ({ source: 'cpp', error }))
                    );
                }

                // 高德算路
                if (routePlanner && routePlanner.isReady()) {
                    promises.push(
                        routePlanner.calculateRoute(
                            [testPair.startPoint.lng, testPair.startPoint.lat],
                            [testPair.endPoint.lng, testPair.endPoint.lat],
                            [] // 无途经点
                        ).then(routeResult => ({ source: 'amap', result: routeResult }))
                        .catch(error => ({ source: 'amap', error }))
                    );
                }

                // 等待所有算路完成
                const results = await Promise.allSettled(promises);

                // 处理C++算路结果
                const cppResult = results.find(r => r.value && r.value.source === 'cpp');
                if (cppResult && cppResult.status === 'fulfilled' && !cppResult.value.error) {
                    const routeResult = cppResult.value.result;
                    if (routeResult && routeResult.routes && routeResult.routes.length > 0) {
                        result.cppStatus = 'success';
                        result.routes = routeResult.routes;
                        result.routeCount = routeResult.routes.length;
                        result.queryTime = routeResult.queryTime;
                        stats.cppSuccess++;
                        stats.totalRoutes += result.routeCount;
                    } else {
                        result.cppStatus = 'failed';
                        result.cppError = '未找到路线';
                    }
                } else {
                    result.cppStatus = 'error';
                    result.cppError = cppResult?.value?.error?.message || '算路失败';
                }

                // 处理高德算路结果
                const amapResult = results.find(r => r.value && r.value.source === 'amap');
                if (amapResult && amapResult.status === 'fulfilled' && !amapResult.value.error) {
                    const routeResult = amapResult.value.result;
                    if (routeResult && routeResult.coordinates && routeResult.coordinates.length > 0) {
                        result.amapStatus = 'success';
                        result.amapRoute = routeResult;
                        stats.amapSuccess++;
                    } else {
                        result.amapStatus = 'failed';
                        result.amapError = '未找到路线';
                    }
                } else {
                    result.amapStatus = 'error';
                    result.amapError = amapResult?.value?.error?.message || '算路失败';
                }

                // 检测异常（只有C++算路成功时才检测）
                if (result.cppStatus === 'success') {
                    result.anomalies = detectAnomalies({ routes: result.routes });
                    if (result.anomalies.length > 0) {
                        stats.anomalies++;
                        log(`⚠️ 评测 ${index + 1} 发现异常: ${result.anomalies.join(', ')}`);
                    }
                }

                // 统计双算路成功
                if (result.cppStatus === 'success' && result.amapStatus === 'success') {
                    stats.bothSuccess++;
                }

            } catch (error) {
                log(`❌ 评测 ${index + 1} 失败: ${error.message}`);
                result.cppStatus = 'error';
                result.amapStatus = 'error';
                result.error = error.message;
            }

            result.totalTime = Date.now() - startTime;
            stats.totalTime += result.totalTime;
            stats.completed++;

            testResults.push(result);
            addResultToTable(result);
            updateStats();

            log(`评测 ${index + 1} 完成: C++=${result.cppStatus}, 高德=${result.amapStatus}, 路线数=${result.routeCount}, 耗时=${result.totalTime}ms`);
        }

        // 停止评测
        function stopEvaluation() {
            evaluationAborted = true;
            log('⏹️ 用户停止了路线评测');
            updateProgress(0, '评测已停止');
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('totalTests').textContent = stats.total;
            document.getElementById('completedTests').textContent = stats.completed;

            const cppSuccessRate = stats.completed > 0 ?
                ((stats.cppSuccess / stats.completed) * 100).toFixed(1) : 0;
            document.getElementById('cppSuccessRate').textContent = cppSuccessRate + '%';

            const amapSuccessRate = stats.completed > 0 ?
                ((stats.amapSuccess / stats.completed) * 100).toFixed(1) : 0;
            document.getElementById('amapSuccessRate').textContent = amapSuccessRate + '%';

            const bothSuccessRate = stats.completed > 0 ?
                ((stats.bothSuccess / stats.completed) * 100).toFixed(1) : 0;
            document.getElementById('bothSuccessRate').textContent = bothSuccessRate + '%';

            const avgRoutes = stats.cppSuccess > 0 ?
                (stats.totalRoutes / stats.cppSuccess).toFixed(1) : 0;
            document.getElementById('avgRoutes').textContent = avgRoutes;

            document.getElementById('anomalyCount').textContent = stats.anomalies;

            const avgTime = stats.completed > 0 ?
                Math.round(stats.totalTime / stats.completed) : 0;
            document.getElementById('avgTime').textContent = avgTime + 'ms';
        }

        // 清空结果表格
        function clearResultsTable() {
            document.getElementById('resultsBody').innerHTML = '';
        }

        // 添加结果到表格
        function addResultToTable(result) {
            const tbody = document.getElementById('resultsBody');
            const row = document.createElement('tr');

            // 根据状态设置行样式
            if (result.cppStatus === 'success' && result.amapStatus === 'success' && result.anomalies.length === 0) {
                row.className = 'route-success';
            } else if (result.anomalies.length > 0) {
                row.className = 'route-anomaly';
            } else if (result.cppStatus === 'failed' || result.amapStatus === 'failed') {
                row.className = 'route-failed';
            } else {
                row.className = 'route-partial';
            }

            // 计算C++路线内部差异
            let lengthDiff = '-', timeDiff = '-';
            if (result.routes.length >= 2) {
                const route1 = result.routes[0];
                const route2 = result.routes[1];

                lengthDiff = (((route2.totalDistance - route1.totalDistance) / route1.totalDistance) * 100).toFixed(1) + '%';
                timeDiff = (((route2.totalTime - route1.totalTime) / route1.totalTime) * 100).toFixed(1) + '%';
            }

            // 计算所有路线对的几何相似度
            let similarityMatrix = [];
            if (result.routes.length >= 2) {
                for (let i = 0; i < result.routes.length; i++) {
                    similarityMatrix[i] = [];
                    for (let j = 0; j < result.routes.length; j++) {
                        if (i === j) {
                            similarityMatrix[i][j] = 1.0; // 自身相似度为1
                        } else if (j < i) {
                            similarityMatrix[i][j] = similarityMatrix[j][i]; // 对称矩阵
                        } else {
                            similarityMatrix[i][j] = calculateGeometricSimilarity(result.routes[i], result.routes[j]);
                        }
                    }
                }
            }

            // 构建C++内部相似度显示字符串
            let cppSimilarityDisplay = '-';
            if (similarityMatrix.length > 0) {
                cppSimilarityDisplay = '';
                for (let i = 0; i < similarityMatrix.length - 1; i++) {
                    for (let j = i + 1; j < similarityMatrix[i].length; j++) {
                        cppSimilarityDisplay += `${i+1}-${j+1}: ${(similarityMatrix[i][j] * 100).toFixed(1)}%<br>`;
                    }
                }
            }

            // 计算C++最优路线与高德路线的几何相似度
            let cppAmapSimilarity = '-';
            if (result.cppStatus === 'success' && result.amapStatus === 'success' &&
                result.routes.length > 0 && result.amapRoute) {
                // 将高德路线转换为与C++路线相同的格式
                const amapRouteFormatted = {
                    coordinates: result.amapRoute.coordinates
                };
                const similarity = calculateGeometricSimilarity(result.routes[0], amapRouteFormatted);
                cppAmapSimilarity = `${(similarity * 100).toFixed(1)}%`;
            }

            // 创建可视化按钮
            const visualizeBtn = (result.routes.length > 0 || result.amapRoute) ?
                `<button class="btn" onclick="visualizeRoutes(${result.index - 1})" style="font-size: 12px; padding: 4px 8px;">🗺️ 查看</button>` :
                '-';

            row.innerHTML = `
                <td>${result.index}</td>
                <td>${result.startPoint.name}</td>
                <td>${result.endPoint.name}</td>
                <td>${result.directDistance.toFixed(1)}km</td>
                <td>${result.routeCount}</td>
                <td>${result.routes.length > 0 ? (result.routes[0].totalDistance / 1000).toFixed(2) + 'km' : '-'}</td>
                <td>${result.routes.length > 0 ? (result.routes[0].totalTime / 60).toFixed(1) + 'min' : '-'}</td>
                <td>${result.routes.length > 1 ? (result.routes[1].totalDistance / 1000).toFixed(2) + 'km' : '-'}</td>
                <td>${result.routes.length > 1 ? (result.routes[1].totalTime / 60).toFixed(1) + 'min' : '-'}</td>
                <td>${result.amapRoute ? (result.amapRoute.totalDistance / 1000).toFixed(2) + 'km' : '-'}</td>
                <td>${result.amapRoute ? (result.amapRoute.totalTime / 60).toFixed(1) + 'min' : '-'}</td>
                <td>${cppSimilarityDisplay}</td>
                <td>${cppAmapSimilarity}</td>
                <td>${getStatusText(result.cppStatus)} / ${getStatusText(result.amapStatus)}</td>
                <td>${result.anomalies.length > 0 ? result.anomalies.join('; ') : '-'}</td>
                <td>${visualizeBtn}</td>
            `;

            tbody.appendChild(row);

            // 滚动到最新行
            row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        function getStatusText(status) {
            const statusMap = {
                'success': '✅ 成功',
                'failed': '❌ 失败',
                'error': '💥 错误',
                'pending': '⏳ 等待'
            };
            return statusMap[status] || status;
        }

        // 清除结果
        function clearResults() {
            testResults = [];
            clearResultsTable();
            stats = {
                total: 0,
                completed: 0,
                success: 0,
                anomalies: 0,
                totalTime: 0,
                totalRoutes: 0
            };
            updateStats();
            updateProgress(0, '结果已清除');
            log('🗑️ 评测结果已清除');
        }

        // 导出结果
        function exportResults(format) {
            if (testResults.length === 0) {
                alert('没有评测结果可导出');
                return;
            }

            switch (format) {
                case 'csv':
                    exportCSV();
                    break;
                case 'geojson':
                    exportGeoJSON();
                    break;
                default:
                    alert('不支持的导出格式');
            }
        }

        // 导出CSV
        function exportCSV() {
            const headers = [
                '序号', '起点名称', '起点经度', '起点纬度', '终点名称', '终点经度', '终点纬度',
                '直线距离(km)', '路线数量', '状态', '异常类型',
                '最优路线长度(km)', '最优路线时间(min)', '最优路线点数',
                '次优路线长度(km)', '次优路线时间(min)', '次优路线点数',
                '长度差异(%)', '时间差异(%)', '路线1-2几何相似度', '路线1-3几何相似度', '路线2-3几何相似度', '总耗时(ms)'
            ];

            let csv = headers.join(',') + '\n';

            testResults.forEach(result => {
                // 计算所有路线对的几何相似度
                let similarity12 = '', similarity13 = '', similarity23 = '';
                if (result.routes.length >= 2) {
                    similarity12 = calculateGeometricSimilarity(result.routes[0], result.routes[1]).toFixed(3);
                }
                if (result.routes.length >= 3) {
                    similarity13 = calculateGeometricSimilarity(result.routes[0], result.routes[2]).toFixed(3);
                    similarity23 = calculateGeometricSimilarity(result.routes[1], result.routes[2]).toFixed(3);
                }

                const row = [
                    result.index,
                    `"${result.startPoint.name}"`,
                    result.startPoint.lng,
                    result.startPoint.lat,
                    `"${result.endPoint.name}"`,
                    result.endPoint.lng,
                    result.endPoint.lat,
                    result.directDistance.toFixed(2),
                    result.routeCount,
                    result.status,
                    `"${result.anomalies.join('; ')}"`,
                    result.routes.length > 0 ? (result.routes[0].totalDistance / 1000).toFixed(2) : '',
                    result.routes.length > 0 ? (result.routes[0].totalTime / 60).toFixed(1) : '',
                    result.routes.length > 0 ? result.routes[0].coordinates.length : '',
                    result.routes.length > 1 ? (result.routes[1].totalDistance / 1000).toFixed(2) : '',
                    result.routes.length > 1 ? (result.routes[1].totalTime / 60).toFixed(1) : '',
                    result.routes.length > 1 ? result.routes[1].coordinates.length : '',
                    result.routes.length > 1 ? (((result.routes[1].totalDistance - result.routes[0].totalDistance) / result.routes[0].totalDistance) * 100).toFixed(1) : '',
                    result.routes.length > 1 ? (((result.routes[1].totalTime - result.routes[0].totalTime) / result.routes[0].totalTime) * 100).toFixed(1) : '',
                    similarity12,
                    similarity13,
                    similarity23,
                    result.totalTime
                ];

                csv += row.join(',') + '\n';
            });

            const filename = `route_evaluation_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
            downloadFile(csv, filename, 'text/csv');
            log(`📊 导出CSV: ${filename}`);
        }

        // 导出GeoJSON
        function exportGeoJSON() {
            const geojson = {
                type: 'FeatureCollection',
                features: []
            };

            // 添加起点和终点
            testResults.forEach(result => {
                // 添加起点
                geojson.features.push({
                    type: 'Feature',
                    properties: {
                        type: 'start',
                        name: result.startPoint.name,
                        testIndex: result.index
                    },
                    geometry: {
                        type: 'Point',
                        coordinates: [result.startPoint.lng, result.startPoint.lat]
                    }
                });

                // 添加终点
                geojson.features.push({
                    type: 'Feature',
                    properties: {
                        type: 'end',
                        name: result.endPoint.name,
                        testIndex: result.index
                    },
                    geometry: {
                        type: 'Point',
                        coordinates: [result.endPoint.lng, result.endPoint.lat]
                    }
                });

                // 添加路线
                if (result.routes && result.routes.length > 0) {
                    result.routes.forEach((route, routeIndex) => {
                        if (route.coordinates && route.coordinates.length > 0) {
                            const lineCoords = route.coordinates.map(coord => [coord[0], coord[1]]);

                            geojson.features.push({
                                type: 'Feature',
                                properties: {
                                    type: 'route',
                                    testIndex: result.index,
                                    routeIndex: routeIndex,
                                    pathId: route.pathId,
                                    length: route.totalDistance,
                                    time: route.totalTime,
                                    hasAnomaly: result.anomalies.length > 0
                                },
                                geometry: {
                                    type: 'LineString',
                                    coordinates: lineCoords
                                }
                            });
                        }
                    });
                }
            });

            const filename = `route_evaluation_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.geojson`;
            downloadFile(JSON.stringify(geojson, null, 2), filename, 'application/geo+json');
            log(`🗺️ 导出GeoJSON: ${filename}`);
        }

        // 下载文件
        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 生成报告
        function generateReport() {
            if (testResults.length === 0) {
                alert('没有评测结果可生成报告');
                return;
            }

            const report = generateSummaryReport();
            const filename = `route_evaluation_report_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`;
            downloadFile(report, filename, 'text/html');
            log(`📊 生成评测报告: ${filename}`);
        }

        // 生成摘要报告
        function generateSummaryReport() {
            const successfulTests = testResults.filter(r => r.status === 'success');
            const anomalyTests = testResults.filter(r => r.anomalies.length > 0);

            let avgRouteCount = 0;
            let avgQueryTime = 0;
            let avgSimilarity = 0;
            let avgLengthDiff = 0;
            let avgTimeDiff = 0;

            if (successfulTests.length > 0) {
                avgRouteCount = successfulTests.reduce((sum, r) => sum + r.routeCount, 0) / successfulTests.length;
                avgQueryTime = successfulTests.reduce((sum, r) => sum + r.queryTime, 0) / successfulTests.length;

                const testsWithMultipleRoutes = successfulTests.filter(r => r.routes.length >= 2);
                if (testsWithMultipleRoutes.length > 0) {
                    avgSimilarity = testsWithMultipleRoutes.reduce((sum, r) => {
                        return sum + calculateGeometricSimilarity(r.routes[0], r.routes[1]);
                    }, 0) / testsWithMultipleRoutes.length;

                    avgLengthDiff = testsWithMultipleRoutes.reduce((sum, r) => {
                        return sum + ((r.routes[1].totalDistance - r.routes[0].totalDistance) / r.routes[0].totalDistance * 100);
                    }, 0) / testsWithMultipleRoutes.length;

                    avgTimeDiff = testsWithMultipleRoutes.reduce((sum, r) => {
                        return sum + ((r.routes[1].totalTime - r.routes[0].totalTime) / r.routes[0].totalTime * 100);
                    }, 0) / testsWithMultipleRoutes.length;
                }
            }

            // 生成HTML报告
            const reportHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>路线评测报告</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1, h2 { color: #1890ff; }
                        .container { max-width: 1200px; margin: 0 auto; }
                        .stat-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
                        .stat-card { background: #f0f2f5; padding: 20px; border-radius: 8px; text-align: center; }
                        .stat-value { font-size: 24px; font-weight: bold; color: #1890ff; }
                        .stat-label { font-size: 14px; color: #666; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background: #f0f2f5; }
                        .anomaly { background: #fff1f0; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>路线评测报告</h1>
                        <p>生成时间: ${new Date().toLocaleString()}</p>

                        <h2>评测概况</h2>
                        <div class="stat-grid">
                            <div class="stat-card">
                                <div class="stat-value">${stats.total}</div>
                                <div class="stat-label">总测试数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${stats.success}</div>
                                <div class="stat-label">成功数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${((stats.success / stats.total) * 100).toFixed(1)}%</div>
                                <div class="stat-label">成功率</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${stats.anomalies}</div>
                                <div class="stat-label">异常场景数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${avgRouteCount.toFixed(1)}</div>
                                <div class="stat-label">平均路线数</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${avgQueryTime.toFixed(0)}ms</div>
                                <div class="stat-label">平均查询时间</div>
                            </div>
                        </div>

                        <h2>路线差异性分析</h2>
                        <div class="stat-grid">
                            <div class="stat-card">
                                <div class="stat-value">${avgSimilarity.toFixed(2)}</div>
                                <div class="stat-label">平均几何相似度</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${avgLengthDiff.toFixed(1)}%</div>
                                <div class="stat-label">平均长度差异</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${avgTimeDiff.toFixed(1)}%</div>
                                <div class="stat-label">平均时间差异</div>
                            </div>
                        </div>

                        <h2>异常场景列表</h2>
                        <table>
                            <tr>
                                <th>序号</th>
                                <th>起点</th>
                                <th>终点</th>
                                <th>直线距离</th>
                                <th>路线数</th>
                                <th>异常类型</th>
                            </tr>
                            ${anomalyTests.map(test => `
                                <tr class="anomaly">
                                    <td>${test.index}</td>
                                    <td>${test.startPoint.name}</td>
                                    <td>${test.endPoint.name}</td>
                                    <td>${test.directDistance.toFixed(1)}km</td>
                                    <td>${test.routeCount}</td>
                                    <td>${test.anomalies.join('; ')}</td>
                                </tr>
                            `).join('')}
                        </table>
                    </div>
                </body>
                </html>
            `;

            return reportHTML;
        }

        // 显示异常场景
        function showAnomalies() {
            const anomalyTests = testResults.filter(r => r.anomalies.length > 0);

            if (anomalyTests.length === 0) {
                alert('没有检测到异常场景');
                return;
            }

            // 清空表格
            clearResultsTable();

            // 只显示异常场景
            anomalyTests.forEach(result => {
                addResultToTable(result);
            });

            log(`🔍 显示 ${anomalyTests.length} 个异常场景`);
        }

        // 可视化路线
        function visualizeRoutes(resultIndex) {
            if (resultIndex < 0 || resultIndex >= testResults.length) {
                alert('无效的结果索引');
                return;
            }

            const result = testResults[resultIndex];
            if ((!result.routes || result.routes.length === 0) && !result.amapRoute) {
                alert('该测试没有有效的路线数据');
                return;
            }

            // 构建可视化页面的URL参数
            const params = new URLSearchParams();
            params.set('startLng', result.startPoint.lng);
            params.set('startLat', result.startPoint.lat);
            params.set('endLng', result.endPoint.lng);
            params.set('endLat', result.endPoint.lat);
            params.set('startName', result.startPoint.name);
            params.set('endName', result.endPoint.name);

            // 添加C++路线数据
            if (result.routes && result.routes.length > 0) {
                result.routes.forEach((route, index) => {
                    if (route.coordinates && route.coordinates.length > 0) {
                        params.set(`cpp_route${index}_coords`, JSON.stringify(route.coordinates));
                        params.set(`cpp_route${index}_distance`, route.totalDistance);
                        params.set(`cpp_route${index}_time`, route.totalTime);
                        params.set(`cpp_route${index}_pathId`, route.pathId || index);
                    }
                });
                params.set('cppRouteCount', result.routes.length);
            } else {
                params.set('cppRouteCount', 0);
            }

            // 添加高德路线数据
            if (result.amapRoute && result.amapRoute.coordinates && result.amapRoute.coordinates.length > 0) {
                params.set('amap_route_coords', JSON.stringify(result.amapRoute.coordinates));
                params.set('amap_route_distance', result.amapRoute.totalDistance);
                params.set('amap_route_time', result.amapRoute.totalTime);
                params.set('hasAmapRoute', 'true');
            } else {
                params.set('hasAmapRoute', 'false');
            }

            params.set('testIndex', result.index);

            // 打开新窗口显示路线
            const visualizeUrl = `route-visualizer.html?${params.toString()}`;
            window.open(visualizeUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            log(`🗺️ 打开路线可视化: 测试 ${result.index} (C++:${result.cppStatus}, 高德:${result.amapStatus})`);
        }
    </script>
</body>
</html>
