<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路线可视化</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- 高德地图 JS SDK -->
    <script src="https://webapi.amap.com/maps?v=2.0&key=f7f401fd4ffb75720cba09ffb7b24a4c&plugin=AMap.Driving,AMap.Walking,AMap.Riding,AMap.PlaceSearch,AMap.AutoComplete,AMap.Geocoder"></script>
    
    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: <PERSON><PERSON>, sans-serif;
        }
        
        #container {
            display: flex;
            height: 100vh;
        }
        
        #map {
            flex: 1;
            height: 100vh;
        }
        
        #sidebar {
            width: 300px;
            background: #f5f5f5;
            padding: 20px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            overflow-y: auto;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .route-info {
            background: white;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .route-card {
            background: white;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #1890ff;
        }
        
        .route-card h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .route-card p {
            margin: 5px 0;
            font-size: 14px;
        }
        
        .route-card.active {
            border-left-color: #52c41a;
            background: #f6ffed;
        }
        
        .route-colors {
            display: flex;
            margin: 10px 0;
        }
        
        .color-item {
            display: flex;
            align-items: center;
            margin-right: 15px;
            font-size: 12px;
        }
        
        .color-box {
            width: 16px;
            height: 16px;
            margin-right: 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="map"></div>
        <div id="sidebar">
            <h2>路线可视化</h2>
            <div id="routeInfo" class="route-info">
                <h3>路线信息</h3>
                <p><strong>起点:</strong> <span id="startPoint">-</span></p>
                <p><strong>终点:</strong> <span id="endPoint">-</span></p>
                <p><strong>直线距离:</strong> <span id="directDistance">-</span></p>
                <p><strong>路线数量:</strong> <span id="routeCount">-</span></p>
            </div>
            
            <div class="route-colors">
                <div class="color-item">
                    <div class="color-box" style="background: #1890ff;"></div>
                    <span>C++路线1 (最优)</span>
                </div>
                <div class="color-item">
                    <div class="color-box" style="background: #52c41a;"></div>
                    <span>C++路线2 (次优)</span>
                </div>
                <div class="color-item">
                    <div class="color-box" style="background: #fa8c16;"></div>
                    <span>C++路线3</span>
                </div>
                <div class="color-item">
                    <div class="color-box" style="background: #f5222d;"></div>
                    <span>高德路线</span>
                </div>
            </div>
            
            <div id="routeCards"></div>
            
            <div class="control-group">
                <h3>操作</h3>
                <button class="btn" onclick="showAllRoutes()">显示所有路线</button>
                <button class="btn" onclick="hideAllRoutes()">隐藏所有路线</button>
                <button class="btn" onclick="window.close()">关闭窗口</button>
            </div>
            
            <div class="control-group">
                <h3>几何相似度</h3>
                <div id="similarityInfo"></div>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let map;
        let routePolylines = [];
        let startMarker, endMarker;
        let cppRouteData = [];
        let amapRouteData = null;

        // 路线颜色
        const routeColors = ['#1890ff', '#52c41a', '#fa8c16', '#eb2f96', '#722ed1', '#13c2c2'];
        const amapRouteColor = '#f5222d';
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            loadRouteData();
        });
        
        // 初始化地图
        function initMap() {
            // 创建地图
            map = L.map('map').setView([31.2304, 121.4737], 12);
            
            // 添加高德地图图层
            L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
                subdomains: "1234",
                attribution: '&copy; <a href="https://amap.com">高德地图</a>'
            }).addTo(map);
        }
        
        // 加载路线数据
        function loadRouteData() {
            // 从URL参数获取数据
            const urlParams = new URLSearchParams(window.location.search);
            
            // 获取起终点信息
            const startLng = parseFloat(urlParams.get('startLng'));
            const startLat = parseFloat(urlParams.get('startLat'));
            const endLng = parseFloat(urlParams.get('endLng'));
            const endLat = parseFloat(urlParams.get('endLat'));
            const startName = urlParams.get('startName') || '起点';
            const endName = urlParams.get('endName') || '终点';
            const cppRouteCount = parseInt(urlParams.get('cppRouteCount') || '0');
            const hasAmapRoute = urlParams.get('hasAmapRoute') === 'true';
            const testIndex = urlParams.get('testIndex') || '';

            // 更新页面标题
            document.title = `路线可视化 - 测试${testIndex}: ${startName} → ${endName}`;

            // 更新路线信息
            document.getElementById('startPoint').textContent = `${startName} (${startLng.toFixed(6)}, ${startLat.toFixed(6)})`;
            document.getElementById('endPoint').textContent = `${endName} (${endLng.toFixed(6)}, ${endLat.toFixed(6)})`;

            // 计算直线距离
            const directDistance = calculateDistance(startLng, startLat, endLng, endLat);
            document.getElementById('directDistance').textContent = `${directDistance.toFixed(2)} 公里`;

            const totalRouteCount = cppRouteCount + (hasAmapRoute ? 1 : 0);
            document.getElementById('routeCount').textContent = `${totalRouteCount} (C++:${cppRouteCount}, 高德:${hasAmapRoute ? 1 : 0})`;

            // 添加起终点标记
            addMarkers(startLng, startLat, endLng, endLat, startName, endName);

            // 加载C++路线数据
            for (let i = 0; i < cppRouteCount; i++) {
                const coordsStr = urlParams.get(`cpp_route${i}_coords`);
                if (coordsStr) {
                    try {
                        const coords = JSON.parse(coordsStr);
                        const distance = parseFloat(urlParams.get(`cpp_route${i}_distance`) || '0');
                        const time = parseFloat(urlParams.get(`cpp_route${i}_time`) || '0');
                        const pathId = urlParams.get(`cpp_route${i}_pathId`) || i;

                        cppRouteData.push({
                            index: i,
                            pathId: pathId,
                            coordinates: coords,
                            totalDistance: distance,
                            totalTime: time,
                            source: 'cpp'
                        });
                    } catch (e) {
                        console.error(`解析C++路线${i}数据失败:`, e);
                    }
                }
            }

            // 加载高德路线数据
            if (hasAmapRoute) {
                const amapCoordsStr = urlParams.get('amap_route_coords');
                if (amapCoordsStr) {
                    try {
                        const coords = JSON.parse(amapCoordsStr);
                        const distance = parseFloat(urlParams.get('amap_route_distance') || '0');
                        const time = parseFloat(urlParams.get('amap_route_time') || '0');

                        amapRouteData = {
                            coordinates: coords,
                            totalDistance: distance,
                            totalTime: time,
                            source: 'amap'
                        };
                    } catch (e) {
                        console.error('解析高德路线数据失败:', e);
                    }
                }
            }
            
            // 显示路线
            displayRoutes();

            // 计算并显示几何相似度
            calculateAndDisplaySimilarity();
        }

        // 添加起终点标记
        function addMarkers(startLng, startLat, endLng, endLat, startName, endName) {
            // 起点标记
            startMarker = L.marker([startLat, startLng], {
                title: startName,
                icon: L.divIcon({
                    className: 'custom-div-icon',
                    html: `<div style="background-color: #1890ff; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
                    iconSize: [16, 16],
                    iconAnchor: [8, 8]
                })
            }).addTo(map);

            // 终点标记
            endMarker = L.marker([endLat, endLng], {
                title: endName,
                icon: L.divIcon({
                    className: 'custom-div-icon',
                    html: `<div style="background-color: #f5222d; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
                    iconSize: [16, 16],
                    iconAnchor: [8, 8]
                })
            }).addTo(map);

            // 调整地图视图以包含起终点
            const bounds = L.latLngBounds([
                [startLat, startLng],
                [endLat, endLng]
            ]);
            map.fitBounds(bounds, { padding: [50, 50] });
        }

        // 显示路线
        function displayRoutes() {
            // 清除之前的路线
            clearRoutes();

            // 创建路线卡片
            const routeCardsDiv = document.getElementById('routeCards');
            routeCardsDiv.innerHTML = '';

            // 显示C++路线
            cppRouteData.forEach((route, index) => {
                const color = routeColors[index % routeColors.length];
                displayRoute(route, color, index);

                // 创建路线卡片
                const card = document.createElement('div');
                card.className = 'route-card';
                card.id = `route-card-${index}`;
                card.innerHTML = `
                    <h4 style="color: ${color};">C++路线 ${index + 1} ${index === 0 ? '(最优)' : index === 1 ? '(次优)' : ''}</h4>
                    <p>路线长度: ${(route.totalDistance / 1000).toFixed(2)} 公里</p>
                    <p>通行时间: ${(route.totalTime / 60).toFixed(1)} 分钟</p>
                    <p>路径点数: ${route.coordinates.length}</p>
                    <button class="btn" style="background: ${color};" onclick="toggleRoute(${index})">
                        <span id="route-btn-text-${index}">隐藏路线</span>
                    </button>
                `;
                routeCardsDiv.appendChild(card);
            });

            // 显示高德路线
            if (amapRouteData) {
                const amapIndex = cppRouteData.length; // 高德路线的索引紧接在C++路线之后
                displayRoute(amapRouteData, amapRouteColor, amapIndex);

                // 创建高德路线卡片
                const card = document.createElement('div');
                card.className = 'route-card';
                card.id = `route-card-${amapIndex}`;
                card.innerHTML = `
                    <h4 style="color: ${amapRouteColor};">高德路线</h4>
                    <p>路线长度: ${(amapRouteData.totalDistance / 1000).toFixed(2)} 公里</p>
                    <p>通行时间: ${(amapRouteData.totalTime / 60).toFixed(1)} 分钟</p>
                    <p>路径点数: ${amapRouteData.coordinates.length}</p>
                    <button class="btn" style="background: ${amapRouteColor};" onclick="toggleRoute(${amapIndex})">
                        <span id="route-btn-text-${amapIndex}">隐藏路线</span>
                    </button>
                `;
                routeCardsDiv.appendChild(card);
            }
        }
        
        // 显示单条路线
        function displayRoute(route, color, index) {
            // 转换坐标格式 [lng, lat] -> [lat, lng]
            const coordinates = route.coordinates.map(coord => [coord[1], coord[0]]);
            
            // 创建路线
            const polyline = L.polyline(coordinates, {
                color: color,
                weight: 5,
                opacity: 0.8
            }).addTo(map);
            
            // 保存路线引用
            routePolylines[index] = polyline;
            
            // 添加点击事件
            polyline.on('click', function() {
                highlightRoute(index);
            });
        }
        
        // 切换路线显示/隐藏
        function toggleRoute(index) {
            if (!routePolylines[index]) return;
            
            const polyline = routePolylines[index];
            const btnText = document.getElementById(`route-btn-text-${index}`);
            const card = document.getElementById(`route-card-${index}`);
            
            if (map.hasLayer(polyline)) {
                map.removeLayer(polyline);
                btnText.textContent = '显示路线';
                card.classList.remove('active');
            } else {
                polyline.addTo(map);
                btnText.textContent = '隐藏路线';
                card.classList.add('active');
            }
        }
        
        // 高亮显示路线
        function highlightRoute(index) {
            // 取消所有路线的高亮
            document.querySelectorAll('.route-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // 高亮选中的路线卡片
            const card = document.getElementById(`route-card-${index}`);
            if (card) {
                card.classList.add('active');
            }
        }
        
        // 显示所有路线
        function showAllRoutes() {
            routePolylines.forEach((polyline, index) => {
                if (polyline && !map.hasLayer(polyline)) {
                    polyline.addTo(map);
                    document.getElementById(`route-btn-text-${index}`).textContent = '隐藏路线';
                    document.getElementById(`route-card-${index}`).classList.add('active');
                }
            });
        }
        
        // 隐藏所有路线
        function hideAllRoutes() {
            routePolylines.forEach((polyline, index) => {
                if (polyline && map.hasLayer(polyline)) {
                    map.removeLayer(polyline);
                    document.getElementById(`route-btn-text-${index}`).textContent = '显示路线';
                    document.getElementById(`route-card-${index}`).classList.remove('active');
                }
            });
        }
        
        // 清除路线
        function clearRoutes() {
            routePolylines.forEach(polyline => {
                if (polyline) {
                    map.removeLayer(polyline);
                }
            });
            routePolylines = [];
        }
        
        // 计算两点间距离（公里）
        function calculateDistance(lng1, lat1, lng2, lat2) {
            const R = 6371; // 地球半径（公里）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }
        
        // 计算并显示几何相似度
        function calculateAndDisplaySimilarity() {
            const similarityDiv = document.getElementById('similarityInfo');
            similarityDiv.innerHTML = '';

            // 计算C++路线之间的几何相似度
            if (cppRouteData.length >= 2) {
                const cppTitle = document.createElement('h4');
                cppTitle.textContent = 'C++路线内部相似度';
                cppTitle.style.color = '#1890ff';
                similarityDiv.appendChild(cppTitle);

                for (let i = 0; i < cppRouteData.length - 1; i++) {
                    for (let j = i + 1; j < cppRouteData.length; j++) {
                        const similarity = calculateGeometricSimilarity(cppRouteData[i], cppRouteData[j]);

                        // 创建相似度显示
                        const simItem = document.createElement('div');
                        simItem.className = 'route-card';
                        simItem.style.borderLeftColor = similarity > 0.8 ? '#f5222d' : '#1890ff';
                        simItem.innerHTML = `
                            <p><strong>C++路线${i+1} - C++路线${j+1}:</strong></p>
                            <p>几何相似度: ${(similarity * 100).toFixed(1)}%</p>
                        `;
                        similarityDiv.appendChild(simItem);
                    }
                }
            }

            // 计算C++路线与高德路线的几何相似度
            if (cppRouteData.length > 0 && amapRouteData) {
                const comparisonTitle = document.createElement('h4');
                comparisonTitle.textContent = 'C++路线与高德路线相似度';
                comparisonTitle.style.color = '#f5222d';
                comparisonTitle.style.marginTop = '20px';
                similarityDiv.appendChild(comparisonTitle);

                cppRouteData.forEach((cppRoute, index) => {
                    const similarity = calculateGeometricSimilarity(cppRoute, amapRouteData);

                    // 创建相似度显示
                    const simItem = document.createElement('div');
                    simItem.className = 'route-card';
                    simItem.style.borderLeftColor = similarity > 0.8 ? '#f5222d' : '#fa8c16';
                    simItem.innerHTML = `
                        <p><strong>C++路线${index+1} - 高德路线:</strong></p>
                        <p>几何相似度: ${(similarity * 100).toFixed(1)}%</p>
                    `;
                    similarityDiv.appendChild(simItem);
                });
            }

            // 如果没有足够的路线进行比较
            if (cppRouteData.length < 2 && !amapRouteData) {
                const noDataItem = document.createElement('div');
                noDataItem.className = 'route-card';
                noDataItem.innerHTML = `
                    <p>路线数量不足，无法计算几何相似度</p>
                `;
                similarityDiv.appendChild(noDataItem);
            }
        }
        
        // 计算几何相似度 - 使用Fréchet距离的简化版本
        function calculateGeometricSimilarity(route1, route2) {
            if (!route1.coordinates || !route2.coordinates) return 0;
            
            const points1 = route1.coordinates;
            const points2 = route2.coordinates;
            
            // 如果任一路径为空，则相似度为0
            if (points1.length === 0 || points2.length === 0) {
                return 0.0;
            }
            
            // 如果两条路径完全相同，则相似度为1
            if (points1.length === points2.length) {
                let identical = true;
                for (let i = 0; i < points1.length; i++) {
                    const dist = calculateDistance(points1[i][0], points1[i][1], points2[i][0], points2[i][1]);
                    if (dist > 0.00001) { // 约1米的误差容忍
                        identical = false;
                        break;
                    }
                }
                if (identical) {
                    return 1.0;
                }
            }
            
            // 使用Fréchet距离的简化版本计算相似度
            // 对每个点找到另一条路径上最近的点，计算平均距离
            let totalDistance = 0.0;
            let count = 0;
            
            // 从路径1采样点（最多采样20个点）
            const step1 = Math.max(1, Math.floor(points1.length / 20));
            for (let i = 0; i < points1.length; i += step1) {
                let minDistance = Infinity;
                for (let j = 0; j < points2.length; j++) {
                    const dist = calculateDistance(points1[i][0], points1[i][1], points2[j][0], points2[j][1]);
                    minDistance = Math.min(minDistance, dist);
                }
                totalDistance += minDistance;
                count++;
            }
            
            // 从路径2采样点（最多采样20个点）
            const step2 = Math.max(1, Math.floor(points2.length / 20));
            for (let i = 0; i < points2.length; i += step2) {
                let minDistance = Infinity;
                for (let j = 0; j < points1.length; j++) {
                    const dist = calculateDistance(points2[i][0], points2[i][1], points1[j][0], points1[j][1]);
                    minDistance = Math.min(minDistance, dist);
                }
                totalDistance += minDistance;
                count++;
            }
            
            // 计算平均距离
            const avgDistance = totalDistance / count;
            
            // 将距离转换为相似度 (0-1)
            // 使用指数衰减函数: similarity = e^(-avg_distance/scale_factor)
            const scaleFactor = 0.5; // 500米作为参考距离
            const similarity = Math.exp(-avgDistance / scaleFactor);
            
            return Math.round(similarity * 100) / 100;
        }
    </script>
</body>
</html>
