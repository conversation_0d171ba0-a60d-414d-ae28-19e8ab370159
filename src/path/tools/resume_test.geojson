{"type": "FeatureCollection", "features": [{"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.45484, 31.228468]}, "properties": {"id": 0, "type": "original_start", "name": "金鹰国际购物中心(上海店)", "poi_type": "购物中心", "district": "静安区", "address": "陕西北路278号", "dest_name": "东方商厦", "point_role": "原始起点", "start_deviation": 22.537266892182423, "route_start_coords": [121.454603, 31.228465]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.437853, 31.193238]}, "properties": {"id": 0, "type": "original_end", "name": "东方商厦", "poi_type": "购物中心", "district": "徐汇区", "address": "漕溪北路8号(徐家汇地铁站11号口旁)", "origin_name": "金鹰国际购物中心(上海店)", "point_role": "原始终点", "end_deviation": 38.49045456414413, "route_end_coords": [121.43782, 31.192893]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.454603, 31.228465]}, "properties": {"id": 0, "type": "route_start", "name": "算路起点_金鹰国际购物中心(上海店)", "original_name": "金鹰国际购物中心(上海店)", "dest_name": "东方商厦", "point_role": "算路起点", "deviation": 22.537266892182423, "original_coords": [121.45484, 31.228468]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.43782, 31.192893]}, "properties": {"id": 0, "type": "route_end", "name": "算路终点_东方商厦", "original_name": "东方商厦", "origin_name": "金鹰国际购物中心(上海店)", "point_role": "算路终点", "deviation": 38.49045456414413, "original_coords": [121.437853, 31.193238]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.454603, 31.228465], [121.454586, 31.228433], [121.454527, 31.228396], [121.454452, 31.228358], [121.454452, 31.228358], [121.45435, 31.228487], [121.454318, 31.228525], [121.454093, 31.228744], [121.453953, 31.22883], [121.453953, 31.22883], [121.454034, 31.228852], [121.454216, 31.228927], [121.454463, 31.229056], [121.454522, 31.229077], [121.454656, 31.229088], [121.454914, 31.229061], [121.454983, 31.22905], [121.455214, 31.228991], [121.455353, 31.228986], [121.455455, 31.229018], [121.45567, 31.229125], [121.455885, 31.229211], [121.455885, 31.229211], [121.455928, 31.229098], [121.456281, 31.228557], [121.456501, 31.228213], [121.456534, 31.22817], [121.456534, 31.22817], [121.456239, 31.227961], [121.455761, 31.227645], [121.455434, 31.227495], [121.455053, 31.227371], [121.455053, 31.227296], [121.455364, 31.226904], [121.455455, 31.226792], [121.455632, 31.226572], [121.456035, 31.226116], [121.456142, 31.225976], [121.456276, 31.225821], [121.456341, 31.225703], [121.456394, 31.225553], [121.456437, 31.225333], [121.456437, 31.225198], [121.456507, 31.224877], [121.45656, 31.224732], [121.456641, 31.224351], [121.456689, 31.22411], [121.456882, 31.223745], [121.456834, 31.223573], [121.456834, 31.223439], [121.456834, 31.223412], [121.456829, 31.223385], [121.456829, 31.223283], [121.456829, 31.223192], [121.456829, 31.223144], [121.456829, 31.223053], [121.456834, 31.222892], [121.456856, 31.2225], [121.456947, 31.221958], [121.457033, 31.221663], [121.457172, 31.221427], [121.457355, 31.221079], [121.457537, 31.220698], [121.457585, 31.220596], [121.457687, 31.220231], [121.457687, 31.220193], [121.457762, 31.219958], [121.457875, 31.219641], [121.457982, 31.219314], [121.458095, 31.218992], [121.458121, 31.218901], [121.458202, 31.21867], [121.458245, 31.218531], [121.458304, 31.218407], [121.458384, 31.218241], [121.458497, 31.217973], [121.458529, 31.217908], [121.458556, 31.21786], [121.458588, 31.217796], [121.458733, 31.217495], [121.459017, 31.216814], [121.459044, 31.216755], [121.459162, 31.216508], [121.459189, 31.216444], [121.459253, 31.216304], [121.459296, 31.216224], [121.459409, 31.215972], [121.459436, 31.215896], [121.459538, 31.215741], [121.45971, 31.215483], [121.459768, 31.215398], [121.459886, 31.21521], [121.460069, 31.214872], [121.460138, 31.214657], [121.460214, 31.214443], [121.460278, 31.214309], [121.460326, 31.214207], [121.460439, 31.213981], [121.460466, 31.213933], [121.460578, 31.213729], [121.460696, 31.21351], [121.460777, 31.213306], [121.461013, 31.212496], [121.461056, 31.21234], [121.461104, 31.212158], [121.461179, 31.211895], [121.461249, 31.211643], [121.46134, 31.211262], [121.461378, 31.211106], [121.461474, 31.210591], [121.461555, 31.210178], [121.461581, 31.209953], [121.461608, 31.209744], [121.461635, 31.209626], [121.461678, 31.209207], [121.461683, 31.209153], [121.461721, 31.208885], [121.461721, 31.208816], [121.46171, 31.208467], [121.461743, 31.208252], [121.461748, 31.208183], [121.461791, 31.207839], [121.461823, 31.207582], [121.461877, 31.207373], [121.46192, 31.207324], [121.461936, 31.207276], [121.461952, 31.20712], [121.461963, 31.207045], [121.461963, 31.206927], [121.461984, 31.20675], [121.462016, 31.20653], [121.462016, 31.206482], [121.462038, 31.20616], [121.462118, 31.205645], [121.462161, 31.205254], [121.462156, 31.204867], [121.462215, 31.20469], [121.462182, 31.204046], [121.462188, 31.203322], [121.462188, 31.203322], [121.461501, 31.203156], [121.460873, 31.203011], [121.460267, 31.202893], [121.459886, 31.202813], [121.459624, 31.202721], [121.458696, 31.2024], [121.457945, 31.202185], [121.457826, 31.202153], [121.457376, 31.202024], [121.45729, 31.202003], [121.456802, 31.201917], [121.456239, 31.201793], [121.455648, 31.201563], [121.455348, 31.201429], [121.45471, 31.201171], [121.454565, 31.201112], [121.454152, 31.200973], [121.453733, 31.200823], [121.453551, 31.200758], [121.453342, 31.200683], [121.451641, 31.200109], [121.451427, 31.200039], [121.450992, 31.199884], [121.450429, 31.199696], [121.450059, 31.199562], [121.449764, 31.199455], [121.449496, 31.199358], [121.44912, 31.199219], [121.447784, 31.198639], [121.447387, 31.198457], [121.446969, 31.198269], [121.446905, 31.198237], [121.446416, 31.198022], [121.44566, 31.197679], [121.444748, 31.197298], [121.444678, 31.197266], [121.443906, 31.196842], [121.443702, 31.196713], [121.443514, 31.196595], [121.442967, 31.196241], [121.442881, 31.196188], [121.44287, 31.196182], [121.441454, 31.195303], [121.440913, 31.194975], [121.440408, 31.194675], [121.440113, 31.194482], [121.439711, 31.19423], [121.439593, 31.194171], [121.439201, 31.19402], [121.438976, 31.193961], [121.438676, 31.193924], [121.438461, 31.193779], [121.438472, 31.193532], [121.43845, 31.193366], [121.438434, 31.193302], [121.438321, 31.192856], [121.438321, 31.192856], [121.438246, 31.192862], [121.438048, 31.192878], [121.43782, 31.192893]]}, "properties": {"id": 0, "type": "route_line", "name": "路线_金鹰国际购物中心(上海店)到东方商厦", "origin_name": "金鹰国际购物中心(上海店)", "dest_name": "东方商厦", "distance": 5952, "duration": 1637, "tolls": 0, "toll_distance": 0, "strategy": 0, "start_deviation": 22.537266892182423, "end_deviation": 38.49045456414413, "original_start_coords": [121.45484, 31.228468], "original_end_coords": [121.437853, 31.193238], "route_start_coords": [121.454603, 31.228465], "route_end_coords": [121.43782, 31.192893]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.491244, 31.230587]}, "properties": {"id": 1, "type": "original_start", "name": "上海开放大学黄浦分校", "poi_type": "学校", "district": "黄浦区", "address": "四川南路37号(豫园地铁站7号口步行230米)", "dest_name": "静安公园-八景园", "point_role": "原始起点", "start_deviation": 7.258797419980508, "route_start_coords": [121.491224, 31.23065]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.447467, 31.222]}, "properties": {"id": 1, "type": "original_end", "name": "静安公园-八景园", "poi_type": "公园", "district": "静安区", "address": "南京西路1649号静安公园内(东北角)", "origin_name": "上海开放大学黄浦分校", "point_role": "原始终点", "end_deviation": 47.204446034114824, "route_end_coords": [121.447334, 31.222409]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.491224, 31.23065]}, "properties": {"id": 1, "type": "route_start", "name": "算路起点_上海开放大学黄浦分校", "original_name": "上海开放大学黄浦分校", "dest_name": "静安公园-八景园", "point_role": "算路起点", "deviation": 7.258797419980508, "original_coords": [121.491244, 31.230587]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.447334, 31.222409]}, "properties": {"id": 1, "type": "route_end", "name": "算路终点_静安公园-八景园", "original_name": "静安公园-八景园", "origin_name": "上海开放大学黄浦分校", "point_role": "算路终点", "deviation": 47.204446034114824, "original_coords": [121.447467, 31.222]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.491224, 31.23065], [121.491075, 31.230616], [121.491075, 31.230616], [121.491016, 31.23081], [121.491016, 31.23081], [121.490925, 31.230788], [121.490791, 31.230761], [121.490791, 31.230761], [121.490759, 31.230869], [121.490732, 31.230976], [121.490641, 31.231325], [121.490641, 31.231325], [121.49092, 31.231395], [121.491499, 31.231539], [121.491558, 31.231555], [121.492164, 31.231716], [121.492325, 31.231759], [121.492411, 31.231781], [121.49254, 31.231813], [121.492701, 31.231866], [121.493022, 31.231995], [121.493049, 31.232205], [121.492465, 31.232735], [121.492368, 31.232832], [121.492261, 31.232939], [121.492196, 31.232998], [121.491837, 31.23325], [121.491708, 31.233208], [121.491429, 31.233106], [121.49129, 31.233052], [121.491236, 31.233036], [121.491065, 31.232971], [121.490619, 31.2328], [121.490566, 31.232778], [121.489975, 31.232483], [121.489975, 31.232483], [121.489841, 31.232376], [121.48952, 31.232194], [121.489267, 31.232097], [121.488693, 31.231947], [121.488559, 31.231899], [121.488484, 31.231856], [121.488211, 31.23177], [121.488071, 31.231695], [121.487953, 31.231609], [121.487706, 31.231389], [121.487502, 31.231244], [121.48739, 31.231185], [121.487143, 31.231105], [121.486826, 31.231051], [121.486816, 31.231051], [121.48644, 31.231078], [121.485405, 31.231244], [121.484874, 31.231271], [121.484606, 31.23126], [121.484198, 31.231196], [121.483157, 31.23096], [121.482685, 31.230853], [121.482422, 31.230778], [121.482208, 31.230697], [121.481859, 31.230536], [121.481049, 31.230112], [121.480582, 31.229871], [121.479992, 31.229555], [121.478469, 31.228755], [121.477857, 31.228433], [121.477213, 31.228058], [121.476194, 31.227382], [121.476114, 31.227333], [121.47584, 31.227178], [121.475701, 31.227119], [121.475277, 31.226926], [121.474537, 31.226609], [121.473791, 31.226304], [121.473791, 31.226304], [121.47252, 31.225751], [121.471967, 31.22552], [121.471602, 31.225365], [121.46877, 31.224217], [121.468453, 31.22411], [121.468239, 31.224056], [121.467885, 31.223975], [121.467611, 31.223933], [121.467574, 31.223927], [121.467531, 31.223922], [121.467477, 31.223916], [121.467026, 31.223863], [121.466769, 31.223847], [121.466485, 31.223841], [121.465836, 31.223863], [121.465181, 31.223922], [121.464843, 31.223949], [121.46414, 31.223991], [121.463593, 31.224013], [121.463143, 31.224029], [121.461984, 31.224008], [121.460353, 31.223991], [121.459881, 31.223991], [121.45972, 31.223986], [121.456732, 31.223959], [121.456555, 31.223959], [121.455745, 31.223916], [121.45516, 31.223879], [121.45494, 31.223847], [121.454565, 31.223755], [121.454265, 31.223659], [121.452923, 31.223144], [121.452006, 31.222795], [121.451555, 31.222629], [121.451207, 31.222495], [121.450949, 31.222393], [121.450853, 31.222361], [121.450853, 31.222361], [121.450611, 31.222328], [121.45015, 31.222211], [121.448471, 31.221626], [121.447688, 31.221427], [121.44757, 31.2214], [121.446792, 31.221261], [121.446647, 31.221309], [121.446003, 31.22124], [121.445901, 31.221229], [121.4458, 31.221191], [121.445687, 31.221116], [121.445687, 31.221116], [121.44566, 31.221288], [121.445633, 31.221438], [121.445585, 31.221722], [121.445547, 31.221921], [121.445467, 31.222248], [121.445413, 31.222479], [121.445322, 31.222634], [121.445322, 31.222634], [121.446304, 31.223139], [121.446304, 31.223139], [121.446352, 31.223042], [121.446411, 31.222929], [121.446448, 31.222849], [121.446486, 31.222779], [121.446599, 31.222538], [121.446636, 31.222516], [121.446711, 31.222511], [121.446942, 31.222565], [121.447114, 31.222554], [121.447334, 31.222409]]}, "properties": {"id": 1, "type": "route_line", "name": "路线_上海开放大学黄浦分校到静安公园-八景园", "origin_name": "上海开放大学黄浦分校", "dest_name": "静安公园-八景园", "distance": 5595, "duration": 1084, "tolls": 0, "toll_distance": 0, "strategy": 0, "start_deviation": 7.258797419980508, "end_deviation": 47.204446034114824, "original_start_coords": [121.491244, 31.230587], "original_end_coords": [121.447467, 31.222], "route_start_coords": [121.491224, 31.23065], "route_end_coords": [121.447334, 31.222409]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.118826, 31.158753]}, "properties": {"id": 2, "type": "original_start", "name": "上海新贵人家宾馆(青浦新城地铁站店)", "poi_type": "酒店", "district": "青浦区", "address": "青安路579号", "dest_name": "中国工商银行(上海长三角一体化示范区支行营业厅)", "point_role": "原始起点", "start_deviation": 15.7261679800078, "route_start_coords": [121.118988, 31.158725]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.115825, 31.146169]}, "properties": {"id": 2, "type": "original_end", "name": "中国工商银行(上海长三角一体化示范区支行营业厅)", "poi_type": "银行", "district": "青浦区", "address": "城中东路485号", "origin_name": "上海新贵人家宾馆(青浦新城地铁站店)", "point_role": "原始终点", "end_deviation": 17.514590897551074, "route_end_coords": [121.115698, 31.146055]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.118988, 31.158725]}, "properties": {"id": 2, "type": "route_start", "name": "算路起点_上海新贵人家宾馆(青浦新城地铁站店)", "original_name": "上海新贵人家宾馆(青浦新城地铁站店)", "dest_name": "中国工商银行(上海长三角一体化示范区支行营业厅)", "point_role": "算路起点", "deviation": 15.7261679800078, "original_coords": [121.118826, 31.158753]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.115698, 31.146055]}, "properties": {"id": 2, "type": "route_end", "name": "算路终点_中国工商银行(上海长三角一体化示范区支行营业厅)", "original_name": "中国工商银行(上海长三角一体化示范区支行营业厅)", "origin_name": "上海新贵人家宾馆(青浦新城地铁站店)", "point_role": "算路终点", "deviation": 17.514590897551074, "original_coords": [121.115825, 31.146169]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.118988, 31.158725], [121.118881, 31.158261], [121.118763, 31.157848], [121.118629, 31.157376], [121.118618, 31.157323], [121.118591, 31.157226], [121.118527, 31.157001], [121.118313, 31.156223], [121.118141, 31.155617], [121.118017, 31.155139], [121.117953, 31.154866], [121.117862, 31.15449], [121.117776, 31.154115], [121.117663, 31.15361], [121.117449, 31.152629], [121.117293, 31.151985], [121.117186, 31.151529], [121.117159, 31.151406], [121.11703, 31.150778], [121.116998, 31.150601], [121.116886, 31.150065], [121.116778, 31.149566], [121.116676, 31.149126], [121.116537, 31.14853], [121.116521, 31.148359], [121.116462, 31.147768], [121.116456, 31.14772], [121.116419, 31.147366], [121.116381, 31.146948], [121.11636, 31.146744], [121.116333, 31.146476], [121.116328, 31.146417], [121.116306, 31.14624], [121.116274, 31.145923], [121.116274, 31.145923], [121.116086, 31.145945], [121.115775, 31.145993], [121.115698, 31.146055]]}, "properties": {"id": 2, "type": "route_line", "name": "路线_上海新贵人家宾馆(青浦新城地铁站店)到中国工商银行(上海长三角一体化示范区支行营业厅)", "origin_name": "上海新贵人家宾馆(青浦新城地铁站店)", "dest_name": "中国工商银行(上海长三角一体化示范区支行营业厅)", "distance": 1489, "duration": 389, "tolls": 0, "toll_distance": 0, "strategy": 0, "start_deviation": 15.7261679800078, "end_deviation": 17.514590897551074, "original_start_coords": [121.118826, 31.158753], "original_end_coords": [121.115825, 31.146169], "route_start_coords": [121.118988, 31.158725], "route_end_coords": [121.115698, 31.146055]}}]}