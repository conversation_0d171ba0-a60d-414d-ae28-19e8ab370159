#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于CSV文件的高德算路复测工具
读取之前生成的CSV文件，重新进行算路测试
"""

import csv
import json
import requests
import time
import os
import argparse
from typing import List, Dict, Optional

# 设置代理环境变量
os.environ['NO_PROXY'] = 'localhost,127.0.0.1'

# 高德API配置
AMAP_CONFIG = {
    'key': 'c6dbca449fbd6baadc69b8540565ef59',
    'driving_url': 'https://restapi.amap.com/v3/direction/driving'
}

class RetestAmapRouting:
    def __init__(self):
        self.test_pairs = []
        self.retest_results = []
        
    def load_test_pairs_from_csv(self, csv_file: str) -> List[Dict]:
        """从CSV文件加载测试用例"""
        print(f"加载测试用例: {csv_file}")
        test_pairs = []
        
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                test_pairs.append({
                    'test_id': int(row['test_id']),
                    'origin': {
                        'name': row['origin_name'],
                        'type': row['origin_type'],
                        'district': row['origin_district'],
                        'longitude': float(row['origin_lon']),
                        'latitude': float(row['origin_lat']),
                        'address': row['origin_address']
                    },
                    'dest': {
                        'name': row['dest_name'],
                        'type': row['dest_type'],
                        'district': row['dest_district'],
                        'longitude': float(row['dest_lon']),
                        'latitude': float(row['dest_lat']),
                        'address': row['dest_address']
                    },
                    'original_results': {
                        'straight_distance': float(row['straight_distance']),
                        'route_distance': int(row['route_distance']),
                        'route_duration': int(row['route_duration']),
                        'start_deviation': float(row['start_deviation']),
                        'end_deviation': float(row['end_deviation']),
                        'strategy': int(row['strategy'])
                    }
                })
        
        print(f"加载了 {len(test_pairs)} 个测试用例")
        self.test_pairs = test_pairs
        return test_pairs
    
    def call_amap_route_api(self, start_lon: float, start_lat: float,
                           end_lon: float, end_lat: float, strategy: int = 0) -> Optional[Dict]:
        """调用高德算路API"""
        try:
            params = {
                'key': AMAP_CONFIG['key'],
                'origin': f"{start_lon},{start_lat}",
                'destination': f"{end_lon},{end_lat}",
                'extensions': 'all',
                'strategy': strategy
            }
            
            response = requests.get(AMAP_CONFIG['driving_url'], params=params, timeout=10)
            
            if response.status_code != 200:
                print(f"高德API请求失败: {response.status_code}")
                return None
            
            result = response.json()
            
            if result.get('status') != '1':
                print(f"高德API错误: {result.get('info')}")
                return None
            
            return result
            
        except Exception as e:
            print(f"高德API调用异常: {e}")
            return None
    
    def extract_route_info(self, route_data: Dict) -> Dict:
        """提取路线信息"""
        try:
            if 'route' in route_data and 'paths' in route_data['route']:
                paths = route_data['route']['paths']
                if paths and len(paths) > 0:
                    path = paths[0]
                    return {
                        'distance': int(path.get('distance', 0)),
                        'duration': int(path.get('duration', 0)),
                        'tolls': int(path.get('tolls', 0)),
                        'toll_distance': int(path.get('toll_distance', 0))
                    }
            return {}
        except Exception as e:
            print(f"提取路线信息失败: {e}")
            return {}
    
    def retest_single_pair(self, test_pair: Dict, new_strategy: int = None) -> Optional[Dict]:
        """重新测试单个路径对"""
        test_id = test_pair['test_id']
        origin = test_pair['origin']
        dest = test_pair['dest']
        original = test_pair['original_results']
        
        # 使用新策略或原策略
        strategy = new_strategy if new_strategy is not None else original['strategy']
        
        print(f"复测 {test_id}: {origin['name']} -> {dest['name']} (策略: {strategy})")
        
        # 调用高德API
        result = self.call_amap_route_api(
            origin['longitude'], origin['latitude'],
            dest['longitude'], dest['latitude'],
            strategy
        )
        
        if not result:
            print("  算路失败")
            return None
        
        # 提取新的路线信息
        new_route_info = self.extract_route_info(result)
        
        if not new_route_info:
            print("  无法提取路线信息")
            return None
        
        # 计算变化
        distance_change = new_route_info['distance'] - original['route_distance']
        duration_change = new_route_info['duration'] - original['route_duration']
        
        distance_change_pct = (distance_change / original['route_distance']) * 100 if original['route_distance'] > 0 else 0
        duration_change_pct = (duration_change / original['route_duration']) * 100 if original['route_duration'] > 0 else 0
        
        retest_result = {
            'test_id': test_id,
            'origin': origin,
            'dest': dest,
            'original_results': original,
            'new_results': new_route_info,
            'new_strategy': strategy,
            'changes': {
                'distance_change': distance_change,
                'duration_change': duration_change,
                'distance_change_pct': distance_change_pct,
                'duration_change_pct': duration_change_pct
            },
            'amap_result': result
        }
        
        print(f"  原距离: {original['route_distance']}m -> 新距离: {new_route_info['distance']}m "
              f"(变化: {distance_change:+d}m, {distance_change_pct:+.1f}%)")
        print(f"  原时间: {original['route_duration']}s -> 新时间: {new_route_info['duration']}s "
              f"(变化: {duration_change:+d}s, {duration_change_pct:+.1f}%)")
        
        return retest_result
    
    def run_batch_retest(self, new_strategy: int = None, test_ids: List[int] = None):
        """运行批量复测"""
        if not self.test_pairs:
            print("请先加载测试用例")
            return
        
        # 筛选要测试的用例
        if test_ids:
            test_pairs_to_run = [pair for pair in self.test_pairs if pair['test_id'] in test_ids]
            print(f"复测指定的 {len(test_pairs_to_run)} 个用例")
        else:
            test_pairs_to_run = self.test_pairs
            print(f"复测全部 {len(test_pairs_to_run)} 个用例")
        
        if new_strategy is not None:
            print(f"使用新策略: {new_strategy}")
        else:
            print("使用原策略")
        
        print("-" * 60)
        
        successful_tests = 0
        
        for i, test_pair in enumerate(test_pairs_to_run):
            print(f"\n=== 复测 {i+1}/{len(test_pairs_to_run)} ===")
            
            result = self.retest_single_pair(test_pair, new_strategy)
            
            if result:
                self.retest_results.append(result)
                successful_tests += 1
                print(f"  复测成功 ({successful_tests}/{i+1})")
            
            # 避免API调用过于频繁
            time.sleep(0.2)
        
        print(f"\n批量复测完成，成功 {successful_tests}/{len(test_pairs_to_run)} 组")
    
    def save_comparison_to_csv(self, filename: str):
        """保存对比结果到CSV"""
        print(f"\n保存对比结果到 {filename}")
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'test_id', 'origin_name', 'dest_name',
                'original_distance', 'new_distance', 'distance_change', 'distance_change_pct',
                'original_duration', 'new_duration', 'duration_change', 'duration_change_pct',
                'original_strategy', 'new_strategy',
                'original_tolls', 'new_tolls'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            
            for result in self.retest_results:
                row = {
                    'test_id': result['test_id'],
                    'origin_name': result['origin']['name'],
                    'dest_name': result['dest']['name'],
                    'original_distance': result['original_results']['route_distance'],
                    'new_distance': result['new_results']['distance'],
                    'distance_change': result['changes']['distance_change'],
                    'distance_change_pct': round(result['changes']['distance_change_pct'], 2),
                    'original_duration': result['original_results']['route_duration'],
                    'new_duration': result['new_results']['duration'],
                    'duration_change': result['changes']['duration_change'],
                    'duration_change_pct': round(result['changes']['duration_change_pct'], 2),
                    'original_strategy': result['original_results']['strategy'],
                    'new_strategy': result['new_strategy'],
                    'original_tolls': 0,  # 原数据中没有过路费信息
                    'new_tolls': result['new_results'].get('tolls', 0)
                }
                writer.writerow(row)
        
        print(f"已保存 {len(self.retest_results)} 组对比结果到 {filename}")
    
    def print_comparison_statistics(self):
        """打印对比统计"""
        if not self.retest_results:
            print("没有复测结果")
            return
        
        print("\n=== 复测对比统计 ===")
        
        total_tests = len(self.retest_results)
        
        # 距离变化统计
        distance_changes = [r['changes']['distance_change'] for r in self.retest_results]
        duration_changes = [r['changes']['duration_change'] for r in self.retest_results]
        
        distance_changes_pct = [r['changes']['distance_change_pct'] for r in self.retest_results]
        duration_changes_pct = [r['changes']['duration_change_pct'] for r in self.retest_results]
        
        print(f"总测试数: {total_tests}")
        
        print(f"\n距离变化统计:")
        print(f"  平均变化: {sum(distance_changes)/total_tests:+.0f}m ({sum(distance_changes_pct)/total_tests:+.1f}%)")
        print(f"  最大增加: {max(distance_changes):+.0f}m")
        print(f"  最大减少: {min(distance_changes):+.0f}m")
        
        print(f"时间变化统计:")
        print(f"  平均变化: {sum(duration_changes)/total_tests:+.0f}s ({sum(duration_changes_pct)/total_tests:+.1f}%)")
        print(f"  最大增加: {max(duration_changes):+.0f}s")
        print(f"  最大减少: {min(duration_changes):+.0f}s")
        
        # 变化方向统计
        distance_increased = sum(1 for d in distance_changes if d > 0)
        distance_decreased = sum(1 for d in distance_changes if d < 0)
        distance_unchanged = sum(1 for d in distance_changes if d == 0)
        
        duration_increased = sum(1 for d in duration_changes if d > 0)
        duration_decreased = sum(1 for d in duration_changes if d < 0)
        duration_unchanged = sum(1 for d in duration_changes if d == 0)
        
        print(f"\n变化方向统计:")
        print(f"  距离: 增加 {distance_increased}, 减少 {distance_decreased}, 不变 {distance_unchanged}")
        print(f"  时间: 增加 {duration_increased}, 减少 {duration_decreased}, 不变 {duration_unchanged}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基于CSV文件的高德算路复测工具')
    parser.add_argument('csv_file', help='输入CSV文件路径')
    parser.add_argument('--strategy', '-s', type=int, choices=[0, 1, 2],
                       help='新的算路策略: 0=最短时间, 1=最短距离, 2=避开拥堵 (默认: 使用原策略)')
    parser.add_argument('--test-ids', '-t', type=str,
                       help='指定测试用例ID，用逗号分隔，如: 1,3,5')
    parser.add_argument('--output-csv', '-o', type=str, default='retest_comparison.csv',
                       help='输出对比结果CSV文件路径 (默认: retest_comparison.csv)')
    
    args = parser.parse_args()
    
    # 解析测试ID
    test_ids = None
    if args.test_ids:
        try:
            test_ids = [int(x.strip()) for x in args.test_ids.split(',')]
        except ValueError:
            print("错误: 测试ID格式不正确")
            return
    
    print("高德算路复测工具")
    print(f"输入CSV文件: {args.csv_file}")
    if args.strategy is not None:
        print(f"新算路策略: {args.strategy}")
    if test_ids:
        print(f"指定测试用例: {test_ids}")
    print(f"输出文件: {args.output_csv}")
    print("=" * 60)
    
    # 创建复测实例
    retest = RetestAmapRouting()
    
    # 加载测试用例
    retest.load_test_pairs_from_csv(args.csv_file)
    
    # 运行复测
    retest.run_batch_retest(args.strategy, test_ids)
    
    # 打印统计结果
    retest.print_comparison_statistics()
    
    # 保存对比结果
    retest.save_comparison_to_csv(args.output_csv)
    
    print("\n复测完成！")

if __name__ == "__main__":
    main()
