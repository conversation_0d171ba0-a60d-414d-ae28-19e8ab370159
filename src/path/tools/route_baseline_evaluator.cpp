// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-18
//
// 算路基线评测工具 - 读取高德算路基线数据，进行多线程算路对比评测

#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <iomanip>
#include <algorithm>

#include "rapidjson/document.h"
#include "rapidjson/error/en.h"
#include "logger.h"
#include "path_def.h"
#include "path_module.h"
#include "path/src/debug/route_evaluator.h"
#include "search/src/execution/BS_thread_pool.hpp"
#include <future>
#include <map>
#include <numeric>

using namespace aurora::path;
using aurora::PointLL;
using aurora::ParameterMap;

// 基线路线数据结构
struct BaselineRoute {
    std::string id;
    PointLL start_point;
    PointLL end_point;
    std::vector<PointLL> geometry;
    double distance;        // 米
    double duration;        // 秒
    std::string source;     // 数据来源，如"amap"
};

// 评测结果数据结构
struct EvaluationResult {
    std::string route_id;
    BaselineRoute baseline;
    PathResultPtr path_result;
    RouteComparisonResult comparison;
    bool success;
    std::string error_message;
    std::chrono::milliseconds calculation_time;
};

// 统计结果
struct EvaluationStatistics {
    size_t total_routes = 0;
    size_t successful_routes = 0;
    size_t failed_routes = 0;
    
    // 长度差异统计
    double avg_length_diff_percent = 0.0;
    double max_length_diff_percent = 0.0;
    double min_length_diff_percent = 0.0;
    
    // 时间差异统计
    double avg_time_diff_percent = 0.0;
    double max_time_diff_percent = 0.0;
    double min_time_diff_percent = 0.0;
    
    // 几何相似度统计
    double avg_geometric_similarity = 0.0;
    double max_geometric_similarity = 0.0;
    double min_geometric_similarity = 0.0;
    
    // 计算时间统计
    std::chrono::milliseconds total_calculation_time{0};
    std::chrono::milliseconds avg_calculation_time{0};
    std::chrono::milliseconds max_calculation_time{0};
    std::chrono::milliseconds min_calculation_time{std::chrono::milliseconds::max()};
    
    // 相似度分布统计
    size_t high_similarity_count = 0;    // >0.9
    size_t medium_similarity_count = 0;  // 0.7-0.9
    size_t low_similarity_count = 0;     // <0.7
};

class RouteBaselineEvaluator {
public:
    RouteBaselineEvaluator(const std::string& config_file, const std::string& data_dir, int thread_count = 8)
        : config_file_(config_file), data_dir_(data_dir), thread_count_(thread_count),
          thread_pool_(thread_count), route_evaluator_(std::make_shared<RouteEvaluator>()) {
        
        // 初始化路径模块
        InitializePathModule();
    }
    
    ~RouteBaselineEvaluator() {
        CleanupPathModule();
    }
    
    // 读取基线数据
    bool LoadBaselineData(const std::string& geojson_file);
    
    // 运行评测
    void RunEvaluation();
    
    // 生成报告
    void GenerateReport(const std::string& output_file = "");
    
private:
    std::string config_file_;
    std::string data_dir_;
    int thread_count_;
    BS::thread_pool<> thread_pool_;
    
    std::shared_ptr<PathModule> path_module_;
    std::shared_ptr<PathInterface> path_interface_;
    std::shared_ptr<RouteEvaluator> route_evaluator_;
    
    std::vector<BaselineRoute> baseline_routes_;
    std::vector<EvaluationResult> evaluation_results_;
    EvaluationStatistics statistics_;
    
    std::mutex results_mutex_;
    std::atomic<size_t> completed_count_{0};
    std::atomic<size_t> failed_count_{0};
    
    // 初始化路径模块
    bool InitializePathModule();
    
    // 清理路径模块
    void CleanupPathModule();
    
    // 创建算路查询
    PathQueryPtr CreatePathQuery(const PointLL& start, const PointLL& end);
    
    // 单个路线评测任务
    void EvaluateRoute(const BaselineRoute& baseline_route);
    
    // 计算统计数据
    void CalculateStatistics();
    
    // 格式化输出
    std::string FormatDistance(double meters);
    std::string FormatTime(double seconds);
    std::string FormatPercentage(double percent);
};

// 路径监听器实现
class EvaluationPathListener : public PathListener {
public:
    EvaluationPathListener() = default;
    
    void OnPathResult(const PathQueryPtr& query, const PathResultPtr& result) override {
        std::lock_guard<std::mutex> lock(mutex_);
        results_[result->uuid] = {query, result};
        cv_.notify_all();
    }
    
    bool WaitForResult(const std::string& uuid, PathResultPtr& result, int timeout_ms = 30000) {
        std::unique_lock<std::mutex> lock(mutex_);
        
        auto deadline = std::chrono::steady_clock::now() + std::chrono::milliseconds(timeout_ms);
        
        while (results_.find(uuid) == results_.end()) {
            if (cv_.wait_until(lock, deadline) == std::cv_status::timeout) {
                return false;
            }
        }
        
        result = results_[uuid].second;
        results_.erase(uuid);
        return true;
    }
    
private:
    std::mutex mutex_;
    std::condition_variable cv_;
    std::map<std::string, std::pair<PathQueryPtr, PathResultPtr>> results_;
};

bool RouteBaselineEvaluator::InitializePathModule() {
    try {
        // 初始化日志
        if (!aurora::logger::get().init("logs/route_baseline_evaluator.log")) {
            std::cerr << "Failed to initialize logger" << std::endl;
            return false;
        }
        aurora::logger::get().set_level(spdlog::level::info);
        
        // 创建路径模块
        path_module_ = std::dynamic_pointer_cast<PathModule>(PathModule::GetInstance());
        if (!path_module_) {
            LOG_ERROR("Failed to create PathModule");
            return false;
        }
        
        // 准备模块
        if (path_module_->Prepare(config_file_) != 0) {
            LOG_ERROR("Failed to prepare PathModule");
            return false;
        }
        
        // 设置参数
        ParameterMap params;
        params["data_dir"] = data_dir_;
        if (path_module_->SetParams(params) != 0) {
            LOG_ERROR("Failed to set PathModule parameters");
            return false;
        }
        
        // 初始化模块
        auto module_finder = [](aurora::ModuleId id) -> std::shared_ptr<aurora::IInterface> {
            return nullptr;
        };
        
        if (path_module_->Init(module_finder) != 0) {
            LOG_ERROR("Failed to initialize PathModule");
            return false;
        }
        
        // 启动模块
        if (path_module_->Start() != 0) {
            LOG_ERROR("Failed to start PathModule");
            return false;
        }
        
        // 获取接口
        path_interface_ = std::dynamic_pointer_cast<PathInterface>(path_module_->GetInterface());
        if (!path_interface_) {
            LOG_ERROR("Failed to get PathInterface");
            return false;
        }
        
        LOG_INFO("PathModule initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("Exception during PathModule initialization: {}", e.what());
        return false;
    }
}

void RouteBaselineEvaluator::CleanupPathModule() {
    if (path_module_) {
        path_module_->Stop();
        path_module_->UnInit();
    }
    aurora::logger::get().shutdown();
}

bool RouteBaselineEvaluator::LoadBaselineData(const std::string& geojson_file) {
    LOG_INFO("Loading baseline data from: {}", geojson_file);

    // 读取文件内容
    std::ifstream file(geojson_file);
    if (!file.is_open()) {
        LOG_ERROR("Failed to open file: {}", geojson_file);
        return false;
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    file.close();
    std::string content = buffer.str();

    // 解析JSON
    rapidjson::Document doc;
    rapidjson::ParseResult result = doc.Parse(content.c_str());
    if (!result) {
        LOG_ERROR("JSON parse error: {} (offset {})",
                 rapidjson::GetParseError_En(result.Code()), result.Offset());
        return false;
    }

    // 检查是否为有效的GeoJSON FeatureCollection
    if (!doc.IsObject() || !doc.HasMember("type") ||
        !doc["type"].IsString() || std::string(doc["type"].GetString()) != "FeatureCollection" ||
        !doc.HasMember("features") || !doc["features"].IsArray()) {
        LOG_ERROR("Invalid GeoJSON format: not a FeatureCollection");
        return false;
    }

    // 处理每个feature
    const rapidjson::Value& features = doc["features"];
    baseline_routes_.clear();
    baseline_routes_.reserve(features.Size());

    for (rapidjson::SizeType i = 0; i < features.Size(); i++) {
        const rapidjson::Value& feature = features[i];

        // 检查feature结构
        if (!feature.IsObject() || !feature.HasMember("geometry") ||
            !feature.HasMember("properties")) {
            LOG_WARN("Skipping invalid feature at index {}", i);
            continue;
        }

        BaselineRoute route;
        route.id = "route_" + std::to_string(i);

        // 处理properties
        const rapidjson::Value& properties = feature["properties"];
        if (properties.IsObject()) {
            // 提取起点坐标
            if (properties.HasMember("original_start_coords") && properties["original_start_coords"].IsArray()) {
                const auto& start_coords = properties["original_start_coords"];
                if (start_coords.Size() >= 2) {
                    route.start_point = PointLL(start_coords[0].GetDouble(), start_coords[1].GetDouble());
                }
            }

            // 提取终点坐标
            if (properties.HasMember("original_end_coords") && properties["original_end_coords"].IsArray()) {
                const auto& end_coords = properties["original_end_coords"];
                if (end_coords.Size() >= 2) {
                    route.end_point = PointLL(end_coords[0].GetDouble(), end_coords[1].GetDouble());
                }
            }

            // 提取距离
            if (properties.HasMember("distance") && properties["distance"].IsNumber()) {
                route.distance = properties["distance"].GetDouble();
            }

            // 提取时间
            if (properties.HasMember("duration") && properties["duration"].IsNumber()) {
                route.duration = properties["duration"].GetDouble();
            }

            // 提取数据源
            if (properties.HasMember("source") && properties["source"].IsString()) {
                route.source = properties["source"].GetString();
            } else {
                route.source = "amap";  // 默认为高德
            }
        }

        // 处理geometry (LineString)
        const rapidjson::Value& geometry = feature["geometry"];
        if (geometry.IsObject() && geometry.HasMember("type") &&
            geometry["type"].IsString() && std::string(geometry["type"].GetString()) == "LineString" &&
            geometry.HasMember("coordinates") && geometry["coordinates"].IsArray()) {

            const rapidjson::Value& coordinates = geometry["coordinates"];
            route.geometry.reserve(coordinates.Size());

            for (rapidjson::SizeType j = 0; j < coordinates.Size(); j++) {
                const rapidjson::Value& point = coordinates[j];
                if (point.IsArray() && point.Size() >= 2) {
                    route.geometry.emplace_back(point[0].GetDouble(), point[1].GetDouble());
                }
            }
        }

        // 验证路线数据完整性
        if (route.start_point.IsValid() && route.end_point.IsValid() &&
            !route.geometry.empty() && route.distance > 0) {
            baseline_routes_.push_back(std::move(route));
        } else {
            LOG_WARN("Skipping incomplete route at index {}", i);
        }
    }

    LOG_INFO("Loaded {} baseline routes from {}", baseline_routes_.size(), geojson_file);
    return !baseline_routes_.empty();
}

PathQueryPtr RouteBaselineEvaluator::CreatePathQuery(const PointLL& start, const PointLL& end) {
    auto query = std::make_shared<PathQuery>();

    // 设置基本查询参数
    query->strategy = PathStrategy::kTimeFirst;
    query->trigger = PathTrigger::kInitialRouting;
    query->mode = PathMode::kOffline;
    query->date_option.type = DateTimeType::kCurrent;

    // 创建起点
    auto start_landmark = std::make_shared<PathLandmark>();
    start_landmark->valid = true;
    start_landmark->waypoint_type = WayPointType::kStartPoint;
    start_landmark->landmark_type = LandmarkType::kClick;
    start_landmark->pt = start;
    start_landmark->name = "Start Point";

    // 创建终点
    auto end_landmark = std::make_shared<PathLandmark>();
    end_landmark->valid = true;
    end_landmark->waypoint_type = WayPointType::kEndPoint;
    end_landmark->landmark_type = LandmarkType::kClick;
    end_landmark->pt = end;
    end_landmark->name = "End Point";

    query->path_points.push_back(start_landmark);
    query->path_points.push_back(end_landmark);

    return query;
}

void RouteBaselineEvaluator::EvaluateRoute(const BaselineRoute& baseline_route) {
    auto start_time = std::chrono::steady_clock::now();

    EvaluationResult eval_result;
    eval_result.route_id = baseline_route.id;
    eval_result.baseline = baseline_route;
    eval_result.success = false;

    try {
        // 创建路径监听器
        auto listener = std::make_shared<EvaluationPathListener>();
        path_interface_->AddPathListener(listener);

        // 创建算路查询
        auto query = CreatePathQuery(baseline_route.start_point, baseline_route.end_point);

        // 请求算路
        std::string uuid = path_interface_->RequestPath(query);
        if (uuid.empty()) {
            eval_result.error_message = "Failed to request path";
            LOG_ERROR("Failed to request path for route {}", baseline_route.id);
        } else {
            // 等待算路结果
            PathResultPtr result;
            if (listener->WaitForResult(uuid, result, 30000)) {
                if (result && result->code == 0 && !result->paths.empty()) {
                    eval_result.path_result = result;
                    eval_result.success = true;

                    // 创建基线路径结果用于对比
                    auto baseline_result = std::make_shared<PathResult>();
                    baseline_result->uuid = "baseline_" + baseline_route.id;
                    baseline_result->code = 0;
                    baseline_result->paths.resize(1);

                    auto& baseline_path = baseline_result->paths[0];
                    baseline_path.path_id = 0;
                    baseline_path.length = baseline_route.distance;
                    baseline_path.travel_time = baseline_route.duration;
                    baseline_path.points = baseline_route.geometry;

                    // 进行路线对比
                    eval_result.comparison = route_evaluator_->CompareRoutes(
                        query, result, 0,  // 自定义算路结果
                        query, baseline_result, 0  // 基线结果
                    );

                } else {
                    eval_result.error_message = "Path calculation failed or returned empty result";
                    LOG_ERROR("Path calculation failed for route {}: code={}",
                             baseline_route.id, result ? result->code : -1);
                }
            } else {
                eval_result.error_message = "Timeout waiting for path result";
                LOG_ERROR("Timeout waiting for path result for route {}", baseline_route.id);
            }
        }

        // 移除监听器
        path_interface_->RemovePathListener(listener);

    } catch (const std::exception& e) {
        eval_result.error_message = std::string("Exception: ") + e.what();
        LOG_ERROR("Exception during route evaluation for {}: {}", baseline_route.id, e.what());
    }

    // 计算耗时
    auto end_time = std::chrono::steady_clock::now();
    eval_result.calculation_time = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    // 保存结果
    {
        std::lock_guard<std::mutex> lock(results_mutex_);
        evaluation_results_.push_back(eval_result);
    }

    // 更新计数器
    if (eval_result.success) {
        completed_count_++;
        LOG_INFO("Route {} evaluation completed successfully ({}/{})",
                baseline_route.id, completed_count_.load(), baseline_routes_.size());
    } else {
        failed_count_++;
        LOG_ERROR("Route {} evaluation failed: {} ({} failed, {} completed)",
                 baseline_route.id, eval_result.error_message,
                 failed_count_.load(), completed_count_.load());
    }
}

void RouteBaselineEvaluator::RunEvaluation() {
    if (baseline_routes_.empty()) {
        LOG_ERROR("No baseline routes loaded");
        return;
    }

    LOG_INFO("Starting evaluation of {} routes using {} threads",
             baseline_routes_.size(), thread_count_);

    // 清空之前的结果
    evaluation_results_.clear();
    evaluation_results_.reserve(baseline_routes_.size());
    completed_count_ = 0;
    failed_count_ = 0;

    auto start_time = std::chrono::steady_clock::now();

    // 提交所有任务到线程池
    for (const auto& baseline_route : baseline_routes_) {
        thread_pool_.detach_task([this, baseline_route]() {
            this->EvaluateRoute(baseline_route);
        });
    }

    // 等待所有任务完成
    thread_pool_.wait();

    auto end_time = std::chrono::steady_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);

    LOG_INFO("Evaluation completed in {} seconds", total_time.count());
    LOG_INFO("Results: {} successful, {} failed, {} total",
             completed_count_.load(), failed_count_.load(), baseline_routes_.size());

    // 计算统计数据
    CalculateStatistics();
}

void RouteBaselineEvaluator::CalculateStatistics() {
    statistics_ = EvaluationStatistics{};

    if (evaluation_results_.empty()) {
        return;
    }

    statistics_.total_routes = evaluation_results_.size();

    std::vector<double> length_diffs;
    std::vector<double> time_diffs;
    std::vector<double> similarities;
    std::vector<std::chrono::milliseconds> calc_times;

    for (const auto& result : evaluation_results_) {
        if (result.success) {
            statistics_.successful_routes++;

            // 收集差异数据
            length_diffs.push_back(result.comparison.length_diff_percent);
            time_diffs.push_back(result.comparison.time_diff_percent);
            similarities.push_back(result.comparison.geometric_similarity);
            calc_times.push_back(result.calculation_time);

            // 相似度分布统计
            if (result.comparison.geometric_similarity > 0.9) {
                statistics_.high_similarity_count++;
            } else if (result.comparison.geometric_similarity > 0.7) {
                statistics_.medium_similarity_count++;
            } else {
                statistics_.low_similarity_count++;
            }

        } else {
            statistics_.failed_routes++;
        }

        // 计算时间统计（包括失败的）
        statistics_.total_calculation_time += result.calculation_time;
        statistics_.max_calculation_time = std::max(statistics_.max_calculation_time, result.calculation_time);
        statistics_.min_calculation_time = std::min(statistics_.min_calculation_time, result.calculation_time);
    }

    // 计算平均值和极值
    if (!length_diffs.empty()) {
        statistics_.avg_length_diff_percent = std::accumulate(length_diffs.begin(), length_diffs.end(), 0.0) / length_diffs.size();
        statistics_.max_length_diff_percent = *std::max_element(length_diffs.begin(), length_diffs.end());
        statistics_.min_length_diff_percent = *std::min_element(length_diffs.begin(), length_diffs.end());

        statistics_.avg_time_diff_percent = std::accumulate(time_diffs.begin(), time_diffs.end(), 0.0) / time_diffs.size();
        statistics_.max_time_diff_percent = *std::max_element(time_diffs.begin(), time_diffs.end());
        statistics_.min_time_diff_percent = *std::min_element(time_diffs.begin(), time_diffs.end());

        statistics_.avg_geometric_similarity = std::accumulate(similarities.begin(), similarities.end(), 0.0) / similarities.size();
        statistics_.max_geometric_similarity = *std::max_element(similarities.begin(), similarities.end());
        statistics_.min_geometric_similarity = *std::min_element(similarities.begin(), similarities.end());
    }

    // 平均计算时间
    if (statistics_.total_routes > 0) {
        statistics_.avg_calculation_time = statistics_.total_calculation_time / statistics_.total_routes;
    }

    if (statistics_.min_calculation_time == std::chrono::milliseconds::max()) {
        statistics_.min_calculation_time = std::chrono::milliseconds{0};
    }
}

std::string RouteBaselineEvaluator::FormatDistance(double meters) {
    if (meters >= 1000) {
        return std::to_string(static_cast<int>(meters / 100) / 10.0) + "公里";
    } else {
        return std::to_string(static_cast<int>(meters)) + "米";
    }
}

std::string RouteBaselineEvaluator::FormatTime(double seconds) {
    int hours = static_cast<int>(seconds / 3600);
    int minutes = static_cast<int>((seconds - hours * 3600) / 60);
    int secs = static_cast<int>(seconds - hours * 3600 - minutes * 60);

    std::stringstream ss;
    if (hours > 0) {
        ss << hours << "小时";
    }
    if (minutes > 0 || hours > 0) {
        ss << minutes << "分";
    }
    ss << secs << "秒";

    return ss.str();
}

std::string RouteBaselineEvaluator::FormatPercentage(double percent) {
    std::stringstream ss;
    ss << std::fixed << std::setprecision(2) << percent << "%";
    return ss.str();
}

void RouteBaselineEvaluator::GenerateReport(const std::string& output_file) {
    std::stringstream report;

    report << "================ 算路基线评测报告 ================\n";
    report << "评测时间: " << std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count() << "\n";
    report << "线程数量: " << thread_count_ << "\n";
    report << "基线数据源: " << (baseline_routes_.empty() ? "未知" : baseline_routes_[0].source) << "\n\n";

    // 总体统计
    report << "============== 总体统计 ==============\n";
    report << "总路线数: " << statistics_.total_routes << "\n";
    report << "成功路线: " << statistics_.successful_routes << " ("
           << FormatPercentage(100.0 * statistics_.successful_routes / statistics_.total_routes) << ")\n";
    report << "失败路线: " << statistics_.failed_routes << " ("
           << FormatPercentage(100.0 * statistics_.failed_routes / statistics_.total_routes) << ")\n\n";

    // 性能统计
    report << "============== 性能统计 ==============\n";
    report << "总计算时间: " << statistics_.total_calculation_time.count() << "毫秒\n";
    report << "平均计算时间: " << statistics_.avg_calculation_time.count() << "毫秒\n";
    report << "最大计算时间: " << statistics_.max_calculation_time.count() << "毫秒\n";
    report << "最小计算时间: " << statistics_.min_calculation_time.count() << "毫秒\n\n";

    if (statistics_.successful_routes > 0) {
        // 长度差异统计
        report << "============== 长度差异统计 ==============\n";
        report << "平均长度差异: " << FormatPercentage(statistics_.avg_length_diff_percent) << "\n";
        report << "最大长度差异: " << FormatPercentage(statistics_.max_length_diff_percent) << "\n";
        report << "最小长度差异: " << FormatPercentage(statistics_.min_length_diff_percent) << "\n\n";

        // 时间差异统计
        report << "============== 时间差异统计 ==============\n";
        report << "平均时间差异: " << FormatPercentage(statistics_.avg_time_diff_percent) << "\n";
        report << "最大时间差异: " << FormatPercentage(statistics_.max_time_diff_percent) << "\n";
        report << "最小时间差异: " << FormatPercentage(statistics_.min_time_diff_percent) << "\n\n";

        // 几何相似度统计
        report << "============== 几何相似度统计 ==============\n";
        report << "平均几何相似度: " << FormatPercentage(statistics_.avg_geometric_similarity * 100) << "\n";
        report << "最高几何相似度: " << FormatPercentage(statistics_.max_geometric_similarity * 100) << "\n";
        report << "最低几何相似度: " << FormatPercentage(statistics_.min_geometric_similarity * 100) << "\n\n";

        // 相似度分布
        report << "============== 相似度分布 ==============\n";
        report << "高相似度 (>90%): " << statistics_.high_similarity_count << " ("
               << FormatPercentage(100.0 * statistics_.high_similarity_count / statistics_.successful_routes) << ")\n";
        report << "中等相似度 (70%-90%): " << statistics_.medium_similarity_count << " ("
               << FormatPercentage(100.0 * statistics_.medium_similarity_count / statistics_.successful_routes) << ")\n";
        report << "低相似度 (<70%): " << statistics_.low_similarity_count << " ("
               << FormatPercentage(100.0 * statistics_.low_similarity_count / statistics_.successful_routes) << ")\n\n";
    }

    report << "================================================\n";

    std::string report_str = report.str();

    // 输出到控制台
    std::cout << report_str << std::endl;

    // 输出到日志
    LOG_INFO("Evaluation Report:\n{}", report_str);

    // 如果指定了输出文件，保存到文件
    if (!output_file.empty()) {
        std::ofstream file(output_file);
        if (file.is_open()) {
            file << report_str;
            file.close();
            LOG_INFO("Report saved to: {}", output_file);
        } else {
            LOG_ERROR("Failed to save report to: {}", output_file);
        }
    }
}

// 主函数
int main(int argc, char** argv) {
    // 默认参数
    std::string config_file = "/home/<USER>/dlc/map_engine/src/path/config/path.yaml";
    std::string data_dir = "/home/<USER>/dlc/map_engine/distribution/data/route";
    std::string geojson_file = "/mnt/d/osm/map_engine/debug/amap_test/amap_routes_backup_100.geojson";
    std::string output_file = "route_baseline_evaluation_report.txt";
    int thread_count = 8;

    // 解析命令行参数
    if (argc >= 2) {
        geojson_file = argv[1];
    }
    if (argc >= 3) {
        thread_count = std::stoi(argv[2]);
    }
    if (argc >= 4) {
        output_file = argv[3];
    }
    if (argc >= 5) {
        config_file = argv[4];
    }
    if (argc >= 6) {
        data_dir = argv[5];
    }

    std::cout << "算路基线评测工具" << std::endl;
    std::cout << "=================" << std::endl;
    std::cout << "GeoJSON文件: " << geojson_file << std::endl;
    std::cout << "线程数量: " << thread_count << std::endl;
    std::cout << "输出文件: " << output_file << std::endl;
    std::cout << "配置文件: " << config_file << std::endl;
    std::cout << "数据目录: " << data_dir << std::endl;
    std::cout << "=================" << std::endl;

    try {
        // 创建评测器
        RouteBaselineEvaluator evaluator(config_file, data_dir, thread_count);

        // 加载基线数据
        if (!evaluator.LoadBaselineData(geojson_file)) {
            std::cerr << "Failed to load baseline data from: " << geojson_file << std::endl;
            return 1;
        }

        // 运行评测
        evaluator.RunEvaluation();

        // 生成报告
        evaluator.GenerateReport(output_file);

        std::cout << "评测完成！报告已保存到: " << output_file << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}

// 使用说明
/*
编译命令:
cd build && make route_baseline_evaluator -j

使用方法:
./distribution/bin/route_baseline_evaluator [geojson_file] [thread_count] [output_file] [config_file] [data_dir]

参数说明:
- geojson_file: 基线数据GeoJSON文件路径 (默认: /mnt/d/osm/map_engine/debug/amap_test/amap_routes_backup_100.geojson)
- thread_count: 线程数量 (默认: 8)
- output_file: 输出报告文件路径 (默认: route_baseline_evaluation_report.txt)
- config_file: 路径配置文件 (默认: /home/<USER>/dlc/map_engine/src/path/config/path.yaml)
- data_dir: 路径数据目录 (默认: /home/<USER>/dlc/map_engine/distribution/data/route)

示例:
./distribution/bin/route_baseline_evaluator /path/to/amap_routes.geojson 16 evaluation_report.txt

功能特性:
1. 多线程并行算路评测
2. 支持高德地图基线数据对比
3. 多维度评测指标：长度差异、时间差异、几何相似度
4. 详细的统计报告和性能分析
5. 支持大规模数据集评测
6. 异常处理和错误恢复
*/
