{"type": "FeatureCollection", "features": [{"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.36361, 31.110043]}, "properties": {"id": 0, "type": "original_start", "name": "上海师范大学康城实验学校(莘北路校区)", "poi_type": "学校", "district": "闵行区", "dest_name": "莘松中学(春申校区)", "point_role": "起点", "distance_to_path_start": 16.896602746235484, "distance_to_amap_start": 17.12346165296348, "path_to_amap_start_distance": 1.2500869385960722, "path_start_distance_category": "10m+", "amap_start_distance_category": "10m+", "start_comparison_distance_category": "1-5m", "path_start_coords": [121.363778, 31.110092], "amap_start_coords": [121.363784, 31.110082]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.404795, 31.106286]}, "properties": {"id": 0, "type": "original_end", "name": "莘松中学(春申校区)", "poi_type": "学校", "district": "闵行区", "origin_name": "上海师范大学康城实验学校(莘北路校区)", "point_role": "终点", "distance_to_path_end": 23.620328965238723, "distance_to_amap_end": 24.207030347216616, "path_to_amap_end_distance": 0.9195015555732915, "path_end_distance_category": "10m+", "amap_end_distance_category": "10m+", "end_comparison_distance_category": "0-1m", "path_end_coords": [121.40501, 31.10618], "amap_end_coords": [121.405019, 31.106183]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.363778, 31.110092]}, "properties": {"id": 0, "type": "path_start", "name": "Path起点_上海师范大学康城实验学校(莘北路校区)", "original_poi_name": "上海师范大学康城实验学校(莘北路校区)", "original_poi_type": "学校", "original_poi_district": "闵行区", "dest_name": "莘松中学(春申校区)", "point_role": "算路起点", "distance_from_original": 16.896602746235484, "distance_category": "10m+", "distance_to_amap_start": 1.2500869385960722, "original_coords": [121.36361, 31.110043]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.363784, 31.110082]}, "properties": {"id": 0, "type": "amap_start", "name": "高德起点_上海师范大学康城实验学校(莘北路校区)", "original_poi_name": "上海师范大学康城实验学校(莘北路校区)", "original_poi_type": "学校", "original_poi_district": "闵行区", "dest_name": "莘松中学(春申校区)", "point_role": "算路起点", "distance_from_original": 17.12346165296348, "distance_category": "10m+", "distance_to_path_start": 1.2500869385960722, "original_coords": [121.36361, 31.110043]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.40501, 31.10618]}, "properties": {"id": 0, "type": "path_end", "name": "Path终点_莘松中学(春申校区)", "original_poi_name": "莘松中学(春申校区)", "original_poi_type": "学校", "original_poi_district": "闵行区", "origin_name": "上海师范大学康城实验学校(莘北路校区)", "point_role": "算路终点", "distance_from_original": 23.620328965238723, "distance_category": "10m+", "distance_to_amap_end": 0.9195015555732915, "original_coords": [121.404795, 31.106286]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.405019, 31.106183]}, "properties": {"id": 0, "type": "amap_end", "name": "高德终点_莘松中学(春申校区)", "original_poi_name": "莘松中学(春申校区)", "original_poi_type": "学校", "original_poi_district": "闵行区", "origin_name": "上海师范大学康城实验学校(莘北路校区)", "point_role": "算路终点", "distance_from_original": 24.207030347216616, "distance_category": "10m+", "distance_to_path_end": 0.9195015555732915, "original_coords": [121.404795, 31.106286]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.363778, 31.110092], [121.3637, 31.11036], [121.3637, 31.11036], [121.36361, 31.11055], [121.36361, 31.11055], [121.36354, 31.11072], [121.36354, 31.11072], [121.36419, 31.1109], [121.36419, 31.1109], [121.36497, 31.11111], [121.36497, 31.11111], [121.36522, 31.11118], [121.36522, 31.11118], [121.36442, 31.11314], [121.36442, 31.11314], [121.36382, 31.11461], [121.36382, 31.11461], [121.36376, 31.11475], [121.36376, 31.11475], [121.36366, 31.11499], [121.36366, 31.11499], [121.36582, 31.11566], [121.36582, 31.11566], [121.36805, 31.11635], [121.36805, 31.11635], [121.37092, 31.11714], [121.37092, 31.11714], [121.37133, 31.11726], [121.37133, 31.11726], [121.37201, 31.11743], [121.37201, 31.11743], [121.3734, 31.1177], [121.3734, 31.1177], [121.37358, 31.11774], [121.37358, 31.11774], [121.37369, 31.11776], [121.37369, 31.11776], [121.37546, 31.11795], [121.37706, 31.11802], [121.37721, 31.11803], [121.37732, 31.11807], [121.37732, 31.11807], [121.37962, 31.11811], [121.37962, 31.11811], [121.37983, 31.11811], [121.37983, 31.11811], [121.38025, 31.11812], [121.38025, 31.11812], [121.3813, 31.11813], [121.3813, 31.11813], [121.38136, 31.11809], [121.38147, 31.11807], [121.38259, 31.118], [121.38377, 31.11797], [121.38377, 31.11797], [121.38452, 31.11794], [121.38518, 31.11786], [121.38518, 31.11786], [121.38599, 31.1177], [121.38647, 31.11757], [121.38701, 31.11737], [121.38748, 31.11712], [121.38803, 31.11678], [121.38869, 31.11628], [121.38901, 31.11597], [121.38964, 31.11542], [121.39029, 31.11481], [121.39029, 31.11481], [121.39062, 31.11448], [121.39101, 31.11402], [121.39156, 31.11327], [121.39156, 31.11327], [121.39266, 31.11146], [121.39266, 31.11146], [121.39312, 31.11073], [121.39312, 31.11073], [121.39398, 31.10943], [121.3942, 31.10905], [121.3942, 31.10905], [121.39494, 31.10792], [121.39494, 31.10792], [121.39493, 31.10785], [121.39494, 31.10775], [121.39517, 31.10734], [121.3967, 31.10494], [121.3967, 31.10494], [121.39694, 31.10486], [121.39694, 31.10486], [121.39701, 31.10482], [121.39711, 31.10469], [121.39723, 31.1046], [121.39749, 31.10455], [121.39749, 31.10455], [121.39764, 31.1046], [121.39764, 31.1046], [121.39944, 31.10525], [121.39944, 31.10525], [121.40101, 31.10581], [121.40101, 31.10581], [121.40177, 31.10609], [121.40177, 31.10609], [121.40346, 31.10669], [121.40519, 31.10733], [121.40519, 31.10733], [121.40578, 31.10606], [121.40578, 31.10606], [121.40559, 31.10606], [121.40559, 31.10606], [121.40553, 31.10603], [121.40553, 31.10603], [121.40543, 31.10632], [121.40543, 31.10632], [121.40501, 31.10618]]}, "properties": {"id": 0, "type": "path_route", "name": "Path路线_上海师范大学康城实验学校(莘北路校区)", "origin_name": "上海师范大学康城实验学校(莘北路校区)", "origin_type": "学校", "origin_district": "闵行区", "dest_name": "莘松中学(春申校区)", "route_type": "Path算路", "start_coords": [121.363778, 31.110092], "end_coords": [121.40501, 31.10618], "original_start_coords": [121.36361, 31.110043], "original_end_coords": [121.404795, 31.106286]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.363784, 31.110082], [121.363697, 31.110362], [121.363616, 31.11055], [121.363547, 31.110722], [121.363547, 31.110722], [121.364255, 31.110904], [121.364974, 31.111119], [121.365226, 31.111178], [121.365789, 31.111333], [121.366492, 31.111526], [121.366959, 31.111645], [121.367506, 31.1118], [121.368648, 31.112111], [121.368799, 31.112154], [121.369238, 31.112278], [121.369544, 31.112374], [121.369737, 31.112444], [121.36985, 31.112481], [121.370242, 31.112642], [121.370891, 31.112921], [121.371427, 31.113184], [121.37162, 31.113275], [121.371631, 31.113281], [121.372071, 31.113501], [121.37257, 31.113742], [121.372811, 31.11386], [121.372972, 31.11394], [121.373235, 31.114069], [121.373686, 31.114289], [121.374378, 31.114638], [121.374608, 31.11475], [121.374914, 31.114895], [121.375327, 31.115115], [121.37544, 31.115174], [121.375053, 31.115732], [121.374834, 31.11606], [121.374544, 31.116483], [121.37449, 31.116558], [121.374383, 31.116714], [121.374308, 31.11687], [121.374163, 31.117079], [121.373691, 31.11776], [121.373691, 31.11776], [121.375466, 31.117948], [121.375595, 31.117958], [121.376953, 31.118017], [121.377065, 31.118023], [121.377215, 31.118039], [121.377323, 31.118076], [121.378428, 31.118098], [121.379624, 31.118114], [121.379833, 31.118114], [121.380257, 31.118119], [121.380257, 31.118119], [121.38141, 31.11813], [121.38141, 31.11813], [121.381469, 31.118087], [121.381534, 31.118076], [121.382585, 31.118007], [121.383776, 31.117975], [121.384522, 31.117942], [121.384828, 31.11791], [121.385181, 31.117867], [121.385181, 31.117867], [121.385852, 31.117733], [121.386185, 31.117658], [121.386474, 31.117572], [121.387011, 31.117368], [121.387488, 31.117121], [121.38803, 31.116778], [121.38869, 31.11629], [121.389017, 31.115979], [121.389645, 31.115421], [121.390165, 31.114922], [121.390294, 31.114815], [121.390621, 31.114482], [121.391018, 31.114021], [121.391479, 31.113388], [121.391479, 31.113388], [121.391549, 31.113281], [121.392305, 31.112116], [121.394478, 31.108748], [121.394478, 31.108748], [121.394687, 31.108313], [121.394929, 31.107938], [121.394998, 31.107809], [121.395755, 31.106602], [121.396871, 31.1048], [121.397064, 31.104478], [121.397643, 31.104606], [121.398925, 31.105062], [121.399446, 31.10525], [121.400851, 31.105754], [121.401012, 31.105813], [121.401082, 31.10584], [121.401221, 31.105894], [121.401774, 31.106098], [121.403469, 31.106693], [121.405164, 31.107321], [121.405164, 31.107321], [121.405716, 31.106135], [121.405781, 31.10606], [121.405781, 31.10606], [121.405598, 31.10606], [121.405534, 31.106039], [121.405534, 31.106039], [121.405432, 31.106323], [121.405432, 31.106323], [121.405019, 31.106183]]}, "properties": {"id": 0, "type": "amap_route", "name": "高德路线_上海师范大学康城实验学校(莘北路校区)", "origin_name": "上海师范大学康城实验学校(莘北路校区)", "origin_type": "学校", "origin_district": "闵行区", "dest_name": "莘松中学(春申校区)", "route_type": "高德算路", "start_coords": [121.363784, 31.110082], "end_coords": [121.405019, 31.106183], "original_start_coords": [121.36361, 31.110043], "original_end_coords": [121.404795, 31.106286]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.431775, 31.363418]}, "properties": {"id": 1, "type": "original_start", "name": "宝山区杨行镇社区卫生服务中心", "poi_type": "医院", "district": "宝山区", "dest_name": "宝宸怡景园", "point_role": "起点", "distance_to_path_start": 7.348888402825026, "distance_to_amap_start": 8.206766331960063, "path_to_amap_start_distance": 0.911716284491788, "path_start_distance_category": "5-10m", "amap_start_distance_category": "5-10m", "start_comparison_distance_category": "0-1m", "path_start_coords": [121.431789, 31.363483], "amap_start_coords": [121.431794, 31.36349]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.43514, 31.3313]}, "properties": {"id": 1, "type": "original_end", "name": "宝宸怡景园", "poi_type": "住宅小区", "district": "宝山区", "origin_name": "宝山区杨行镇社区卫生服务中心", "point_role": "终点", "distance_to_path_end": 22.238985328859922, "distance_to_amap_end": 22.79495996225919, "path_to_amap_end_distance": 0.5559746333992677, "path_end_distance_category": "10m+", "amap_end_distance_category": "10m+", "end_comparison_distance_category": "0-1m", "path_end_coords": [121.43514, 31.3315], "amap_end_coords": [121.43514, 31.331505]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.431789, 31.363483]}, "properties": {"id": 1, "type": "path_start", "name": "Path起点_宝山区杨行镇社区卫生服务中心", "original_poi_name": "宝山区杨行镇社区卫生服务中心", "original_poi_type": "医院", "original_poi_district": "宝山区", "dest_name": "宝宸怡景园", "point_role": "算路起点", "distance_from_original": 7.348888402825026, "distance_category": "5-10m", "distance_to_amap_start": 0.911716284491788, "original_coords": [121.431775, 31.363418]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.431794, 31.36349]}, "properties": {"id": 1, "type": "amap_start", "name": "高德起点_宝山区杨行镇社区卫生服务中心", "original_poi_name": "宝山区杨行镇社区卫生服务中心", "original_poi_type": "医院", "original_poi_district": "宝山区", "dest_name": "宝宸怡景园", "point_role": "算路起点", "distance_from_original": 8.206766331960063, "distance_category": "5-10m", "distance_to_path_start": 0.911716284491788, "original_coords": [121.431775, 31.363418]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.43514, 31.3315]}, "properties": {"id": 1, "type": "path_end", "name": "Path终点_宝宸怡景园", "original_poi_name": "宝宸怡景园", "original_poi_type": "住宅小区", "original_poi_district": "宝山区", "origin_name": "宝山区杨行镇社区卫生服务中心", "point_role": "算路终点", "distance_from_original": 22.238985328859922, "distance_category": "10m+", "distance_to_amap_end": 0.5559746333992677, "original_coords": [121.43514, 31.3313]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.43514, 31.331505]}, "properties": {"id": 1, "type": "amap_end", "name": "高德终点_宝宸怡景园", "original_poi_name": "宝宸怡景园", "original_poi_type": "住宅小区", "original_poi_district": "宝山区", "origin_name": "宝山区杨行镇社区卫生服务中心", "point_role": "算路终点", "distance_from_original": 22.79495996225919, "distance_category": "10m+", "distance_to_path_end": 0.5559746333992677, "original_coords": [121.43514, 31.3313]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.431789, 31.363483], [121.43161, 31.36352], [121.43161, 31.36352], [121.43156, 31.36361], [121.43156, 31.36361], [121.43192, 31.36374], [121.43192, 31.36374], [121.43212, 31.36381], [121.43212, 31.36381], [121.43226, 31.36372], [121.43241, 31.36367], [121.43241, 31.36367], [121.43257, 31.36368], [121.43257, 31.36368], [121.43273, 31.36281], [121.43291, 31.36211], [121.43291, 31.36211], [121.43293, 31.36203], [121.43293, 31.36203], [121.43314, 31.36095], [121.43349, 31.35942], [121.43349, 31.35942], [121.43356, 31.35903], [121.43356, 31.35903], [121.4337, 31.35796], [121.43394, 31.35539], [121.43394, 31.35539], [121.434, 31.35469], [121.434, 31.35469], [121.43406, 31.35404], [121.43422, 31.35272], [121.43435, 31.35148], [121.43435, 31.35148], [121.43443, 31.35056], [121.43443, 31.35056], [121.43449, 31.34985], [121.43449, 31.34985], [121.43458, 31.34897], [121.43458, 31.34897], [121.43471, 31.34773], [121.43501, 31.34628], [121.43501, 31.34628], [121.4352, 31.34559], [121.4352, 31.34559], [121.43541, 31.34498], [121.43598, 31.34357], [121.43598, 31.34357], [121.43634, 31.3427], [121.43634, 31.3427], [121.4363, 31.34262], [121.43706, 31.34075], [121.43706, 31.34075], [121.43795, 31.3386], [121.43864, 31.33704], [121.43864, 31.33704], [121.43889, 31.33656], [121.43913, 31.33604], [121.43913, 31.33604], [121.43853, 31.33545], [121.4385, 31.33538], [121.4385, 31.33538], [121.43829, 31.3352], [121.43829, 31.3352], [121.43816, 31.33509], [121.43816, 31.33509], [121.43788, 31.33485], [121.43788, 31.33485], [121.43777, 31.33475], [121.43777, 31.33475], [121.43733, 31.33439], [121.43733, 31.33439], [121.43686, 31.33408], [121.43657, 31.33393], [121.43633, 31.33382], [121.43573, 31.33359], [121.43573, 31.33359], [121.43637, 31.33228], [121.43637, 31.33228], [121.43675, 31.33144], [121.43675, 31.33144], [121.43711, 31.33064], [121.43711, 31.33064], [121.4369, 31.33058], [121.4369, 31.33058], [121.43673, 31.33054], [121.43673, 31.33054], [121.43616, 31.33046], [121.43616, 31.33046], [121.43597, 31.33087], [121.43597, 31.33087], [121.43593, 31.33096], [121.43593, 31.33096], [121.43577, 31.33129], [121.43577, 31.33129], [121.43573, 31.33138], [121.43573, 31.33138], [121.43549, 31.33144], [121.43549, 31.33144], [121.43531, 31.3315], [121.43514, 31.3315]]}, "properties": {"id": 1, "type": "path_route", "name": "Path路线_宝山区杨行镇社区卫生服务中心", "origin_name": "宝山区杨行镇社区卫生服务中心", "origin_type": "医院", "origin_district": "宝山区", "dest_name": "宝宸怡景园", "route_type": "Path算路", "start_coords": [121.431789, 31.363483], "end_coords": [121.43514, 31.3315], "original_start_coords": [121.431775, 31.363418], "original_end_coords": [121.43514, 31.3313]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.431794, 31.36349], [121.431616, 31.363525], [121.431616, 31.363525], [121.431568, 31.363616], [121.431568, 31.363616], [121.431927, 31.36374], [121.432126, 31.36382], [121.432126, 31.36382], [121.43219, 31.363761], [121.432372, 31.363686], [121.432415, 31.363676], [121.432512, 31.363681], [121.432571, 31.363692], [121.432571, 31.363692], [121.432732, 31.362817], [121.432909, 31.36212], [121.432936, 31.362029], [121.433145, 31.36095], [121.433499, 31.359427], [121.433563, 31.359035], [121.433563, 31.359035], [121.433617, 31.358719], [121.43373, 31.357689], [121.433939, 31.355388], [121.433944, 31.355328], [121.433976, 31.35498], [121.434003, 31.354696], [121.434062, 31.354041], [121.434228, 31.352721], [121.434352, 31.351482], [121.434395, 31.351026], [121.434438, 31.35057], [121.434497, 31.349857], [121.434588, 31.348972], [121.434716, 31.347733], [121.435011, 31.346284], [121.435199, 31.345592], [121.435409, 31.344986], [121.43557, 31.344584], [121.435811, 31.343993], [121.435983, 31.343575], [121.43601, 31.3435], [121.436138, 31.343189], [121.436353, 31.342674], [121.436455, 31.342427], [121.436567, 31.342153], [121.436851, 31.341467], [121.437141, 31.340775], [121.437141, 31.340775], [121.4372, 31.340442], [121.437726, 31.339187], [121.438016, 31.338468], [121.438429, 31.33753], [121.438649, 31.337046], [121.438895, 31.336564], [121.439131, 31.336038], [121.439131, 31.336038], [121.438536, 31.335459], [121.438504, 31.335378], [121.438161, 31.335099], [121.437887, 31.334853], [121.437774, 31.334756], [121.437335, 31.334396], [121.4372, 31.334289], [121.436868, 31.334085], [121.436546, 31.333919], [121.436219, 31.33378], [121.436031, 31.33371], [121.435736, 31.333597], [121.435736, 31.333597], [121.43602, 31.333007], [121.436374, 31.332283], [121.436519, 31.331972], [121.436755, 31.331446], [121.437115, 31.330641], [121.437115, 31.330641], [121.436911, 31.330588], [121.436728, 31.33054], [121.436406, 31.330491], [121.436165, 31.330465], [121.436165, 31.330465], [121.436127, 31.330545], [121.435977, 31.330872], [121.435934, 31.330958], [121.435773, 31.331301], [121.435741, 31.331387], [121.435741, 31.331387], [121.4355, 31.331451], [121.435317, 31.331505], [121.43514, 31.331505]]}, "properties": {"id": 1, "type": "amap_route", "name": "高德路线_宝山区杨行镇社区卫生服务中心", "origin_name": "宝山区杨行镇社区卫生服务中心", "origin_type": "医院", "origin_district": "宝山区", "dest_name": "宝宸怡景园", "route_type": "高德算路", "start_coords": [121.431794, 31.36349], "end_coords": [121.43514, 31.331505], "original_start_coords": [121.431775, 31.363418], "original_end_coords": [121.43514, 31.3313]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.477288, 31.237436]}, "properties": {"id": 2, "type": "original_start", "name": "艾妮时尚宾馆(浙江路店)", "poi_type": "酒店", "district": "黄浦区", "dest_name": "上海开放大学时尚学院", "point_role": "起点", "distance_to_path_start": 10.327165861142982, "distance_to_amap_start": 9.762869473629287, "path_to_amap_start_distance": 1.4579867504246469, "path_start_distance_category": "10m+", "amap_start_distance_category": "5-10m", "start_comparison_distance_category": "1-5m", "path_start_coords": [121.477198, 31.237384], "amap_start_coords": [121.477196, 31.237397]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.432809, 31.238791]}, "properties": {"id": 2, "type": "original_end", "name": "上海开放大学时尚学院", "poi_type": "学校", "district": "普陀区", "origin_name": "艾妮时尚宾馆(浙江路店)", "point_role": "终点", "distance_to_path_end": 14.299487876466458, "distance_to_amap_end": 13.553911015472737, "path_to_amap_end_distance": 3.226054100148811, "path_end_distance_category": "10m+", "amap_end_distance_category": "10m+", "end_comparison_distance_category": "1-5m", "path_end_coords": [121.432669, 31.238744], "amap_end_coords": [121.432668, 31.238773]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.477198, 31.237384]}, "properties": {"id": 2, "type": "path_start", "name": "Path起点_艾妮时尚宾馆(浙江路店)", "original_poi_name": "艾妮时尚宾馆(浙江路店)", "original_poi_type": "酒店", "original_poi_district": "黄浦区", "dest_name": "上海开放大学时尚学院", "point_role": "算路起点", "distance_from_original": 10.327165861142982, "distance_category": "10m+", "distance_to_amap_start": 1.4579867504246469, "original_coords": [121.477288, 31.237436]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.477196, 31.237397]}, "properties": {"id": 2, "type": "amap_start", "name": "高德起点_艾妮时尚宾馆(浙江路店)", "original_poi_name": "艾妮时尚宾馆(浙江路店)", "original_poi_type": "酒店", "original_poi_district": "黄浦区", "dest_name": "上海开放大学时尚学院", "point_role": "算路起点", "distance_from_original": 9.762869473629287, "distance_category": "5-10m", "distance_to_path_start": 1.4579867504246469, "original_coords": [121.477288, 31.237436]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.432669, 31.238744]}, "properties": {"id": 2, "type": "path_end", "name": "Path终点_上海开放大学时尚学院", "original_poi_name": "上海开放大学时尚学院", "original_poi_type": "学校", "original_poi_district": "普陀区", "origin_name": "艾妮时尚宾馆(浙江路店)", "point_role": "算路终点", "distance_from_original": 14.299487876466458, "distance_category": "10m+", "distance_to_amap_end": 3.226054100148811, "original_coords": [121.432809, 31.238791]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.432668, 31.238773]}, "properties": {"id": 2, "type": "amap_end", "name": "高德终点_上海开放大学时尚学院", "original_poi_name": "上海开放大学时尚学院", "original_poi_type": "学校", "original_poi_district": "普陀区", "origin_name": "艾妮时尚宾馆(浙江路店)", "point_role": "算路终点", "distance_from_original": 13.553911015472737, "distance_category": "10m+", "distance_to_path_end": 3.226054100148811, "original_coords": [121.432809, 31.238791]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.477198, 31.237384], [121.47746, 31.23693], [121.47746, 31.23693], [121.4777, 31.23702], [121.4777, 31.23702], [121.47818, 31.2372], [121.47818, 31.2372], [121.47852, 31.23732], [121.47852, 31.23732], [121.47879, 31.23741], [121.47879, 31.23741], [121.47885, 31.23743], [121.47885, 31.23743], [121.47898, 31.23749], [121.47898, 31.23749], [121.47961, 31.23769], [121.47961, 31.23769], [121.47946, 31.23808], [121.47946, 31.23808], [121.47937, 31.23833], [121.47937, 31.23833], [121.47915, 31.23892], [121.47915, 31.23892], [121.4791, 31.23909], [121.4791, 31.23909], [121.47908, 31.23916], [121.47908, 31.23916], [121.47894, 31.23955], [121.4789, 31.23971], [121.47888, 31.24], [121.47888, 31.24], [121.47888, 31.24031], [121.47888, 31.24031], [121.47888, 31.24063], [121.47888, 31.24063], [121.47887, 31.24149], [121.47878, 31.24204], [121.47878, 31.24204], [121.47872, 31.24235], [121.47872, 31.24235], [121.47853, 31.24334], [121.47853, 31.24334], [121.47848, 31.24342], [121.47848, 31.24342], [121.47733, 31.2433], [121.47733, 31.2433], [121.47617, 31.24317], [121.47617, 31.24317], [121.47612, 31.24354], [121.47612, 31.24354], [121.47602, 31.2441], [121.47584, 31.24465], [121.47584, 31.24465], [121.47578, 31.24481], [121.47578, 31.24481], [121.47574, 31.24493], [121.47574, 31.24493], [121.47539, 31.24566], [121.47539, 31.24566], [121.47526, 31.24604], [121.47526, 31.24604], [121.47482, 31.2459], [121.47395, 31.24569], [121.47395, 31.24569], [121.47372, 31.24564], [121.47372, 31.24564], [121.47356, 31.24553], [121.47317, 31.24535], [121.47317, 31.24535], [121.46948, 31.24456], [121.46903, 31.24448], [121.46793, 31.24435], [121.46793, 31.24435], [121.46735, 31.24432], [121.4668, 31.24432], [121.46619, 31.24438], [121.46619, 31.24438], [121.46482, 31.24456], [121.46432, 31.24466], [121.46375, 31.24481], [121.46298, 31.24506], [121.46042, 31.24583], [121.46004, 31.24593], [121.4598, 31.24597], [121.45911, 31.24604], [121.45911, 31.24604], [121.45841, 31.24604], [121.45841, 31.24604], [121.45809, 31.24605], [121.45809, 31.24605], [121.45758, 31.24608], [121.45628, 31.24608], [121.45549, 31.24606], [121.4545, 31.24598], [121.4545, 31.24598], [121.45244, 31.24571], [121.45244, 31.24571], [121.4512, 31.24564], [121.4512, 31.24564], [121.4507, 31.24555], [121.4507, 31.24555], [121.45032, 31.24553], [121.45032, 31.24553], [121.45004, 31.24549], [121.45004, 31.24549], [121.44967, 31.24544], [121.44967, 31.24544], [121.44902, 31.24534], [121.44902, 31.24534], [121.44833, 31.24525], [121.44833, 31.24525], [121.44746, 31.24509], [121.44746, 31.24509], [121.44643, 31.24487], [121.44643, 31.24487], [121.4459, 31.2447], [121.44584, 31.24467], [121.44572, 31.24456], [121.44572, 31.24456], [121.44491, 31.2444], [121.44491, 31.2444], [121.44437, 31.24428], [121.44437, 31.24428], [121.444, 31.24422], [121.444, 31.24422], [121.44351, 31.24411], [121.44351, 31.24411], [121.44342, 31.24409], [121.44342, 31.24409], [121.44316, 31.24402], [121.44261, 31.24381], [121.44261, 31.24381], [121.44225, 31.24365], [121.44225, 31.24365], [121.44194, 31.24351], [121.44194, 31.24351], [121.44136, 31.24325], [121.44136, 31.24325], [121.44075, 31.24296], [121.44026, 31.24261], [121.44026, 31.24261], [121.43998, 31.24238], [121.43998, 31.24238], [121.43977, 31.24217], [121.43977, 31.24217], [121.43868, 31.2412], [121.43868, 31.2412], [121.43861, 31.24114], [121.43861, 31.24114], [121.43846, 31.24102], [121.43846, 31.24102], [121.43809, 31.24077], [121.43809, 31.24077], [121.43761, 31.24045], [121.43761, 31.24045], [121.43732, 31.24025], [121.43706, 31.24004], [121.43681, 31.2398], [121.43681, 31.2398], [121.43673, 31.23971], [121.43673, 31.23971], [121.4364, 31.23928], [121.43568, 31.23846], [121.43568, 31.23846], [121.43546, 31.2383], [121.43546, 31.2383], [121.43533, 31.23821], [121.43509, 31.2381], [121.43471, 31.23798], [121.43471, 31.23798], [121.43428, 31.23786], [121.43428, 31.23786], [121.43414, 31.23797], [121.43395, 31.23803], [121.43395, 31.23803], [121.43367, 31.2381], [121.43367, 31.2381], [121.43356, 31.23813], [121.43356, 31.23813], [121.43322, 31.23822], [121.43322, 31.23822], [121.43302, 31.23827], [121.43302, 31.23827], [121.43289, 31.2383], [121.43289, 31.2383], [121.43254, 31.23839], [121.43254, 31.23839], [121.43259, 31.23852], [121.43259, 31.23852], [121.43261, 31.23858], [121.43261, 31.23858], [121.43267, 31.23874], [121.432669, 31.238744]]}, "properties": {"id": 2, "type": "path_route", "name": "Path路线_艾妮时尚宾馆(浙江路店)", "origin_name": "艾妮时尚宾馆(浙江路店)", "origin_type": "酒店", "origin_district": "黄浦区", "dest_name": "上海开放大学时尚学院", "route_type": "Path算路", "start_coords": [121.477198, 31.237384], "end_coords": [121.432669, 31.238744], "original_start_coords": [121.477288, 31.237436], "original_end_coords": [121.432809, 31.238791]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.477196, 31.237397], [121.47746, 31.236936], [121.47746, 31.236936], [121.477707, 31.237027], [121.478185, 31.237204], [121.478522, 31.237322], [121.478791, 31.237419], [121.47885, 31.23744], [121.478989, 31.237494], [121.479617, 31.237698], [121.479617, 31.237698], [121.479466, 31.238089], [121.479375, 31.238331], [121.479155, 31.238926], [121.479107, 31.239098], [121.479091, 31.239162], [121.478946, 31.239543], [121.478903, 31.239709], [121.478887, 31.23987], [121.478882, 31.240315], [121.478882, 31.240637], [121.478871, 31.241383], [121.47885, 31.241662], [121.478785, 31.242043], [121.478726, 31.242359], [121.478533, 31.243346], [121.478485, 31.243421], [121.477337, 31.243309], [121.476173, 31.243175], [121.476173, 31.243175], [121.476125, 31.243545], [121.476071, 31.243893], [121.476023, 31.244108], [121.475846, 31.244661], [121.475781, 31.244816], [121.475738, 31.24494], [121.475652, 31.245116], [121.475368, 31.245744], [121.475363, 31.245932], [121.473984, 31.24568], [121.473796, 31.245648], [121.473796, 31.245648], [121.473566, 31.245535], [121.47289, 31.245342], [121.472648, 31.245235], [121.472106, 31.245116], [121.471935, 31.245079], [121.469489, 31.244564], [121.468743, 31.244441], [121.467938, 31.24435], [121.467365, 31.244323], [121.466806, 31.244323], [121.46619, 31.244387], [121.464843, 31.24457], [121.464645, 31.244601], [121.464328, 31.244661], [121.463754, 31.244816], [121.462982, 31.245063], [121.462805, 31.245116], [121.460423, 31.245835], [121.460042, 31.245932], [121.459559, 31.246012], [121.459119, 31.246039], [121.458765, 31.246045], [121.458411, 31.24605], [121.4581, 31.246055], [121.457719, 31.246071], [121.456287, 31.246088], [121.455198, 31.246039], [121.454506, 31.24598], [121.454307, 31.245953], [121.454023, 31.245916], [121.452854, 31.245766], [121.452113, 31.245669], [121.452113, 31.245669], [121.451716, 31.245658], [121.450826, 31.245546], [121.450826, 31.245546], [121.450053, 31.245353], [121.448433, 31.245116], [121.448315, 31.2451], [121.447779, 31.245015], [121.447033, 31.244865], [121.446261, 31.244703], [121.445735, 31.244596], [121.445279, 31.244484], [121.445215, 31.244473], [121.44492, 31.244403], [121.444373, 31.24429], [121.444067, 31.244231], [121.444008, 31.244221], [121.443809, 31.244178], [121.44352, 31.244119], [121.443428, 31.244097], [121.44316, 31.244028], [121.44287, 31.24392], [121.442618, 31.243818], [121.442254, 31.243652], [121.441942, 31.243518], [121.441363, 31.24326], [121.44088, 31.243035], [121.440757, 31.24296], [121.440644, 31.242896], [121.440263, 31.242617], [121.43999, 31.242381], [121.43977, 31.242177], [121.439335, 31.241791], [121.438751, 31.241265], [121.438686, 31.241206], [121.438611, 31.241147], [121.438461, 31.241023], [121.438091, 31.240777], [121.437898, 31.240643], [121.437619, 31.240455], [121.437286, 31.240224], [121.437168, 31.240128], [121.437066, 31.240047], [121.436814, 31.239806], [121.436739, 31.239709], [121.436401, 31.23928], [121.435688, 31.238465], [121.435333, 31.238218], [121.435092, 31.238111], [121.434716, 31.237982], [121.434282, 31.237864], [121.434282, 31.237864], [121.434185, 31.237955], [121.434143, 31.237976], [121.433955, 31.238036], [121.433676, 31.238105], [121.433563, 31.238138], [121.433225, 31.238223], [121.433021, 31.238277], [121.432898, 31.238309], [121.432549, 31.238395], [121.432549, 31.238395], [121.432592, 31.238524], [121.432608, 31.238588], [121.432673, 31.238744], [121.432668, 31.238773]]}, "properties": {"id": 2, "type": "amap_route", "name": "高德路线_艾妮时尚宾馆(浙江路店)", "origin_name": "艾妮时尚宾馆(浙江路店)", "origin_type": "酒店", "origin_district": "黄浦区", "dest_name": "上海开放大学时尚学院", "route_type": "高德算路", "start_coords": [121.477196, 31.237397], "end_coords": [121.432668, 31.238773], "original_start_coords": [121.477288, 31.237436], "original_end_coords": [121.432809, 31.238791]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.557671, 31.203162]}, "properties": {"id": 3, "type": "original_start", "name": "海底捞火锅(龙阳广场店)", "poi_type": "餐厅", "district": "浦东新区", "dest_name": "北蔡社区卫生服务中心", "point_role": "起点", "distance_to_path_start": 47.530598082106856, "distance_to_amap_start": 47.583855813286384, "path_to_amap_start_distance": 0.9510855782645378, "path_start_distance_category": "10m+", "amap_start_distance_category": "10m+", "start_comparison_distance_category": "0-1m", "path_start_coords": [121.557694, 31.203589], "amap_start_coords": [121.557704, 31.203589]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.553468, 31.185281]}, "properties": {"id": 3, "type": "original_end", "name": "北蔡社区卫生服务中心", "poi_type": "医院", "district": "浦东新区", "origin_name": "海底捞火锅(龙阳广场店)", "point_role": "终点", "distance_to_path_end": 6.5635091759827535, "distance_to_amap_end": 6.1888372887873375, "path_to_amap_end_distance": 0.38050788332571905, "path_end_distance_category": "5-10m", "amap_end_distance_category": "5-10m", "end_comparison_distance_category": "0-1m", "path_end_coords": [121.5534, 31.185271], "amap_end_coords": [121.553404, 31.185271]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.557694, 31.203589]}, "properties": {"id": 3, "type": "path_start", "name": "Path起点_海底捞火锅(龙阳广场店)", "original_poi_name": "海底捞火锅(龙阳广场店)", "original_poi_type": "餐厅", "original_poi_district": "浦东新区", "dest_name": "北蔡社区卫生服务中心", "point_role": "算路起点", "distance_from_original": 47.530598082106856, "distance_category": "10m+", "distance_to_amap_start": 0.9510855782645378, "original_coords": [121.557671, 31.203162]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.557704, 31.203589]}, "properties": {"id": 3, "type": "amap_start", "name": "高德起点_海底捞火锅(龙阳广场店)", "original_poi_name": "海底捞火锅(龙阳广场店)", "original_poi_type": "餐厅", "original_poi_district": "浦东新区", "dest_name": "北蔡社区卫生服务中心", "point_role": "算路起点", "distance_from_original": 47.583855813286384, "distance_category": "10m+", "distance_to_path_start": 0.9510855782645378, "original_coords": [121.557671, 31.203162]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.5534, 31.185271]}, "properties": {"id": 3, "type": "path_end", "name": "Path终点_北蔡社区卫生服务中心", "original_poi_name": "北蔡社区卫生服务中心", "original_poi_type": "医院", "original_poi_district": "浦东新区", "origin_name": "海底捞火锅(龙阳广场店)", "point_role": "算路终点", "distance_from_original": 6.5635091759827535, "distance_category": "5-10m", "distance_to_amap_end": 0.38050788332571905, "original_coords": [121.553468, 31.185281]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.553404, 31.185271]}, "properties": {"id": 3, "type": "amap_end", "name": "高德终点_北蔡社区卫生服务中心", "original_poi_name": "北蔡社区卫生服务中心", "original_poi_type": "医院", "original_poi_district": "浦东新区", "origin_name": "海底捞火锅(龙阳广场店)", "point_role": "算路终点", "distance_from_original": 6.1888372887873375, "distance_category": "5-10m", "distance_to_path_end": 0.38050788332571905, "original_coords": [121.553468, 31.185281]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.557694, 31.203589], [121.55786, 31.20358], [121.55818, 31.20345], [121.55818, 31.20345], [121.55882, 31.20335], [121.55882, 31.20335], [121.55887, 31.20332], [121.55886, 31.20327], [121.55886, 31.20327], [121.55896, 31.20327], [121.55896, 31.20327], [121.5591, 31.20327], [121.5591, 31.20327], [121.55914, 31.20305], [121.55914, 31.20305], [121.55923, 31.20267], [121.55923, 31.20245], [121.55929, 31.20215], [121.55929, 31.20215], [121.55934, 31.20192], [121.55934, 31.20176], [121.5593, 31.20168], [121.55917, 31.20159], [121.55917, 31.20159], [121.55792, 31.20135], [121.55695, 31.20108], [121.5565, 31.20102], [121.5565, 31.20102], [121.55642, 31.20101], [121.55642, 31.20101], [121.55659, 31.20029], [121.55659, 31.20029], [121.55662, 31.20004], [121.55668, 31.19984], [121.55668, 31.19984], [121.55786, 31.19771], [121.55786, 31.19771], [121.55787, 31.19767], [121.55787, 31.19767], [121.55837, 31.19588], [121.55837, 31.19588], [121.55863, 31.19468], [121.55863, 31.19468], [121.55866, 31.19446], [121.55866, 31.19446], [121.55879, 31.19371], [121.55879, 31.19371], [121.55895, 31.19277], [121.55895, 31.19277], [121.55906, 31.19213], [121.55906, 31.19213], [121.55922, 31.19126], [121.55922, 31.19126], [121.55925, 31.19111], [121.55925, 31.19111], [121.55937, 31.19038], [121.55937, 31.19038], [121.55941, 31.19012], [121.55941, 31.19012], [121.55954, 31.1894], [121.55954, 31.1894], [121.55942, 31.18938], [121.55942, 31.18938], [121.55926, 31.18937], [121.55926, 31.18937], [121.55846, 31.18927], [121.55846, 31.18927], [121.55828, 31.18926], [121.55828, 31.18926], [121.55804, 31.18923], [121.55804, 31.18923], [121.55789, 31.18922], [121.55789, 31.18922], [121.55774, 31.1892], [121.55774, 31.1892], [121.55695, 31.18913], [121.55695, 31.18913], [121.55624, 31.18907], [121.55624, 31.18907], [121.55455, 31.18892], [121.55455, 31.18892], [121.55438, 31.18891], [121.55438, 31.18891], [121.55403, 31.18887], [121.55328, 31.18872], [121.55328, 31.18872], [121.55274, 31.18858], [121.55274, 31.18858], [121.55258, 31.18856], [121.55258, 31.18856], [121.55265, 31.18844], [121.55265, 31.18844], [121.55296, 31.18788], [121.55296, 31.18788], [121.55326, 31.18727], [121.55326, 31.18727], [121.55363, 31.1862], [121.55363, 31.1862], [121.55396, 31.18517], [121.55396, 31.18517], [121.55343, 31.18512], [121.55343, 31.18512], [121.5534, 31.18527], [121.5534, 31.18527], [121.5534, 31.185271]]}, "properties": {"id": 3, "type": "path_route", "name": "Path路线_海底捞火锅(龙阳广场店)", "origin_name": "海底捞火锅(龙阳广场店)", "origin_type": "餐厅", "origin_district": "浦东新区", "dest_name": "北蔡社区卫生服务中心", "route_type": "Path算路", "start_coords": [121.557694, 31.203589], "end_coords": [121.5534, 31.185271], "original_start_coords": [121.557671, 31.203162], "original_end_coords": [121.553468, 31.185281]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.557704, 31.203589], [121.557862, 31.20358], [121.558023, 31.203521], [121.558179, 31.203456], [121.558828, 31.203355], [121.558828, 31.203355], [121.558871, 31.203322], [121.558865, 31.203274], [121.558865, 31.203274], [121.558967, 31.203274], [121.559101, 31.20328], [121.559101, 31.20328], [121.559139, 31.203054], [121.559193, 31.20284], [121.559219, 31.202711], [121.559235, 31.202453], [121.559353, 31.202148], [121.559402, 31.201928], [121.559413, 31.201815], [121.559402, 31.201772], [121.559343, 31.201697], [121.559257, 31.201616], [121.558903, 31.201418], [121.558769, 31.201386], [121.558061, 31.201284], [121.557631, 31.201225], [121.557471, 31.201209], [121.557315, 31.201193], [121.557122, 31.201171], [121.556548, 31.201107], [121.55643, 31.201107], [121.556532, 31.200576], [121.556591, 31.200291], [121.556628, 31.200045], [121.556671, 31.199878], [121.556687, 31.19982], [121.556703, 31.199793], [121.556918, 31.199449], [121.557841, 31.1977], [121.5579, 31.197561], [121.558372, 31.195882], [121.558629, 31.194686], [121.558667, 31.194466], [121.558796, 31.193715], [121.558951, 31.192771], [121.559064, 31.192138], [121.559225, 31.191263], [121.559251, 31.191118], [121.559337, 31.190598], [121.559375, 31.190389], [121.559407, 31.190185], [121.559418, 31.190126], [121.559541, 31.189407], [121.559541, 31.189407], [121.559268, 31.18937], [121.558463, 31.189278], [121.557744, 31.189209], [121.557573, 31.189193], [121.55695, 31.189133], [121.556242, 31.18908], [121.554558, 31.18893], [121.554381, 31.188914], [121.554037, 31.188871], [121.553281, 31.18872], [121.553002, 31.188651], [121.552868, 31.188618], [121.552745, 31.188586], [121.552659, 31.188447], [121.552734, 31.188313], [121.552771, 31.188243], [121.552793, 31.188205], [121.552873, 31.188066], [121.552965, 31.187878], [121.55326, 31.187277], [121.553399, 31.186918], [121.55363, 31.18621], [121.55371, 31.185958], [121.553962, 31.185175], [121.553962, 31.185175], [121.553431, 31.185121], [121.553431, 31.185121], [121.553404, 31.185271]]}, "properties": {"id": 3, "type": "amap_route", "name": "高德路线_海底捞火锅(龙阳广场店)", "origin_name": "海底捞火锅(龙阳广场店)", "origin_type": "餐厅", "origin_district": "浦东新区", "dest_name": "北蔡社区卫生服务中心", "route_type": "高德算路", "start_coords": [121.557704, 31.203589], "end_coords": [121.553404, 31.185271], "original_start_coords": [121.557671, 31.203162], "original_end_coords": [121.553468, 31.185281]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.307174, 31.016697]}, "properties": {"id": 4, "type": "original_start", "name": "车墩小学", "poi_type": "学校", "district": "松江区", "dest_name": "松卫烟酒超市", "point_role": "起点", "distance_to_path_start": 5.145981932339639, "distance_to_amap_start": 5.051909819117065, "path_to_amap_start_distance": 0.14644327199310064, "path_start_distance_category": "5-10m", "amap_start_distance_category": "5-10m", "start_comparison_distance_category": "0-1m", "path_start_coords": [121.30712, 31.016697], "amap_start_coords": [121.307121, 31.016698]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.261659, 31.022573]}, "properties": {"id": 4, "type": "original_end", "name": "松卫烟酒超市", "poi_type": "购物中心", "district": "松江区", "origin_name": "车墩小学", "point_role": "终点", "distance_to_path_end": 10.331563279681431, "distance_to_amap_end": 9.905166230548382, "path_to_amap_end_distance": 0.5065195472344078, "path_end_distance_category": "10m+", "amap_end_distance_category": "5-10m", "end_comparison_distance_category": "0-1m", "path_end_coords": [121.261552, 31.022558], "amap_end_coords": [121.261556, 31.022561]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.30712, 31.016697]}, "properties": {"id": 4, "type": "path_start", "name": "Path起点_车墩小学", "original_poi_name": "车墩小学", "original_poi_type": "学校", "original_poi_district": "松江区", "dest_name": "松卫烟酒超市", "point_role": "算路起点", "distance_from_original": 5.145981932339639, "distance_category": "5-10m", "distance_to_amap_start": 0.14644327199310064, "original_coords": [121.307174, 31.016697]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.307121, 31.016698]}, "properties": {"id": 4, "type": "amap_start", "name": "高德起点_车墩小学", "original_poi_name": "车墩小学", "original_poi_type": "学校", "original_poi_district": "松江区", "dest_name": "松卫烟酒超市", "point_role": "算路起点", "distance_from_original": 5.051909819117065, "distance_category": "5-10m", "distance_to_path_start": 0.14644327199310064, "original_coords": [121.307174, 31.016697]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.261552, 31.022558]}, "properties": {"id": 4, "type": "path_end", "name": "Path终点_松卫烟酒超市", "original_poi_name": "松卫烟酒超市", "original_poi_type": "购物中心", "original_poi_district": "松江区", "origin_name": "车墩小学", "point_role": "算路终点", "distance_from_original": 10.331563279681431, "distance_category": "10m+", "distance_to_amap_end": 0.5065195472344078, "original_coords": [121.261659, 31.022573]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.261556, 31.022561]}, "properties": {"id": 4, "type": "amap_end", "name": "高德终点_松卫烟酒超市", "original_poi_name": "松卫烟酒超市", "original_poi_type": "购物中心", "original_poi_district": "松江区", "origin_name": "车墩小学", "point_role": "算路终点", "distance_from_original": 9.905166230548382, "distance_category": "5-10m", "distance_to_path_end": 0.5065195472344078, "original_coords": [121.261659, 31.022573]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.30712, 31.016697], [121.30712, 31.01694], [121.30712, 31.01694], [121.30674, 31.01692], [121.30674, 31.01692], [121.30675, 31.01705], [121.30675, 31.01705], [121.30676, 31.01733], [121.30676, 31.01733], [121.30663, 31.01733], [121.30663, 31.01733], [121.30647, 31.01734], [121.30647, 31.01734], [121.3064, 31.0162], [121.3064, 31.0162], [121.30636, 31.01564], [121.30636, 31.01564], [121.30634, 31.01509], [121.30635, 31.01478], [121.30635, 31.01478], [121.30638, 31.01403], [121.30638, 31.01403], [121.30637, 31.01394], [121.30637, 31.01394], [121.30466, 31.01381], [121.30466, 31.01381], [121.30457, 31.01379], [121.30457, 31.01379], [121.30438, 31.01377], [121.30438, 31.01377], [121.30367, 31.01363], [121.30367, 31.01363], [121.30333, 31.01352], [121.3026, 31.01322], [121.3026, 31.01322], [121.30215, 31.01302], [121.30215, 31.01302], [121.30029, 31.01219], [121.30029, 31.01219], [121.29399, 31.00933], [121.29372, 31.00919], [121.29372, 31.00919], [121.29322, 31.00892], [121.29322, 31.00892], [121.29272, 31.00861], [121.29235, 31.00834], [121.29193, 31.00799], [121.29193, 31.00799], [121.29183, 31.00789], [121.29183, 31.00789], [121.29161, 31.00769], [121.29161, 31.00769], [121.29129, 31.0081], [121.29129, 31.0081], [121.28966, 31.00741], [121.28966, 31.00741], [121.28949, 31.00745], [121.28932, 31.00746], [121.28749, 31.00748], [121.28749, 31.00748], [121.28594, 31.00748], [121.28594, 31.00748], [121.28505, 31.00749], [121.28505, 31.00749], [121.2839, 31.00749], [121.2839, 31.00749], [121.2768, 31.00748], [121.2768, 31.00748], [121.27536, 31.00748], [121.27515, 31.00752], [121.2751, 31.00755], [121.27507, 31.00761], [121.27507, 31.00761], [121.27471, 31.00758], [121.27386, 31.00747], [121.27386, 31.00747], [121.27251, 31.00726], [121.27251, 31.00726], [121.27169, 31.00714], [121.27169, 31.00714], [121.27125, 31.00712], [121.27125, 31.00712], [121.27075, 31.00718], [121.27055, 31.00723], [121.27034, 31.00731], [121.27034, 31.00731], [121.26997, 31.00748], [121.26963, 31.00771], [121.26941, 31.00792], [121.26915, 31.00824], [121.26915, 31.00824], [121.26901, 31.00848], [121.26893, 31.00869], [121.26887, 31.00892], [121.26887, 31.00892], [121.26885, 31.00906], [121.26884, 31.00951], [121.26884, 31.00951], [121.26893, 31.01042], [121.26893, 31.01042], [121.26841, 31.01048], [121.26828, 31.01052], [121.26811, 31.0106], [121.2667, 31.01152], [121.26657, 31.0116], [121.26648, 31.01163], [121.26648, 31.01163], [121.26637, 31.01165], [121.26637, 31.01165], [121.26592, 31.01189], [121.26592, 31.01189], [121.2655, 31.01217], [121.2655, 31.01217], [121.26541, 31.01222], [121.26517, 31.0123], [121.26488, 31.01232], [121.26459, 31.01229], [121.26459, 31.01229], [121.26435, 31.01225], [121.26435, 31.01225], [121.26394, 31.0122], [121.26394, 31.0122], [121.26306, 31.01209], [121.26306, 31.01209], [121.26286, 31.01251], [121.26264, 31.01306], [121.26264, 31.01306], [121.26247, 31.01359], [121.26247, 31.01359], [121.26234, 31.01419], [121.26234, 31.01419], [121.26228, 31.01457], [121.26222, 31.01527], [121.2622, 31.01582], [121.2622, 31.01582], [121.2622, 31.01608], [121.26211, 31.01742], [121.26211, 31.01742], [121.26209, 31.01758], [121.26209, 31.01758], [121.26198, 31.01836], [121.26198, 31.01836], [121.26178, 31.01981], [121.26178, 31.01981], [121.26167, 31.02055], [121.26167, 31.02055], [121.26156, 31.0213], [121.26156, 31.0213], [121.26149, 31.02164], [121.26149, 31.02164], [121.26141, 31.02216], [121.26141, 31.02216], [121.26131, 31.02287], [121.26131, 31.02287], [121.26136, 31.02285], [121.26142, 31.02285], [121.26142, 31.02285], [121.26151, 31.02285], [121.26151, 31.02285], [121.261552, 31.022558]]}, "properties": {"id": 4, "type": "path_route", "name": "Path路线_车墩小学", "origin_name": "车墩小学", "origin_type": "学校", "origin_district": "松江区", "dest_name": "松卫烟酒超市", "route_type": "Path算路", "start_coords": [121.30712, 31.016697], "end_coords": [121.261552, 31.022558], "original_start_coords": [121.307174, 31.016697], "original_end_coords": [121.261659, 31.022573]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.307121, 31.016698], [121.307129, 31.016946], [121.307129, 31.016946], [121.306856, 31.01692], [121.306743, 31.016925], [121.306743, 31.016925], [121.306759, 31.017054], [121.30677, 31.017333], [121.30677, 31.017333], [121.306636, 31.017338], [121.306475, 31.017349], [121.306475, 31.017349], [121.306405, 31.016201], [121.306368, 31.015643], [121.306351, 31.014924], [121.306357, 31.014785], [121.306368, 31.014404], [121.306389, 31.014039], [121.306378, 31.013948], [121.304962, 31.013846], [121.304667, 31.013814], [121.30457, 31.013798], [121.304522, 31.013792], [121.304388, 31.013771], [121.304098, 31.013728], [121.303675, 31.013631], [121.303336, 31.013524], [121.302607, 31.013229], [121.302156, 31.013025], [121.300295, 31.012193], [121.295113, 31.009844], [121.293992, 31.009334], [121.293724, 31.009195], [121.29323, 31.008932], [121.292731, 31.008621], [121.292361, 31.008347], [121.291932, 31.007993], [121.291615, 31.007693], [121.291497, 31.007832], [121.291347, 31.008074], [121.29132, 31.00809], [121.291277, 31.008085], [121.289663, 31.007414], [121.289663, 31.007414], [121.289416, 31.007462], [121.289062, 31.007473], [121.288992, 31.007473], [121.287914, 31.007489], [121.28594, 31.007489], [121.285055, 31.007489], [121.283901, 31.007489], [121.281455, 31.0075], [121.276804, 31.007484], [121.275366, 31.007484], [121.275152, 31.007526], [121.275109, 31.007553], [121.275077, 31.007612], [121.275077, 31.007612], [121.274717, 31.007586], [121.273865, 31.007473], [121.272303, 31.007237], [121.271697, 31.007146], [121.271461, 31.007124], [121.271252, 31.007124], [121.270984, 31.007151], [121.270549, 31.007237], [121.27034, 31.007317], [121.270147, 31.007392], [121.269975, 31.007478], [121.269793, 31.007591], [121.269632, 31.00772], [121.269412, 31.007923], [121.26916, 31.00824], [121.26902, 31.008487], [121.26894, 31.008691], [121.268876, 31.008927], [121.268849, 31.009066], [121.268849, 31.009517], [121.26894, 31.010423], [121.26894, 31.010423], [121.268414, 31.010482], [121.268285, 31.01052], [121.268114, 31.010606], [121.267089, 31.011271], [121.266703, 31.011523], [121.266569, 31.011603], [121.266483, 31.011636], [121.266376, 31.011652], [121.265925, 31.011898], [121.265501, 31.012172], [121.26541, 31.012226], [121.265174, 31.012306], [121.264885, 31.012322], [121.264595, 31.01229], [121.264359, 31.012258], [121.26394, 31.01221], [121.263066, 31.012092], [121.263066, 31.012092], [121.262867, 31.012515], [121.262642, 31.013063], [121.262605, 31.013148], [121.26247, 31.013599], [121.26239, 31.013932], [121.262347, 31.0142], [121.262315, 31.014404], [121.262288, 31.01457], [121.262229, 31.015273], [121.262202, 31.015825], [121.262202, 31.016083], [121.262116, 31.017429], [121.2621, 31.01759], [121.261988, 31.018363], [121.261784, 31.019816], [121.261676, 31.020551], [121.261558, 31.021308], [121.2615, 31.021646], [121.261419, 31.022171], [121.261317, 31.022853], [121.261317, 31.022853], [121.261419, 31.022853], [121.26151, 31.022858], [121.26151, 31.022858], [121.261556, 31.022561]]}, "properties": {"id": 4, "type": "amap_route", "name": "高德路线_车墩小学", "origin_name": "车墩小学", "origin_type": "学校", "origin_district": "松江区", "dest_name": "松卫烟酒超市", "route_type": "高德算路", "start_coords": [121.307121, 31.016698], "end_coords": [121.261556, 31.022561], "original_start_coords": [121.307174, 31.016697], "original_end_coords": [121.261659, 31.022573]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.523435, 31.239881]}, "properties": {"id": 5, "type": "original_start", "name": "浦东新区梅园小学", "poi_type": "学校", "district": "浦东新区", "dest_name": "浦东新区南码头社区卫生服务中心", "point_role": "起点", "distance_to_path_start": 33.05176661070239, "distance_to_amap_start": 32.69860946118883, "path_to_amap_start_distance": 1.574431303782432, "path_start_distance_category": "10m+", "amap_start_distance_category": "10m+", "start_comparison_distance_category": "1-5m", "path_start_coords": [121.5235, 31.239589], "amap_start_coords": [121.523515, 31.239595]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.509855, 31.200689]}, "properties": {"id": 5, "type": "original_end", "name": "浦东新区南码头社区卫生服务中心", "poi_type": "医院", "district": "浦东新区", "origin_name": "浦东新区梅园小学", "point_role": "终点", "distance_to_path_end": 10.170387795703377, "distance_to_amap_end": 10.121721510651337, "path_to_amap_end_distance": 0.830804527980744, "path_end_distance_category": "10m+", "amap_end_distance_category": "10m+", "end_comparison_distance_category": "0-1m", "path_end_coords": [121.509888, 31.200602], "amap_end_coords": [121.509896, 31.200605]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.5235, 31.239589]}, "properties": {"id": 5, "type": "path_start", "name": "Path起点_浦东新区梅园小学", "original_poi_name": "浦东新区梅园小学", "original_poi_type": "学校", "original_poi_district": "浦东新区", "dest_name": "浦东新区南码头社区卫生服务中心", "point_role": "算路起点", "distance_from_original": 33.05176661070239, "distance_category": "10m+", "distance_to_amap_start": 1.574431303782432, "original_coords": [121.523435, 31.239881]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.523515, 31.239595]}, "properties": {"id": 5, "type": "amap_start", "name": "高德起点_浦东新区梅园小学", "original_poi_name": "浦东新区梅园小学", "original_poi_type": "学校", "original_poi_district": "浦东新区", "dest_name": "浦东新区南码头社区卫生服务中心", "point_role": "算路起点", "distance_from_original": 32.69860946118883, "distance_category": "10m+", "distance_to_path_start": 1.574431303782432, "original_coords": [121.523435, 31.239881]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.509888, 31.200602]}, "properties": {"id": 5, "type": "path_end", "name": "Path终点_浦东新区南码头社区卫生服务中心", "original_poi_name": "浦东新区南码头社区卫生服务中心", "original_poi_type": "医院", "original_poi_district": "浦东新区", "origin_name": "浦东新区梅园小学", "point_role": "算路终点", "distance_from_original": 10.170387795703377, "distance_category": "10m+", "distance_to_amap_end": 0.830804527980744, "original_coords": [121.509855, 31.200689]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.509896, 31.200605]}, "properties": {"id": 5, "type": "amap_end", "name": "高德终点_浦东新区南码头社区卫生服务中心", "original_poi_name": "浦东新区南码头社区卫生服务中心", "original_poi_type": "医院", "original_poi_district": "浦东新区", "origin_name": "浦东新区梅园小学", "point_role": "算路终点", "distance_from_original": 10.121721510651337, "distance_category": "10m+", "distance_to_path_end": 0.830804527980744, "original_coords": [121.509855, 31.200689]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.5235, 31.239589], [121.52328, 31.23954], [121.52328, 31.23954], [121.52336, 31.23926], [121.52336, 31.23926], [121.52342, 31.23908], [121.52342, 31.23908], [121.52301, 31.23896], [121.52301, 31.23896], [121.52303, 31.23888], [121.52303, 31.23888], [121.52307, 31.23873], [121.52307, 31.23873], [121.52309, 31.23865], [121.52309, 31.23865], [121.52344, 31.23878], [121.52344, 31.23878], [121.52387, 31.23893], [121.52387, 31.23893], [121.52393, 31.23895], [121.52393, 31.23895], [121.52441, 31.23914], [121.52441, 31.23914], [121.52446, 31.23893], [121.52446, 31.23893], [121.52448, 31.23887], [121.52448, 31.23887], [121.52468, 31.23827], [121.52468, 31.23827], [121.52475, 31.23812], [121.52475, 31.23812], [121.5252, 31.23721], [121.5252, 31.23721], [121.52544, 31.23673], [121.52544, 31.23673], [121.52469, 31.23646], [121.52469, 31.23646], [121.52437, 31.23635], [121.52437, 31.23635], [121.52405, 31.23623], [121.52405, 31.23623], [121.52389, 31.23618], [121.52389, 31.23618], [121.52351, 31.23605], [121.52351, 31.23605], [121.5233, 31.23597], [121.5233, 31.23597], [121.52301, 31.23587], [121.52301, 31.23587], [121.52283, 31.2358], [121.52283, 31.2358], [121.5222, 31.23557], [121.5222, 31.23557], [121.52207, 31.23552], [121.52207, 31.23552], [121.52133, 31.23525], [121.52133, 31.23525], [121.52059, 31.23497], [121.52059, 31.23497], [121.52046, 31.23493], [121.52046, 31.23493], [121.52033, 31.23488], [121.52033, 31.23488], [121.51972, 31.23465], [121.51972, 31.23465], [121.52006, 31.23396], [121.52006, 31.23396], [121.52011, 31.23385], [121.52011, 31.23385], [121.52018, 31.23371], [121.52018, 31.23371], [121.52022, 31.23363], [121.52022, 31.23363], [121.5203, 31.23344], [121.5203, 31.23344], [121.5205, 31.23304], [121.5205, 31.23304], [121.52066, 31.23272], [121.52066, 31.23272], [121.52087, 31.23231], [121.52087, 31.23231], [121.52097, 31.23212], [121.52097, 31.23212], [121.52095, 31.23194], [121.52092, 31.23188], [121.52077, 31.23173], [121.52065, 31.23164], [121.52065, 31.23164], [121.5205, 31.23155], [121.5205, 31.23155], [121.52027, 31.23146], [121.52027, 31.23146], [121.52006, 31.23139], [121.52006, 31.23139], [121.51956, 31.23122], [121.51956, 31.23122], [121.51941, 31.23118], [121.51941, 31.23118], [121.51888, 31.23103], [121.51888, 31.23103], [121.51848, 31.23091], [121.51848, 31.23091], [121.51834, 31.23087], [121.51834, 31.23087], [121.51824, 31.23087], [121.51761, 31.23068], [121.51761, 31.23068], [121.5168, 31.23042], [121.5168, 31.23042], [121.51643, 31.23031], [121.51643, 31.23031], [121.51633, 31.23027], [121.51633, 31.23027], [121.51625, 31.23025], [121.51625, 31.23025], [121.516, 31.23017], [121.516, 31.23017], [121.51534, 31.22996], [121.51534, 31.22996], [121.51521, 31.22991], [121.51521, 31.22991], [121.51536, 31.22952], [121.51536, 31.22952], [121.516, 31.22804], [121.516, 31.22804], [121.51684, 31.22603], [121.51684, 31.22603], [121.51706, 31.22546], [121.51719, 31.22507], [121.51737, 31.22435], [121.51737, 31.22435], [121.51745, 31.22408], [121.51745, 31.22408], [121.51753, 31.22383], [121.51753, 31.22383], [121.51775, 31.22314], [121.51775, 31.22314], [121.51791, 31.22265], [121.51791, 31.22265], [121.51795, 31.22253], [121.51795, 31.22253], [121.51802, 31.2223], [121.51802, 31.2223], [121.51811, 31.22199], [121.51811, 31.22199], [121.51814, 31.22189], [121.51814, 31.22189], [121.51827, 31.22145], [121.51827, 31.22145], [121.51841, 31.22095], [121.51841, 31.22095], [121.51844, 31.22089], [121.51844, 31.22089], [121.51846, 31.22083], [121.51846, 31.22083], [121.51852, 31.22062], [121.51852, 31.22062], [121.51858, 31.2204], [121.51858, 31.2204], [121.51863, 31.22025], [121.51863, 31.22025], [121.5187, 31.21998], [121.5187, 31.21998], [121.51881, 31.21942], [121.51881, 31.21942], [121.51883, 31.21924], [121.51883, 31.21924], [121.51886, 31.21896], [121.51886, 31.21896], [121.51887, 31.21881], [121.51887, 31.21881], [121.5189, 31.21823], [121.5189, 31.21823], [121.51886, 31.21758], [121.51886, 31.21758], [121.51882, 31.21732], [121.51882, 31.21732], [121.51876, 31.21668], [121.51876, 31.21668], [121.51873, 31.2163], [121.51873, 31.2163], [121.51871, 31.21588], [121.51871, 31.21588], [121.51869, 31.21571], [121.51869, 31.21571], [121.51857, 31.21386], [121.51857, 31.21361], [121.51857, 31.21361], [121.51854, 31.21317], [121.51854, 31.21317], [121.51845, 31.21281], [121.51819, 31.21214], [121.51819, 31.21214], [121.5179, 31.21166], [121.5179, 31.21166], [121.51775, 31.21145], [121.51775, 31.21145], [121.51755, 31.2112], [121.51755, 31.2112], [121.51736, 31.21098], [121.51736, 31.21098], [121.51695, 31.21053], [121.51695, 31.21053], [121.51682, 31.21037], [121.51682, 31.21037], [121.51665, 31.21015], [121.51665, 31.21015], [121.51648, 31.20991], [121.51648, 31.20991], [121.51629, 31.20958], [121.51629, 31.20958], [121.51614, 31.20931], [121.51614, 31.20931], [121.516, 31.20905], [121.516, 31.20905], [121.51591, 31.20883], [121.51591, 31.20883], [121.51536, 31.20769], [121.51509, 31.20706], [121.51509, 31.20706], [121.51483, 31.20642], [121.51483, 31.20642], [121.51411, 31.20463], [121.51411, 31.20463], [121.51394, 31.20469], [121.51394, 31.20469], [121.51378, 31.20428], [121.51378, 31.20428], [121.5137, 31.20408], [121.5137, 31.20408], [121.51331, 31.20313], [121.51331, 31.20313], [121.51307, 31.20263], [121.51307, 31.20263], [121.51276, 31.20175], [121.51246, 31.20111], [121.51223, 31.20054], [121.51223, 31.20054], [121.51201, 31.20062], [121.51201, 31.20062], [121.51173, 31.20071], [121.51173, 31.20071], [121.51132, 31.20082], [121.51098, 31.20086], [121.51062, 31.20083], [121.5104, 31.20077], [121.5104, 31.20077], [121.50996, 31.20063], [121.50996, 31.20063], [121.509888, 31.200602]]}, "properties": {"id": 5, "type": "path_route", "name": "Path路线_浦东新区梅园小学", "origin_name": "浦东新区梅园小学", "origin_type": "学校", "origin_district": "浦东新区", "dest_name": "浦东新区南码头社区卫生服务中心", "route_type": "Path算路", "start_coords": [121.5235, 31.239589], "end_coords": [121.509888, 31.200602], "original_start_coords": [121.523435, 31.239881], "original_end_coords": [121.509855, 31.200689]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.523515, 31.239595], [121.523734, 31.23964], [121.523734, 31.23964], [121.523793, 31.23943], [121.523803, 31.239382], [121.523857, 31.23921], [121.523857, 31.23921], [121.523428, 31.239087], [121.52301, 31.238964], [121.52301, 31.238964], [121.523031, 31.238888], [121.523031, 31.238888], [121.522602, 31.238797], [121.522602, 31.238797], [121.52265, 31.238642], [121.522698, 31.238513], [121.522698, 31.238513], [121.523095, 31.238658], [121.523444, 31.238781], [121.523868, 31.238937], [121.523927, 31.238958], [121.524426, 31.239092], [121.524479, 31.238878], [121.524683, 31.238271], [121.524753, 31.238121], [121.525204, 31.237215], [121.525445, 31.236737], [121.525445, 31.236737], [121.524699, 31.236469], [121.524372, 31.236351], [121.52405, 31.236238], [121.523895, 31.236185], [121.523519, 31.236051], [121.523305, 31.23597], [121.523015, 31.235868], [121.522838, 31.235804], [121.522205, 31.235568], [121.522076, 31.235525], [121.522677, 31.234318], [121.522773, 31.23413], [121.523106, 31.23346], [121.523412, 31.232832], [121.523793, 31.232076], [121.523793, 31.232076], [121.52398, 31.231786], [121.524555, 31.230638], [121.525048, 31.22956], [121.525284, 31.22897], [121.526153, 31.226786], [121.526218, 31.226609], [121.526266, 31.226497], [121.526529, 31.225869], [121.526529, 31.225869], [121.526142, 31.225746], [121.525815, 31.225644], [121.525627, 31.22559], [121.52522, 31.225461], [121.524994, 31.225381], [121.524651, 31.225284], [121.524495, 31.225236], [121.523873, 31.225038], [121.523192, 31.224818], [121.522639, 31.224646], [121.52214, 31.224485], [121.521357, 31.224244], [121.521164, 31.22419], [121.520601, 31.224013], [121.520204, 31.223895], [121.519748, 31.223755], [121.519571, 31.223696], [121.519163, 31.223573], [121.518954, 31.223503], [121.518536, 31.223375], [121.51838, 31.223332], [121.517903, 31.223181], [121.517752, 31.223149], [121.517752, 31.223144], [121.517919, 31.222656], [121.517956, 31.222538], [121.518026, 31.222302], [121.518117, 31.221996], [121.518149, 31.221894], [121.518278, 31.221454], [121.518412, 31.220961], [121.518439, 31.220891], [121.51846, 31.220837], [121.518525, 31.220623], [121.518589, 31.220403], [121.518632, 31.220253], [121.518702, 31.21999], [121.518815, 31.219426], [121.518836, 31.219249], [121.518868, 31.21897], [121.518874, 31.218815], [121.518906, 31.21823], [121.518863, 31.217586], [121.518831, 31.217323], [121.518766, 31.216691], [121.518734, 31.21631], [121.518713, 31.215886], [121.518696, 31.215714], [121.518664, 31.215124], [121.518621, 31.214502], [121.518573, 31.213863], [121.518568, 31.213617], [121.518562, 31.213391], [121.518541, 31.213177], [121.51845, 31.212812], [121.518294, 31.212383], [121.518198, 31.212158], [121.518192, 31.212147], [121.51808, 31.211932], [121.517908, 31.211659], [121.517758, 31.211455], [121.517554, 31.211203], [121.517361, 31.210983], [121.517055, 31.210645], [121.516958, 31.210538], [121.516824, 31.210376], [121.516658, 31.210157], [121.516486, 31.209921], [121.516293, 31.209583], [121.516143, 31.209309], [121.516143, 31.209309], [121.515998, 31.209057], [121.515966, 31.208939], [121.515918, 31.208832], [121.515371, 31.207695], [121.515092, 31.207067], [121.51484, 31.206423], [121.51411, 31.204631], [121.513944, 31.204696], [121.513783, 31.204283], [121.513622, 31.203896], [121.513311, 31.20314], [121.513225, 31.202936], [121.513069, 31.202636], [121.512903, 31.202121], [121.512678, 31.201558], [121.512522, 31.201236], [121.512495, 31.201171], [121.512238, 31.200549], [121.512238, 31.200549], [121.512013, 31.200624], [121.511739, 31.200715], [121.511406, 31.200806], [121.51116, 31.200855], [121.510988, 31.200865], [121.5108, 31.20086], [121.510628, 31.200833], [121.510408, 31.200774], [121.509963, 31.20063], [121.509896, 31.200605]]}, "properties": {"id": 5, "type": "amap_route", "name": "高德路线_浦东新区梅园小学", "origin_name": "浦东新区梅园小学", "origin_type": "学校", "origin_district": "浦东新区", "dest_name": "浦东新区南码头社区卫生服务中心", "route_type": "高德算路", "start_coords": [121.523515, 31.239595], "end_coords": [121.509896, 31.200605], "original_start_coords": [121.523435, 31.239881], "original_end_coords": [121.509855, 31.200689]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.363777, 31.097343]}, "properties": {"id": 6, "type": "original_start", "name": "浅水湾公园", "poi_type": "公园", "district": "松江区", "dest_name": "千家惠百货(华银苑店)", "point_role": "起点", "distance_to_path_start": 33.218095974711666, "distance_to_amap_start": 33.49739443984264, "path_to_amap_start_distance": 3.324667614049213, "path_start_distance_category": "10m+", "amap_start_distance_category": "10m+", "start_comparison_distance_category": "1-5m", "path_start_coords": [121.364097, 31.097462], "amap_start_coords": [121.364112, 31.097435]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.399723, 31.067318]}, "properties": {"id": 6, "type": "original_end", "name": "千家惠百货(华银苑店)", "poi_type": "购物中心", "district": "闵行区", "origin_name": "浅水湾公园", "point_role": "终点", "distance_to_path_end": 9.48560231804841, "distance_to_amap_end": 13.968904994063182, "path_to_amap_end_distance": 4.6097090362925375, "path_end_distance_category": "5-10m", "amap_end_distance_category": "10m+", "end_comparison_distance_category": "1-5m", "path_end_coords": [121.3997, 31.067401], "amap_end_coords": [121.399676, 31.067437]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.364097, 31.097462]}, "properties": {"id": 6, "type": "path_start", "name": "Path起点_浅水湾公园", "original_poi_name": "浅水湾公园", "original_poi_type": "公园", "original_poi_district": "松江区", "dest_name": "千家惠百货(华银苑店)", "point_role": "算路起点", "distance_from_original": 33.218095974711666, "distance_category": "10m+", "distance_to_amap_start": 3.324667614049213, "original_coords": [121.363777, 31.097343]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.364112, 31.097435]}, "properties": {"id": 6, "type": "amap_start", "name": "高德起点_浅水湾公园", "original_poi_name": "浅水湾公园", "original_poi_type": "公园", "original_poi_district": "松江区", "dest_name": "千家惠百货(华银苑店)", "point_role": "算路起点", "distance_from_original": 33.49739443984264, "distance_category": "10m+", "distance_to_path_start": 3.324667614049213, "original_coords": [121.363777, 31.097343]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.3997, 31.067401]}, "properties": {"id": 6, "type": "path_end", "name": "Path终点_千家惠百货(华银苑店)", "original_poi_name": "千家惠百货(华银苑店)", "original_poi_type": "购物中心", "original_poi_district": "闵行区", "origin_name": "浅水湾公园", "point_role": "算路终点", "distance_from_original": 9.48560231804841, "distance_category": "5-10m", "distance_to_amap_end": 4.6097090362925375, "original_coords": [121.399723, 31.067318]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.399676, 31.067437]}, "properties": {"id": 6, "type": "amap_end", "name": "高德终点_千家惠百货(华银苑店)", "original_poi_name": "千家惠百货(华银苑店)", "original_poi_type": "购物中心", "original_poi_district": "闵行区", "origin_name": "浅水湾公园", "point_role": "算路终点", "distance_from_original": 13.968904994063182, "distance_category": "10m+", "distance_to_path_end": 4.6097090362925375, "original_coords": [121.399723, 31.067318]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.364097, 31.097462], [121.36453, 31.0963], [121.36524, 31.09459], [121.36524, 31.09459], [121.36546, 31.09401], [121.36546, 31.09401], [121.36613, 31.09238], [121.36613, 31.09238], [121.36643, 31.09165], [121.36643, 31.09165], [121.3668, 31.09078], [121.36718, 31.08997], [121.36718, 31.08997], [121.36738, 31.08954], [121.36738, 31.08954], [121.36739, 31.08934], [121.36758, 31.0889], [121.36798, 31.08804], [121.36914, 31.08568], [121.36983, 31.0842], [121.36983, 31.0842], [121.37024, 31.08333], [121.37024, 31.08333], [121.37028, 31.08325], [121.37028, 31.08325], [121.37039, 31.08328], [121.37039, 31.08328], [121.37055, 31.08333], [121.37055, 31.08333], [121.37085, 31.08342], [121.37085, 31.08342], [121.3713, 31.08356], [121.3713, 31.08356], [121.37248, 31.0839], [121.37248, 31.0839], [121.3738, 31.08427], [121.3738, 31.08427], [121.37401, 31.08433], [121.37401, 31.08433], [121.37575, 31.0848], [121.37575, 31.0848], [121.37612, 31.08491], [121.37612, 31.08491], [121.3762, 31.08493], [121.3762, 31.08493], [121.37711, 31.08515], [121.37711, 31.08515], [121.37728, 31.0852], [121.37728, 31.0852], [121.37819, 31.08557], [121.37819, 31.08557], [121.37933, 31.086], [121.37933, 31.086], [121.37962, 31.08612], [121.37962, 31.08612], [121.38085, 31.08659], [121.38085, 31.08659], [121.38137, 31.08679], [121.38137, 31.08679], [121.38172, 31.08692], [121.38172, 31.08692], [121.38262, 31.08727], [121.38262, 31.08727], [121.38397, 31.08779], [121.38397, 31.08779], [121.38405, 31.08782], [121.38405, 31.08782], [121.38723, 31.08905], [121.38723, 31.08905], [121.38843, 31.0895], [121.38843, 31.0895], [121.3888, 31.08964], [121.3888, 31.08964], [121.38942, 31.08987], [121.38942, 31.08987], [121.39024, 31.08835], [121.39024, 31.08835], [121.39035, 31.0881], [121.39035, 31.0881], [121.39142, 31.08611], [121.39142, 31.08611], [121.39193, 31.08515], [121.39193, 31.08515], [121.39198, 31.08505], [121.39198, 31.08505], [121.39281, 31.08346], [121.39281, 31.08346], [121.39357, 31.08212], [121.39357, 31.08212], [121.3936, 31.08205], [121.3936, 31.08205], [121.39393, 31.08138], [121.39393, 31.08138], [121.39432, 31.08066], [121.39432, 31.08066], [121.39515, 31.07911], [121.39515, 31.07911], [121.39553, 31.07838], [121.39553, 31.07838], [121.39625, 31.07699], [121.39625, 31.07699], [121.39694, 31.0757], [121.39694, 31.0757], [121.39734, 31.07493], [121.39734, 31.07493], [121.3982, 31.0733], [121.3982, 31.0733], [121.39846, 31.07281], [121.39846, 31.07281], [121.39854, 31.07266], [121.39854, 31.07266], [121.39887, 31.07203], [121.39887, 31.07203], [121.39942, 31.07099], [121.39942, 31.07099], [121.39964, 31.07058], [121.39964, 31.07058], [121.40073, 31.0685], [121.40073, 31.0685], [121.40077, 31.06833], [121.40095, 31.06788], [121.40095, 31.06788], [121.40004, 31.06763], [121.40004, 31.06763], [121.39985, 31.06758], [121.39985, 31.06758], [121.39966, 31.06754], [121.39966, 31.06754], [121.39937, 31.06746], [121.39937, 31.06746], [121.39913, 31.06739], [121.39913, 31.06739], [121.39854, 31.06724], [121.39854, 31.06724], [121.39861, 31.06713], [121.39861, 31.06713], [121.39916, 31.06729], [121.39916, 31.06729], [121.39927, 31.06732], [121.39927, 31.06732], [121.39944, 31.06733], [121.3997, 31.067401]]}, "properties": {"id": 6, "type": "path_route", "name": "Path路线_浅水湾公园", "origin_name": "浅水湾公园", "origin_type": "公园", "origin_district": "松江区", "dest_name": "千家惠百货(华银苑店)", "route_type": "Path算路", "start_coords": [121.364097, 31.097462], "end_coords": [121.3997, 31.067401], "original_start_coords": [121.363777, 31.097343], "original_end_coords": [121.399723, 31.067318]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.364112, 31.097435], [121.364534, 31.096308], [121.365242, 31.094591], [121.365467, 31.094017], [121.366138, 31.092381], [121.366438, 31.091657], [121.366583, 31.091308], [121.366803, 31.090788], [121.367184, 31.089972], [121.367382, 31.089543], [121.367382, 31.089543], [121.367388, 31.08935], [121.367495, 31.089093], [121.367581, 31.088899], [121.367978, 31.088046], [121.369142, 31.085691], [121.369448, 31.085026], [121.369839, 31.084205], [121.370247, 31.083336], [121.370392, 31.083288], [121.370553, 31.083331], [121.370848, 31.083428], [121.371309, 31.083567], [121.371443, 31.083605], [121.372479, 31.0839], [121.373798, 31.084275], [121.374008, 31.084334], [121.375751, 31.084806], [121.376126, 31.084908], [121.376201, 31.08493], [121.376953, 31.085112], [121.377119, 31.085155], [121.377119, 31.085155], [121.377403, 31.084479], [121.37757, 31.08405], [121.377934, 31.083079], [121.378154, 31.082516], [121.378519, 31.081502], [121.378556, 31.081405], [121.378911, 31.080461], [121.378964, 31.080322], [121.379619, 31.078551], [121.379839, 31.077929], [121.38022, 31.076958], [121.380252, 31.076883], [121.38052, 31.07617], [121.381137, 31.074528], [121.381512, 31.073541], [121.38221, 31.071749], [121.382285, 31.071551], [121.382639, 31.070639], [121.383207, 31.069335], [121.383422, 31.068836], [121.383819, 31.067887], [121.383856, 31.067801], [121.384473, 31.06639], [121.384768, 31.065795], [121.385654, 31.064148], [121.386126, 31.063268], [121.386308, 31.063215], [121.387563, 31.063606], [121.387681, 31.063649], [121.388668, 31.063955], [121.389731, 31.064287], [121.391254, 31.064776], [121.391565, 31.064867], [121.391673, 31.064899], [121.39244, 31.065156], [121.392965, 31.065323], [121.393598, 31.065526], [121.394478, 31.065795], [121.395251, 31.066052], [121.396527, 31.066492], [121.396629, 31.066524], [121.397037, 31.066653], [121.39745, 31.066787], [121.397622, 31.066846], [121.397761, 31.066889], [121.398609, 31.067136], [121.398925, 31.067222], [121.399166, 31.067291], [121.399279, 31.067324], [121.399676, 31.067437]]}, "properties": {"id": 6, "type": "amap_route", "name": "高德路线_浅水湾公园", "origin_name": "浅水湾公园", "origin_type": "公园", "origin_district": "松江区", "dest_name": "千家惠百货(华银苑店)", "route_type": "高德算路", "start_coords": [121.364112, 31.097435], "end_coords": [121.399676, 31.067437], "original_start_coords": [121.363777, 31.097343], "original_end_coords": [121.399723, 31.067318]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.728686, 31.150758]}, "properties": {"id": 7, "type": "original_start", "name": "浦东新区六团小学", "poi_type": "学校", "district": "浦东新区", "dest_name": "易佰良品酒店(上海浦东机场店)", "point_role": "起点", "distance_to_path_start": 9.230127475738115, "distance_to_amap_start": 8.698547824485528, "path_to_amap_start_distance": 1.018691424543069, "path_start_distance_category": "5-10m", "amap_start_distance_category": "5-10m", "start_comparison_distance_category": "1-5m", "path_start_coords": [121.728597, 31.150725], "amap_start_coords": [121.728599, 31.150734]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.75152, 31.181426]}, "properties": {"id": 7, "type": "original_end", "name": "易佰良品酒店(上海浦东机场店)", "poi_type": "酒店", "district": "浦东新区", "origin_name": "浦东新区六团小学", "point_role": "终点", "distance_to_path_end": 6.676046517231601, "distance_to_amap_end": 7.0446825205516905, "path_to_amap_end_distance": 0.7020687954384468, "path_end_distance_category": "5-10m", "amap_end_distance_category": "5-10m", "end_comparison_distance_category": "0-1m", "path_end_coords": [121.751507, 31.181485], "amap_end_coords": [121.7515, 31.181487]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.728597, 31.150725]}, "properties": {"id": 7, "type": "path_start", "name": "Path起点_浦东新区六团小学", "original_poi_name": "浦东新区六团小学", "original_poi_type": "学校", "original_poi_district": "浦东新区", "dest_name": "易佰良品酒店(上海浦东机场店)", "point_role": "算路起点", "distance_from_original": 9.230127475738115, "distance_category": "5-10m", "distance_to_amap_start": 1.018691424543069, "original_coords": [121.728686, 31.150758]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.728599, 31.150734]}, "properties": {"id": 7, "type": "amap_start", "name": "高德起点_浦东新区六团小学", "original_poi_name": "浦东新区六团小学", "original_poi_type": "学校", "original_poi_district": "浦东新区", "dest_name": "易佰良品酒店(上海浦东机场店)", "point_role": "算路起点", "distance_from_original": 8.698547824485528, "distance_category": "5-10m", "distance_to_path_start": 1.018691424543069, "original_coords": [121.728686, 31.150758]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.751507, 31.181485]}, "properties": {"id": 7, "type": "path_end", "name": "Path终点_易佰良品酒店(上海浦东机场店)", "original_poi_name": "易佰良品酒店(上海浦东机场店)", "original_poi_type": "酒店", "original_poi_district": "浦东新区", "origin_name": "浦东新区六团小学", "point_role": "算路终点", "distance_from_original": 6.676046517231601, "distance_category": "5-10m", "distance_to_amap_end": 0.7020687954384468, "original_coords": [121.75152, 31.181426]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.7515, 31.181487]}, "properties": {"id": 7, "type": "amap_end", "name": "高德终点_易佰良品酒店(上海浦东机场店)", "original_poi_name": "易佰良品酒店(上海浦东机场店)", "original_poi_type": "酒店", "original_poi_district": "浦东新区", "origin_name": "浦东新区六团小学", "point_role": "算路终点", "distance_from_original": 7.0446825205516905, "distance_category": "5-10m", "distance_to_path_end": 0.7020687954384468, "original_coords": [121.75152, 31.181426]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.728597, 31.150725], [121.72864, 31.15061], [121.72864, 31.15061], [121.72894, 31.15068], [121.72894, 31.15068], [121.72908, 31.15072], [121.72908, 31.15072], [121.72928, 31.15078], [121.72928, 31.15078], [121.72954, 31.15084], [121.72954, 31.15084], [121.7295, 31.15098], [121.72956, 31.15106], [121.72956, 31.15106], [121.72912, 31.15213], [121.72912, 31.15213], [121.72902, 31.15237], [121.72902, 31.15237], [121.72879, 31.15291], [121.72879, 31.15291], [121.72842, 31.15376], [121.72842, 31.15376], [121.72827, 31.15408], [121.72827, 31.15408], [121.72803, 31.15456], [121.72803, 31.15456], [121.72846, 31.15466], [121.72908, 31.15487], [121.72908, 31.15487], [121.72962, 31.15509], [121.72962, 31.15509], [121.72987, 31.1552], [121.72987, 31.1552], [121.73078, 31.1556], [121.73078, 31.1556], [121.73097, 31.15569], [121.73139, 31.15584], [121.73139, 31.15584], [121.7319, 31.15598], [121.7319, 31.15598], [121.73251, 31.15616], [121.73251, 31.15616], [121.7331, 31.15633], [121.7331, 31.15633], [121.73341, 31.15642], [121.73341, 31.15642], [121.73358, 31.15647], [121.73358, 31.15647], [121.73379, 31.15654], [121.73379, 31.15654], [121.73541, 31.15709], [121.73587, 31.15728], [121.73587, 31.15728], [121.73614, 31.15736], [121.73614, 31.15736], [121.7368, 31.15758], [121.7368, 31.15758], [121.73693, 31.15763], [121.73693, 31.15763], [121.7371, 31.15768], [121.7371, 31.15768], [121.73739, 31.15776], [121.73739, 31.15776], [121.73827, 31.15801], [121.73827, 31.15801], [121.73984, 31.15849], [121.74018, 31.15856], [121.74018, 31.15856], [121.74053, 31.15862], [121.74053, 31.15862], [121.74108, 31.1587], [121.74108, 31.1587], [121.74171, 31.1588], [121.74262, 31.15899], [121.74396, 31.15931], [121.74396, 31.15931], [121.74472, 31.1595], [121.74472, 31.1595], [121.7457, 31.15976], [121.7457, 31.15976], [121.74721, 31.16017], [121.74721, 31.16017], [121.74862, 31.16055], [121.74862, 31.16055], [121.74868, 31.16056], [121.74868, 31.16056], [121.74895, 31.16063], [121.74895, 31.16063], [121.74936, 31.16073], [121.74936, 31.16073], [121.75007, 31.16091], [121.75007, 31.16091], [121.75077, 31.16109], [121.75077, 31.16109], [121.75064, 31.16138], [121.75058, 31.16158], [121.75058, 31.16158], [121.75055, 31.1618], [121.75054, 31.16217], [121.75054, 31.16217], [121.75053, 31.16228], [121.75053, 31.16228], [121.75053, 31.16238], [121.75053, 31.16238], [121.75057, 31.16397], [121.75057, 31.16397], [121.7506, 31.16461], [121.7506, 31.16461], [121.7506, 31.16483], [121.7506, 31.16483], [121.7506, 31.16496], [121.7506, 31.16496], [121.75057, 31.16553], [121.7505, 31.16608], [121.7505, 31.16608], [121.75043, 31.16635], [121.75025, 31.16684], [121.75025, 31.16684], [121.75007, 31.16731], [121.75007, 31.16731], [121.7497, 31.16829], [121.7497, 31.16829], [121.74947, 31.16886], [121.74947, 31.16886], [121.74928, 31.16936], [121.74928, 31.16936], [121.74924, 31.16946], [121.74924, 31.16946], [121.74904, 31.17002], [121.74904, 31.17002], [121.74895, 31.17026], [121.74895, 31.17026], [121.74868, 31.171], [121.74868, 31.171], [121.74836, 31.17173], [121.74836, 31.17173], [121.74818, 31.17218], [121.74818, 31.17218], [121.74799, 31.17286], [121.74799, 31.17286], [121.74793, 31.17308], [121.74793, 31.17308], [121.74782, 31.17346], [121.74782, 31.17346], [121.74776, 31.17366], [121.74764, 31.17391], [121.74764, 31.17391], [121.74793, 31.17395], [121.74793, 31.17395], [121.74799, 31.17399], [121.74802, 31.17405], [121.74802, 31.17405], [121.74859, 31.17414], [121.74859, 31.17414], [121.74957, 31.17431], [121.74957, 31.17431], [121.74964, 31.17432], [121.74964, 31.17432], [121.7506, 31.17445], [121.7506, 31.17445], [121.7526, 31.1747], [121.7526, 31.1747], [121.75252, 31.17512], [121.75252, 31.17512], [121.75241, 31.17564], [121.75241, 31.17564], [121.75233, 31.17603], [121.75233, 31.17603], [121.75231, 31.17613], [121.75231, 31.17613], [121.75221, 31.17663], [121.75221, 31.17663], [121.75203, 31.17753], [121.75203, 31.17753], [121.75184, 31.17842], [121.75184, 31.17842], [121.75177, 31.17877], [121.75177, 31.17877], [121.75152, 31.18009], [121.75152, 31.18009], [121.75128, 31.18126], [121.75128, 31.18126], [121.75125, 31.18143], [121.75125, 31.18143], [121.751507, 31.181485]]}, "properties": {"id": 7, "type": "path_route", "name": "Path路线_浦东新区六团小学", "origin_name": "浦东新区六团小学", "origin_type": "学校", "origin_district": "浦东新区", "dest_name": "易佰良品酒店(上海浦东机场店)", "route_type": "Path算路", "start_coords": [121.728597, 31.150725], "end_coords": [121.751507, 31.181485], "original_start_coords": [121.728686, 31.150758], "original_end_coords": [121.75152, 31.181426]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.728599, 31.150734], [121.728644, 31.150612], [121.728644, 31.150612], [121.728697, 31.150622], [121.72895, 31.150687], [121.729084, 31.150724], [121.729282, 31.150778], [121.729545, 31.150848], [121.729545, 31.150848], [121.729502, 31.150987], [121.729566, 31.151068], [121.729566, 31.151068], [121.729126, 31.15213], [121.729025, 31.152371], [121.728794, 31.152913], [121.728515, 31.153557], [121.728424, 31.153766], [121.728274, 31.154082], [121.728032, 31.15456], [121.728032, 31.15456], [121.728461, 31.154667], [121.728515, 31.154683], [121.729084, 31.154871], [121.72962, 31.155096], [121.729878, 31.155198], [121.730779, 31.155606], [121.730977, 31.155692], [121.731396, 31.155847], [121.7319, 31.155987], [121.732517, 31.156164], [121.733107, 31.156335], [121.733413, 31.156432], [121.733585, 31.156475], [121.733799, 31.156539], [121.734201, 31.156673], [121.735414, 31.157097], [121.73573, 31.157226], [121.735875, 31.157285], [121.736143, 31.157371], [121.736803, 31.157585], [121.736937, 31.157634], [121.737109, 31.157682], [121.737393, 31.157768], [121.738273, 31.15802], [121.73985, 31.158497], [121.740183, 31.158562], [121.740537, 31.158621], [121.741084, 31.158701], [121.7414, 31.159039], [121.741513, 31.159109], [121.741744, 31.159179], [121.742076, 31.159254], [121.742328, 31.159291], [121.742484, 31.159297], [121.742656, 31.159275], [121.742816, 31.159205], [121.742924, 31.159146], [121.74302, 31.159125], [121.743246, 31.15912], [121.743965, 31.159318], [121.744726, 31.1595], [121.745703, 31.159763], [121.747215, 31.160171], [121.748621, 31.160552], [121.748691, 31.160568], [121.748953, 31.160632], [121.749361, 31.160734], [121.750069, 31.160911], [121.750488, 31.161019], [121.750772, 31.161099], [121.750772, 31.161099], [121.750724, 31.161174], [121.750643, 31.161383], [121.750584, 31.161582], [121.750557, 31.161802], [121.750541, 31.162172], [121.750536, 31.16229], [121.750536, 31.162386], [121.750579, 31.16398], [121.750606, 31.164613], [121.750606, 31.164838], [121.7506, 31.164967], [121.750573, 31.165535], [121.750504, 31.166083], [121.750488, 31.166152], [121.750429, 31.166356], [121.750257, 31.166839], [121.750075, 31.167311], [121.749731, 31.168212], [121.749699, 31.168293], [121.749479, 31.168861], [121.749286, 31.169366], [121.749286, 31.169366], [121.75008, 31.169376], [121.750466, 31.169382], [121.750488, 31.169382], [121.751233, 31.169435], [121.753277, 31.169591], [121.75354, 31.169682], [121.75354, 31.169682], [121.753331, 31.17105], [121.75332, 31.171098], [121.753272, 31.17134], [121.753143, 31.171994], [121.752971, 31.172853], [121.752896, 31.173206], [121.752826, 31.17355], [121.75265, 31.174473], [121.752607, 31.174703], [121.752521, 31.175122], [121.752413, 31.175642], [121.752333, 31.176028], [121.752311, 31.17613], [121.75221, 31.176629], [121.752033, 31.17753], [121.751845, 31.178426], [121.75178, 31.17877], [121.751695, 31.179199], [121.751518, 31.180095], [121.751281, 31.181264], [121.75125, 31.18143], [121.7515, 31.181487]]}, "properties": {"id": 7, "type": "amap_route", "name": "高德路线_浦东新区六团小学", "origin_name": "浦东新区六团小学", "origin_type": "学校", "origin_district": "浦东新区", "dest_name": "易佰良品酒店(上海浦东机场店)", "route_type": "高德算路", "start_coords": [121.728599, 31.150734], "end_coords": [121.7515, 31.181487], "original_start_coords": [121.728686, 31.150758], "original_end_coords": [121.75152, 31.181426]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.459079, 31.229216]}, "properties": {"id": 8, "type": "original_start", "name": "潇湘阁(吴江路湟普汇店)", "poi_type": "餐厅", "district": "静安区", "dest_name": "兆丰花园", "point_role": "起点", "distance_to_path_start": 4.132920074184557, "distance_to_amap_start": 4.679358935441274, "path_to_amap_start_distance": 0.8629387904391962, "path_start_distance_category": "1-5m", "amap_start_distance_category": "1-5m", "start_comparison_distance_category": "0-1m", "path_start_coords": [121.459059, 31.229249], "amap_start_coords": [121.45905, 31.22925]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.419253, 31.217612]}, "properties": {"id": 8, "type": "original_end", "name": "兆丰花园", "poi_type": "住宅小区", "district": "长宁区", "origin_name": "潇湘阁(吴江路湟普汇店)", "point_role": "终点", "distance_to_path_end": 18.66629353936073, "distance_to_amap_end": 5.907573306491443, "path_to_amap_end_distance": 20.956039436989805, "path_end_distance_category": "10m+", "amap_end_distance_category": "5-10m", "end_comparison_distance_category": "10m+", "path_end_coords": [121.41938, 31.21774], "amap_end_coords": [121.419197, 31.217635]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.459059, 31.229249]}, "properties": {"id": 8, "type": "path_start", "name": "Path起点_潇湘阁(吴江路湟普汇店)", "original_poi_name": "潇湘阁(吴江路湟普汇店)", "original_poi_type": "餐厅", "original_poi_district": "静安区", "dest_name": "兆丰花园", "point_role": "算路起点", "distance_from_original": 4.132920074184557, "distance_category": "1-5m", "distance_to_amap_start": 0.8629387904391962, "original_coords": [121.459079, 31.229216]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.45905, 31.22925]}, "properties": {"id": 8, "type": "amap_start", "name": "高德起点_潇湘阁(吴江路湟普汇店)", "original_poi_name": "潇湘阁(吴江路湟普汇店)", "original_poi_type": "餐厅", "original_poi_district": "静安区", "dest_name": "兆丰花园", "point_role": "算路起点", "distance_from_original": 4.679358935441274, "distance_category": "1-5m", "distance_to_path_start": 0.8629387904391962, "original_coords": [121.459079, 31.229216]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.41938, 31.21774]}, "properties": {"id": 8, "type": "path_end", "name": "Path终点_兆丰花园", "original_poi_name": "兆丰花园", "original_poi_type": "住宅小区", "original_poi_district": "长宁区", "origin_name": "潇湘阁(吴江路湟普汇店)", "point_role": "算路终点", "distance_from_original": 18.66629353936073, "distance_category": "10m+", "distance_to_amap_end": 20.956039436989805, "original_coords": [121.419253, 31.217612]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.419197, 31.217635]}, "properties": {"id": 8, "type": "amap_end", "name": "高德终点_兆丰花园", "original_poi_name": "兆丰花园", "original_poi_type": "住宅小区", "original_poi_district": "长宁区", "origin_name": "潇湘阁(吴江路湟普汇店)", "point_role": "算路终点", "distance_from_original": 5.907573306491443, "distance_category": "5-10m", "distance_to_path_end": 20.956039436989805, "original_coords": [121.419253, 31.217612]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.459059, 31.229249], [121.45921, 31.22934], [121.45921, 31.22934], [121.45959, 31.22958], [121.45959, 31.22958], [121.45938, 31.22997], [121.45929, 31.23008], [121.45929, 31.23008], [121.45926, 31.23012], [121.45926, 31.23012], [121.45903, 31.22999], [121.45865, 31.22962], [121.45865, 31.22962], [121.45848, 31.22944], [121.45848, 31.22944], [121.45825, 31.22921], [121.45818, 31.22916], [121.45818, 31.22916], [121.45796, 31.22901], [121.45769, 31.22886], [121.45769, 31.22886], [121.45756, 31.22879], [121.45756, 31.22879], [121.45685, 31.22839], [121.45685, 31.22839], [121.45658, 31.2282], [121.45658, 31.2282], [121.45653, 31.22816], [121.45653, 31.22816], [121.45624, 31.22796], [121.45624, 31.22796], [121.45576, 31.22764], [121.45543, 31.22749], [121.45543, 31.22749], [121.45505, 31.22737], [121.45505, 31.22737], [121.455, 31.22732], [121.455, 31.22732], [121.45443, 31.22725], [121.45443, 31.22725], [121.45416, 31.22721], [121.45394, 31.22715], [121.45394, 31.22715], [121.4538, 31.22714], [121.45334, 31.22698], [121.45321, 31.22692], [121.45299, 31.22678], [121.45299, 31.22678], [121.45243, 31.2264], [121.45243, 31.2264], [121.45195, 31.22614], [121.45195, 31.22614], [121.4516, 31.22596], [121.4516, 31.22596], [121.45142, 31.22587], [121.45142, 31.22587], [121.45104, 31.22566], [121.45104, 31.22566], [121.45071, 31.22549], [121.45071, 31.22549], [121.45031, 31.22531], [121.45031, 31.22531], [121.44967, 31.22505], [121.44967, 31.22505], [121.44952, 31.225], [121.44952, 31.225], [121.44896, 31.22476], [121.44896, 31.22476], [121.44854, 31.22457], [121.44854, 31.22457], [121.44833, 31.22446], [121.44807, 31.22428], [121.44807, 31.22428], [121.44798, 31.22419], [121.44798, 31.22419], [121.44766, 31.22393], [121.44766, 31.22393], [121.44749, 31.22378], [121.44708, 31.22357], [121.44708, 31.22357], [121.44661, 31.22335], [121.44661, 31.22335], [121.44626, 31.22322], [121.44626, 31.22322], [121.44608, 31.22314], [121.44608, 31.22314], [121.44581, 31.22301], [121.44581, 31.22301], [121.44572, 31.22296], [121.44572, 31.22296], [121.44553, 31.22287], [121.44553, 31.22287], [121.44522, 31.22269], [121.44522, 31.22269], [121.44514, 31.2226], [121.44514, 31.2226], [121.44441, 31.22215], [121.44441, 31.22215], [121.44415, 31.22201], [121.44415, 31.22201], [121.44406, 31.22197], [121.44406, 31.22197], [121.44355, 31.2217], [121.44355, 31.2217], [121.44292, 31.22142], [121.44292, 31.22142], [121.44246, 31.22122], [121.44246, 31.22122], [121.44234, 31.22114], [121.44215, 31.22098], [121.44215, 31.22098], [121.44188, 31.22074], [121.44188, 31.22074], [121.44167, 31.22056], [121.44167, 31.22056], [121.44146, 31.22037], [121.44146, 31.22037], [121.44079, 31.21978], [121.44079, 31.21978], [121.44057, 31.21955], [121.4405, 31.21944], [121.44044, 31.2193], [121.44044, 31.2193], [121.44022, 31.21924], [121.43997, 31.21914], [121.43969, 31.219], [121.43969, 31.219], [121.4395, 31.21887], [121.4395, 31.21887], [121.4394, 31.21881], [121.4394, 31.21881], [121.4391, 31.21856], [121.4391, 31.21856], [121.439, 31.21849], [121.439, 31.21849], [121.43874, 31.21828], [121.43874, 31.21828], [121.43838, 31.21798], [121.43838, 31.21798], [121.43767, 31.21749], [121.43767, 31.21749], [121.43763, 31.21746], [121.43763, 31.21746], [121.4376, 31.21745], [121.4376, 31.21745], [121.43695, 31.21709], [121.43695, 31.21709], [121.43626, 31.2167], [121.43626, 31.2167], [121.43606, 31.21656], [121.43606, 31.21656], [121.43596, 31.21649], [121.43596, 31.21649], [121.43529, 31.21601], [121.43441, 31.21525], [121.43427, 31.21521], [121.43427, 31.21521], [121.4342, 31.21515], [121.4342, 31.21515], [121.43379, 31.2148], [121.43379, 31.2148], [121.4335, 31.21461], [121.4335, 31.21461], [121.43298, 31.21435], [121.43298, 31.21435], [121.43292, 31.21432], [121.43292, 31.21432], [121.43257, 31.21416], [121.43257, 31.21416], [121.4323, 31.21403], [121.4323, 31.21403], [121.43197, 31.21387], [121.43197, 31.21387], [121.43169, 31.21373], [121.43169, 31.21373], [121.43147, 31.21359], [121.43147, 31.21359], [121.43098, 31.2132], [121.43098, 31.2132], [121.43071, 31.21291], [121.43071, 31.21291], [121.43029, 31.21278], [121.43029, 31.21278], [121.42991, 31.21272], [121.42991, 31.21272], [121.42924, 31.21261], [121.42924, 31.21261], [121.42909, 31.21258], [121.42909, 31.21258], [121.42899, 31.21256], [121.42899, 31.21256], [121.42848, 31.21246], [121.42848, 31.21246], [121.42831, 31.21244], [121.42831, 31.21244], [121.42814, 31.21241], [121.42814, 31.21241], [121.42777, 31.21235], [121.42777, 31.21235], [121.4276, 31.21234], [121.4276, 31.21234], [121.42717, 31.21224], [121.42717, 31.21224], [121.4266, 31.21211], [121.4266, 31.21211], [121.42599, 31.21197], [121.42599, 31.21197], [121.4253, 31.21183], [121.4253, 31.21183], [121.42491, 31.21177], [121.42491, 31.21177], [121.42401, 31.2116], [121.42401, 31.2116], [121.42336, 31.21148], [121.42336, 31.21148], [121.42286, 31.21142], [121.42286, 31.21142], [121.42253, 31.21137], [121.42253, 31.21137], [121.4224, 31.21136], [121.4224, 31.21136], [121.42231, 31.21134], [121.42231, 31.21134], [121.42203, 31.2113], [121.42203, 31.2113], [121.42185, 31.21129], [121.42185, 31.21129], [121.42152, 31.21124], [121.42152, 31.21124], [121.42133, 31.21122], [121.42133, 31.21122], [121.42096, 31.21118], [121.42096, 31.21118], [121.42079, 31.21119], [121.42079, 31.21119], [121.42045, 31.21121], [121.42045, 31.21121], [121.42027, 31.21123], [121.42027, 31.21123], [121.41999, 31.21123], [121.41999, 31.21123], [121.41972, 31.21119], [121.41927, 31.21101], [121.41912, 31.21099], [121.41912, 31.21099], [121.419, 31.2111], [121.41884, 31.21138], [121.41884, 31.21138], [121.4188, 31.21145], [121.4188, 31.21145], [121.4184, 31.21216], [121.4184, 31.21216], [121.41824, 31.21239], [121.41814, 31.21244], [121.41798, 31.21248], [121.41798, 31.21248], [121.41799, 31.21263], [121.41795, 31.21288], [121.41795, 31.21288], [121.4176, 31.21407], [121.4176, 31.21407], [121.41754, 31.21429], [121.41754, 31.21429], [121.41734, 31.21508], [121.41734, 31.21508], [121.41735, 31.21511], [121.41735, 31.21511], [121.41726, 31.21539], [121.41726, 31.21539], [121.41721, 31.21553], [121.41721, 31.21553], [121.41724, 31.21598], [121.41724, 31.21598], [121.41736, 31.21674], [121.41736, 31.21674], [121.41739, 31.21692], [121.41739, 31.21692], [121.41744, 31.21737], [121.41744, 31.21737], [121.41748, 31.21756], [121.41748, 31.21756], [121.41757, 31.21822], [121.41757, 31.21822], [121.4193, 31.21866], [121.4193, 31.21866], [121.41931, 31.21851], [121.41931, 31.21851], [121.41932, 31.21813], [121.41932, 31.21813], [121.41937, 31.21805], [121.41955, 31.21794], [121.41956, 31.21791], [121.41955, 31.21775], [121.41955, 31.21775], [121.41938, 31.21774]]}, "properties": {"id": 8, "type": "path_route", "name": "Path路线_潇湘阁(吴江路湟普汇店)", "origin_name": "潇湘阁(吴江路湟普汇店)", "origin_type": "餐厅", "origin_district": "静安区", "dest_name": "兆丰花园", "route_type": "Path算路", "start_coords": [121.459059, 31.229249], "end_coords": [121.41938, 31.21774], "original_start_coords": [121.459079, 31.229216], "original_end_coords": [121.419253, 31.217612]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.45905, 31.22925], [121.45921, 31.229345], [121.459597, 31.229581], [121.459597, 31.229581], [121.459377, 31.229973], [121.459296, 31.23008], [121.459264, 31.230123], [121.459033, 31.229994], [121.458658, 31.229619], [121.458486, 31.229442], [121.458524, 31.229393], [121.45876, 31.229077], [121.458846, 31.228948], [121.458926, 31.228836], [121.458975, 31.228766], [121.459076, 31.2286], [121.459168, 31.228455], [121.45921, 31.228385], [121.459237, 31.228321], [121.459275, 31.228251], [121.459296, 31.228208], [121.459366, 31.228058], [121.459388, 31.228004], [121.459446, 31.227859], [121.459506, 31.22772], [121.459532, 31.227661], [121.459575, 31.227564], [121.459597, 31.227511], [121.459677, 31.227323], [121.459741, 31.227167], [121.459768, 31.227103], [121.459843, 31.226931], [121.459876, 31.226861], [121.45994, 31.226706], [121.46016, 31.22618], [121.460192, 31.226094], [121.460214, 31.226041], [121.460305, 31.225853], [121.460353, 31.225724], [121.460487, 31.225386], [121.460557, 31.225198], [121.460632, 31.224973], [121.460702, 31.224759], [121.460723, 31.2247], [121.460755, 31.224592], [121.460782, 31.22449], [121.460831, 31.22426], [121.460831, 31.224061], [121.460831, 31.224061], [121.460407, 31.224056], [121.459961, 31.224066], [121.459758, 31.224077], [121.459672, 31.224077], [121.459602, 31.224083], [121.459522, 31.224088], [121.459522, 31.224088], [121.458154, 31.224077], [121.457488, 31.224072], [121.455675, 31.224002], [121.45523, 31.223949], [121.455091, 31.223911], [121.45494, 31.223847], [121.454565, 31.223755], [121.454265, 31.223659], [121.452923, 31.223144], [121.452006, 31.222795], [121.451555, 31.222629], [121.451207, 31.222495], [121.450949, 31.222393], [121.450853, 31.222361], [121.450177, 31.222087], [121.449185, 31.221717], [121.448541, 31.221513], [121.448155, 31.2214], [121.447704, 31.221299], [121.447613, 31.221283], [121.447135, 31.221191], [121.446663, 31.221127], [121.445703, 31.221003], [121.445113, 31.220891], [121.444753, 31.220789], [121.444469, 31.220687], [121.443927, 31.220435], [121.443316, 31.220043], [121.443021, 31.219872], [121.44287, 31.219802], [121.442731, 31.219732], [121.442581, 31.219673], [121.44227, 31.219571], [121.442114, 31.219539], [121.440784, 31.219346], [121.440516, 31.219276], [121.440167, 31.219147], [121.439765, 31.218949], [121.439437, 31.218724], [121.43925, 31.218579], [121.438756, 31.218139], [121.438434, 31.217881], [121.437887, 31.217522], [121.437662, 31.217404], [121.437029, 31.217055], [121.436428, 31.216701], [121.435301, 31.215918], [121.434389, 31.21513], [121.434051, 31.214872], [121.433756, 31.214652], [121.433558, 31.214534], [121.432995, 31.214228], [121.432002, 31.213772], [121.431498, 31.213488], [121.431316, 31.213354], [121.430919, 31.213005], [121.430645, 31.212673], [121.430586, 31.212598], [121.430307, 31.212158], [121.430259, 31.212077], [121.430184, 31.211959], [121.429996, 31.211664], [121.429808, 31.211417], [121.429626, 31.211219], [121.429395, 31.211015], [121.428907, 31.210704], [121.428531, 31.210543], [121.428236, 31.210446], [121.427931, 31.210376], [121.42755, 31.210328], [121.427083, 31.210296], [121.426235, 31.210243], [121.425753, 31.210189], [121.42534, 31.210119], [121.425007, 31.210044], [121.423789, 31.20976], [121.423789, 31.20976], [121.423623, 31.209792], [121.423473, 31.209786], [121.42321, 31.209738], [121.42232, 31.20954], [121.421381, 31.209331], [121.421064, 31.209245], [121.420898, 31.209196], [121.420613, 31.209111], [121.420431, 31.209062], [121.420361, 31.209041], [121.420297, 31.20903], [121.419954, 31.208955], [121.4196, 31.208885], [121.418596, 31.208676], [121.418156, 31.208585], [121.417593, 31.208472], [121.417497, 31.208451], [121.417062, 31.20836], [121.417062, 31.20836], [121.417116, 31.20866], [121.417127, 31.208756], [121.417261, 31.209545], [121.417288, 31.209722], [121.417432, 31.210586], [121.41754, 31.211213], [121.417604, 31.211401], [121.417706, 31.211702], [121.417861, 31.212136], [121.417867, 31.212158], [121.41798, 31.212485], [121.41799, 31.21264], [121.417974, 31.212791], [121.417947, 31.212887], [121.417604, 31.214078], [121.417545, 31.214293], [121.417346, 31.215081], [121.417352, 31.215113], [121.417266, 31.215398], [121.417213, 31.215532], [121.417213, 31.215532], [121.417239, 31.215988], [121.417304, 31.216363], [121.41733, 31.216513], [121.417363, 31.216739], [121.417395, 31.216926], [121.417443, 31.217377], [121.417481, 31.217565], [121.417534, 31.217892], [121.417577, 31.218225], [121.417577, 31.218225], [121.419305, 31.218659], [121.419305, 31.218659], [121.419326, 31.218139], [121.419342, 31.218091], [121.419374, 31.218058], [121.419557, 31.21794], [121.419568, 31.217914], [121.419557, 31.217753], [121.419557, 31.217753], [121.419385, 31.217742], [121.419331, 31.217742], [121.419251, 31.21771], [121.419219, 31.217688], [121.419197, 31.217635]]}, "properties": {"id": 8, "type": "amap_route", "name": "高德路线_潇湘阁(吴江路湟普汇店)", "origin_name": "潇湘阁(吴江路湟普汇店)", "origin_type": "餐厅", "origin_district": "静安区", "dest_name": "兆丰花园", "route_type": "高德算路", "start_coords": [121.45905, 31.22925], "end_coords": [121.419197, 31.217635], "original_start_coords": [121.459079, 31.229216], "original_end_coords": [121.419253, 31.217612]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.400353, 31.175458]}, "properties": {"id": 9, "type": "original_start", "name": "上海嘉会国际医院", "poi_type": "医院", "district": "徐汇区", "dest_name": "裕德小区", "point_role": "起点", "distance_to_path_start": 38.889268030059036, "distance_to_amap_start": 37.62465823428158, "path_to_amap_start_distance": 7.008856074752684, "path_start_distance_category": "10m+", "amap_start_distance_category": "10m+", "start_comparison_distance_category": "5-10m", "path_start_coords": [121.400072, 31.175204], "amap_start_coords": [121.400034, 31.175258]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.434524, 31.184691]}, "properties": {"id": 9, "type": "original_end", "name": "裕德小区", "poi_type": "住宅小区", "district": "徐汇区", "origin_name": "上海嘉会国际医院", "point_role": "终点", "distance_to_path_end": 1.9295784954873245, "distance_to_amap_end": 2.3197729488812198, "path_to_amap_end_distance": 0.39642440545136887, "path_end_distance_category": "1-5m", "amap_end_distance_category": "1-5m", "end_comparison_distance_category": "0-1m", "path_end_coords": [121.434506, 31.184699], "amap_end_coords": [121.434502, 31.1847]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.400072, 31.175204]}, "properties": {"id": 9, "type": "path_start", "name": "Path起点_上海嘉会国际医院", "original_poi_name": "上海嘉会国际医院", "original_poi_type": "医院", "original_poi_district": "徐汇区", "dest_name": "裕德小区", "point_role": "算路起点", "distance_from_original": 38.889268030059036, "distance_category": "10m+", "distance_to_amap_start": 7.008856074752684, "original_coords": [121.400353, 31.175458]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.400034, 31.175258]}, "properties": {"id": 9, "type": "amap_start", "name": "高德起点_上海嘉会国际医院", "original_poi_name": "上海嘉会国际医院", "original_poi_type": "医院", "original_poi_district": "徐汇区", "dest_name": "裕德小区", "point_role": "算路起点", "distance_from_original": 37.62465823428158, "distance_category": "10m+", "distance_to_path_start": 7.008856074752684, "original_coords": [121.400353, 31.175458]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.434506, 31.184699]}, "properties": {"id": 9, "type": "path_end", "name": "Path终点_裕德小区", "original_poi_name": "裕德小区", "original_poi_type": "住宅小区", "original_poi_district": "徐汇区", "origin_name": "上海嘉会国际医院", "point_role": "算路终点", "distance_from_original": 1.9295784954873245, "distance_category": "1-5m", "distance_to_amap_end": 0.39642440545136887, "original_coords": [121.434524, 31.184691]}}, {"type": "Feature", "geometry": {"type": "Point", "coordinates": [121.434502, 31.1847]}, "properties": {"id": 9, "type": "amap_end", "name": "高德终点_裕德小区", "original_poi_name": "裕德小区", "original_poi_type": "住宅小区", "original_poi_district": "徐汇区", "origin_name": "上海嘉会国际医院", "point_role": "算路终点", "distance_from_original": 2.3197729488812198, "distance_category": "1-5m", "distance_to_path_end": 0.39642440545136887, "original_coords": [121.434524, 31.184691]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.400072, 31.175204], [121.40013, 31.17514], [121.40026, 31.17487], [121.40026, 31.17487], [121.40043, 31.17483], [121.40101, 31.17493], [121.40101, 31.17493], [121.40101, 31.17487], [121.40101, 31.17487], [121.40101, 31.17482], [121.40101, 31.17482], [121.40124, 31.17486], [121.40124, 31.17486], [121.40172, 31.17495], [121.40172, 31.17495], [121.40201, 31.17393], [121.40201, 31.17393], [121.40208, 31.17368], [121.40208, 31.17368], [121.40209, 31.17363], [121.40209, 31.17363], [121.40214, 31.17347], [121.40214, 31.17347], [121.40235, 31.17275], [121.40235, 31.17275], [121.40242, 31.17251], [121.40242, 31.17251], [121.40263, 31.17169], [121.40263, 31.17169], [121.40266, 31.17159], [121.40266, 31.17159], [121.4041, 31.17187], [121.4041, 31.17187], [121.4043, 31.17191], [121.4043, 31.17191], [121.40558, 31.17216], [121.40558, 31.17216], [121.40703, 31.1725], [121.40703, 31.1725], [121.40786, 31.17268], [121.40786, 31.17268], [121.40828, 31.17278], [121.40828, 31.17278], [121.40951, 31.17305], [121.40951, 31.17305], [121.41, 31.17316], [121.41, 31.17316], [121.41156, 31.17343], [121.41156, 31.17343], [121.41236, 31.17357], [121.41236, 31.17357], [121.4131, 31.1737], [121.4131, 31.1737], [121.41517, 31.1741], [121.41517, 31.1741], [121.41622, 31.17431], [121.41622, 31.17431], [121.41631, 31.17433], [121.41631, 31.17433], [121.41738, 31.17457], [121.41738, 31.17457], [121.41814, 31.17475], [121.41814, 31.17475], [121.41903, 31.17494], [121.41903, 31.17494], [121.41949, 31.17505], [121.41949, 31.17505], [121.41998, 31.17518], [121.41998, 31.17518], [121.42046, 31.17534], [121.42046, 31.17534], [121.42157, 31.17582], [121.42157, 31.17582], [121.42229, 31.17624], [121.42268, 31.1765], [121.42268, 31.1765], [121.42308, 31.17684], [121.42352, 31.17727], [121.42352, 31.17727], [121.4237, 31.17746], [121.42396, 31.17779], [121.42396, 31.17779], [121.42408, 31.17792], [121.42422, 31.17817], [121.42422, 31.17817], [121.42449, 31.17865], [121.42449, 31.17865], [121.42475, 31.17918], [121.42475, 31.17918], [121.42519, 31.18007], [121.42519, 31.18007], [121.42529, 31.18027], [121.42611, 31.18154], [121.42611, 31.18154], [121.42633, 31.18201], [121.42633, 31.18201], [121.42658, 31.1825], [121.42658, 31.1825], [121.42668, 31.1827], [121.42668, 31.1827], [121.42704, 31.18336], [121.42704, 31.18336], [121.42709, 31.18345], [121.42709, 31.18345], [121.42715, 31.18358], [121.42715, 31.18358], [121.42719, 31.18367], [121.42719, 31.18367], [121.42754, 31.18439], [121.42754, 31.18439], [121.42764, 31.18458], [121.42764, 31.18458], [121.42813, 31.1855], [121.42813, 31.1855], [121.42821, 31.18571], [121.4282, 31.18588], [121.4282, 31.18588], [121.42868, 31.18541], [121.42868, 31.18541], [121.42935, 31.18487], [121.42935, 31.18487], [121.42987, 31.18443], [121.42987, 31.18443], [121.43008, 31.18423], [121.43008, 31.18423], [121.43045, 31.18394], [121.43101, 31.18345], [121.43101, 31.18345], [121.43128, 31.18386], [121.43128, 31.18386], [121.43146, 31.18424], [121.43146, 31.18424], [121.43158, 31.18447], [121.43158, 31.18447], [121.43177, 31.18481], [121.43177, 31.18481], [121.43179, 31.18485], [121.43179, 31.18485], [121.43202, 31.1853], [121.43202, 31.1853], [121.43209, 31.18544], [121.43209, 31.18544], [121.4323, 31.18582], [121.4323, 31.18582], [121.43237, 31.18599], [121.43237, 31.18599], [121.43325, 31.18569], [121.43325, 31.18569], [121.43398, 31.18545], [121.43398, 31.18545], [121.43424, 31.18537], [121.43424, 31.18537], [121.43443, 31.18532], [121.43452, 31.18527], [121.43467, 31.18511], [121.43467, 31.18511], [121.43464, 31.18506], [121.43464, 31.18506], [121.43462, 31.185], [121.43462, 31.185], [121.43453, 31.18475], [121.43453, 31.18475], [121.434506, 31.184699]]}, "properties": {"id": 9, "type": "path_route", "name": "Path路线_上海嘉会国际医院", "origin_name": "上海嘉会国际医院", "origin_type": "医院", "origin_district": "徐汇区", "dest_name": "裕德小区", "route_type": "Path算路", "start_coords": [121.400072, 31.175204], "end_coords": [121.434506, 31.184699], "original_start_coords": [121.400353, 31.175458], "original_end_coords": [121.434524, 31.184691]}}, {"type": "Feature", "geometry": {"type": "LineString", "coordinates": [[121.400034, 31.175258], [121.39995, 31.175358], [121.399901, 31.175449], [121.399869, 31.175578], [121.399826, 31.175851], [121.399843, 31.175889], [121.399923, 31.175975], [121.400036, 31.175991], [121.400138, 31.176066], [121.4004, 31.176136], [121.400545, 31.176157], [121.400781, 31.176173], [121.40106, 31.176211], [121.401141, 31.176221], [121.401355, 31.176253], [121.401355, 31.176253], [121.401441, 31.175942], [121.401484, 31.175776], [121.401548, 31.175556], [121.40172, 31.17495], [121.402015, 31.173931], [121.402085, 31.173684], [121.402096, 31.173641], [121.402144, 31.17347], [121.402353, 31.172751], [121.402423, 31.17252], [121.402637, 31.171699], [121.40267, 31.171597], [121.404102, 31.171871], [121.404306, 31.171919], [121.405556, 31.172171], [121.40562, 31.172182], [121.407036, 31.172504], [121.407868, 31.172691], [121.408286, 31.172783], [121.409509, 31.173056], [121.410008, 31.173158], [121.411569, 31.173437], [121.412363, 31.173571], [121.413103, 31.173711], [121.413988, 31.173872], [121.41467, 31.174006], [121.415174, 31.174102], [121.416225, 31.174312], [121.416317, 31.174333], [121.417384, 31.17458], [121.418141, 31.174751], [121.419031, 31.174945], [121.419498, 31.175052], [121.419675, 31.1751], [121.419814, 31.175138], [121.419986, 31.175186], [121.420318, 31.175288], [121.420469, 31.175347], [121.42078, 31.17547], [121.420898, 31.175524], [121.421579, 31.175824], [121.421928, 31.176023], [121.422679, 31.176506], [121.423081, 31.176838], [121.423355, 31.177096], [121.423526, 31.177278], [121.423698, 31.177466], [121.423961, 31.177793], [121.424079, 31.177927], [121.424224, 31.178174], [121.424497, 31.178651], [121.42476, 31.179188], [121.424766, 31.179199], [121.425195, 31.180073], [121.425297, 31.180277], [121.426117, 31.181548], [121.426337, 31.182015], [121.42659, 31.182503], [121.426681, 31.182701], [121.427045, 31.183367], [121.427094, 31.183447], [121.427158, 31.183581], [121.427201, 31.183673], [121.427544, 31.184391], [121.427636, 31.184558], [121.427646, 31.18459], [121.428135, 31.185507], [121.42821, 31.185883], [121.428681, 31.185411], [121.429095, 31.185078], [121.429293, 31.184922], [121.429352, 31.184874], [121.429873, 31.184434], [121.430082, 31.184236], [121.430532, 31.183866], [121.43101, 31.183453], [121.43101, 31.183453], [121.431283, 31.183866], [121.431466, 31.184241], [121.431584, 31.184477], [121.431771, 31.18481], [121.431793, 31.184858], [121.432024, 31.185303], [121.432088, 31.185443], [121.432303, 31.185824], [121.432378, 31.185995], [121.432378, 31.185995], [121.433252, 31.185695], [121.433987, 31.185459], [121.434245, 31.185378], [121.434432, 31.18532], [121.434523, 31.185276], [121.434577, 31.185228], [121.434674, 31.185121], [121.434674, 31.185121], [121.434647, 31.185062], [121.434625, 31.185003], [121.434534, 31.184756], [121.434502, 31.1847]]}, "properties": {"id": 9, "type": "amap_route", "name": "高德路线_上海嘉会国际医院", "origin_name": "上海嘉会国际医院", "origin_type": "医院", "origin_district": "徐汇区", "dest_name": "裕德小区", "route_type": "高德算路", "start_coords": [121.400034, 31.175258], "end_coords": [121.434502, 31.1847], "original_start_coords": [121.400353, 31.175458], "original_end_coords": [121.434524, 31.184691]}}]}