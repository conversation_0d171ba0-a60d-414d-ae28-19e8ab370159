// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-18
//

#include <iostream>
#include <memory>

#include "path/include/path_def.h"
#include "path/src/online/online_route_engine.h"
#include "logger.h"

using namespace aurora::path;

int main() {
    try {
        // 初始化日志
        aurora::logger::get().set_level(spdlog::level::info);
        
        std::cout << "=== 在线算路功能测试 ===" << std::endl;
        
        // 创建在线算路引擎
        auto online_engine = std::make_unique<OnlineRouteEngine>();
        
        if (!online_engine->IsInitialized()) {
            std::cerr << "在线算路引擎初始化失败!" << std::endl;
            return 1;
        }
        
        std::cout << "在线算路引擎初始化成功" << std::endl;
        
        // 创建测试查询：上海人民广场到外滩
        auto query = std::make_shared<PathQuery>();
        query->strategy = PathStrategy::kTimeFirst;
        query->mode = PathMode::kOnline;
        query->trigger = PathTrigger::kInitialRouting;
        
        // 起点：人民广场 (121.475, 31.233)
        auto start = std::make_shared<PathLandmark>();
        start->valid = true;
        start->waypoint_type = WayPointType::kStartPoint;
        start->landmark_type = LandmarkType::kClick;
        start->pt = PointLL(121.475, 31.233);
        query->path_points.push_back(start);
        
        // 终点：外滩 (121.487, 31.240)
        auto end = std::make_shared<PathLandmark>();
        end->valid = true;
        end->waypoint_type = WayPointType::kEndPoint;
        end->landmark_type = LandmarkType::kClick;
        end->pt = PointLL(121.487, 31.240);
        query->path_points.push_back(end);
        
        std::cout << "开始算路: 人民广场 -> 外滩" << std::endl;
        std::cout << "起点: (" << start->pt.lng() << ", " << start->pt.lat() << ")" << std::endl;
        std::cout << "终点: (" << end->pt.lng() << ", " << end->pt.lat() << ")" << std::endl;
        
        // 执行算路
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = online_engine->CalculateRoute(*query);
        auto end_time = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        // 输出结果
        std::cout << "\n=== 算路结果 ===" << std::endl;
        std::cout << "计算时间: " << duration.count() << "ms" << std::endl;
        
        if (result && result->code == 0) {
            std::cout << "✓ 算路成功!" << std::endl;
            std::cout << "状态: " << result->status << std::endl;
            std::cout << "路径数量: " << result->paths.size() << std::endl;
            
            if (!result->paths.empty()) {
                const auto& path = result->paths[0];
                std::cout << "距离: " << (path.length / 1000.0) << " km" << std::endl;
                std::cout << "预计时间: " << (path.travel_time / 60.0) << " 分钟" << std::endl;
                std::cout << "坐标点数量: " << path.points.size() << std::endl;
                std::cout << "路径标签: " << result->tag << std::endl;
                
                // 显示前几个坐标点
                std::cout << "前5个坐标点:" << std::endl;
                for (size_t i = 0; i < std::min(size_t(5), path.points.size()); ++i) {
                    const auto& point = path.points[i];
                    std::cout << "  " << i+1 << ": (" << point.lng() << ", " << point.lat() << ")" << std::endl;
                }
            }
        } else {
            std::cout << "✗ 算路失败!" << std::endl;
            if (result) {
                std::cout << "状态: " << result->status << std::endl;
                std::cout << "错误代码: " << result->code << std::endl;
            } else {
                std::cout << "结果为空" << std::endl;
            }
        }
        
        // 显示性能指标
        const auto& metrics = online_engine->GetMetrics();
        std::cout << "\n=== 性能指标 ===" << std::endl;
        std::cout << "总请求数: " << metrics.total_requests << std::endl;
        std::cout << "成功请求数: " << metrics.successful_requests << std::endl;
        std::cout << "失败请求数: " << metrics.failed_requests << std::endl;
        std::cout << "平均响应时间: " << metrics.average_response_time_ms << "ms" << std::endl;
        
        if (metrics.total_requests > 0) {
            double success_rate = (double)metrics.successful_requests / metrics.total_requests * 100.0;
            std::cout << "成功率: " << success_rate << "%" << std::endl;
        }
        
        std::cout << "\n测试完成!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试异常: " << e.what() << std::endl;
        return 1;
    }
}
