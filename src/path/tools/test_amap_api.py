#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高德API连接
"""

import requests
import json

# 高德API配置
AMAP_CONFIG = {
    'key': 'c6dbca449fbd6baadc69b8540565ef59',  # 新的Web API密钥
    'place_search_url': 'https://restapi.amap.com/v3/place/text'
}

def test_amap_api():
    """测试高德API"""
    print("测试高德API连接...")
    
    # 测试参数
    params = {
        'key': AMAP_CONFIG['key'],
        'keywords': '购物中心',
        'city': '上海',
        'offset': 5,
        'page': 1,
        'extensions': 'all'
    }
    
    try:
        print(f"请求URL: {AMAP_CONFIG['place_search_url']}")
        print(f"请求参数: {params}")
        
        response = requests.get(AMAP_CONFIG['place_search_url'], params=params)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API状态: {result.get('status')}")
            print(f"API信息: {result.get('info')}")
            
            if result.get('status') == '1':
                pois = result.get('pois', [])
                print(f"找到 {len(pois)} 个POI")
                
                if pois:
                    print("\n第一个POI示例:")
                    poi = pois[0]
                    print(f"  名称: {poi.get('name')}")
                    print(f"  地址: {poi.get('address')}")
                    print(f"  坐标: {poi.get('location')}")
                    print(f"  类型: {poi.get('type')}")
                    print(f"  区域: {poi.get('adname')}")
            else:
                print(f"API错误: {result.get('info')}")
        else:
            print(f"HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_amap_api()
