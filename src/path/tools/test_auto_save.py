#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动保存功能
"""

from batch_amap_routing import BatchAmapRouting

def test_auto_save():
    """测试自动保存功能"""
    print("测试自动保存功能...")
    
    # 创建测试实例
    routing = BatchAmapRouting()
    
    # 加载POI数据
    routing.load_poi_data('shanghai_poi_amap_10k.csv')
    
    # 运行2个测试，验证自动保存
    routing.run_batch_routing(2, 1000, 0, 'test_auto.geojson', 'test_auto.csv')
    
    # 打印统计结果
    routing.print_statistics()
    
    # 保存最终结果
    routing.save_route_pairs_to_csv('test_auto.csv')
    routing.save_results_to_geojson('test_auto.geojson')
    
    print("测试完成！")

if __name__ == "__main__":
    test_auto_save()
