#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试终点功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dual_route_comparison import RouteComparison

def test_endpoint_feature():
    """测试终点功能"""
    print("测试终点功能...")
    
    # 创建测试实例
    comparison = RouteComparison()
    
    # 加载POI数据
    comparison.load_poi_data('shanghai_poi_amap_10k.csv')
    
    # 运行1个测试
    comparison.run_batch_test(1)
    
    # 打印统计结果
    comparison.print_statistics()
    
    # 保存结果
    comparison.save_results_to_geojson('test_endpoint.geojson')
    
    print("测试完成！")

if __name__ == "__main__":
    test_endpoint_feature()
