// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-18
//

#include <iostream>
#include <memory>
#include <chrono>
#include <thread>

#include "path/include/path_def.h"
#include "path/src/online/online_route_engine.h"
#include "logger.h"

using namespace aurora::path;

/**
 * 测试在线算路功能的简单程序
 */
class OnlineRoutingTest {
public:
    OnlineRoutingTest() {
        // 初始化日志
        aurora::logger::get().set_level(spdlog::level::info);
        
        // 创建在线算路引擎
        online_engine_ = std::make_unique<OnlineRouteEngine>();
        
        LOG_INFO("OnlineRoutingTest initialized");
    }
    
    void TestBasicRouting() {
        LOG_INFO("=== Testing Basic Online Routing ===");
        
        // 创建测试查询：上海人民广场到外滩
        auto query = CreateTestQuery(
            PointLL(121.475, 31.233),  // 人民广场
            PointLL(121.487, 31.240),  // 外滩
            PathStrategy::kTimeFirst
        );
        
        // 执行算路
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = online_engine_->CalculateRoute(*query);
        auto end_time = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        // 输出结果
        if (result && result->code == 0) {
            LOG_INFO("✓ Basic routing test PASSED");
            LOG_INFO("  - Status: {}", result->status);
            LOG_INFO("  - Paths found: {}", result->paths.size());
            LOG_INFO("  - Calculation time: {}ms", duration.count());
            
            if (!result->paths.empty()) {
                const auto& path = result->paths[0];
                LOG_INFO("  - Distance: {:.1f}km", path.length / 1000.0);
                LOG_INFO("  - Travel time: {:.1f}min", path.travel_time / 60.0);
                LOG_INFO("  - Coordinate points: {}", path.points.size());
                LOG_INFO("  - Tag: {}", result->tag);
            }
        } else {
            LOG_ERROR("✗ Basic routing test FAILED");
            if (result) {
                LOG_ERROR("  - Status: {}, Code: {}", result->status, result->code);
            }
        }
    }
    
    void TestDifferentStrategies() {
        LOG_INFO("=== Testing Different Routing Strategies ===");
        
        PointLL start(121.475, 31.233);  // 人民广场
        PointLL end(121.487, 31.240);    // 外滩
        
        std::vector<PathStrategy> strategies = {
            PathStrategy::kTimeFirst,
            PathStrategy::kDistanceFirst,
            PathStrategy::KHighWayFirst,
            PathStrategy::kAvoidToll
        };
        
        std::vector<std::string> strategy_names = {
            "Time First",
            "Distance First", 
            "Highway First",
            "Avoid Toll"
        };
        
        for (size_t i = 0; i < strategies.size(); ++i) {
            LOG_INFO("Testing strategy: {}", strategy_names[i]);
            
            auto query = CreateTestQuery(start, end, strategies[i]);
            auto result = online_engine_->CalculateRoute(*query);
            
            if (result && result->code == 0 && !result->paths.empty()) {
                const auto& path = result->paths[0];
                LOG_INFO("  ✓ {} - Distance: {:.1f}km, Time: {:.1f}min", 
                        strategy_names[i], path.length / 1000.0, path.travel_time / 60.0);
            } else {
                LOG_ERROR("  ✗ {} - Failed", strategy_names[i]);
            }
            
            // 避免请求过于频繁
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }
    
    void TestWithWaypoints() {
        LOG_INFO("=== Testing Routing with Waypoints ===");
        
        // 创建带途经点的查询：人民广场 -> 南京路 -> 外滩
        auto query = std::make_shared<PathQuery>();
        query->strategy = PathStrategy::kTimeFirst;
        query->mode = PathMode::kOnline;
        query->trigger = PathTrigger::kInitialRouting;
        
        // 起点：人民广场
        auto start = std::make_shared<PathLandmark>();
        start->valid = true;
        start->waypoint_type = WayPointType::kStartPoint;
        start->landmark_type = LandmarkType::kClick;
        start->pt = PointLL(121.475, 31.233);
        query->path_points.push_back(start);
        
        // 途经点：南京路
        auto waypoint = std::make_shared<PathLandmark>();
        waypoint->valid = true;
        waypoint->waypoint_type = WayPointType::kViaPoint;
        waypoint->landmark_type = LandmarkType::kClick;
        waypoint->pt = PointLL(121.481, 31.236);
        query->path_points.push_back(waypoint);
        
        // 终点：外滩
        auto end = std::make_shared<PathLandmark>();
        end->valid = true;
        end->waypoint_type = WayPointType::kEndPoint;
        end->landmark_type = LandmarkType::kClick;
        end->pt = PointLL(121.487, 31.240);
        query->path_points.push_back(end);
        
        auto result = online_engine_->CalculateRoute(*query);
        
        if (result && result->code == 0 && !result->paths.empty()) {
            const auto& path = result->paths[0];
            LOG_INFO("✓ Waypoint routing test PASSED");
            LOG_INFO("  - Distance: {:.1f}km", path.length / 1000.0);
            LOG_INFO("  - Travel time: {:.1f}min", path.travel_time / 60.0);
            LOG_INFO("  - Coordinate points: {}", path.points.size());
        } else {
            LOG_ERROR("✗ Waypoint routing test FAILED");
        }
    }
    
    void TestPerformanceMetrics() {
        LOG_INFO("=== Testing Performance Metrics ===");
        
        // 执行多次请求来测试性能指标
        const int num_requests = 3;
        
        for (int i = 0; i < num_requests; ++i) {
            auto query = CreateTestQuery(
                PointLL(121.475 + i * 0.001, 31.233 + i * 0.001),
                PointLL(121.487 + i * 0.001, 31.240 + i * 0.001),
                PathStrategy::kTimeFirst
            );
            
            auto result = online_engine_->CalculateRoute(*query);
            LOG_INFO("Request {}: {}", i + 1, result && result->code == 0 ? "Success" : "Failed");
            
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
        }
        
        // 获取性能指标
        const auto& metrics = online_engine_->GetMetrics();
        LOG_INFO("Performance Metrics:");
        LOG_INFO("  - Total requests: {}", metrics.total_requests);
        LOG_INFO("  - Successful requests: {}", metrics.successful_requests);
        LOG_INFO("  - Failed requests: {}", metrics.failed_requests);
        LOG_INFO("  - Average response time: {:.1f}ms", metrics.average_response_time_ms);
        
        if (metrics.total_requests > 0) {
            double success_rate = (double)metrics.successful_requests / metrics.total_requests * 100.0;
            LOG_INFO("  - Success rate: {:.1f}%", success_rate);
        }
    }
    
    void RunAllTests() {
        LOG_INFO("Starting Online Routing Tests...");
        
        if (!online_engine_->IsInitialized()) {
            LOG_ERROR("Online route engine is not initialized!");
            return;
        }
        
        TestBasicRouting();
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        TestDifferentStrategies();
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        TestWithWaypoints();
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        TestPerformanceMetrics();
        
        LOG_INFO("All tests completed!");
    }

private:
    std::shared_ptr<PathQuery> CreateTestQuery(const PointLL& start, const PointLL& end, PathStrategy strategy) {
        auto query = std::make_shared<PathQuery>();
        query->strategy = strategy;
        query->mode = PathMode::kOnline;
        query->trigger = PathTrigger::kInitialRouting;
        
        // 起点
        auto start_landmark = std::make_shared<PathLandmark>();
        start_landmark->valid = true;
        start_landmark->waypoint_type = WayPointType::kStartPoint;
        start_landmark->landmark_type = LandmarkType::kClick;
        start_landmark->pt = start;
        query->path_points.push_back(start_landmark);
        
        // 终点
        auto end_landmark = std::make_shared<PathLandmark>();
        end_landmark->valid = true;
        end_landmark->waypoint_type = WayPointType::kEndPoint;
        end_landmark->landmark_type = LandmarkType::kClick;
        end_landmark->pt = end;
        query->path_points.push_back(end_landmark);
        
        return query;
    }

private:
    std::unique_ptr<OnlineRouteEngine> online_engine_;
};

int main() {
    try {
        OnlineRoutingTest test;
        test.RunAllTests();
        return 0;
    } catch (const std::exception& e) {
        LOG_ERROR("Test failed with exception: {}", e.what());
        return 1;
    }
}
