#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的高德POI文件
"""

import json
import csv

def main():
    print("验证高德POI文件...")
    
    # 验证CSV文件
    print("\n=== CSV文件验证 ===")
    try:
        with open('shanghai_poi_amap_10k.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        print(f"CSV行数: {len(rows)} (不包含表头)")
        
        if rows:
            print(f"第一行数据: {rows[0]}")
            
            # 统计POI类型
            type_counts = {}
            for row in rows:
                poi_type = row['POIType']
                type_counts[poi_type] = type_counts.get(poi_type, 0) + 1
            
            print("\nPOI类型分布:")
            for poi_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"  {poi_type}: {count} 个")
            
            # 统计区域分布
            district_counts = {}
            for row in rows:
                district = row['DistName']
                district_counts[district] = district_counts.get(district, 0) + 1
            
            print("\n区域分布:")
            for district, count in sorted(district_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"  {district}: {count} 个")
                
            # 检查坐标范围
            lons = [float(row['DistLon']) for row in rows]
            lats = [float(row['DistLat']) for row in rows]
            
            print(f"\n坐标范围:")
            print(f"  经度: {min(lons):.6f} ~ {max(lons):.6f}")
            print(f"  纬度: {min(lats):.6f} ~ {max(lats):.6f}")
            
    except Exception as e:
        print(f"CSV文件验证失败: {e}")
    
    # 验证GeoJSON文件
    print("\n=== GeoJSON文件验证 ===")
    try:
        with open('shanghai_poi_amap_10k.geojson', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"GeoJSON类型: {data['type']}")
        print(f"特征数量: {len(data['features'])}")
        
        if data['features']:
            first_feature = data['features'][0]
            print(f"\n第一个特征:")
            print(f"  名称: {first_feature['properties']['name']}")
            print(f"  类型: {first_feature['properties']['type']}")
            print(f"  区域: {first_feature['properties']['district']}")
            print(f"  坐标: {first_feature['geometry']['coordinates']}")
            
    except Exception as e:
        print(f"GeoJSON文件验证失败: {e}")
    
    print("\n验证完成!")

if __name__ == "__main__":
    main()
