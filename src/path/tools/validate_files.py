#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的POI文件
"""

import json
import csv

def validate_csv():
    """验证CSV文件"""
    print("验证CSV文件...")
    with open('shanghai_poi_10k.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        rows = list(reader)
        
    print(f"CSV行数: {len(rows)} (不包含表头)")
    
    # 检查必要字段
    required_fields = ['ProvName', 'ProvCode', 'ProvLon', 'ProvLat', 'CityName', 'CityCode', 
                      'CityLon', 'CityLat', 'DistName', 'DistCode', 'DistLon', 'DistLat', 
                      'POIName', 'POIType']
    
    if rows:
        first_row = rows[0]
        missing_fields = [field for field in required_fields if field not in first_row]
        if missing_fields:
            print(f"缺少字段: {missing_fields}")
        else:
            print("所有必要字段都存在")
            
        # 显示示例数据
        print(f"示例数据: {first_row}")

def validate_geojson():
    """验证GeoJSON文件"""
    print("\n验证GeoJSON文件...")
    with open('shanghai_poi_10k.geojson', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"GeoJSON类型: {data['type']}")
    print(f"特征数量: {len(data['features'])}")
    
    if data['features']:
        first_feature = data['features'][0]
        print(f"第一个特征:")
        print(f"  类型: {first_feature['type']}")
        print(f"  几何类型: {first_feature['geometry']['type']}")
        print(f"  坐标: {first_feature['geometry']['coordinates']}")
        print(f"  属性: {first_feature['properties']}")

if __name__ == "__main__":
    validate_csv()
    validate_geojson()
    print("\n验证完成!")
