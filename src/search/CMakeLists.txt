cmake_minimum_required(VERSION 3.10)

project(aurora_search VERSION 1.0)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)

include(FetchContent)

FetchContent_Declare(
        rapidjson
        URL ${CMAKE_SOURCE_DIR}/third_party/rapidjson.tar.gz
)

FetchContent_MakeAvailable(rapidjson)

set(BASE_DIR ${CMAKE_SOURCE_DIR}/src/base)
set(THIRD_PARTY_DIR ${CMAKE_SOURCE_DIR}/third_party)
set(RAPIDJSON_SOURCE_DIR ${rapidjson_SOURCE_DIR})
set(DATA_PROVIDER ${PROJECT_SOURCE_DIR}/../data_provider)


set(INCLUDE_DIRS
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        ${CMAKE_CURRENT_SOURCE_DIR}/src
        ${BASE_DIR}/include
        ${THIRD_PARTY_DIR}/cppjieba/include
        ${THIRD_PARTY_DIR}/magic_enum/include
        ${RAPIDJSON_SOURCE_DIR}/include
)

file(GLOB_RECURSE OUTPUT_INC ${PROJECT_SOURCE_DIR}/include/*.h)

file(GLOB_RECURSE SEARCH_SRC
        ${PROJECT_SOURCE_DIR}/src/*.cc
)

include_directories(${INCLUDE_DIRS})

add_library(${PROJECT_NAME} SHARED
        ${OUTPUT_INC}
        ${SEARCH_SRC})

target_link_libraries(${PROJECT_NAME} PUBLIC aurora_base data_provider)

target_include_directories(${PROJECT_NAME}
        PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        ${RAPIDJSON_SOURCE_DIR}/include
        ${DATA_PROVIDER}/include
)

# Enable warnings
#target_compile_options(${PROJECT_NAME} PRIVATE
#        -Wall
#        -Wextra
#        -Wpedantic
#)

add_executable(search_service keyword_main.cc)
target_link_libraries(search_service PUBLIC ${PROJECT_NAME})

add_executable(sug_service sug_main.cc)
target_link_libraries(sug_service PUBLIC ${PROJECT_NAME})
