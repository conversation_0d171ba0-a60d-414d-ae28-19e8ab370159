#include "bounds.h"

#include <guidance/src/common/geo_util.h>
#include <logger.h>

#include <utility>

#include "common/search_internal_def.h"
#include "util.h"
#include "utils/geohash.h"

namespace aurora::search {
namespace {
class AutoFreeMemory {
 public:
  explicit AutoFreeMemory(const size_t capacity)
      : capacity_(capacity), size_(0), array_x_(nullptr), array_y_(nullptr) {
    array_x_ = new double[capacity_];
    array_y_ = new double[capacity_];
  }

  ~AutoFreeMemory() {
    delete[] array_x_;
    delete[] array_y_;
  }

  void add(const double x, const double y) {
    array_x_[size_] = x;
    array_y_[size_] = y;
    ++size_;
  }

  size_t capacity_;
  size_t size_;
  double* array_x_;
  double* array_y_;
};
bool AddFirstLevelGeohash(std::vector<std::string>* geohashes) {
  constexpr int first_level_geohash_num = sizeof(kGeohashBase32Codes) / sizeof(char);
  geohashes->reserve(first_level_geohash_num);
  for (char c : kGeohashBase32Codes) {
    geohashes->emplace_back(1, c);
  }
  return true;
}

}  // namespace

BoundsPtr CreateCircularBounds(const PointLL& center, double radius) {
  return std::make_shared<CircularBounds>(center, radius);
}

BoundsPtr CreateRectangularBounds(const PointLL& north_east, const PointLL& south_west) {
  return std::make_shared<RectangularBounds>(north_east, south_west);
}

BoundsPtr CreatePolylineBounds(const std::vector<PointLL>& points, double width) {
  return std::make_shared<PolylineBounds>(points, width);
}

CircularBounds::CircularBounds(PointLL center, const double radius)
    : center_(std::move(center)), radius_(radius) {}

BoundsInternal::Type CircularBounds::type() { return Type::kCircular; }

bool CircularBounds::CalculateGeohash(std::vector<std::string>* geohashes) {
  if (GeoHashForBoundLongitudeLatitude(GetMbr(), geohashes) < 0) {
    LOG_WARN("Failed to generate geohashes");
    return false;
  }

  std::sort(geohashes->begin(), geohashes->end());
  return true;
}

bool CircularBounds::IsInBounds(double lng, double lat) {
  return CalculateDistance(lng, lat) < radius_;
}
double CircularBounds::GetMaximalAllowDistance() { return radius_; }
std::string CircularBounds::ToString() {
  return fmt::format("CircularBounds: center= {}, radius= {}", center_.ToString(), radius_);
}

const PointLL& CircularBounds::center() const { return center_; }

double CircularBounds::radius() const { return radius_; }

double CircularBounds::CalculateDistance(double lng, double lat) {
  return HaversineDistance(lng, lat, center_.lng(), center_.lat());
}

Mbr CircularBounds::GetMbr() { return GenerateMbr(center_.lng(), center_.lat(), radius_); }
RectangularBounds::RectangularBounds(const PointLL& south_west, const PointLL& north_west)
    : mbr_(south_west, north_west),
      center_(mbr_.Center()),
      radius_(HaversineDistance(center_, south_west)) {}

BoundsInternal::Type RectangularBounds::type() { return Type::kRectangular; }

bool RectangularBounds::CalculateGeohash(std::vector<std::string>* geohashes) {
  if (GeoHashForBoundLongitudeLatitude(mbr_, geohashes) < 0) {
    LOG_WARN("Failed to generate geohashes");
    return false;
  }

  std::sort(geohashes->begin(), geohashes->end());
  return true;
}
bool RectangularBounds::IsInBounds(double lng, double lat) { return mbr_.Contains({lng, lat}); }

double RectangularBounds::GetMaximalAllowDistance() { return radius_; }

std::string RectangularBounds::ToString() {
  return fmt::format("RectangularBounds: south_west= {}, north_east = {}", mbr_.minpt().ToString(),
                     mbr_.maxpt().ToString());
}

double RectangularBounds::CalculateDistance(double lng, double lat) {
  return HaversineDistance(center_.lng(), center_.lat(), lng, lat);
}

Mbr RectangularBounds::GetMbr() { return mbr_; }

PolylineBounds::PolylineBounds(const std::vector<PointLL>& points, double width)
    : points_(points), width_(width) {}

BoundsInternal::Type PolylineBounds::type() { return Type::kPolyline; }

bool PolylineBounds::CalculateGeohash(std::vector<std::string>* geohashes) {
  AutoFreeMemory space(points_.size());
  for (const auto& [latitude, longitude] : points_) {
    space.add(longitude, latitude);
  }

  if (GeoHashForBand(space.array_x_, space.array_y_, space.size_, width_, geohashes) < 0) {
    LOG_WARN("Failed to generate geohashes");
    return false;
  }

  std::sort(geohashes->begin(), geohashes->end());
  return true;
}

bool PolylineBounds::IsInBounds(double lng, double lat) {
  if (points_.empty()) {
    return false;
  }
  return CalculateDistance(lng, lat) < width_;
}
double PolylineBounds::GetMaximalAllowDistance() { return width_; }

std::string PolylineBounds::ToString() { return ""; }

double PolylineBounds::CalculateDistance(double lng, double lat) {
  uint32_t begin = 0;
  uint32_t end = points_.size() - 1;

  while (begin + 4 < end) {
    const uint32_t mid = (begin + end) >> 1;
    const uint32_t first_half = (begin + mid) >> 1;
    const uint32_t second_half = (mid + end) >> 1;
    const double first_dis =
        HaversineDistance(lng, lat, points_[first_half].lng(), points_[first_half].lat());
    const double mid_dis = HaversineDistance(lng, lat, points_[mid].lng(), points_[mid].lat());
    const double second_dis =
        HaversineDistance(lng, lat, points_[second_half].lng(), points_[second_half].lat());
    if (mid_dis >= first_half) {
      // 抛弃后半段
      end = mid;
    } else if (mid_dis >= second_dis) {
      // 抛弃前半段
      begin = mid;
    } else {
      // mid < first && mid < second
      begin = first_half;
      end = second_half;
      if (first_dis <= second_dis) {
        end = mid;
      } else {
        begin = mid;
      }
    }
  }
  double min_distance = std::numeric_limits<double>::max();
  for (uint32_t i = begin; i <= end; ++i) {
    double d = HaversineDistance(lng, lat, points_[i].lng(), points_[i].lat());
    min_distance = std::min(min_distance, d);
  }
  return min_distance;
}

Mbr PolylineBounds::GetMbr() {
  if (!points_.empty()) {
    double lat_min = points_[0].lat();
    double lat_max = points_[0].lat();
    double lng_min = points_[0].lng();
    double lng_max = points_[0].lng();
    for (int i = 1; i < points_.size(); i++) {
      lat_min = std::min(lat_min, points_[i].lat());
      lat_max = std::max(lat_max, points_[i].lat());
      lng_min = std::min(lng_min, points_[i].lng());
      lng_max = std::max(lng_max, points_[i].lng());
    }
    if (lat_min == lat_max) {
      lat_min -= 0.001;
      lat_max += 0.001;
    }
    if (lng_min == lng_max) {
      lng_min -= 0.001;
      lng_max += 0.001;
    }
    mbr_ = Mbr(lng_min, lat_min, lng_max, lat_max);
  }
  return mbr_;
}

InfiniteBounds::InfiniteBounds(PointLL center) : center_(std::move(center)) {}

BoundsInternal::Type InfiniteBounds::type() { return Type::kInfinite; }

bool InfiniteBounds::CalculateGeohash(std::vector<std::string>* geohashes) {
  AddFirstLevelGeohash(geohashes);
  return true;
}

bool InfiniteBounds::IsInBounds(double lng, double lat) { return true; }

double InfiniteBounds::GetMaximalAllowDistance() { return std::numeric_limits<double>::max(); }
std::string InfiniteBounds::ToString() {
  return fmt::format("InfiniteBounds: center= {}", center_.ToString());
}
double InfiniteBounds::CalculateDistance(double lng, double lat) {
  return HaversineDistance(lng, lat, center_.lng(), center_.lat());
}

Mbr InfiniteBounds::GetMbr() { return mbr_; }
BoundsInternal::Type EmptyBounds::type() { return Type::kEmpty; }
bool EmptyBounds::CalculateGeohash(std::vector<std::string>* geohashes) {
  AddFirstLevelGeohash(geohashes);
  return true;
}
bool EmptyBounds::IsInBounds(double lng, double lat) { return true; }
double EmptyBounds::GetMaximalAllowDistance() { return std::numeric_limits<double>::max(); }
std::string EmptyBounds::ToString() { return "EmptyBounds: no bounds defined"; }
double EmptyBounds::CalculateDistance(double lng, double lat) { return 0.0; }

Mbr EmptyBounds::GetMbr() { return mbr_; };

}  // namespace aurora::search