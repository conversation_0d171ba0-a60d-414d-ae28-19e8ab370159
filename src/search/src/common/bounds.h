#ifndef SEARCH_SRC_COMMON_BOUNDS_H
#define SEARCH_SRC_COMMON_BOUNDS_H
#include <memory>
#include <string>
#include <vector>

#include "include/search_def.h"
#include "search_internal_def.h"

namespace aurora::search {
class BoundsInternal : public Bounds {
 public:
  ~BoundsInternal() override = default;
  enum class Type {
    kRectangular,
    kCircular,
    kPolyline,
    kInfinite,
    kEmpty,
  };

  virtual Type type() = 0;
  virtual bool CalculateGeohash(std::vector<std::string> *geohashes) = 0;
  virtual bool IsInBounds(double lng, double lat) = 0;
  virtual double CalculateDistance(double lng, double lat) = 0;
  virtual double GetMaximalAllowDistance() = 0;
  virtual std::string ToString() = 0;
  virtual Mbr GetMbr() = 0;
};

using BoundsInternalPtr = std::shared_ptr<BoundsInternal>;

class CircularBounds final : public BoundsInternal {
 public:
  CircularBounds(PointLL center, double radius);
  ~CircularBounds() override = default;
  Type type() override;
  bool CalculateGeohash(std::vector<std::string> *geohashes) override;
  bool IsInBounds(double lng, double lat) override;
  double CalculateDistance(double lng, double lat) override;
  double GetMaximalAllowDistance() override;
  Mbr GetMbr() override;

  std::string ToString() override;
  const PointLL &center() const;
  double radius() const;

 private:
  PointLL center_;
  double radius_;
  Mbr mbr_;
};

class RectangularBounds final : public BoundsInternal {
 public:
  RectangularBounds(const PointLL &south_west, const PointLL &north_west);
  ~RectangularBounds() override = default;
  Type type() override;
  bool CalculateGeohash(std::vector<std::string> *geohashes) override;
  bool IsInBounds(double lng, double lat) override;
  double GetMaximalAllowDistance() override;
  std::string ToString() override;
  double CalculateDistance(double lng, double lat) override;
  Mbr GetMbr() override;

 private:
  PointLL center_;
  double radius_;
  Mbr mbr_;
};

class PolylineBounds final : public BoundsInternal {
 public:
  PolylineBounds(const std::vector<PointLL> &points, double width);
  ~PolylineBounds() override = default;

  Type type() override;
  bool CalculateGeohash(std::vector<std::string> *geohashes) override;
  bool IsInBounds(double lng, double lat) override;
  double GetMaximalAllowDistance() override;
  std::string ToString() override;
  double CalculateDistance(double lng, double lat) override;
  Mbr GetMbr() override;

 private:
  const std::vector<PointLL> points_;
  double width_ = 0;
  Mbr mbr_;
};

class InfiniteBounds final : public BoundsInternal {
 public:
  explicit InfiniteBounds(PointLL center);
  ~InfiniteBounds() override = default;

  Type type() override;
  bool CalculateGeohash(std::vector<std::string> *geohashes) override;
  bool IsInBounds(double lng, double lat) override;
  double GetMaximalAllowDistance() override;
  std::string ToString() override;
  double CalculateDistance(double lng, double lat) override;
  Mbr GetMbr() override;

 private:
  PointLL center_;
  Mbr mbr_;
};

class EmptyBounds final : public BoundsInternal {
 public:
  EmptyBounds() = default;
  ~EmptyBounds() override = default;

  Type type() override;
  bool CalculateGeohash(std::vector<std::string> *geohashes) override;
  bool IsInBounds(double lng, double lat) override;
  double GetMaximalAllowDistance() override;
  std::string ToString() override;
  double CalculateDistance(double lng, double lat) override;
  Mbr GetMbr() override;

 private:
  Mbr mbr_;
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_COMMON_BOUNDS_H