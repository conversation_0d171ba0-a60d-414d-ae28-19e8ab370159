#include "search_config.h"

#include <logger.h>

#include <fstream>
#include <iostream>
#include <sstream>
#include <utility>

#include "data_folder_layout.h"
#include "rapidjson/document.h"
#include "rapidjson/error/en.h"
#include "utils/file_utils.h"
#include "utils/json_utils.h"
#include "utils/log_utils.h"

namespace aurora::search {
const std::string SearchConfig::kConfigFileName = "search_cfg.json";
const std::string SearchConfig::kInvertIndexDir = "invert_index";

SearchConfig::SearchConfig(std::string search_dir)
    : root_dir(std::move(search_dir)),
      max_retrieval_count(0),
      autocomplete_max_retrieval_count(0),
      level_one_pruning_threshold(0),
      level_two_pruning_threshold(0),
      online_engine_port(0),
      debug_mode(false),
      print_rank_info(false),
      data_folder_layout(nullptr) {}
SearchConfig::~SearchConfig() = default;

bool SearchConfig::Load() {
  const std::string config_file = FileUtils::JoinPath(root_dir, kConfigFileName);
  if (!ParseConfigFile(config_file)) {
    LOG_FATAL("Failed to load: {}", config_file);
    return false;
  }
  NormalizeDirectories();
  data_folder_layout = std::make_shared<DataFolderLayout>();
  Display();
  return true;
}
void SearchConfig::Display() {
  LOG_INFO("SearchConfig:");
  LOG_INFO("  root_dir: {}", root_dir);
  LOG_INFO("  data_dir: {}", data_dir);
  LOG_INFO("  jieba_dir: {}", jieba_dir);
  LOG_INFO("  synonym_dict: {}", synonym_dict);
  LOG_INFO("  rank_policy: {}", rank_policy);
  LOG_INFO("  national_city_code: {}", LogUtils::ToString(national_city_code));
  LOG_INFO("  default_city_code: {}", default_city_code);
  LOG_INFO("  place_table_cache_size: {}", place_table_cache_size);
  LOG_INFO("  invert_index_cache_size: {}", invert_index_cache_size);
  LOG_INFO("  prefix_index_cache_size: {}", prefix_index_cache_size);
  LOG_INFO("  max_retrieval_count: {}", max_retrieval_count);
  LOG_INFO("  autocomplete_max_retrieval_count: {}", autocomplete_max_retrieval_count);
  LOG_INFO("  level_one_pruning_threshold: {}", level_one_pruning_threshold);
  LOG_INFO("  level_two_pruning_threshold: {}", level_two_pruning_threshold);
  LOG_INFO("  online_engine_host: {}", online_engine_host);
  LOG_INFO("  online_engine_port: {}", online_engine_port);
  LOG_INFO("  debug_mode: {}", debug_mode);
  LOG_INFO("  force_city_code: {}", force_city_code);
  LOG_INFO("  print_rank_info: {}", print_rank_info);
}

bool SearchConfig::ParseConfigFile(const std::string& config_file) {
  // Read the file content
  std::ifstream file(config_file);
  if (!file.is_open()) {
    LOG_WARN("Failed to open config file: {}", config_file);
    return false;
  }

  std::stringstream buffer;
  buffer << file.rdbuf();
  file.close();
  std::string content = buffer.str();

  // Parse the JSON
  rapidjson::Document doc;
  rapidjson::ParseResult result = doc.Parse(content.c_str());

  if (!result || !doc.IsObject()) {
    LOG_WARN("Invalid config file: {}", config_file);
    return false;
  }
  data_dir = JsonUtils::ParseString(doc, "data_dir");
  jieba_dir = JsonUtils::ParseString(doc, "jieba_dir");
  synonym_dict = JsonUtils::ParseString(doc, "synonym_dict");
  rank_policy = JsonUtils::ParseString(doc, "rank_policy");
  JsonUtils::SetIfExist(doc, "national_city_code", &national_city_code);
  default_city_code = JsonUtils::ParseString(doc, "default_city_code");

  place_table_cache_size = JsonUtils::ParseInt(doc, "place_table_cache_size");
  invert_index_cache_size = JsonUtils::ParseInt(doc, "invert_index_cache_size");
  prefix_index_cache_size = JsonUtils::ParseInt(doc, "prefix_index_cache_size");

  max_retrieval_count = JsonUtils::ParseInt(doc, "max_retrieval_count");
  autocomplete_max_retrieval_count = JsonUtils::ParseInt(doc, "autocomplete_max_retrieval_count");
  if (doc.HasMember("geo_pruning_threshold") && doc["geo_pruning_threshold"].IsObject()) {
    const rapidjson::Value& geo_pruning_config = doc["geo_pruning_threshold"];
    level_one_pruning_threshold = JsonUtils::ParseInt(geo_pruning_config, "level_one");
    level_two_pruning_threshold = JsonUtils::ParseInt(geo_pruning_config, "level_two");
  }

  if (doc.HasMember("online_engine") && doc["online_engine"].IsObject()) {
    const rapidjson::Value& online_config = doc["online_engine"];
    online_engine_host = JsonUtils::ParseString(online_config, "host");
    online_engine_port = JsonUtils::ParseInt(online_config, "port");

    if (doc.HasMember("debug") && doc["debug"].IsObject()) {
      const rapidjson::Value& debug_config = doc["debug"];
      debug_mode = JsonUtils::ParseBool(debug_config, "on", false);
      force_city_code = JsonUtils::ParseString(debug_config, "force_city_code", "440300");
      print_rank_info = JsonUtils::ParseBool(debug_config, "print_rank_info", false);
      city_data_list = JsonUtils::ParseStringArray(debug_config, "city_data_list");
    }
  }

  return true;
}

void SearchConfig::NormalizeDirectories() {
  root_dir = FileUtils::GetCleanPath(root_dir);
  data_dir = JoinIfRelative(root_dir, data_dir);
  jieba_dir = JoinIfRelative(root_dir, jieba_dir);
  synonym_dict = JoinIfRelative(root_dir, synonym_dict);
}

std::string SearchConfig::JoinIfRelative(const std::string& base, const std::string& relative) {
  if (FileUtils::IsAbsolutePath(relative)) {
    return relative;
  }
  return FileUtils::JoinPath(base, relative);
}

}  // namespace aurora::search