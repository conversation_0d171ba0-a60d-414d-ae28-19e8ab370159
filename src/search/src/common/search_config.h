#ifndef SEARCH_COMMON_SEARCH_CONFIG_H
#define SEARCH_COMMON_SEARCH_CONFIG_H

#include <memory>
#include <optional>
#include <string>
#include <vector>

namespace aurora::search {
struct DataFolderLayout;
struct SearchConfig {
  static const std::string kConfigFileName;
  static const std::string kInvertIndexDir;

  std::string root_dir;
  std::string data_dir;
  std::string jieba_dir;
  std::string synonym_dict;
  std::string rank_policy;

  std::optional<std::string> national_city_code;
  std::string default_city_code;

  int place_table_cache_size;
  int invert_index_cache_size;
  int prefix_index_cache_size;

  int max_retrieval_count;
  int autocomplete_max_retrieval_count;
  int level_one_pruning_threshold;
  int level_two_pruning_threshold;

  std::string online_engine_host;
  int online_engine_port;

  bool debug_mode;
  std::string force_city_code;
  bool print_rank_info;
  std::vector<std::string> city_data_list;

  std::shared_ptr<DataFolderLayout> data_folder_layout;

  explicit SearchConfig(std::string search_dir);
  ~SearchConfig();
  bool Load();
  void Display();

 private:
  bool ParseConfigFile(const std::string& config_file);
  void NormalizeDirectories();
  // convenient method, if relative is absolute dir, the target dir is relative,
  // other join it to base
  std::string JoinIfRelative(const std::string& base, const std::string& relative);
};

}  // namespace aurora::search

#endif  // SEARCH_COMMON_SEARCH_CONFIG_H