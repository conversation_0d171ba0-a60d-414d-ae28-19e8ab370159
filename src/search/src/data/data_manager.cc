#include "data_manager.h"

#include "common/search_config.h"
#include "data/invert_index/invert_index.h"
#include "data/multi_city_data_loader.h"
#include "data/place_table/place_table.h"
#include "data/prefix_index/prefix_index.h"
#include "geocoder.h"
#include "utils/log_utils.h"
#include "utils/string_utils.h"

namespace aurora::search {
namespace {
struct CityTuple {
  std::string city_code;
  CityType type;
};
}  // namespace

DataManager::DataManager(std::shared_ptr<SearchConfig> config) : config_(std::move(config)) {}
bool DataManager::Init() {
  data_provider_.SetConfig(config_);
  national_city_code_ = config_->national_city_code;
  current_city_code_ = DetermineInitialCity();
  UpdateCityList(data_provider_.GetCityList(), false);
  PrintStatus();

  geocoder_ = std::make_shared<Geocoder>(config_);
  geocoder_->Init();

  prefix_index_loader_ =
      std::make_shared<MultiCityDataLoader<PrefixIndex>>(config_, config_->invert_index_cache_size);
  prefix_index_loader_->Init(std::nullopt, current_city_code_);

  invert_index_loader_ =
      std::make_shared<MultiCityDataLoader<InvertIndex>>(config_, config_->invert_index_cache_size);
  invert_index_loader_->Init(national_city_code_, current_city_code_);

  place_table_loader_ =
      std::make_shared<MultiCityDataLoader<PlaceTable>>(config_, config_->place_table_cache_size);
  place_table_loader_->Init(national_city_code_, current_city_code_);

  return true;
}

AutocompleteDataSource DataManager::GetAutocompleteDataSource() const {
  std::string city = current_city_code_;
  CityType type = CityType::kPrimary;
  auto prefix_index = prefix_index_loader_->Get(city, type);
  auto detail_data = place_table_loader_->Get(city, type);
  return AutocompleteDataSource{city, type, prefix_index, detail_data};
}

std::vector<SearchByTextDataSource> DataManager::GetTextSearchDataSource(
    const BoundsInternalPtr& bounds) const {
  std::vector<CityTuple> cities;

  if (national_city_code_.has_value()) {
    cities.push_back({national_city_code_.value(), CityType::kNational});
  }

  CityVector overlap_cities = GetOverlapCities(bounds);
  for (auto& city : overlap_cities) {
    if (valid_cities_.count(city) != 0) {
      if (IsPrimary(city)) {
        cities.push_back({city, CityType::kPrimary});
      } else {
        cities.push_back({city, CityType::kNormal});
      }
    }
  }

  std::vector<SearchByTextDataSource> data_sources;
  for (auto& [city, type] : cities) {
    auto invert_index = invert_index_loader_->Get(city, type);
    auto detail_data = place_table_loader_->Get(city, type);
    if (invert_index != nullptr && detail_data != nullptr) {
      data_sources.emplace_back(SearchByTextDataSource{city, type, invert_index, detail_data});
    }
  }
  return data_sources;
}

DetailDataSource DataManager::GetDetailDataSource(const std::string& city_code) const {
  CityType type = IsPrimary(city_code) ? CityType::kPrimary : CityType::kNormal;
  return {city_code, type, place_table_loader_->Get(city_code, type), {}};
}

std::vector<ReverseGeocodeDataSource> DataManager::GetReverseGeocodeDataSource(
    const std::string& geo_cell) const {
  std::vector<std::string> cities = GetPossibleCities(geo_cell);
  std::vector<ReverseGeocodeDataSource> data_sources;
  for (auto& city : cities) {
    CityType type = IsPrimary(city) ? CityType::kPrimary : CityType::kNormal;
    auto place_table = place_table_loader_->Get(city, type);
    if (place_table != nullptr) {
      data_sources.emplace_back(ReverseGeocodeDataSource{city, type, place_table});
    }
  }
  return data_sources;
}

std::vector<std::string> DataManager::GetPossibleCities(const std::string& place_id) const {
  return geocoder_->GetPossibleCities(place_id);
}

void DataManager::OnCityListUpdated(const CityVector& city_codes) {
  UpdateCityList(city_codes, true);
}

std::string DataManager::DetermineInitialCity() const {
  // TODO: get initial city from multiple sources
  // 1. from data service
  // 2. from file cache
  // 3. from cache
  return config_->default_city_code;
}

void DataManager::UpdateCityList(const CityVector& city_codes, bool notify_loaders) {
  CityVector added;
  CityVector removed;
  for (auto& city : city_codes) {
    if (valid_cities_.count(city) == 0) {
      added.emplace_back(city);
    } else {
      removed.emplace_back(city);
    }
  }
  valid_cities_.clear();
  for (auto& city : city_codes) {
    valid_cities_.emplace(city);
  }
  LOG_INFO("CityList added: {}, removed: {}", StringUtils::Join(added.begin(), added.end(), ","),
           StringUtils::Join(removed.begin(), removed.end(), ","));

  if (notify_loaders) {
    prefix_index_loader_->InvalidateCities(removed);
    invert_index_loader_->InvalidateCities(removed);
    place_table_loader_->InvalidateCities(removed);
  }
}

bool DataManager::IsPrimary(const std::string& city) const { return current_city_code_ == city; }

void DataManager::PrintStatus() const {
  LOG_INFO("current: {}, city list: [{}], national: {}", current_city_code_,
           StringUtils::Join(valid_cities_.begin(), valid_cities_.end(), ","),
           LogUtils::ToString(national_city_code_));
}

void DataManager::UpdateCurrentCity(const PointLL& current_location) {
  // current_city_code_ = "440300";
  auto city_list = data_provider_.GetCityList(current_location);
  if (city_list.size() == 1) {
    current_city_code_ = city_list[0];
  }
  return;
}

CityVector DataManager::GetOverlapCities(const BoundsInternalPtr& bounds) const {
  return data_provider_.GetCityList(bounds);
}

}  // namespace aurora::search
