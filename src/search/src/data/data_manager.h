#ifndef SEARCH_SRC_DATA_DATA_MANAGER_H
#define SEARCH_SRC_DATA_DATA_MANAGER_H
#include <memory>
#include <optional>
#include <unordered_set>

#include "common/bounds.h"
#include "data_source.h"
#include "wrapper/data_provider_wrapper.h"

namespace aurora::search {
class Geocoder;
template <typename DataType>
class MultiCityDataLoader;
class PrefixIndexLoader;
struct SearchConfig;

class DataManager {
 public:
  explicit DataManager(std::shared_ptr<SearchConfig> config);
  bool Init();

  // TODO(ZQ): currently we will just use current city for autocomplete
  AutocompleteDataSource GetAutocompleteDataSource() const;

  std::vector<SearchByTextDataSource> GetTextSearchDataSource(
      const BoundsInternalPtr& bounds) const;

  DetailDataSource GetDetailDataSource(const std::string& city_code) const;
  std::vector<ReverseGeocodeDataSource> GetReverseGeocodeDataSource(
      const std::string& geo_cell) const;

  std::vector<std::string> GetPossibleCities(const std::string& place_id) const;

  void UpdateCurrentCity(const PointLL& current_location);
  void OnCityListUpdated(const CityVector& city_codes);

 private:
  std::string DetermineInitialCity() const;
  void UpdateCityList(const CityVector& city_codes, bool notify_loaders);
  CityVector GetOverlapCities(const BoundsInternalPtr& bounds) const;
  bool IsPrimary(const std::string& city) const;

  void PrintStatus() const;

  DataProviderWrapper data_provider_;
  std::shared_ptr<SearchConfig> config_;
  std::optional<std::string> national_city_code_;
  std::string current_city_code_;
  std::unordered_set<std::string> valid_cities_;

  std::shared_ptr<Geocoder> geocoder_;
  std::shared_ptr<MultiCityDataLoader<PrefixIndex>> prefix_index_loader_;
  std::shared_ptr<MultiCityDataLoader<InvertIndex>> invert_index_loader_;
  std::shared_ptr<MultiCityDataLoader<PlaceTable>> place_table_loader_;
};
using DataManagerPtr = std::shared_ptr<DataManager>;
}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_DATA_MANAGER_H