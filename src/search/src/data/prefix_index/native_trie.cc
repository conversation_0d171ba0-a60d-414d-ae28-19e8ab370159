#include "native_trie.h"

#include <fcntl.h>
#include <logger.h>
#include <sys/stat.h>
#include <sys/types.h>

#include "utils/file_utils.h"
#include "utils/string_utils.h"

namespace aurora::search {
namespace {
int BinSearch(const NativeTrieNode* trie_index, int len, const char* word) {
  if (len == 0 || trie_index == nullptr) {
    return -1;
  }

  int left = 0;
  int right = len;

  while (left < right) {
    const int mid = (right + left) / 2;
    const auto center = trie_index + mid;
    const int compare_result = strncmp(word, center->word, kMaxCharLen);
    if (compare_result == 0) {
      return mid;
    }
    if (compare_result > 0) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }
  return -1;
}
}  // namespace

bool NativeTrie::Load(const std::string& index_directory, const std::string& trie_file,
                      const std::string& native_trans_mapping_file) {
  if (!LoadIndex(index_directory, trie_file)) {
    return false;
  }

  if (!LoadMappingFile(index_directory, native_trans_mapping_file)) {
    return false;
  }
  return true;
}

int NativeTrie::SearchTrie(const char* words, const IndexResult* trie_start,
                           IndexResult* trie_result) const {
  if (trie_result == nullptr) {
    return -1;
  }

  char one_word[kMaxCharLen];
  int word_off = 0;
  int deep = 0;
  int trie_off = 0;

  if (trie_file_.size() <= 0) {
    LOG_ERROR("TrieData::SearchTrie error trie_file_ is nullptr");
    return -1;
  }
  uint32_t trie_len = trie_file_.data_size(deep);
  int ret = 0;
  const NativeTrieNode* aim = nullptr;
  // 有指定起始节点
  if (trie_start != nullptr) {
    deep = trie_start->deep + 1;
    aim = GetNode(*trie_start);
    if (aim != nullptr) {
      trie_off = aim->child_off;
      trie_len = aim->child_len;
      trie_result->deep = trie_start->deep;
      trie_result->index_id = trie_start->index_id;
    }
  }
  while (deep < trie_file_.size()) {
    // 当前层起始位置和字获取
    word_off = GetNewWord(words, word_off, one_word);
    if (word_off == -1) {
      ret = 0;
      break;
    }
    aim = trie_file_.data(deep) + trie_off;

    // 当前层搜索
    trie_off = BinSearch(aim, trie_len, one_word);
    if (trie_off == -1) {
      ret = -1;
      break;
    }
    aim = aim + trie_off;

    // 前往下一层搜索
    trie_result->deep = deep;
    trie_result->index_id = aim - trie_file_.data(deep);

    trie_len = aim->child_len;
    trie_off = aim->child_off;
    ++deep;
  }

  if (deep == trie_file_.size() && word_off != -1) {
    ret = -1;
  }

  return ret;
}
const NativeTrieNode* NativeTrie::GetNode(const IndexResult& index) const {
  return GetNode(index.deep, index.index_id);
}

const IndexResult* NativeTrie::GetTransIndex(const IndexResult& index_result) const {
  uint32_t line = 0;
  for (int i = 0; i < index_result.deep; ++i) {
    if (i < trie_file_.size()) {
      line += trie_file_.data_size(i);
    }
  }
  return trans_mapping_.data() + line + index_result.index_id;
}

bool NativeTrie::LoadIndex(const std::string& index_directory, const std::string& file_name) {
  return trie_file_.Load(FileUtils::JoinPath(index_directory, file_name));
}

bool NativeTrie::LoadMappingFile(const std::string& index_directory, const std::string& file_name) {
  return trans_mapping_.Load(FileUtils::JoinPath(index_directory, file_name));
}

const NativeTrieNode* NativeTrie::GetNode(int deep, int trie_id) const {
  if (deep >= trie_file_.size() || deep < 0) {
    return nullptr;
  }
  if (trie_id < 0 || trie_id >= trie_file_.data_size(deep)) {
    return nullptr;
  }
  return trie_file_.data(deep) + trie_id;
}
}  // namespace aurora::search
