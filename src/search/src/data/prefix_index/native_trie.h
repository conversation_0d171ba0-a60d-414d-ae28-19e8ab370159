#ifndef SEARCH_SRC_DATA_PREFIX_INDEX_NATIVE_TRIE_H
#define SEARCH_SRC_DATA_PREFIX_INDEX_NATIVE_TRIE_H

#include <string>

#include "prefix_index_def.h"
#include "utils/mmap_file.h"
#include "utils/mmap_file_header.h"
namespace aurora::search {

class NativeTrie {
 public:
  bool Load(const std::string& index_directory, const std::string& trie_file,
            const std::string& native_trans_mapping_file);

  int SearchTrie(const char* words, const IndexResult* trie_start, IndexResult* trie_result) const;
  const NativeTrieNode* GetNode(const IndexResult& index) const;
  const IndexResult* GetTransIndex(const IndexResult& index_result) const;

 private:
  bool LoadIndex(const std::string& index_directory, const std::string& file_name);
  bool LoadMappingFile(const std::string& index_directory, const std::string& file_name);

  const NativeTrieNode* GetNode(int deep, int trie_id) const;

  using NativeTrieFile = MMapFileHeader<NativeTrieNode>;
  NativeTrieFile trie_file_;
  MMapFile<IndexResult> trans_mapping_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_PREFIX_INDEX_NATIVE_TRIE_H
