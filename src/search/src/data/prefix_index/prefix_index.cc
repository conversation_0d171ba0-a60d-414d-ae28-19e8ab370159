﻿#include "prefix_index.h"

#include <logger.h>
#include <sys/stat.h>

#include "common/data_folder_layout.h"
#include "common/search_config.h"
#include "data/place_table/place_table.h"
#include "native_trie.h"
#include "trans_trie.h"
#include "utils/file_utils.h"

namespace aurora::search {
PrefixIndex::PrefixIndex(std::shared_ptr<SearchConfig> config, const std::string& city_code)
    : SearchData(std::move(config), city_code),
      folder_layout_(config_->data_folder_layout->prefix_index_layout),
      native_trie_(nullptr),
      trans_trie_(nullptr),
      poi_lines_(nullptr) {}

PrefixIndex::~PrefixIndex() {
  delete[] poi_lines_;
}

bool PrefixIndex::Load() {
  std::string index_directory = FileUtils::JoinPath(
      FileUtils::JoinPath(config_->data_dir, city_code_), folder_layout_->prefix_index_dir);

  native_trie_ = std::make_shared<NativeTrie>();
  if (!native_trie_->Load(index_directory, folder_layout_->native_trie,
                          folder_layout_->native_trans_mapping)) {
    LOG_FATAL("Failed to load native trie");
    return false;
  }

  trans_trie_ = std::make_shared<TransTrie>();
  if (!trans_trie_->Load(index_directory, folder_layout_->trans_trie,
                         folder_layout_->trans_native_mapping)) {
    LOG_FATAL("Load pinyin_index failed");
    return false;
  }

  LoadTempFile(index_directory);
  return true;
}

uint32_t PrefixIndex::TempGetMappingOffset(uint32_t k) const { return poi_lines_[k]; }
void PrefixIndex::LoadTempFile(const std::string& index_directory) {
  FILE* fp = nullptr;
  struct stat sb;

  std::string filename = FileUtils::JoinPath(index_directory, folder_layout_->place_name_sort_file);
  if ((fp = fopen(filename.c_str(), "rb")) == nullptr) {
    LOG_FATAL("Load index to poi failed");
    return;
  }
  if (stat(filename.c_str(), &sb) == -1) {
    return;
  }
  LOG_INFO("load index to place success");

  uint32_t poi_num = sb.st_size / sizeof(uint32_t);
  poi_lines_ = new uint32_t[poi_num];
  for (uint32_t j = 0; j < sb.st_size / sizeof(uint32_t); ++j) {
    fread(&poi_lines_[j], sizeof(uint32_t), 1, fp);
  }
  fclose(fp);
}

}  // namespace aurora::search
