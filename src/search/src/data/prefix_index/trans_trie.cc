#include "trans_trie.h"

#include <fcntl.h>
#include <logger.h>
#include <sys/stat.h>

#include <cstring>

#include "utils/file_utils.h"

namespace aurora::search {
namespace {
bool BinSearch(const TransTrieNode* node, uint32_t len, char letter, uint32_t* result) {
  if (len == 0 || node == nullptr) {
    return false;
  }

  uint32_t left = 0;
  uint32_t right = len;

  while (left < right) {
    const uint32_t mid = (right + left) / 2;
    const auto center = node + mid;
    if (center->code == letter) {
      *result = mid;
      return true;
    }
    if (center->code < letter) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }
  return false;
}

}  // namespace

bool TransTrie::Load(const std::string& index_directory, const std::string& trie_file,
                     const std::string& trans_native_mapping_file) {
  if (!LoadIndex(index_directory, trie_file)) {
    return false;
  }

  if (!LoadMappingFile(index_directory, trans_native_mapping_file)) {
    return false;
  }
  return true;
}

int TransTrie::SearchTrie(const char* words, const IndexResult* trans_start,
                          IndexResult* result) const {
  uint32_t deep = 0;
  uint32_t index_off = 0;
  uint32_t index_len = trie_file_.data_size(deep);
  int ret = 0;

  if (trans_start != nullptr) {
    auto start_node = GetNode(*trans_start);
    if (start_node != nullptr) {
      deep = trans_start->deep + 1;
      index_off = start_node->child_off;
      index_len = start_node->child_len;

      result->deep = trans_start->deep;
      result->index_id = trans_start->index_id;
    }
  }

  const uint32_t word_len = strlen(words);
  uint32_t char_idx = 0;
  while (deep < trie_file_.size() && char_idx < word_len) {
    // 当前层起始位置和拼音获取
    auto current_node = trie_file_.data(deep) + index_off;
    const TransTrieNode* last_aim = current_node;

    // 当前层搜索
    bool found = BinSearch(current_node, index_len, words[char_idx], &index_off);

    while (!found) {
      // 当前层搜索失败，但是当前层向下只有一个子节点，往后扩展
      if (deep + 1 < trie_file_.size() && index_len == 1) {
        result->deep = deep;
        result->index_id = current_node - trie_file_.data(deep);
        ++deep;

        index_off = last_aim->child_off;
        index_len = last_aim->child_len;
        current_node = trie_file_.data(deep) + index_off;
        found = BinSearch(current_node, index_len, words[char_idx], &index_off);
        last_aim = current_node;
      } else
        // 搜索无结果，结束
        if (index_len != 0) {
          ret = -1;
          break;
        }
        // 搜索下一节点不存在，结束
        else {
          deep = trie_file_.size();
          break;
        }
    }

    if (ret == -1 || deep == trie_file_.size()) {
      break;
    }
    current_node = current_node + index_off;

    // 前往下一层搜索
    result->deep = deep;
    result->index_id = current_node - trie_file_.data(deep);

    index_len = current_node->child_len;
    index_off = current_node->child_off;
    ++deep;
    ++char_idx;
  }

  if (deep == trie_file_.size()) {
    return ret;
  }

  if (char_idx < word_len) {
    return -1;
  }
  return ret;
}
TransTrieNode* TransTrie::GetNode(const IndexResult& index) const {
  return GetNode(index.deep, index.index_id);
}

const IndexResult* TransTrie::GetNativeIndex(uint32_t offset) const {
  if (offset >= native_mapping_.size()) {
    return nullptr;
  }
  return native_mapping_.data() + offset;
}

bool TransTrie::LoadIndex(const std::string& index_directory, const std::string& file_name) {
  return trie_file_.Load(FileUtils::JoinPath(index_directory, file_name));
}

bool TransTrie::LoadMappingFile(const std::string& index_directory, const std::string& file_name) {
  return native_mapping_.Load(FileUtils::JoinPath(index_directory, file_name));
}

TransTrieNode* TransTrie::GetNode(int deep, int trie_id) const {
  if (deep < 0 || deep >= trie_file_.size()) {
    return nullptr;
  }
  if (trie_id < 0 || trie_id >= trie_file_.data_size(deep)) {
    return nullptr;
  }
  return trie_file_.Get(deep, trie_id);
}
}  // namespace aurora::search