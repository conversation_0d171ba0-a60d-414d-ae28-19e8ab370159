#ifndef SEARCH_SRC_DATA_PREFIX_INDEX_TRANS_TRIE_H
#define SEARCH_SRC_DATA_PREFIX_INDEX_TRANS_TRIE_H

#include <string>

#include "prefix_index_def.h"
#include "utils/mmap_file.h"
#include "utils/mmap_file_header.h"

namespace aurora::search {

class TransTrie {
 public:
  bool Load(const std::string& index_directory, const std::string& trie_file,
            const std::string& trans_native_mapping_file);

  int SearchTrie(const char* words, const IndexResult* trans_start, IndexResult* result) const;
  TransTrieNode* GetNode(const IndexResult& index) const;
  const IndexResult* GetNativeIndex(uint32_t offset) const;

 private:
  bool LoadIndex(const std::string& index_directory, const std::string& file_name);
  bool LoadMappingFile(const std::string& index_directory, const std::string& file_name);

  TransTrieNode* GetNode(int deep, int trie_id) const;

  using TransTrieFile = MMapFileHeader<TransTrieNode>;
  TransTrieFile trie_file_;
  MMapFile<IndexResult> native_mapping_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_PREFIX_INDEX_TRANS_TRIE_H
