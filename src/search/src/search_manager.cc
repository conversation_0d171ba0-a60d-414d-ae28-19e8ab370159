#include "search_manager.h"

#include <execution/search_task_manager.h>
#include <logger.h>

#include "common/search_config.h"
#include "search_offline_engine.h"
#include "search_online_engine.h"
#include "wrapper/data_provider_wrapper.h"

namespace aurora::search {
SearchManager::SearchManager() = default;
SearchManager::~SearchManager() = default;

int SearchManager::Init(const std::string &search_dir) {
  
  search_config_ = std::make_shared<SearchConfig>(search_dir);
  if (!search_config_->Load()) {
    LOG_FATAL("Failed to load search config: {}", search_dir);
    return -1;
  }

  DataProviderWrapper wrapper;
  wrapper.Init(search_dir);

  search_offline_engine_ = std::make_shared<SearchOfflineEngine>();
  if (!search_offline_engine_->Init(search_config_)) {
    LOG_FATAL("Failed to init offline engine");
    return -1;
  }

  search_online_engine_ = std::make_shared<SearchOnlineEngine>();
  if (!search_online_engine_->Init(search_config_)) {
    LOG_FATAL("Failed to init online engine");
    return -1;
  }

  search_task_manager_ =
      std::make_shared<SearchTaskManager>(search_offline_engine_, search_online_engine_);
  is_init_.store(true);
  LOG_INFO("Init is successful");
  return 0;
}

int SearchManager::Destroy() { return 0; }

int SearchManager::SearchByText(const SearchByTextRequestPtr &request,
                                const SearchByTextResponseHandlerPtr &handler) {
  if (!CheckEnv(request, handler)) {
    return -1;
  }
  return search_task_manager_->AddSearchByTextTask(request, handler);
}

int SearchManager::GetDetail(const GetDetailRequestPtr &request,
                             const GetDetailResponseHandlerPtr &handler) {
  if (!CheckEnv(request, handler)) {
    return -1;
  }
  return search_task_manager_->AddGetDetailTask(request, handler);
}

int SearchManager::Autocomplete(const AutocompleteRequestPtr &request,
                                const AutocompleteResponseHandlerPtr &handler) {
  if (!CheckEnv(request, handler)) {
    return -1;
  }
  return search_task_manager_->AddAutocompleteTask(request, handler);
}

int SearchManager::ReverseGeocode(const ReverseGeocodeRequestPtr &request,
                                  const ReverseGeocodeResponseHandlerPtr &handler) {
  if (!CheckEnv(request, handler)) {
    return -1;
  }
  return search_task_manager_->AddReverseGeocodeTask(request, handler);
}

bool SearchManager::CheckEnv(const SearchRequestBasePtr &request,
                             const ResponseHandlerBasePtr &handler) {
  if (!is_init_.load()) {
    LOG_FATAL("Request before engine inited.");
    return false;
  }

  if (request == nullptr || handler == nullptr) {
    LOG_ERROR("Request and handler must not be empty.");
    return false;
  }

  return true;
}

}  // namespace aurora::search
