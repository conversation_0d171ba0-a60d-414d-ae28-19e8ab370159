#include "json_utils.h"

#include <logger.h>
namespace aurora::search {

void JsonUtils::SetIfExist(const rapidjson::Value& element, const std::string& key,
                           std::optional<std::string>* value) {
  if (element.HasMember(key.c_str()) && element[key.c_str()].IsString()) {
    *value = element[key.c_str()].GetString();
  } else {
    *value = std::nullopt;
  }
}

std::string JsonUtils::ParseString(const rapidjson::Value& element, const std::string& key,
                                   const std::string& default_value) {
  if (element.HasMember(key.c_str()) && element[key.c_str()].IsString()) {
    return element[key.c_str()].GetString();
  }
  return default_value;
}

int JsonUtils::ParseInt(const rapidjson::Value& element, const std::string& key,
                        const int default_value) {
  if (element.HasMember(key.c_str()) && element[key.c_str()].IsInt()) {
    return element[key.c_str()].GetInt();
  }
  return default_value;
}

double JsonUtils::ParseDouble(const rapidjson::Value& element, const std::string& key,
                              const double default_value) {
  if (element.HasMember(key.c_str()) && element[key.c_str()].IsNumber()) {
    return element[key.c_str()].GetDouble();
  }
  return default_value;
}
bool JsonUtils::ParseBool(const rapidjson::Value& element, const std::string& key,
                          bool default_value) {
  if (element.HasMember(key.c_str()) && element[key.c_str()].IsBool()) {
    return element[key.c_str()].GetBool();
  }
  return default_value;
}

std::vector<std::string> JsonUtils::ParseStringArray(const rapidjson::Value& element,
                                                     const std::string& key) {
  std::vector<std::string> city_list;
  if (element.HasMember(key.c_str()) && element[key.c_str()].IsArray()) {
    const rapidjson::Value& arr = element[key.c_str()];
    for (size_t i = 0; i < arr.Size(); ++i) {
      const rapidjson::Value& item = arr[i];
      if (item.IsString()) {
        city_list.push_back(item.GetString());
      }
    }
  }
  return city_list;
}
}  // namespace aurora::search