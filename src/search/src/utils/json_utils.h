#ifndef SEARCH_SRC_UTILS_JSON_UTILS_H
#define SEARCH_SRC_UTILS_JSON_UTILS_H

#include <rapidjson/document.h>

#include <optional>
#include <vector>

namespace aurora::search {
class JsonUtils {
 public:
  static void SetIfExist(const rapidjson::Value& element, const std::string& key,
                         std::optional<std::string>* value);
  static std::string ParseString(const rapidjson::Value& element, const std::string& key,
                                 const std::string& default_value = "");
  static int ParseInt(const rapidjson::Value& element, const std::string& key,
                      int default_value = 0);
  static double ParseDouble(const rapidjson::Value& element, const std::string& key,
                            double default_value = 0.0);
  static bool ParseBool(const rapidjson::Value& element, const std::string& key,
                        bool default_value);
  static std::vector<std::string> ParseStringArray(const rapidjson::Value& element,
                                                   const std::string& key);
};
}  // namespace aurora::search
#endif  // SEARCH_SRC_UTILS_JSON_UTILS_H