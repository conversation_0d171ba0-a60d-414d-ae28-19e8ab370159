#ifndef SEARCH_SRC_UTILS_MMAP_FILE_H
#define SEARCH_SRC_UTILS_MMAP_FILE_H
#include <fcntl.h>
#include <logger.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>

namespace aurora::search {

template <typename T>
class MMapFile {
 public:
  MMapFile() : data_(nullptr), file_size_(0), size_(0) {}
  ~MMapFile() {
    if (data_ != nullptr) {
      if (munmap(data_, file_size_) == -1) {
        LOG_ERROR("Failed to munmap {}: {}", name_, strerror(errno));
      } else {
        LOG_INFO("Successfully munmap: {}", name_);
      }
      data_ = nullptr;
    }
  }
  bool Load(const std::string& file_name) {
    struct stat file_stat{};

    // Check if the file exists
    if (stat(file_name.c_str(), &file_stat) == -1) {
      LOG_ERROR("Failed to stat file: {}", file_name);
      return false;
    }

    // Open the file
    int file_descriptor = open(file_name.c_str(), O_RDONLY);
    if (file_descriptor == -1) {
      LOG_ERROR("Failed to open file: {}", file_name);
      return false;
    }

    // Memory-map the file
    void* mapped_memory =
        mmap(nullptr, file_stat.st_size, PROT_READ, MAP_PRIVATE, file_descriptor, 0);
    if (mapped_memory == reinterpret_cast<void*>(-1)) {
      LOG_ERROR("Failed to mmap file: %s", file_name.c_str());
      close(file_descriptor);
      return false;
    }

    // Close the file descriptor
    if (close(file_descriptor) == -1) {
      LOG_WARN("Failed to close file: {}", file_name);
    }
    name_ = file_name;
    data_ = static_cast<T*>(mapped_memory);
    file_size_ = file_stat.st_size;
    size_ = file_size_ / sizeof(T);
    return true;
  }
  T* Get(uint32_t index) const { return data_ + index; }
  T* data() const { return data_; }
  uint32_t size() const { return size_; }

 private:
  std::string name_;
  T* data_;
  uint32_t file_size_;  // file_size
  uint32_t size_;       // item_num
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_UTILS_MMAP_FILE_H
