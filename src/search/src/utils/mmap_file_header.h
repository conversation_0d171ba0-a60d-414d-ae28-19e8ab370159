#ifndef SEARCH_SRC_UTILS_MMAP_FILE_HEADER_H
#define SEARCH_SRC_UTILS_MMAP_FILE_HEADER_H
#include <fcntl.h>
#include <logger.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>

#include <vector>

#include "../data/prefix_index/prefix_index_def.h"
#include <string>
namespace aurora::search {

struct FileHeaderVersion {
  uint32_t version;
  uint32_t data_num;
  FileHeaderVersion() : version(1), data_num(0) {}
  FileHeaderVersion(uint32_t data_num_) : version(1), data_num(data_num_) {}
};

struct FileHeaderInfo {
  uint32_t offset;
  uint32_t len;
  FileHeaderInfo() : offset(0), len(0) {}
  FileHeaderInfo(uint32_t offset_, uint32_t len_) : offset(offset_), len(len_) {}
};

struct DataInfo {
  uint32_t size;
  void* p;
};

template <typename T>
class MMapFileHeader {
 public:
  ~MMapFileHeader() {
    if (data_info_.size() > 0) {
      for (auto info : data_info_) {
        if (info.p != nullptr) {
          if (munmap(info.p, info.size * sizeof(T)) == -1) {
            LOG_ERROR("Failed to munmap {}: {}", name_, strerror(errno));
          } else {
            LOG_INFO("Successfully munmap: {}", name_);
          }
          info.p = nullptr;
          info.size = 0;
        }
      }
    }
  }
  bool Load(const std::string& file_name) {
    struct stat file_stat{};

    // Check if the file exists
    if (stat(file_name.c_str(), &file_stat) == -1) {
      LOG_ERROR("Failed to stat file: {}", file_name);
      return false;
    }

    // Open the file
    int file_descriptor = open(file_name.c_str(), O_RDONLY);
    if (file_descriptor == -1) {
      LOG_ERROR("Failed to open file: {}", file_name);
      return false;
    }
    // read head version
    uint32_t len = sizeof(FileHeaderVersion);
    ssize_t read_len = pread(file_descriptor, &index_header_version_, len, 0);
    if (read_len != len) {
      LOG_ERROR("read FileHeaderVersion size error {} !=  {}", read_len, len);
      return false;
    }
    if (index_header_version_.data_num <= 0) {
      LOG_ERROR("no data to read {}", file_name.c_str());
      return true;
    }
    // Memory-map the file
    len = sizeof(FileHeaderInfo) * index_header_version_.data_num;
    index_header_info_.resize(index_header_version_.data_num);
    data_info_.resize(index_header_version_.data_num);
    read_len = pread(file_descriptor, index_header_info_.data(), len, sizeof(FileHeaderVersion));
    if (read_len != len) {
      LOG_ERROR("read FileHeaderVersion size error {} !=  {}", read_len, len);
      return false;
    }
    uint32_t off =
        sizeof(FileHeaderVersion) + sizeof(FileHeaderInfo) * index_header_version_.data_num;
    uint32_t max_index = index_header_info_.size() - 1;
    uint32_t total_len =
        off + index_header_info_[max_index].offset + index_header_info_[max_index].len;
    mapped_memory_ = mmap(nullptr, total_len, PROT_READ, MAP_PRIVATE, file_descriptor, 0);
    if (mapped_memory_ == reinterpret_cast<void*>(-1)) {
      LOG_ERROR("Failed to mmap file: {}", file_name);
      close(file_descriptor);
      return false;
    }
    for (uint32_t i = 0; i < index_header_version_.data_num; i++) {
      uint32_t off = sizeof(FileHeaderVersion) +
                     sizeof(FileHeaderInfo) * index_header_version_.data_num;
      data_info_[i].p = mapped_memory_ + off + index_header_info_[i].offset;
      std::string word(((NativeTrieNode*)(data_info_[i].p))->word);
      data_info_[i].size = index_header_info_[i].len / sizeof(T);
    }

    // Close the file descriptor
    if (close(file_descriptor) == -1) {
      LOG_WARN("Failed to close file: {}", file_name);
    }

    LOG_INFO("Successfully opened and mmaped file: {}", file_name.c_str());
    name_ = file_name;
    // data_ = static_cast<T*>(mapped_memory);
    return true;
  }

  // T* Get(uint32_t index) const { return data_ + index; }
  T* data(uint32_t data_index) const {
    if (data_index >= data_info_.size()) {
      return nullptr;
    }
    return static_cast<T*>(data_info_[data_index].p);
  }
  uint32_t size() const { return index_header_version_.data_num; }
  T* Get(uint32_t data_index, uint32_t index) const {
    if (data_index >= data_info_.size()) {
      return nullptr;
    }
    return static_cast<T*>(data_info_[data_index].p) + index;
  }

  uint32_t data_size(uint32_t data_index) const {
    if (data_index >= data_info_.size()) {
      return 0;
    }
    return data_info_[data_index].size;
  }

 private:
  std::string name_;
  void* mapped_memory_;
  FileHeaderVersion index_header_version_;
  std::vector<FileHeaderInfo> index_header_info_;
  std::vector<DataInfo> data_info_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_UTILS_MMAP_FILE_H
