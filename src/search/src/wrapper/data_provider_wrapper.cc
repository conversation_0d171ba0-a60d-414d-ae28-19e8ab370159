#include "data_provider_wrapper.h"

#include <cmath>

#include "logger.h"
#include "utils/file_utils.h"

using namespace std;
using namespace aurora::parser;

namespace aurora::search {
bool DataProviderWrapper::Init(const std::string& data_dir) {
  DataProvider provider;
  bool ret = provider.InitRouteParser(FileUtils::JoinPath(data_dir, "../route").c_str());
  LOG_DEBUG("init {}", ret);
  return ret;
}

void DataProviderWrapper::SetConfig(std::shared_ptr<SearchConfig> config) {
  config_ = std::move(config);
}

std::vector<std::string> DataProviderWrapper::GetCityList() const {
  // todo zq get for DataProvider
  return config_->city_data_list;
}
std::vector<std::string> DataProviderWrapper::GetCityList(const PointLL& location) const {
  vector<std::string> city_list;
  DataProvider provider;
  GeoMbr mbr(location.lng() - 0.001, location.lat() - 0.001, location.lng() + 0.001,
             location.lat() + 0.001);
  LOG_DEBUG("lat: {}, lng :{}", location.lat(), location.lng());
  vector<uint32_t> adcode_list = provider.GetAdcodeByMBR(mbr);
  for (auto adcode : adcode_list) {
    auto citycode = Trans2Citycode(adcode);
    LOG_DEBUG("adcode :{} ==> citycode: {} , totalsize: {}", adcode, citycode, adcode_list.size());
    if (!citycode.empty() &&
        std::find(city_list.begin(), city_list.end(), citycode) == city_list.end()) {
      city_list.push_back(citycode);
    }
  }
  return city_list;
}

std::vector<std::string> DataProviderWrapper::GetCityList(const BoundsInternalPtr& bounds) const {
  vector<std::string> city_list;
  if (bounds == nullptr) {
    return city_list;
  }
  if (bounds->type() == BoundsInternal::Type::kRectangular ||
      bounds->type() == BoundsInternal::Type::kCircular ||
      bounds->type() == BoundsInternal::Type::kPolyline) {
    DataProvider provider;
    vector<uint32_t> adcode_list = provider.GetAdcodeByMBR(bounds->GetMbr());
    for (auto adcode : adcode_list) {
      auto citycode = Trans2Citycode(adcode);
      LOG_DEBUG("adcode :{} ==> citycode: {} , totalsize: {}", adcode, citycode,
                adcode_list.size());
      if (!citycode.empty() &&
          std::find(city_list.begin(), city_list.end(), citycode) == city_list.end()) {
        city_list.push_back(citycode);
      }
    }
  }
  return city_list;
}

std::string DataProviderWrapper::Trans2Citycode(uint32_t adcode) const {
  char buffer[7];
  sprintf(buffer, "%06u", adcode);
  string str(buffer);
  string prefix = str.substr(0, 2);
  auto it = mapping_.find(prefix);
  if (it != mapping_.end()) {
    return it->second;
  }
  str[4] = '0';
  str[5] = '0';
  return str;
}

}  // namespace aurora::search
