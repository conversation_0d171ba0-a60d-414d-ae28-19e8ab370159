#ifndef SEARCH_SRC_WRAPPER_DATA_PROVIDER_WRAPPER_H
#define SEARCH_SRC_WRAPPER_DATA_PROVIDER_WRAPPER_H

#include <map>
#include <string>
#include <vector>

#include "common/bounds.h"
#include "common/search_config.h"
#include "data_provider.h"

namespace aurora::search {

struct SearchConfig;

class DataProviderWrapper {
 public:
  bool Init(const std::string& data_dir);
  void SetConfig(std::shared_ptr<SearchConfig> config);
  std::vector<std::string> GetCityList() const;
  std::vector<std::string> GetCityList(const PointLL& location) const;
  std::vector<std::string> GetCityList(const BoundsInternalPtr& bounds) const;

 private:
  std::string Trans2Citycode(uint32_t adcode) const;

  std::shared_ptr<SearchConfig> config_;

  std::map<std::string, std::string> mapping_ = {
      {"11", "110000"}, {"12", "120000"}, {"31", "310000"}, {"50", "500000"}};
};

}  // namespace aurora::search
#endif