cmake_minimum_required(VERSION 3.10)

include(third_party.cmake)
# 强制使用自己编译的三方库
# if (USE_THIRD_PARTY STREQUAL "ON")
#     message(STATUS "USE_THIRD_PARTY is ON, using third-party libraries.")
#     add_subdirectory(zlib)
#     set(BUILD_ZLIB ON)
#     add_subdirectory(libpng)
#     set(BUILD_PNG ON)
#     include(ExternalProject)
#     ExternalProject_Add(
#         libjpeg-turbo
#         SOURCE_DIR "${CMAKE_SOURCE_DIR}/third_party/libjpeg-turbo"  # libjpeg-turbo 源码路径
#         CMAKE_ARGS 
#             "-DCMAKE_INSTALL_PREFIX=${CMAKE_LIBRARY_OUTPUT_DIRECTORY}/../"  # 指定安装到 CMAKE_LIBRARY_OUTPUT_DIRECTORY
#         BUILD_COMMAND ${CMAKE_COMMAND} --build .  # 使用 CMake 构建
#         INSTALL_COMMAND ${CMAKE_COMMAND} --install .  # 触发安装，将库复制到目标目录
#         LOG_CONFIGURE ON
#         LOG_BUILD ON
#         LOG_INSTALL ON  # 可选：启用安装日志
#     )
#     set(BUILD_JPEG ON)
#     add_subdirectory(freetype2)
#     set(BUILD_FREETYPE ON)


# else()
find_package(ZLIB)
if (NOT ZLIB_FOUND OR "${USE_THIRD_PARTY}" STREQUAL "ON")
    message(STATUS "ZLIB not found. Building from source.")
    add_subdirectory(zlib)
    SET(BUILD_ZLIB ON CACHE PATH "build ZLIB" FORCE)
    # 修改此行：添加 CACHE 参数使其全局生效
    SET(ZLIB_INCLUDE_DIRS ${CMAKE_SOURCE_DIR}/third_party/zlib CACHE PATH "Include directory for ZLIB" FORCE)
    message("======zlib include dir:${ZLIB_INCLUDE_DIRS}")
    SET(ZLIB_INCLUDE_DIR ${CMAKE_SOURCE_DIR}/third_party/zlib CACHE PATH "Include directory for ZLIB" FORCE)
    message("======zlib include dir:${ZLIB_INCLUDE_DIR}")
endif()

find_package(PNG)
if (NOT PNG_FOUND OR "${USE_THIRD_PARTY}"STREQUAL "ON")
    message(STATUS "PNG not found. Building from source.")
    add_subdirectory(libpng)
    SET(BUILD_PNG ON CACHE PATH "build png" FORCE)
endif()
find_package(JPEG)
if (NOT JPEG_FOUND OR "${USE_THIRD_PARTY}" STREQUAL "ON")
    message(STATUS "JPEG not found. Building from source.")
    include(ExternalProject)
    ExternalProject_Add(
        libjpeg-turbo
        SOURCE_DIR "${CMAKE_SOURCE_DIR}/third_party/libjpeg-turbo"  # libjpeg-turbo 源码路径
        CMAKE_ARGS 
            "-DCMAKE_INSTALL_PREFIX=${CMAKE_LIBRARY_OUTPUT_DIRECTORY}/../"  # 指定安装到 CMAKE_LIBRARY_OUTPUT_DIRECTORY
        BUILD_COMMAND ${CMAKE_COMMAND} --build .  # 使用 CMake 构建
        INSTALL_COMMAND ${CMAKE_COMMAND} --install .  # 触发安装，将库复制到目标目录
        LOG_CONFIGURE ON
        LOG_BUILD ON
        LOG_INSTALL ON  # 可选：启用安装日志
    )
    SET(BUILD_JPEG ON CACHE PATH "build jpeg" FORCE)
endif()
find_package(Freetype)
if (NOT FREETYPE_FOUND OR "${USE_THIRD_PARTY}" STREQUAL "ON")
    message(STATUS "Freetype not found. Building from source.")
     set(SKIP_INSTALL_HEADERS OFF CACHE BOOL "Skip installing headers" FORCE)
    add_subdirectory(freetype2)
    SET(BUILD_FREETYPE ON CACHE PATH "build freetype" FORCE)
else()
    message(STATUS "Freetype found. Using system freetype. ${FREETYPE_INCLUDE_DIRS}")
    set(FREETYPE_INCLUDE_DIR ${FREETYPE_INCLUDE_DIRS} CACHE PATH "Include directory for Freetype" FORCE)
endif()

# endif(USE_THIRD_PARTY)


include(ExternalProject)
# 声明 Boost 源码下载和解压
ExternalProject_Add(
    Boost_1_71_0
    URL ${CMAKE_SOURCE_DIR}/third_party/boost_1_71_0.tar.gz
    CONFIGURE_COMMAND ./bootstrap.sh
    BUILD_COMMAND ./b2 --with-system --with-filesystem --with-chrono --with-date_time  --with-regex
    INSTALL_COMMAND ""
    BUILD_IN_SOURCE TRUE
    LOG_DOWNLOAD ON
)

# 设置Boost路径变量，使用正确的路径并导出到父作用域
set(BOOST_ROOT "${CMAKE_BINARY_DIR}/third_party/Boost_1_71_0-prefix/src/Boost_1_71_0" CACHE PATH "Boost root directory" FORCE)
set(BOOST_LIBRARYDIR "${BOOST_ROOT}/stage/lib" CACHE PATH "Boost library directory" FORCE)
set(Boost_INCLUDE_DIRS "${BOOST_ROOT}" CACHE PATH "Boost include directories" FORCE)

# 导出变量到父作用域，方便其他模块使用
set(BOOST_ROOT ${BOOST_ROOT} PARENT_SCOPE)
set(BOOST_LIBRARYDIR ${BOOST_LIBRARYDIR} PARENT_SCOPE)
set(Boost_INCLUDE_DIRS ${Boost_INCLUDE_DIRS} PARENT_SCOPE)
set(RAPIDJSON_SOURCE_DIR ${RAPIDJSON_SOURCE_DIR} PARENT_SCOPE)

# 创建接口库，便于其他模块链接
add_library(Boost_INTERFACE INTERFACE)
target_include_directories(Boost_INTERFACE INTERFACE ${Boost_INCLUDE_DIRS})
target_link_directories(Boost_INTERFACE INTERFACE ${BOOST_LIBRARYDIR})
target_link_libraries(Boost_INTERFACE INTERFACE 
    ${BOOST_LIBRARYDIR}/libboost_system.so
    ${BOOST_LIBRARYDIR}/libboost_filesystem.so
    ${BOOST_LIBRARYDIR}/libboost_chrono.so
    ${BOOST_LIBRARYDIR}/libboost_date_time.so
    ${BOOST_LIBRARYDIR}/libboost_regex.so
)

# 导出Boost库列表，方便其他模块使用
set(Boost_LIBRARIES
    ${BOOST_LIBRARYDIR}/libboost_system.so
    ${BOOST_LIBRARYDIR}/libboost_filesystem.so
    ${BOOST_LIBRARYDIR}/libboost_chrono.so
    ${BOOST_LIBRARYDIR}/libboost_date_time.so
    ${BOOST_LIBRARYDIR}/libboost_regex.so
    CACHE STRING "Boost libraries" FORCE
)
set(Boost_LIBRARIES ${Boost_LIBRARIES} PARENT_SCOPE)

message(STATUS "Boost root directory: ${BOOST_ROOT}")
message(STATUS "Boost library directory: ${BOOST_LIBRARYDIR}")
message(STATUS "Boost include directories: ${Boost_INCLUDE_DIRS}")
message(STATUS "Boost libraries: ${Boost_LIBRARIES}")


# 根据选项决定是否添加 gtest/gmock 子目录
if(ENABLE_GTEST)
    message(STATUS "GoogleTest is enabled.")

    set(GTEST_ROOT ${CMAKE_SOURCE_DIR}/third_party/googletest-1.16.x)
    add_subdirectory(${GTEST_ROOT})
    include_directories(${GTEST_ROOT}/googletest/include)
    if (ENABLE_GMOCK)
        include_directories(${GTEST_ROOT}/googlemock/include)
    endif()
endif()

add_subdirectory(lua-5.4.7)
add_subdirectory(sqlite)
